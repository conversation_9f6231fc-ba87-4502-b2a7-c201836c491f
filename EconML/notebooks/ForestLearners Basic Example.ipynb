{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Forest<PERSON><PERSON>, ForestDR<PERSON><PERSON>ner, OrthoForest and CausalForest: Basic Example\n", "\n", "We depict the performance of our `ForestDML`, `ForestDRLearner`, `OrthoForest` and `CausalForest` estimators on the same data generating process as the one used in the tutorial page of the grf package (see https://github.com/grf-labs/grf#usage-examples). This is mostly for qualitative comparison and verification purposes among our implementation of variants of Causal Forests and the implementation in the grf R package."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Helper imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DGP\n", "\n", "We use the following data generating process (DGP) from [here](https://github.com/grf-labs/grf#usage-examples):\n", "\n", "\\begin{align}\n", "X \\sim& \\text{Normal}(0,\\, I_{p})\\\\\n", "T =& \\text{Binomial}(1, .4 + .2 \\cdot 1\\{X[0] > 0\\})\\\\\n", "Y =& (X[0] \\cdot 1\\{X[0] > 0\\}) \\cdot T + X[1] + X[2] \\cdot 1\\{X[2] < 0\\} + \\epsilon, &\\; \\epsilon \\sim \\text{Normal}(0, 1)\\\\\n", "\\end{align}\n", "\n", "We use $p=10$ and draw $n=2000$ samples from this DGP."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import scipy.special\n", "np.random.seed(123)\n", "n = 2000\n", "p = 10\n", "X = np.random.normal(size=(n, p))\n", "true_propensity = lambda x: .4 + .2 * (x[:, 0] > 0)\n", "true_effect = lambda x: (x[:, 0] * (x[:, 0] > 0))\n", "true_conf = lambda x: x[:, 1] + np.clip(x[:, 2], - np.inf, 0)\n", "T = np.random.binomial(1, true_propensity(X))\n", "Y =  true_effect(X) * T + true_conf(X) + np.random.normal(size=(n,))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cross-Validated Forest Nuisance Models\n", "\n", "We use forest based estimators (Gradient Boosted Forests or Random Forests) as nuisance models. For the meta-learner versions of our forest based estimators, we also use a generic forest estimator even as a final model. The hyperparameters of the forest models (e.g. number of estimators, max depth, min leaf size) is chosen via cross validation. We also choose among Gradient or Random Forests via cross validation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from econml.sklearn_extensions.model_selection import GridSearchCVList\n", "from sklearn.linear_model import Lasso, LogisticRegression\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.base import clone\n", "from econml.sklearn_extensions.linear_model import WeightedLasso\n", "\n", "def first_stage_reg():\n", "    return GridSearchCVList([<PERSON><PERSON>(),\n", "                             RandomForestRegressor(n_estimators=100, random_state=123),\n", "                             GradientBoostingRegressor(random_state=123)],\n", "                             param_grid_list=[{'alpha': [.001, .01, .1, 1, 10]},\n", "                                               {'max_depth': [3, None],\n", "                                               'min_samples_leaf': [10, 50]},\n", "                                              {'n_estimators': [50, 100],\n", "                                               'max_depth': [3],\n", "                                               'min_samples_leaf': [10, 30]}],\n", "                             cv=5,\n", "                             scoring='neg_mean_squared_error')\n", "\n", "def first_stage_clf():\n", "    return GridSearchCVList([LogisticRegression(),\n", "                             RandomForestClassifier(n_estimators=100, random_state=123),\n", "                             GradientBoostingClassifier(random_state=123)],\n", "                             param_grid_list=[{'C': [0.01, .1, 1, 10, 100]},\n", "                                              {'max_depth': [3, 5],\n", "                                               'min_samples_leaf': [10, 50]},\n", "                                              {'n_estimators': [50, 100],\n", "                                               'max_depth': [3],\n", "                                               'min_samples_leaf': [10, 30]}],\n", "                             cv=5,\n", "                             scoring='neg_mean_squared_error')\n", "\n", "def final_stage():\n", "    return GridSearchCVList([WeightedLasso(),\n", "                             RandomForestRegressor(n_estimators=100, random_state=123)],\n", "                             param_grid_list=[{'alpha': [.001, .01, .1, 1, 10]},\n", "                                              {'max_depth': [3, 5],\n", "                                               'min_samples_leaf': [10, 50]}],\n", "                             cv=5,\n", "                             scoring='neg_mean_squared_error')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["GradientBoostingRegressor(min_samples_leaf=30, n_estimators=50,\n", "                          random_state=123)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["model_y = clone(first_stage_reg().fit(X, Y).best_estimator_)\n", "model_y"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["RandomForestClassifier(max_depth=3, min_samples_leaf=10, random_state=123)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["model_t = clone(first_stage_clf().fit(X, T).best_estimator_)\n", "model_t"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DML Estimators"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.causal_forest.CausalForestDML at 0x1a07d0cd188>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.dml import CausalForestDML\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier\n", "from sklearn.dummy import DummyRegress<PERSON>, DummyClassifier\n", "\n", "n_samples, n_features = X.shape\n", "subsample_fr_ = (n_samples/2)**(1-1/(2*n_features+2))/(n_samples/2)\n", "est = CausalForestDML(model_y=model_y,\n", "                      model_t=model_t,\n", "                      discrete_treatment=True,\n", "                      cv=3,\n", "                      n_estimators=4000,\n", "                      random_state=123)\n", "est.tune(Y, T, X=X).fit(Y, T, X=X, cache_values=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.dml.NonParamDML at 0x1a07cee4708>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.dml import NonParamDML\n", "est2 = NonParamDML(model_y=model_y,\n", "                   model_t=model_t,\n", "                   cv=3,\n", "                   discrete_treatment=True,\n", "                   model_final=final_stage())\n", "est2.fit(Y, T, X=X)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["X_test = np.zeros((100, p))\n", "X_test[:, 0] = np.linspace(-2, 2, 100)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["pred = est.effect(X_test)\n", "lb, ub = est.effect_interval(X_test, alpha=0.01)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["pred2 = est2.effect(X_test)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.plot(X_test[:, 0], true_effect(X_test), '--')\n", "plt.plot(X_test[:, 0], pred2, label='nonparamdml')\n", "plt.plot(X_test[:, 0], pred, label='forestdml (causal forest)')\n", "plt.fill_between(X_test[:, 0], lb, ub, alpha=.4, label='honestrf_ci')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/latex": ["$\\displaystyle 0.02469985268609504$"], "text/plain": ["0.02469985268609504"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["np.mean((true_effect(X) - est.effect(X))**2)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/latex": ["$\\displaystyle 0.041538512990649396$"], "text/plain": ["0.041538512990649396"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["np.mean((true_effect(X) - est2.effect(X))**2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### First Stage Learned Models"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Model T\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title('honestrf')\n", "for mdls in est.models_t:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict_proba(X_test)[:, 1])\n", "plt.plot(X_test[:, 0], true_propensity(X_test), '--', label='truth')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title('rf')\n", "for mdls in est2.models_t:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict_proba(X_test)[:, 1])\n", "plt.plot(X_test[:, 0], true_propensity(X_test), '--', label='truth')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Model Y\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title('honestrf')\n", "for mdls in est.models_y:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict(X_test))\n", "plt.plot(X_test[:, 0], true_effect(X_test) * true_propensity(X_test) + true_conf(X_test), '--', label='truth')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title('rf')\n", "for mdls in est2.models_y:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict(X_test))\n", "plt.plot(X_test[:, 0], true_effect(X_test) * true_propensity(X_test) + true_conf(X_test), '--', label='truth')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interpretability of CATE Model of NonParamDML with SHAP"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 97%|=================== | 97/100 [00:41<00:01]       "]}], "source": ["import shap\n", "import string\n", "\n", "feature_names = list(string.ascii_lowercase)[:X.shape[1]]\n", "# explain the model's predictions using SHAP values\n", "shap_values = est.shap_values(X[:100], feature_names=feature_names)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(shap_values[\"Y0\"][\"T0_1\"][0], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAdgAAAFfCAYAAADtbN35AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+j8jraAAAgAElEQVR4nOzdd5xcdb3/8dd32vaa3nuFEIRDEZASOhppioCAiHpRudiu4k+vSvOiomJHRUBBLCAiEoqCgdBLDiWQkEJIL5vtu7Nl6vn+/phls9lN2Q0Zzuzk/Xw85pHvnDnnO5+zMzmf+ZZzjrHWIiIiIvtWwO8ARERE8pESrIiISBYowYqIiGSBEqyIiEgWKMGKiIhkgRKsiIhIFijBiojIoGCMWWeMObDXMtcYc7wx5jpjzMf6Ucc1xpgfZS/K7ULvxZuIiIhkk7X2O37H0JtasCIiMugZY/5gjPnvrnKFMebvxpgVxpiFxpg7e7VaxxhjHu56/SFjTHE2YlILVkREBpN7jTGxHs+n72Sd7wBN1tqZxphq4GXg7z1ed4DDgBbg38DHgd/t60BzPcHqOo4DtGDBAgDmz5/vcyQikodMdmo9p++x3t63q/f6iLV2afemxrg7WecE4EoAa22jMeb+Xq//21rb3LX9i8CUvQl7T9RFLCIiPjM7ebzrCnfXQOvZAk6TpcamEqyIiOSbJ4BPABhjqoAz/QhCCVZERHy2z1uw1wHDjTHLgLuAZ8mMt76ncn0MVkRE8l7/Eqq1duJOljldxUU9FrcDF1hrY8aYcuAZ4I6u9a/ptf0Oz/clJVgREfHZPp87VQU8YowJAoXAn621/9nXb7InSrAiIpJXrLW1wKF+x6EEKyIiPsvO2T9+U4IVERGf5WeC1SxiERGRLFALVkREfJafLVglWBER8ZkSrIiISBYowYqIiGSBEqyIiMg+Z3eSYPMh5WoWsYiISBbsVy3YZzdbHnzb44hRhrOmBaitS/Lkk1EqK0OcOK+MQCAffjOJiEgu2G8S7Bt1lnl3p0l4AJa/nubx1G+20NLqAdDQkOLcsyrY4DZRNqyA4dPLfI1XRGT/kZ+Nm/0mwb60xetKroC1PHxvLe1tlgIyH+2KN9q5+7E1bFseBQOnfXMmB5wxyseIRUT2DxqDHeQODCUoSKXBs5C03Bkr5+8jhrCwupw0UFHbkkmuABaWPVLja7wiIvuPfX4/2Jyw3yTYORPCXLpqPXhALAU2s3xbQYTOtnaobScdMKRCQdLBAERj3HPqYzz8yWfpqI35GruISH5Tgh3U1m1IMmVWKWHrgdnxwytLJSna3IQNBMAYbCDApuXttKxtY/Oztbzwgzd8ilpEJP9ZTJ9HPsj7MdjWuOXO5xO4f9iKl4aDK5pZPKIcQgZSHoXxJCe//japcBBKSnbYNpRMkwoFSESTPkUvIiKDVV63YLe2eRzx+yTf/XeMmGdoDAUZ0plgek0UYklIe3zSfRPIJNPi1g4AjOdRXddKUTxNccrj0CtnEW9NUr+ihVQ8vdfxxDa107asaaeveUmPtiWNJOp33x0d39ZJ9PVGbNrb7XqDUWJZPalNUb/D2CkbT5F4rQavqbPf26SW1ZLe2LLbddJvN5B+q35gsURjeK9txLbH92K7TQPeLutWbIJ1tX5Hkd+shaXrYXOD35HsQn52EedtC/apjZbT/+7RkQpAWQH/GF5FIhCgPJXCaeskEDSsKC8mWlLYvU0g7VHQFqOqrhljDKmgIeJZgsUh/vThRcSaElRPK+Ps37+fgrLwgOKp+csall3yDDZlGXnxZA688wPdr3nxNK+d+C9anq0lWBZm7r9PoeL9w/vU0bhwC0vmL8TrTFN9ymjmPnQSgVB+/EbadslDtP1xGYQCDL/zDMoumO13SN28aJzaY/9I8rVtBKqLGLboIiJz+n4+PbV86n5it78CwQDlvz+LoosP7rNOx/efpOMbjwJQfM08iq8+cY+x2A2NJI+6ETY3w+ShhJ+7CjOifM/7sKGR+NE/xW5qxkweQuFzX+7Xdln3P3+AmxZkhm1+8Sm44nS/I8pPF/0U/vw0hILwpy/BeUf7HdEOrN8BZEm/js6O43zRcZwVjuNEHcfZ4DjO9xzHCWY7uL31ep3ljPvSdKTI/HJrS5IIZHa1NRQiGU/QbiwB4K7DD+CGU47gpRnjaR5WSXlja2YcFkgHDaXDC3j66IcoX1ZPOJGm8a0oax7byrPnPcE/x97DC5c8jZfItGpTrQmWnfggLxTdxsqP/YeVX1nMf4r+yNMld7Ds409hUxawNP1xJS8U3sabpz1Muj1J81M1dD67hRLiRKLtbLj2FZYf+0/cot+x5pLHsV7m67fxp2/idWbeq/HRLay/4ums/h07Fm9jxcTfs6z81zT8Zu/HoeNv1LFm2u2sKv0FjT92+7xe+4WFmeQKkPJo/tHi3daXeL2WjdN+y7qym2i56aXdrpta38Lmg29jffEPafza43sVf+yRt0m+tg0LJBtjbDnk9zRfu/O/fcPnHmFD0fczyRUg7dHxw2f71vnQKlq/+TgpgngYOr73FDbt0XLh39hWdB2NJ9yO17K9N8Pb1ETnoTcSm3I13uZW0gRJr2ki/ctF3eukH19J5+hv0ln1NVJ/2f53TnztfmJTr8NuaiREnOCazaQu//Ne/S26/fwhKLsQJn8OXl3T9/U31sO0K6D0QvjR/Tu+1h6DU6+Doo/BTQ8ANvP/9Af3961HBuZbf4aSC2DOl2BN15kQ62szyRUglYbr/+ZffLuUny3Y/jZ/NgGnA+XAmcBlwKezFdS79ZUnPNp3M2y6raiA0R0xhre0M6ytkxkdcUqAR8eO4s2RQ3t8xob2De3YhEcobSnv6h6MvlJP7eM1eLE0Wx/exIZ71gFQ84ultDy+BS+Wpvaetaz/yZsEYknoSGJsJkmG8AjhYeNpWv69iW23LIdYighpDBDEklhcQ9vTW7GxNA1/XEXzPzP1F4zrOUZsabrlDeJvNe/jv952W65cRHJ9FC+aZMt/LyLdtHezqWu/8iTJ1c3Y9iR1X3uKZI9u4PjrdbT84pUdfsGGxu3+Ih8NX1lIanUTti1B41cfJ7V5193KTVc/TXJJLbYzReuPXiS+eMuA4w+Oy7T0vHe+GCmP5mueIbFix+62zoVrafvNq9hYeof9CYzr21Jsvez+rp/tBo8ApjhM7J6lxP7yBsRSJBeto+Nnz3evn7zu39hXNkHKI/PTMBNLeuGq7etc/lfY2grNnSQv+xM27ZFevJ7Ujx6HpEeQ1PbpIw+8gt208+GKPapthi/dDm0xWLstU+7tf/4Aq7dmkulVf4SNPbrBf/0vePS1zDBNT+OG7F08kvH6Ovi/e6EjDks3wP92/Yhq6/X/tm73wxZ+yNdJTv1KsK7r/t113bWu61rXdV8F/gjsuT/rXYpGo3td3oExEDBgoCjt0RKJsKyinG3FhdQFQ6wrKmTh2JGsKy+lqaRox217HCkD1nLEZ6dSNWrHdWKxPSeeEGkCeH3+4vFYnKKpOx6Aw0MLdnje2ZlJ7FN/4FAyLkKYFGXECPYIrue+RyKRnS4faDm9i3HegdaTSu983Hr7Ou/8p4LghHKKf3z0TtbZ9efb1tY2oPUHGn/iwAqqbjmD4LDiHerpaG/fxXsZEkQw4ysJnjuT8lvP6rOOZ3fsFEs1xUk8vmNLMBFPdJeTyUy594EnNbx0e53e9s/L7rHTzfTr77arcs/ae36+/fmMYvFeY8BzJsC5R9J2y2f2Oh6V+0omu37AlO14vErNGL1P6t+39uMWrOM4FziOs9hxnAbHcVqAK4Bh2Q0NysrK9qp80wkBhhXazEUl2pKZBBsMEA4YKpIpmoojtBVFsJEQFATpCAeJRjJjqs9OGcf6qnKstSQDhnVDyvBM5oBirGXCIdVMunQaw+eNJFgUZNQHxzL9kpkAjLzyQCrmjSZQFGT4eZOY8JXZeEUR0u9c49hYYjZA5YfGEygKUnHqWMZ/8X0Uzapi9NWHEigJUTi7isl3zaP0A6MIFIUYcvF0Rp0/C4BQWZiDHz2N4bMKKSoxjLzuCAqmVfb5OyQS2w/Me/s3BBh38zzCE8oIlIUZ/cvjCVYV7lU9o382j/DUSkxpmGE/PJbw2LLudQoOGkbV/x6JKQ4TPnA4Yx4/n4opI3Zb55CbTiQ0tQpTGqH6R/OonDFql+tXXfcBIgePwBSHKf/qERQcNnqv/ialn3kfI5+5mPDsoZiSMJXXHEPloeN3WKfoxEmUfu4QTFGI8OFjGfLifzH03gsJjirrU2fl78/GlEXYnqoMgWElFF4wBwpDhI+fSOVVx3WvX3z9fMwhY6GoAHPERCiJYGaPpOiH53SvU3Drx2FUOVQVE7n9IkwwQPCwCYS+Og+KI3jTR8PEIVBaQOCH51I2c9yA/w4ADK/E/PQyKC2EySMI/eIzfdf58aUwbVRmnRsvpnTWxO51Cr90JpxyMBRG4Jwjwb0R7v0apQdM2rt4VM4UDpoI//sRKC6AA8cTvvETmeXjh8EPLoaSQpg2itDNl+/1e2VLvrZgjbW7/6XrOM44YB1wDvCI67oJx3F+BDiu6x6f5fj2euy7ts3jqF+183aDpcBaTHUh6bSlur6NbaVF28+FtZbPLF5KW3Exz47LHKhP2rCRQ5Zv4NozjqKurIRwOs3/PvQCx08J8oG/Hk8gPLCJRRtvXcWyz78InmX8FTOY/bMj9na39mjBggUAzJ8/P2vvIfuG19BB49G3kl5ZT2BkKVXPfJrQlGq/wxLZnaxkvoS5vM+xPmJ/O+izbH9mEZeSaenWAUnHcY4ELgaWZzOwd2t4aYDXv1zKbQ9GeeyhFmhqZdOoMpaUFkLKg3BmjlZJPMm0tg5sZ4xJ0SgGuOr3B3HtZ+PUlWXGPJPBIGu/dCTfvqhkr2btjvv0dIaePJp0R4rSWZX7cjdlEAsMKWbIq58jtaqB4KRKAuWFe95IJC8N+ly6U3tMsK7rLncc52rgn0AEeAL4C9D3vIMcUxwxXHlOOcdMC7OtPs2Zz4XxEh50xCDtgYEkhmQ4RMhaKpIpAJY92cgFPzyIn//Vkur64N83o/BdnRJTNKF0zyvJfscUhQnPHel3GCK+ypcu4d76dR6s67rXAddlOZased+czCB/wUtxEukAlBRg4klsMEAiHKQjHKI8kZkQYIGnbl3Px39RwQMXlfLXpSnmjghw5RF5e8qwiIhkwX6TNV6rtbSbABgPEwkyqiPGlqLMbNs/zZ7Cp95YRbjHrNl4W5rT5wQ5fXrOnu4rIpIX8rUFmx+XAeqHFY0WL2CgKIgtCjK2rZUyMgk1EYmwdMxoCqoiYAyTj6xi0mFVPkcsIrK/yM/TdPabFuyJ4w3jymBjFAqDlh9cNZaKRJJrftpAIGmpGFXIpb9wiGAprhzYZRBFRGTv5WsLdr9JsMOKDa9eEuS5LZZZ1YapVQYI8evrRrB5a4qZUyOUlao7WERE9o39JsECDCkyzJ+y4y+l0SPCjB6hFquIiH/UghUREdnn8rWLeL+Z5CQiIvJeUgtWRER8la8tWCVYERHxmRKsiIjIPpevLViNwYqIiGSBWrAiIuIrtWAlZ7QvrqX5ofV48bTfoYiIyC6oBTvI1N68lA1XPA1A6XGjmbFwPiao30kiMnipBSs5oeGOld3ltie3EF8X9TEaEZF9QRf7lxxQeEAV0ZdqCQChYYXE0x5sagXPIxSwpFs9YqEQ2zZ1YkpCFEcMsRSUlgQIGIPxLImEB6EgxREwJj++yCIyeOVrC1YJdhBpcOt5/YkGUkMrCaVTtBQXsfK4RzFYJjdvY0isk6EVldz6gcO55NUk6UCa0bE4m4aVQcgSNHDk+gamNLWzKRJm5YRyHr28jNkjdZMDEfFPvibYPXYRO46zznGci96LYGT3lv3fElJtKQCSwRAF8XTX19KwvnwYAG9OGscD0yaQDAbxjGFzYQGEDBhDGsNLY6sxwNhEkpaGFNc/1unX7oiI5DW1YAeRYPH2j8v2es10LQl6HpG01708YC095xqHvO2vpQyURPLzl6OIDCb5eRxSgh1EDvjyTNr+tZaEDVLZ2U5jaQEJU4TBYgMe98xxeH3yGI7b0sCzI9PMbe1gaGeM0FttXLDkIQpSSW49/MOsGTqBuqIIVBXxYkeEbz2T5vqjAxqPFRFf9G4w5Iv+JtjJjuM8AxwMrAA+57ru4uyFJTvTcf69TG/MzBpOYYjFh1NoEgDUDinhX0fOxnYlySNaOpnS2obxPK586g7GtmwFYO7WjZx/0Q2UJtLUVBSxtAGWNlgOGGK5YJYSrIi89/bbMdgunwW+CFQD9wIPO45TnrWoukSjUZV7lNPN8e5lHgE8s31yUkdBpDu5AnhdRQOUxtu7l5fGOwh5mU7jSGp7d3FNe27so8oqq5y75ezJz9N0jLW7b5w7jrMO+KPrut/uem6A9cD/c133z1mOL197DvZK9LevUP/ZfwOGJIYO47HNjMGQpqWykEfffwCd5UXETYBkMEBNKEAkYBmabOTMZU/xiZef4s5DTuOvh5xBY2GYxaMr8cJBZlTD0+cHGVacH19qEcmarBwkGsw3+xzrh9gbBv0Bqb9dxOveKbiuax3H2QCMzUpEsktllx9C/cJtNP9tDR6GZbNGEUgmaC0spTrVwJBAGq8jMyv4weHVLK8sg6HFEBjLP+YcxC9Pv4BQnUc6GGDOpBD3fTpEezrA+DIoCA3677KIDFL52kXc3wQ78Z1CVwt2PLApGwHJ7rUvacIjQDpgaCkshsJiALaUjsILbO8yHppIQSgAge1f3HXBUqabKAHPsmVLitHlOv9VRCRb+jsGe5njOIc4jhMGvgYUAw9lLyzZlcoPTwQg6FmqzPYTcGbUraYgmRmjNZ7H5qICSKahxyk7c8u2l4+eU/DeBCwisp/qbwv2FuDnZGYRrwQ+6LpuS9aikl0ac+P7KXn/SNLNcQ48axJrnqwl9Opqpk6fzeGdKf7z1HpWjRzDFU4R6aEFxD2PNVGPk6aG+OjsUv7jhggFYd6hhX7viogIkL9dxHuc5OSznA4uFy1YsACA+fPn+xyJiOShrGTCWvPtPsf64fb6QZ91daEJERHx2aDPpTulBCsiIr7K165KJVgREfFVvo7B6obrIiIiWaAWrIiI+CpfW7BKsCIi4jMlWBERkX1OLVgREZEs0CxiERGRLFALVkREJCvyM8HqNB0REZEsUAs2D3R2pGla10E4ZGlrDJJMhbDWYsyOvwo7k5aadhhfDsFAfv5iFJHBR13EkpNWLe/kvs+9THlzlLaKEiylrB82hDeXrOOGn04k0JVIVzVYjr8rydY2OGac4bELQxTqJusikgPydZKTuogHuYf/Uc/odTW0VZQAmZGMEc2tbGqG1xdHu9f7hZtma1um/MxGy0Or8/UrLSKDjcX0eeQDtWAHubLKEKmAwXgeNpD5vZQKBsFaqoaEu9cbXrzjF3Z48XsapojILuVLQu1NCXYQircluedzL7NmmwEPJoZDHPvmq0xor6GhpIK3RlTwjSeeo+C+IOnffZ7ghUdyhWP4/euwOQpHjTV8YLw6L0QkV+RngtVRdhB64Tdvs26LJRGJkCiMsGrqOMbG6qiIdzClcQNnLl9IcbKTYEcbfOJX2LTHzS9b1rZAwoNFGywPr/b83g0RESAzBtv7kQ/Ugh2EEm1JbM8ZwsaQCL3THdyrsyWVhrRHNLFjHdFEvnyFRURykxLsIJNOW1a3hyns6MALGZKhCNX1zbQES3hh7gzqS8r40PLHmNS0iZQJcqfzIZ6+bAWbSkspqq6mMxCkuCTAb14z/HaJ5bgxhq8fFeD5LZavPukRCcAVc+HpTYbWhKU4Yjh9kuEjM9TZISLZsd+PwTqOUwpcA5wDDAM2AJe7rvtMdkKTnbntp5t4aXOQj9e8wazn3+aJUSew4n0TuPnkD9NWWMD8pU8xqakBKMQjxOtjpgOFjG1LcXKqiQdGVtMRirBoS+YL/cS6NG81We5eDamuRu0LWy2kLQQNYLl9qWVhEczTuK2IZEG+JtiBHDFvA44ATgTKgbOAmmwE9Y5oNKpyr/LaNZ0UJFMMb6+j3ZQRKykAIBEMAjC+6Z2PxBCxaUa31ndvWxlLQiQIvbqX3S2p7uT6zra9vbQx5vu+q6yyyv6WsyVfT9Mx1u55LM5xnOHANuBA13WXZT2q7TRQ2MtjD9Tzx7ubOXXpi5zxxuM8WXoir75/Go2lxTSUFnPIxhV8edGfCFpLbUkV3zrjsyQjhQC8XVHM02WlUF0EAQPWQtLyo3kBbnwFajsy7xEJWBIJIAgYQ2UBvHRRkGlV+fGlF5G9lpWDwFvmh32O9dPs1wb9Aae/XcQTu/5dlaU4pJ9O/vBQaurTLKmZTvnQGKMaGil76g3aKoswQY9gwOM/Y48nVhJgeXWEmaFagiOreKBkNE+bEgiHqCq0fPcYQ5AAB48wHDEmwKcOtvx2iUdByHDBTHhuExSEoDMNR4wyjC0b9N91EclZ+Xl86W+CXdf17zTgzeyEIv21od4ybksDgc4IEa+T6lgH1TUdBEhTRoy6inIWTT6Q+csf4YDytfDnb/PMA2lYlfmR2JSAmUMDO4ypVhYavn5EsPv52TPe890Skf1UvnQJ99avMVjXdWuBe4GbHceZ6DiOcRxnquM4U7MbnuzMpIkRmipLSRtDqsd4apDMua1NpSVgLcPa6+GQyQAcMmL7eiVhmK7uXhGRrBrIaTqXAdcDTwJDgPXA5cDqLMQlu/Gxc6t4qPhA2v9RTM2LG+hoTdFcUQKFaQ5cv551o6s5uHMV5d88A66aD8DXDzeUhAOsarRcfEBAXb4ikjPytQXbr0lOPsrp4HLBd7+5ng1r493PRzc2Eez6TL9y96GUVIV3tamIyEBlJROuMDf1OdbPtF8Z9FlXF5oY5IqKtvfyB7CYruQaDBtCkUH//RSR/UC+tmB15YBB7uLPjGDazCJGj43w8UuHUTY6QWF1inO+MY2CEv1+EpHcl6/nweoIPMgNHxnha1eP637enGgBYOYx1X6FJCIyIPk6FqgEKyIivsqXFmtv6iIWERHJArVgRUTEV/naglWCFRERX2kMVkREJAvUghUREckCJVgREZEsUBexiIhIFuRrC1an6YiIiGSBWrB5oL09zc9+uo116+KMbx/CKY++wobm66kvKGGI2cg4r4bAcbPhb1+F0iK/wxUR6UUtWMlRDz/UwooVMYgmmOOuY1xDPeF0mskd65nQvp5AZxz+9Src/C+/QxUR6UPXIpac5b1zy0FLr9kCvaYOpL33KCIRkf7TJCfxXTxlufo5jzcbLAFg8+ZOPv7oQ4y2QYaEZlAc7WDJhElsrayksD1GCjh13eOM7qyhqXIEpRfOo9DvnRAR6SVfWqy97VWCdRznEeAJ13Vv3MfxyG5c97zHD17q0VqliE1zT+PclVuwGErDIRKBAJ0mjLFJsJaFI47v/uoO/azLBx852afoRUR2Ti3YHlzXPX1fByJ79lZTjycGsBDxDNYEMN727t9gKr3Dau9o39qR9RhFRAbKUwtW/JRKWzraUhgbIJj2OGBLC0WpFG8PKWJ1ZZhxTQnGbKqjpK2DWCREKgABY0gFA4TSHljL7DHtcPMjMHYIfPhw2NwA/3gRpo+GUw72exdFRPLK3nYRLwL+47rud/dtOLIrR/+6nZfWpwmGA8yraWJSR4wnJwylrqKMRyvKmLWpnvP+9QItxUUUdiZJhgKEbZqRNa0kw2GKvTZmr18ED3dVePV58Lv/wJbGzPNbPw+fOsmv3ROR/Vi+jsHqNJ1B4pVNmW7fQs8yNJ4EYGNFcffry8cOpbF4+zmu4ZRHIAUhz1AUT0EyQorI9grvf2l7cgV4+JXs7oCIyC7YnTzyQU4n2Gg0qnJXefqwIABxY2gOZ8ojo7HudSbXNFLZuf15KmgoaY93P48QI0yy+3niAzOhurT7OcfO9n0fVVZZ5dwuZ0u+ngdrrB34b4X3sIs4X37IvGudSct5f4nx3LoUESxzG9optGmWTaimsthwwoJlHLR6E6FYks5wkFAyRXk0QVlnnKp4lFFsoPTQYZTMn4wZNxQ+OQ9WbYG/PgPTRsGFx/q9iyKS+7KS+Z4xt/Y51h9jP+1rljXGnAycDwy31s43xjhAubX28f7WoUlOg0RR2LDgkp6XOSzvLn3p0uWUeB5vTx4NQEFHnGnLN5EKBWkqK2ZIop0GO57mDYXMvPpj26uYMQZ6PhcR8UGutaSMMVcCXwRuBT7StbgT+DlwVH/ryekuYumfcGUB9OiJCHg9v66WgM2cwhMcWoyISK7JwS7iLwEnWWu/D7xzDuQKYMZAKlGCzQPf+M442keU4gGheBzSaVrLi+ksjBAsCRKoiBCZXsWEf53ld6giIoNBGbCxq/xOiyUMJAZSyd52EQcH+kaSPdXVYW688wAAFixYAMD8+Wf6GZKISL/lQIu1t6eA/wf8X49lXwCeGEglA06wjuOUAVOB1QPdVkREpLdcG4MFrgQWGGM+A5QZY1YCrcD8gVQyoATrOM77gEXAv4AHBrKtiIjIzuRaC9Zau9UYcxhwODCeTHfxS9baAd2SbEAJ1nXdV4GKgWwjIiKyOznYgsVmzmF9seuxV3SajoiI+CrXWrDGmI3sIu9ba8f3tx4lWBER8VWuJVjgol7PR5E5L/avA6lECVZERKQHa+2TvZcZYxaRmX/0s/7WowQrIiK+GtDMIf/EgUkD2UAJVkREfGUDudVFbIy5rteiYuAM4JGB1KMEKyIivrK5lV8BxvV63g7cBPxxIJUowYqIiK9yrQVrrf3kvqhHCVZERHxlc+Cq+MaYef1ZT7er2w9teL6etYtqaUsZSg/OxdO2RUR2zgZzogV7Wz/WscDk/laoBJsHape3sOALL2PTFghhAqkBXjFTRGT/Zq0d0Azh/siBhrm8W/Wrol3JNSO9JE2qts3HiERE+s8LmD6PfKAEmwdGTCgilE5nnlhLxxvw6tg76bhvub+BiYj0gw30ffjJGFNujLnJGPOyMWa9MWbDO4+B1KMEmweablnK6JpGTDpNIJ0mFQ5SU1lO3f972u/QRExj/G4AACAASURBVET2yAZMn4fPbgYOAa4Dqsncvm4D8JOBVLJXY7CO4zwCPOG67o17s728e00xy9cfiRP990Y+8GwDbcOHMry5g3RBCCysqyonmkixxPkzEz42m81L23khUspbx0zmujOKmTYs6PcuiIgAOXke7CnALGttgzEmba39pzHGBRYwgCS7VwnWdd3T92Y72Xc+95jH3auD3PngS7w+YSznPbccC6wfXkE6GKAEqIx7zF23ivYl66gfM52pwDN1AT64ZTyrvqG7DopIbsiBFmtvAaClq9xmjKkEtgJTB1KJZhEPMtZa0s+vZcKD9fz3xjbGxOuJrM7MHbdAMmBYW1FOxPNoqigmHTZUe020FcYImHLGdnTyfF2al1fFmTslQig3pseLyH7My73D0BLgOGAh8DTwK6ANWDWQSva2i3gR8B/Xdb+7N9vL3ktc8kc67lrK1zG0U0oDlRTRTpoABsuvD5vDM1NHY6zlpE31DIvM5uAl60mOSnDs6iVsNSOYnYpz+U+TONPD/PLKKiVZEZEdfQa676H3BeB7QCVwyUAq0SSnQcQ2d5C662XShDBAjCJiRLpf9zA8M3V0Zl1jeLOsmEQkRKwozMohUxjbvJGQtUxrbCFtwF2VZM3WlE97IyKSkYOTnNZba98GsNbWWWs/ba39mLX2zYFUktMJNhqNqtyzXFqAHV4CeFggQJpgjxs9xQrDXLR6HZGuU3aqYwkAijoTBG0Sz2QmNrUURAhYKCowDK0I+r9fKqus8qAoZ4s1fR8+qzHG3GyMOebdVGKsHfhl9d7DLmJd868X740txL76APHX60m2pIh2FlAbqqS2qpL1E4cRK4rwdkEhqwuKmZlIMKStk0igg1Fta1k2aib1JdXM2riVpsscPnZ8CYdOj+z5TUVEMrKS+u4ZdXefY/15Wz/mW5o1xrwPuAA4n8ztav8C/Nla+8ZA6tEkp0EmMGc0xf/+LMVdz8seW8ua859k5awx3escVNfIiTVr8Qw88f5p/PbvR3P/EQ/ivFED1GAChqsuLScY0ak6IuK/XJvkZK19FXgVuMoYcxyZZLvQGFNjrT2ov/XkdBex7Fnh8eOZMsZS0dwOQCCVprypHQu0FEc48r8ys8oP+p8DMF2TmQ788mwlVxHJGTk4BtvTSmA5sBGYOJAN1YId5Ew4SPvRkyl8tBYvmSIRgoJUB5NPHMK4n5/CiOrMRzz5vEmMPmk0XsKjeGSRz1GLiGyXA2OuO+g67/Vc4ELgSOBR4AfAAwOpZ28vNHH83mwn2THsmJHEFzVhjKUgDSNjUeacdiAF1Tt+vIXVBT5FKCIyqGwBngP+DJxjrW3Zw/o7pRZsHhj58RmcVBtn1d/WEuhoIHxRAQUXvc/vsERE+sWaHGvCwhRr7dZ3W4kSbJ6Y/OWDmPzlg1iwYAFxv4MRERmAHJzk9K6TKyjBioiIz3KwBbtPKMGKiIivcm2S076iBCsiIr7y8rQFq/NgRUTEV7l2qUST8RljzOPGmNe7lh1rjDlvIPUowYqIiOzoOuBTwC3A+K5lm4CvD6QSJVgREfGVNabPw2eXAh+y1v6V7dfEXwtMHkglGoMVERFf5UBC7S1I5gbrsD3BlvZY1i9qwYqIiK9ybQwWeAS4yRhTAJkxWeB6YMFAKlGCFRERX+Xgxf6/DIwCWoAKMi3XCQxwDFZdxHlk5a+WE/tlB2aYofOwTop0UX8RGQRyqYvYGBMEPkLmFnXlZBLrRmttzUDrUgs2T7SsaOH165dAk8Wu8njjhiV+hyQi0i+51IK11qaBm6y1MWttrbV28d4kV1ALNm8kF63h6G3LCWJpDRXR0DrK75BERAarBcaY+dbaAY259qYEmyfst/9NqGuyW3mqk6rqhM8RiYj0Uw51EXcpBO41xjxP5kbr78wkxlp7SX8rUYLNA289soWfH3Yyy8aMpCCZImUsk+MpftzpUVqkUQARyW05MKmpt6Vdj3dlrxKs4zhjyWT1Sa7rrnu3Qcjes9bywP8t540DZgPQWRChrDPGUq+UPz3axuVnlpOIpwgFDcYYTFAJV0RySy5NcgKw1l67L+pRC3YQizYmufbKlYyMJcHaPt0sf34oyt/+Wcvjk8fx2eee44Yn/kPxXecTOXeOTxGLiPRlTW798DfGzNvVa9bax/tbjxLsIGWt5ZFbNlBe08a0xjpOXL6Cp6ZPoyCZImk8Sjo6qC8uZkIiRFmsjbsPPpCPvraE911xP5EPzYRkCkp7nMbT3A5lhRAM+rdTIrJfysEu4tt6PR8GRMhcj7jfl0vsV4J1HGckmYseHwdsA27s7xvIvtfWmuK3F7/MtKfe5vjmTmzAct76xTivbSQZCGHCSTaMGQ7G8rfZE3nxtz9kZv1WHp80m5tHHcJVVZcQiiXgho/D186Ej/4I/vEijBsKC6+BaaP93kUR2Y/kYBfxpJ7Pu86N/RYQHUg9/W2X/wlIk7mrwLFkLoQsPnnyvlqql9dQ2dwJgPEM6wqHkwxkfi/ZZJhIIg0EOPfN5cys3wrAvLVv8vawYkKd8UyX8jf/BA++nEmuABvr4cb7/dglEZGc1XVu7P8BVw1kuz0mWMdxxgDzgK+6rtvium4NsE8GgPckGo2qvJNyYWmQdHDHX3xFXscOz9+5gfGwzpYdlsdCke1PCsNQVbLD64mC7V3EubK/Kquscm6Us8bs5JF7Tga8gWxgrLW7XcFxnCOAF4Cw67qprmVTgbfI/izi3Qe3n0olLXd+fTlF961gSE2U0fGtTDSrWFo0hzZTQjCSZuOIYZTH2zhl4xM8M3EiUxq28cAshwWnn8YD//gFpZ2d8IOLYf5hcMO9cPvjcMA4uOMLUFmy5yBEZH+UldT388Me7XOs/8LiU3xLs8aYHc59BYrJnBt7hbX2jn7X048E+84pOVNd1327a9nJwKMowfru2V++xaFfvJ5CL3NhiRhFvM1cACbwJgXhDsLbfgNVpX6GKSL5IStJ72dHPNbnWP/FF0/2M8Ee12tRO7DKWts6kHr22EXsuu4mYBFwo+M45Y7jjAC+PZA3kew57LJJLDrtHKKmjBYqWcNM0oCJtFAzupzEXV9QchWRnJaDN1w/zFr7ZI+Ha61tNcZ8ZSCV9HeS04VAAZmW7NPAnQMMVrIkUhzifTccyx0HfZQlHEY9Q2mmjMAnTmTq5h9Rct7hfocoIrJbOZhgv7OL5d8aSCX9Ok3Hdd2twId6Lb51IG8k2TNibjVzzxzBmpZOhjS2E0kksVveg4kJIiL7QA4kVGCHC0wEjTEnsGOX+GQGeJqOLjSRJw6/bDLh7y3GJjOT3AomlvkckYjIoPPOBSYKgdt7LLdADXDlQCpTgs0TBRPKmH7vSbxx9SLSI0NM+P4RfockItIvudKCfecCE8aYOwdy15xdUYLNI9UfnkirGQJAsDTsczQiIv2TKwn2HfsiuYISrIiI+CzXEqwxphy4hszlgYfSYyzWWju+v/Xk1i0MRERkv2MDps/DZzcDhwDXAdVkxl43AD8ZSCVqwYqIiK9yrQULnALMstY2GGPS1tp/GmNcYAEDSLJqwYqIiOwoALxzIfc2Y0wlsBWYOpBK1IIVERFf5WALdgmZ8deFZC6u9CugDVg1kErUghUREV/l4JWcPgOs6yp/AegEKoEBzS5WC1ZERHyVAwl1B9baNT3KdcCn96YetWBFRMRXudaCNRmfMcY8box5vWvZscaY8wZSjxJsHvIS8Mz3lvLw519k/aJtfocjIrJbuZZgyZye8yngFuCd8143AV8fSCXqIs5DrY/C1mfXArDp+XrOf/AEyscU+xyViMjOWd/zaR+XAu+z1tYbY37dtWwtmQv+95sSbB5KbbMMbavn8LpX8AJBYi9Novzs2X6HJSIyWATJzBqGzIX+AUp7LOsXdRHnIdPicdrmxxnfsYWJbRsZeo3uLCgiuSsHu4gfBm4yxhRAZkwWuJ7MhSb6TS3YQe7JxR38/V9RCuo6CGxpY9SGIsoamilOdW5faUO9fwGKiOxBDiTU3r4C3EnmYhNhMi3XRxngaTpqwQ5idY0pbrytibpV7cS3xug0IdZMGENVPMlrVQcCmb6NJUMP8jdQEZHd8Izp8/CDMWYkgLW21Vp7FpkJTkcCU6y1Z1trB3TDdSXYQSzaYdkQDBOMtRCNhGkqjFAU7WBSbR1DWoJsMHNZwlFE3u7E+/CNkEj6HbKISB8W0+fhk95XavqNtXaxtbZmbypTgh3ETFGAN0siHL9hGfFQkHQgwMYR1bQMLaDIS1JoUwyljRo7jqYFb8NvHvU7ZBGRPnJoDLb3Gx//birr1xis4zgjgd8BxwLbgB8AtwKTXNdd924CkF275tk0ty+1HDDE8K0jDV94wmNdC0RbkhD3mN3WxuTGNlYPm7LDdk9NncI1J5/EyNYo33rsEY5uf4wEYbzv/p3ALx6Gn10GZxzq016JiOwoh8Zg7Z5X6b/+TnL6E9AMjAMKgXv2ZRDS13ObLdc+n/msN0YtS+osW9u7XoyEoCPBkqJSJkeS1JRWY8n89PIs3H3gHLxAgG1l5fzuqCP42WOLKaSTtY0TmVS3Ds6/CZr/CAF1YIiI9BAyxpzA9pZs7+dYax/vb2V7PMI6jjMWmAd8zXXdVtd1a8lMV866aDS635brWzvoKZFmR10fd8TzwBgscPDSdVx2z1N8496nGNqaycbx8PaPOGXCmUIsSbSl1fd9VFlllQdXOVtyqIu4FrgduK3r0dDr+YDOeTTW7r5F7DjOEcALQNh13VTXsqnAW2S/i3ifNtcHE89aLnjQ456VlkkVcO1RAa5Y6BFNWOhMQyzNyI5O2uIpLtiyjZAX4MIFL3Zvv3jqaO75wAH8esGvOLh+NavKpjIhVUNBogN+/in4/Ok+7p2IDFJZyXzfnP9qn2P9DQvelzP9xnurP13Em7v+HQ+s6VGWLAoYw93zg/z+NEtRCIwxXDjLkPTA84J4FlobwnzpG1v44Gsvc8CmrdQwonv7uVvXc/TvV7Gh9ABeOOgDBMZV8l/3HwVpDwrCPu6ZiMiO/DotJ9v22EXsuu4mYBHwfcdxyhzHGQ58K9uBSUZx2GC6vnzBgKEwZCiOBCgtCDB6dITzzqnkCedQQoEUfz12NqmAYUt1Kdd89ESipoKKtjSBtOGkb8yCUFDJVURyTg51Ee9T/Z3kdCGZWcSbyMwi/glwAhDPUlzSTx85s5Jz5lew5oRl3HL6Idz8wcOwAUMgmeaUb43h4qeX8b8vPsPkoz/kd6giIjuVLwm1t34lWNd1twLdR2jHcU4lk1z36uRb2bcCAcOEW07lu5c/yXdPPIZYKEgyCQQC3HHcHK66vMrvEEVEdilfu4j7ex7sXDITjt4AJgHfBe52XXe/nYSUa8IzhnDVolOZtWAB1711MG58KAABAxVnTPU5OhGR/U9/u4iryXQRjyJz8eNHgP/JVlDy7nxq3Cq8xmFsiVq+c1yYMeX5+etQRPJDDt4Pdp/obxfxE4CaQYPEmMIOXr680O8wRET6xcdrD2eVblcnIiK+2q/HYEVERLJlv55FLCIiki1KsCIiIlng5Wd+1f1gRUREskEtWBER8ZW6iEVERLLA02k6IiIi+55asDJoBOK9784uIpK78nWSkxJsHrHJNNOuf4vKxS3UzdhI9cJPEBxT7ndYIiK7la8XmtAs4jwSf3AVlYtbAEivrKf9pud8jkhEZM/29/vByiBgisOsGDWaVyZNYlxDA6eVRPwOSURkv6UEm0e2HTieX516Kp7JdEyUH1XGqT7HJCKyJxqDldySTMGabTCmGi8YovafK/jDyiISJkIIMNay7bYlvLWsgqEfn0XVSN1dR0Ryk+6mI7mjPQbHfxvct7HDK3iuZBofOudySEKw1HJIewe/uu82JrXUkL43wBvfmUXbbz7CCReP8TtyEZE+NMlJcscCF9y3ATC1Ldx41DxIZl5KG0N7vJNJLTUABPGY3LGRF29fg7XWr4hFRHbJM6bPIx8owQ5Go6poLijj1VEzeGDmkYxst9AjeZam4nzvpHN5YcI0ANIEOWLzG3z+S0v48E21PLHe8ytyEZE+PNP3kQ/61UXsOE4xcB1wLlABvAT8t+u6q7MYm+zChtZCRiXg4K2bqC0fT6kp49jGFlaUFjOyvYlQSRVuSRWvjJ3BT/7xaxLpAHcedCB/GH0AJGHhPUnWXRFhWHGefItFZFDL10sl9rcFeyswEzgSGAm8CDzoOE44W4EBRKNRlXdS7lheS8hmWqENJZUATO3o5EO1DRxTX9v9q8kLBGkoKSNskqytHtJdR4cXYE1te07si8oqqzx4yjIwZk/jco7jDAXqgAmu627oWhYAmoAPuq77TBbj06DhTrTXdrB57vVMq9nEm8MncseR55IKhogkkszcvIyH5hxJPFzAlLqNXPvInawpHMvyScO45PxPkAoGOXWMx0MXRggG8vNXo4hkTVYOGh+7dH2fY/3df5gw6A9Q/ekintT17+uO4/RcHgbG7fOIZI9Khhcz4c3v4N63mmRHjLmvtZF4aSuF8Rgz61cwa9Pb1EZGgmd5Kzid4jbDKWfPZOWRNdSOH4kzuUDJVURyRr6MufbWnwS7vuvfaa7r1mUzGOm/gqoiDvvUHAAOaU3y27OepSENZ615mKJ0nG3EaGQ47/zgLDh8HJPnTWSyjzGLiOxMvswa7m2PY7Cu69YCfwZudhxnDIDjOJWO45ztOE5ptgOUPSssD3Pezw5m+PAQf5nzYZYMm876YWEChe0ECxNUfmk2pR9UahWR3ORh+jzyQX8vNPEZ4JvAIsdxRgLNwNPAo9kKTAZm1JxKLjonTPrCx7uXBb56CsEfftTHqERE9iydH/m0j34lWNd1O4BvdT0kR6WPmE57sISSdDsJwqQPnI5uViciuS5fu4h1qcQ80vZyA2vTUwmTJEWIIc83Uf4Jv6MSEdk/KcHmkaKDhmDDhmQyc5u6Yme4zxGJiOzZ/jyLWAaJwhlV1F8/nKLnOjngnMMZ8omZfockIrJH+TKpqTcl2DyTmF1IYnYhQ+YruYrI4JDWGKyIiMi+py5iERGRLEiri1hERGTfy9fzYHU/WBERkSxQC1ZERHylC02IiIhkgWYRi4iIZEHK7wCyRAk2D3gpj6evfZ0tL9aRGu1RcXZ+/hoUkfykFqzkrBX3bWDFvV237d0IkQnAmb6GJCLSb6n8zK+aRZwPlm1MZgrWMrq1icTTnVz8ww08/+sXoTPub3AiInuQwvR55AMl2EHu1tc9Lo2NZcOQcuZs28yhWzZw4hMrOetXCzm67iBe+9jv/A5RRGS/pAQ7yD20xtJREOaGM48hbJPdy9+/fiPFiST/aK+GDrViRSR3JU3fRz7Y4xis4zjrgG+5rntX9sORgYrWxgi3Qjjt0VRSysYhw9lSWU11e5TlP/gR21JDeLTiLgrLAxz2+Acpmqtb2IlIbknm6SQntWAHsWTC46nNhmQwSGUixXOzD2RT9VC8QID6sgrWDBvFGFNHSSpFNBbmrcsW+h2yiEgfyZ088oFmEQ8y0ZpOlt2/iXRhmMXFlVgyN1c/Zu06vMCOv5c8Y2gvDPLc9HJOXf42mKl+hCwislsdedqC7W+CHe84zkLgCGAd8F+u6z6Xtahkp1KxNPd+6iXatsUAeGvEUL5oG1hTVMHP7nuQJw6cy9rq4RR3xolHwiwbP4HvnHQsz0yaRCjyZz5Z1eLzHoiI9NWZn/m1313ElwFfACqAx4A7shZRD9FoVOUe5ei2WHdyBRgZbeejb77AzQvuJgB8YPky6odUs2HsKLYNG8LyEWN5ZuJEAJ4bNx3eWJsz+6KyyioPvnK2JDB9HvnAWGt3u0LXJKdfua77w67nBwBLgUrXdbPdJNp9cPuZdNLj7oufp2F1GwCvjR3J+2tXceELC2hhIkFrueuYedSVVXD00ysoa4vxxrihfPXiE/jNA7dzzqHDKf/DBT7vhYgMYlnJfOZLjX2O9fan1YM+y/a3i3hrj3J7179lgPoc30PBcIBzf3c4qxduI1AaZmy4lGvvHc5zo0Zz4oqljGpIc5i7mmikmJK2zKk5czbWc+9v72JuQwOJUw/yeQ9ERHZi0KfSndMs4kGmoCzMAWeNZdZJIzj7uBLGdET5/eGHc9Ell/Hr9x+NBapb23bYZkJDgjhlBCoK/AlaRGR3jOn7yANKsIPcTZ+q5rD6eqo74jQOq2T1uKGkwhA0aQhYQhUJyicWUHrhLCr/5zC/wxUR2W/oNJ1BbsZRQ7mltZYFP15KOhBg04RRJM+u4PIfzPM7NBGR/smTFmtve0ywrutO7PV8HXnbYz44jZpVRrSsmBQBjPWYMCb7s/5ERPaZPM0oasHmgbWrOkl19fZbE6C5scjniEREBiI/M6zGYPNA1bDwDs8LSlI+RSIiIu9QCzYPzDi4jLMuG8nSl6IkA5sZPUNdxCIyiORnA1YJNl8c+6GhHPuhoSxYsNTvUEREBkYJVkREJBvyM8MqwYqIiL/yM78qwYqIiN/yM8MqwYqIiL/yM7/qNB0REZFsUAtWRET8lactWCVYERHxWX5mWHUR55m2ZJCbV05mzs2dnPCXFHcs9fwOSURk98xOHnlALdg8c/PKqTzTNgoIQwcs2uwxqcJw7Lg8+caKSP7J07vpqAWbJzqjSd66axWHP7Oas1ctA2u7X1vdbHezpYiIZINasHmgrTnBrz7+MvOfX8Q1TXVY4L9efZ7TP3slVakkp4yN+B2iiMiu5WcDVgk2H7z50FZKWqKMa6oDMt/VD7y9hk+6KyhOQftpE6CqzN8gRUT2MwNOsI7jVAN/AY4EVruue+g+j0r6LbHwLQq/cR+tM46h3RSyJTKS9oIiWoZGuPy+J1lbXY378xhTfjGXUEl4zxWKiLzn8rMJuzdjsJ8FSoEhSq7+8876OZvDQ6mpqGJTaAyhOFS0dlJRm2TjuBEctG0bdU9t5u3v6S47IpKj8nQW8d4k2MnActd1dVdvn6U8yz+mzOKF8ZMoaW0nlEx3v1aQSBKPhEkbw+rhldz3cidvXf0qDY9toaElzeOLO1m3Jelj9CIiXfI0wRpr+z/D1HGcBcBpXU/jwI9d1706G4F10fTX3TjrV408XRfghrufwAaCjKlrZkJdM56BlVNGceDWlXzx1LNYOGsSAGe6q/n8o6/x2mGT+PfBUwmH4AdfHMLc6QU+74mIDBJZSX3mO519jvX2uqJBn2YH1IJ1XXc+8CfgDtd1S7OcXGU34inLPzvLOXHlSmwgCMDmYZVsGBNhbMEbnL3mXkLhhu7kCvDkrLF4BiYv3wpAMgVPvxrzJX4RkW552oLN6fNgo9GoyrsoF4QMM1JRlowdg/G2X62p1IsyNNZAxEtS1ZFgZt227tem1jRjLGwbWt69bNr4sO/7orLKKg+OsgzMgLqIARzH+QOQcl3301mJaEfqIt6Nza1prvrJBo7655PE7HA2jhiOV5jklFUvMHpblCYzhnHxjXz95DMpKohwyZqNTDlxGC3nzODZVSlmTIhw2tHFfu+GiAwe2ekivnonXcTXDv4uYp0HO4iNKQ/yp6sn0Xz33byUbKI0HGPViDHc8v6PcPazrzC+oZGRbW1cXbeSg58+Hzige1vH8S9uEZEdDPpUunNKsHmg8H9P4aiL/sDRrCNtDLcfdgozNtVQEWimPlLKlO8f6XeIIiK7pmsRS64KNkQxXT8Bg9Zy5KrlLBs5jBeHTWFc8k3KttX4HKGIyP5nwC1Y13UvzUIc8i4EjpkM4SAk01gDtcVVGGMYEm8iXBYBZ4rfIYqI7Fp+NmDVRZwPgoeMo/CZL5L+z0pesFvpbKvkiGQ5M8o8+Oj3YPwwv0MUEdmN/MywSrB5Inj4BIKHT6BlwQKKgTnzT/A7JBGR/snP/KoxWBERkWxQghUREckCdRGLiIi/1EUsIiIi/aUWrIiI+CtPLzShBCsiIv7Kz/yqLmIREZFsUIIVERHJAnURi4iIv/K0i1gJVkREfPb/2zv/aDunM49/vkkMJX5XWqkfSUoMUjLp1o7SVpkaP6plsrrUFAmjosVMuwY1GGNimfjVzlRbK34GYaVdhlAaQUxjSkdrTyKEKgkRCWkERciEG8/8sffJeu/JOeeee3PPOffe83zWOuu+79773e+zn3ff99n72fvde2BaWDewjuM4TmsZmPbVx2Adx3EcpxF4D9ZxHMdpLd6DdRzHcRynXtzAOo7jOE4DcAPrOI7jtBZV+FVKJi2RNKZ5gm0cbmAdx3EcpwG4gXUcx3Fai7Thr+5LdaKkpyQ9KWmmpGE5/H8k7ZePr5b0dD4eImmVpC0aUpYCbmAdx3Gc1lKni3iDy5K7+FLgUDPbB1gI/DhHPwQcko8PBNZI2hHYD/i9mb3bW+JXo09/piPpfuCjXaUbMmTIRzs6OlY1QaR+getjQ1wnnXF9dMb10Zka+phtZof19v3srCE9/VDnS8AsM3s1n18DLMjH/wWcJ+k24HXgYZLBHUkyvg2nTxvYeh9kCCHGGEOj5ekvuD42xHXSGddHZ1wfnelH+hBgZWGl80eBccCRJIP6MHAyycBe2Azh3EXsOI7j9FceAo6Q9PF8/i1gDoCZrQXmAefmsMeAA4B98nHD6dM9WMdxHMcpY46kjsL5ecCDkgx4AZhUiHuINOYazaxD0iLgRTN7vxmCDhQDe22rBehjuD42xHXSGddHZ1wfnemT+jCzEVWibq6SfgowpXB+RAPEqorMyt3XjuM4juNsLD4G6ziO4zgNoF+6iEMImwPTgE8DHcBZMcZ7K6Q7CJgFPJeD1sYYP9ssORtJCGE0yS2yPWkK+okxxufL0gwGrgIOI82suzTGeH2zZW0WderkIuA7wCs56NEY4+nNlLMZhBCuBMYDI4BPxRgXVkjTbvWjHp1cRHvUj+2B6cAngbXAImBSjPG1snRtVUd6+I3C9QAAC7dJREFUm/7agz0LeCfGuBtwFHB9CGFolbTPxBjH5t+AMK6ZqcBPY4yjgZ+Svv8q55vAbsDuwP7ARSGEEU2TsPnUoxOAWwp1YsC9PDN3AV8AXqqRpt3qRz06gfaoHwZcHmPcI8a4D7CYtGBDOe1WR3qV/mpgjyW9TMk9lAgc3lKJmkgIYRjp+64ZOWgGMC6EsENZ0mOB62KMH+aW6V3A15snafPohk7aghjjIzHGl7tI1jb1A+rWSVsQY3wjxji3EPQYsGuFpG1VR3qb/mpgd6FzK3QpsHOVtKNDCPNCCL8NIUxovGhNYWdgeYxxHUD++wob6qA7eurv1KsTgG+EEJ4MITwQQti/mUL2MdqpfnSHtqofIYRBwLeBX1SI9jqyEfTJMdgQwjzSg63Ex7qR1Txg5xjjWyGEkcCcEMLyGOOcjRbS6a9MBS6JMX4QQvgycHcIYc8Y4+utFszpE7Rj/fgxsBr4SasFGWj0SQMbYxxXKz6EsJTkzigNyO8C/KpCPm8Xjl8MIdxFWsmjvxvYl4FPhBAGxxjX5YkIw3N4kZKeHs/n5a3RgURdOokxrigcPxhCeBkYQ1pGrd1op/pRF+1WP/LEr92Bo2KMH1ZI4nVkI+ivLuLbyat1hBB2J63UMbs8UQhhxxCC8vF2wKHAE02UsyHEGFeSynFcDjoOmF8+A5Ckp2+FEAblscijgTuaJ2nzqFcnIYRPFI7HkmaU/qFJYvY12qZ+1Es71Y8QwiWkLzGOjjGurZLM68hG0F8N7BXANiGERcC9wKkxxncAQgiTQwin5XTjgYUhhCeA/wamxxjvbonEvc9pwJkhhOeAM/M5IYRZIYTSIt3TSUuHPU+axDA5xvhCK4RtEvXo5N9CCAtDCAuA64ATir2WgUII4aoQwjJgJ9LQyNM5vG3rR506aZf6sTdpicHhwG9CCE+EEGbmuLatI72Nr+TkOI7jOA2gv/ZgHcdxHKdP4wbWcRzHcRqAG1jHcRzHaQBuYB3HcRynAbiBdRzHcZwG4AbW6XUkjZBkknZq8H1OkzS9cH6fpHMaeU+nMpIWSZpYZ9qm1I9mIGlTSc9L+vNWy+L0PdzAthBJoyTdLmmFpNWSXpY0U9Kf5fiJkhZVuK5a+PH5xXVhhbi5ktbm+7wlab6k8Y0pWeORtAUwGbioFGZmh5vZ5S0Tqgvyszmw1XK0A43QtaSDJHUUw8xsLXAl6dt8x+mEG9jWMgt4FdgD2JK0HdT9gHqY36nAG8ApkgZXiL/YzIaS9kudAfxc0uge3qvVHA88ZWaLWy2I0/bMAA6WtFurBXH6Fm5gW4Sk7UmGdaqZvWWJZWY2NbeKu5vfnsDngQnAjtTYvs/MOoCrgcHApyrkdYak+WVhIyWtkzQin0/LPe53JD0j6W9ryHaRpDllYXMlXVA4HyPpfkmrJC2VNEXSJjWKfDTwYLU8C27ICVm+dyXNkrStpEslrcyeg9ML10/Mrs7vS3o1p/lBUY6uyi1pH0mzJb0m6Q1JD+bwBTnJA9mLUHHTakmbS/pRvscqSXdJ2qUQPzfLdEeWYbGkr1VTUqFM35O0LF9zpaTtcx5vS3q22NuTNETShZJeyGV4SNKYQvwmkn5Y0OH3K9z385IeydcvlvSPkupuOEoaL2lB9rYskHRMeZnK0t9U0mk1XUtaksv1SA6PkvarlEchbImSZ2g4cB8wOF+7WtIEADN7m7RW71frLZ/THriBbRFm9jrwNHC9pBMl7dWdF1AFJpF6dPeSesanVkuo5II+HfgAWFAhyW3AnpLGFsImAnPNbEk+fwQYC2xDctXeJGmvngguaRhpMfU7SUu37Q98GfinGpeNA56pI/vxwIGkRcpHAL8lbS49HDgJ+I+iASMtbL4LMCrLcRRwViG+arkl7ZjL8XC+18eBywDMbN98/aFmNtTMTqki778Df5l/uwKrgHvU2SMxAfghsDVpB5SbJW1eQwe7ZnlHZV2cSTIWVwDbkvQ+rZD+bOBE4AhSY+3XwIOStsrx5wJfAT4HjMxlXb+XqKS9SXXwCmAH4EjgDOCEGjKuR9L+pDp4Lsnbch4wQ9Jn67m+C12fBvwDsB3wn8CsQrlq5fkKqdG6Luc51MxuLiR5ilQnHWc9bmBby0HAXOC7pIXq/yjpn8sM7UhJfyr+SL3P9UjajPTyujEH3QAcoQ0nkZyfr18GfA0Yb2YbjOWa2ZvA3SQDRJZnQiF/zOwGM3vdzNaZ2c+AJ3N5esKJwAIzu8bM3jez5cCUHF6NbYG3a8SXuNjM3sgNmnuBD8zsOjPrMLP7gDeBvyik/xA428zWZPfz5WQ9QJflPgFYZGZTzOzdXJa6d26SNIhU5gvMbLmZvUuqG3sCnykk/bmZPWpmHwLXkgzt7jWyXgP8a5ZnAalR9biZPWZm64Bbgd0kbZ3TnwRcZmbPZm/KZGAdyVCSZbzMzBaZ2RpSA6S45uq3gdvN7O6sp2dJDYFaz7PIScAdZnZffk6/BGYCJ9d5fS1uMLP/NbP3SY2fNaTGwsbyNsloO8563MC2EDNbZWbnmdk4Ug/jHOBCCi904EUz26b4A75TltXXgaGkFyWk3sNKoLyXdEnOY5iZfc7M7qkh3jTgm7m3e3CW705IhkDSZEl/yC68PwH7knorPWEkcEBZI+JGUg+wGm8CXfY8SGPcJd4rOy+FbVk4X2lm7xXOl5AWh6+n3COA5+qQqRo7AJuRFlcHwMxWk55lcZPrVwvx7+bDYhnKWZmNcYlyPZTKW8pj5zIZPiTpoSTDTvm8KMPKQn4jgePKnue/kHrD9dDp/pnF9M5G30tKB5YWYl9Kfr4byVak+Q+Osx43sH0EM3vPzG4i9YjGdpG8nEmk8dSFklaQeqjbAX+nypOd6uEB4P9IrfuJwM9ybwXSVnCnkNyv22ajv4Dqk7NWA1uUhQ0vHL8EzClrSGydJ2RVYz7QI5d0Fwwrc7eOIOkTui73Emr3JLvaWeM1YC3JQAEgaSgwjA33+m0kL5fJMIikh5IMy/N5KX4LkowlXgJuLHueW5nZ3j25f2ZU4f5d1Seoruui3CINB5Seb6d8JQ2hc7kq7ZdaYgypTjrOetzAtgilyTZTlCb3bJInlown/aP+uhv57EXaRP4YkmEu/T5D6gEe0RP5cq/lFuDvgb+h4B4mtdY7SAZhkKSTST25akRgnKRP53KeQecX6C1AkHSypM1yT3GUpMNq5HkX8FfdL1mXDAIulfQRSaNI7s/SWFtX5b4V2ENpktTm+bkeUohfQQ0DXND5xZKGZ0P/A+BZ4He9VL56uAk4R9Lo7ME4HxgC/DLHTwfOlvRJSR8hudGLjaurgW9IOqpQt/eS9MVu3H+8pL+WNFjS4aQ6WBonnk9qCH0l15VjgC+U5VFN1ydLGqc0ce1sYPNCuSJwiNKEvk2BS4DiRLsVpElOnYy/pC1J/2+/qLN8TpvgBrZ1vE9qHd9Jci29BlwAnGlmt3cjn0nAPDO7x8xWFH5PUtiYvodMA75IclMXX/A3kyYLLSL1ZvaiRqPAzOaSDMVskmvyY8CjhfgVwJdIM4OXkNy/M0m9lmpMB/bNRrA3eYlUphdJZZxNMiDQRbnzRJiDSBO0lgF/BIozbM8HJkt6U9I1Ve7/PdKL/nGS+3JH4Kt5rLRZXEH69OQBUhkOJk0YKo15TyF9TvYYSU9LSXoDwMwWkjwf3yU975Uko1nXEIKZ/YY05n8lqS5cDhxvZo/l+MWkiUrXkv53DmPDTcCr6fpa4Kqc77HAkWb2Vo67jWQk55Fc0ktJz7kk13OkxsPvsuu7NGnrOOBXZvZ8PeVz2gffD9bpt0g6DTjAzOqanVpHfhNJE4z8e8YBiKQlpOd7a1dpu5HnpsBCUiPo972VrzMwGNJqARynp5jZVGBqq+Vw2pc8y7rWuLvTxriL2HEcx3EagLuIHcdxHKcBeA/WcRzHcRqAG1jHcRzHaQBuYB3HcRynAbiBdRzHcZwG4AbWcRzHcRqAG1jHcRzHaQD/D053I3kgq476AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x396 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values[\"Y0\"][\"T0_1\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DRL<PERSON>ner"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["GradientBoostingRegressor(min_samples_leaf=30, n_estimators=50,\n", "                          random_state=123)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["model_regression = clone(first_stage_reg().fit(np.hstack([T.reshape(-1, 1), X]), Y).best_estimator_)\n", "model_regression"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._d<PERSON><PERSON>ner.ForestDRLearner at 0x1a07f5b6248>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.dr import ForestDRLearner\n", "from sklearn.dummy import DummyRegress<PERSON>, DummyClassifier\n", "\n", "est = ForestDRLearner(model_regression=model_y,\n", "                      model_propensity=model_t,\n", "                      cv=3,\n", "                      n_estimators=4000,\n", "                      min_samples_leaf=10,\n", "                      verbose=0,\n", "                      min_weight_fraction_leaf=.005)\n", "est.fit(Y, T, X=X)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._drlearner.DRLearner at 0x1a006e87688>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.dr import DRLearner\n", "est2 = DRLearner(model_regression=model_y,\n", "                 model_propensity=model_t,\n", "                 model_final=final_stage(),\n", "                 cv=3)\n", "est2.fit(Y, T.reshape((-1, 1)), X=X)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["X_test = np.zeros((100, p))\n", "X_test[:, 0] = np.linspace(-2, 2, 100)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["pred = est.effect(X_test)\n", "lb, ub = est.effect_interval(X_test, alpha=0.01)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["pred2 = est2.effect(X_test)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.plot(X_test[:, 0], true_effect(X_test), '--')\n", "plt.plot(X_test[:, 0], pred2, label='nonparamdml')\n", "plt.plot(X_test[:, 0], pred, label='forestdml (causal forest)')\n", "plt.fill_between(X_test[:, 0], lb, ub, alpha=.4, label='honestrf_ci')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### First stage nuisance models"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Model T\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title('honestrf')\n", "for mdls in est.models_propensity:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict_proba(X_test)[:, 1])\n", "plt.plot(X_test[:, 0], true_propensity(X_test), '--', label='truth')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title('rf')\n", "for mdls in est2.models_propensity:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict_proba(X_test)[:, 1])\n", "plt.plot(X_test[:, 0], true_propensity(X_test), '--', label='truth')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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*********************************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\n", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Model Y\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title('honestrf')\n", "for mdls in est.models_regression:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict(np.hstack([X_test, np.ones((X_test.shape[0], 1))])))\n", "plt.plot(X_test[:, 0], true_effect(X_test) + true_conf(X_test), '--', label='truth')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.title('rf')\n", "for mdls in est2.models_regression:\n", "    for mdl in mdls:\n", "        plt.plot(X_test[:, 0], mdl.predict(np.hstack([X_test, np.ones((X_test.shape[0], 1))])))\n", "plt.plot(X_test[:, 0], true_effect(X_test) + true_conf(X_test), '--', label='truth')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interpretability of CATE Model of DRLearner with SHAP"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 98%|===================| 98/100 [00:24<00:00]        "]}], "source": ["# explain the model's predictions using SHAP values\n", "shap_values = est.shap_values(X[:100], feature_names=feature_names)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(shap_values[\"Y0\"][\"T0_1\"][0], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x396 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values[\"Y0\"][\"T0_1\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OrthoForest"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   45.3s\n", "[Parallel(n_jobs=-1)]: Done 112 tasks      | elapsed:   48.1s\n", "[Parallel(n_jobs=-1)]: Done 272 tasks      | elapsed:   53.0s\n", "[Parallel(n_jobs=-1)]: Done 496 tasks      | elapsed:  1.0min\n", "[Parallel(n_jobs=-1)]: Done 784 tasks      | elapsed:  1.2min\n", "[Parallel(n_jobs=-1)]: Done 1000 out of 1000 | elapsed:  1.4min finished\n", "[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    0.9s\n", "[Parallel(n_jobs=-1)]: Done 112 tasks      | elapsed:    5.6s\n", "[Parallel(n_jobs=-1)]: Done 272 tasks      | elapsed:   12.9s\n", "[Parallel(n_jobs=-1)]: Done 496 tasks      | elapsed:   25.0s\n", "[Parallel(n_jobs=-1)]: Done 784 tasks      | elapsed:   44.6s\n", "[Parallel(n_jobs=-1)]: Done 1000 out of 1000 | elapsed:  1.1min finished\n"]}, {"data": {"text/plain": ["<econml.orf._ortho_forest.DROrthoForest at 0x1a00ac3db48>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.orf import DROrthoForest\n", "from sklearn.linear_model import Lasso, LassoCV, LogisticRegression, LogisticRegressionCV\n", "from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "\n", "est3 = DROrthoForest(model_Y=Lasso(alpha=0.01),\n", "                     propensity_model=LogisticRegression(C=1),\n", "                     model_Y_final=WeightedLassoCV(cv=3),\n", "                     propensity_model_final=LogisticRegressionCV(cv=3),\n", "                     n_trees=1000, min_leaf_size=10)\n", "est3.fit(Y, T, X=X)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:  1.2min\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:  1.4min finished\n"]}], "source": ["pred3 = est3.effect(X_test)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************************/ZVnseBU2v1m91QkZCU8rtSSqfy8iDQVWXYPUC/lPKslNICvgA8s1Y6zkZK+baU8mQ9jn291KjrerHtM8DnKs8/B/xoHXRYilrs9AzweelzEEgKIdrXWlHWz9+0JqSUB4DxJYasF7vWouu6QUo5JKU8Wnk+BbwNdM4btiq23VBOaB4/i++V59MJXJz1+hILjbnekMB3hRBHhBCfqLcyS7BebNsqpRwC/58HaFlkXL3sWoud1osta9XjfiHEcSHEt4QQt62NajfEerFrraw7uwoheoG9wKF5olWxrXGzO1hphBD/ALRVEf2GlPKrlTG/ATjAX1XbRZX3Vi0FsBZ9a+BBKeWgEKIF+HshxDuVq6gVZQV0XTPbLqXrdexmTexahVrstKa/0yWoRY+j+CVXckKIp4G/A7atumY3xnqxay2sO7sKIWLAl4B/L6XMzhdX+chN23bdOSEp5eNLyYUQHwd+BHhMViYq53EJ2DTrdRcwuHIazmU5fWvcx2Dl8aoQ4iv4UyQrfrJcAV3XzLZL6SqEGBZCtEsphyrTAVcX2cea2LUKtdhpTX+nS7CsHrNPRlLKbwohPiOESEsp12Pts/Vi12VZb3YVQpj4DuivpJRfrjJkVWy7oabjhBBPAr8GfEhKWVhk2GFgmxCiTwgRAH4KeG6tdLxehBBRIUR8+jl+8kXVbJp1wHqx7XPAxyvPPw4siOLqbNda7PQc8LFKxtF9QGZ6inGNWVZXIUSbEEJUnt+Df94YW3NNa2O92HVZ1pNdK3p8FnhbSvmHiwxbHdvWOyvjejagH39O8lhl+7PK+x3AN2eNexo/u+MM/lRTvfR9Fv/qoQwMA9+Zry9+VtLxyvZmvfStRdf1YlugCXgeOF15TK03u1azE/ALwC9UngvgTyryN1gie3Id6PrJig2P4ycEPVBHXf8aGALsyu/1X61juy6n63qy60P4U2uvzzq/Pr0WtlUVExQKhUJRNzbUdJxCoVAobi2UE1IoFApF3VBOSKFQKBR1QzkhhUKhUNQN5YQUCoX+XsI9AAAAIklEQVRCUTeUE1IoFApF3VBOSKFQKBR1QzkhhUKhUNSN/w+XbnYX6zw1HwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.plot(X_test[:, 0], true_effect(X_test), '--')\n", "plt.plot(X_test[:, 0], pred, label='forestdr')\n", "plt.plot(X_test[:, 0], pred2, label='nonparamdr')\n", "plt.plot(X_test[:, 0], pred3, label='discreteorf')\n", "plt.fill_between(X_test[:, 0], lb, ub, alpha=.4, label='forest_dr_ci')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}