[build-system]
build-backend = 'setuptools.build_meta'
requires = ['setuptools>=61.2']

[project]
name = "pygraphviz"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>"},
]
description = "Python interface to Graphviz"
readme = "README.rst"
requires-python = ">=3.10"
license = {text = "BSD-3-Clause"}
dynamic = ["version"]
keywords = [
    "Networks",
    "Graph Visualization",
    "network",
    "graph",
    "graph drawing",
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: BSD License",
    "Programming Language :: C",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Visualization",
]

[project.urls]
Homepage = "https://pygraphviz.github.io"
"Bug Tracker" = "https://github.com/pygraphviz/pygraphviz/issues/"
Documentation = "https://pygraphviz.github.io/documentation/stable/"
"Source Code" = "https://github.com/pygraphviz/pygraphviz/"
Download = "https://pypi.python.org/pypi/pygraphviz"

[tool.setuptools]
packages = ["pygraphviz", "pygraphviz.tests"]
platforms = ["Linux", "Mac OSX", "Windows"]

[tool.setuptools.package-data]
#"" = ["*.txt"]

[tool.setuptools.dynamic.version]
attr = "pygraphviz.__version__"

[tool.ruff.lint]
select = [
  "B",       # flake8-bugbear
  "C4",      # flake8-comprehensions
  # "E",       # pycodestyle-error
  "EM",      # flake8-errmsg
  "EXE",     # flake8-executable
  "FURB",    # refurb
  "NPY",     # NumPy specific rules
  "PD",      # pandas-vet
  "PGH",     # pygrep-hooks
  "PIE",     # flake8-pie
  "PL",      # pylint
  # "PTH",     # flake8-use-pathlib
  "PYI",     # flake8-pyi
  # "RET",     # flake8-return
  "RUF",     # ruff-specific
  # "SIM",     # flake8-simplify
  "SIM2",    # simplify boolean comparisons
  "UP",      # pyupgrade
  "YTT"      # flake8-2020
]
ignore = [
  "B006",     # Do not use mutable data structures for argument defaults
  "B007",     # Loop control variable {name} not used within loop body
  "B018",     # Found useless expression. Either assign it to a variable or remove it.
  "B028",     # No explicit `stacklevel` keyword argument found
  "B904",     # Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None`
  "E402",     # Module level import not at top of file
  "E501",     # Line too long
  "E741",     # Ambiguous variable name
  "E721",     # Do not compare types, use `isinstance()`
  "E731",     # Do not assign a `lambda` expression, use a `def`
  "EM101",    # Exception must not use a string literal, assign to variable first
  "EM102",    # Exception must not use an f-string literal, assign to variable first
  "ISC001",   # Conflicts with formatter
  "NPY002",   # Replace legacy np.random.{method_name} call with np.random.Generator
  "PD002",    # inplace=True should be avoided; it has inconsistent behavior
  "PLR",      # pylint-refactor
  "PLR09",    # Too many <...>
  "PLR2004",  # Magic value used in comparison
  "PLW0127",  # Self-assignment of variable {name}
  "PLW2901",  # Outer {outer_kind} variable {name} overwritten by inner {inner_kind} target
  "RUF005",   # Consider {expression} instead of concatenation
  "RUF015",   # Prefer next({iterable}) over single element slice
  "SIM115"    # Use context handler for opening files
]

[tool.ruff.format]
docstring-code-format = true
