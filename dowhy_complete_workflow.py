"""
DoWhy 完整工作流程示例
=====================
这个脚本展示了使用 DoWhy 进行完整因果分析的标准流程：
1. 数据准备和初始化
2. 以多种方式创建和可视化因果图
3. 识别因果效应
4. 估计因果效应
5. 反驳检验
"""

import dowhy
import networkx as nx
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from IPython.display import display, HTML

# --------
# 1. 数据准备 - 我们将创建一个模拟数据集
# --------
print("1. 准备数据")
np.random.seed(123)  # 设置随机种子以确保可复现性
num_samples = 1000

# 生成混杂变量
W = np.random.normal(size=num_samples)

# 生成处理变量 (受W影响)
prob_t = 1 / (1 + np.exp(-W))  # logistic function
T = np.random.binomial(1, prob_t, size=num_samples)

# 生成中介变量 (受T影响)
M = 0.5 * T + 0.2 * W + np.random.normal(size=num_samples)

# 生成结果变量 (受T和M影响)
Y = 2 * T + 3 * M + 0.5 * W + np.random.normal(size=num_samples)

# 生成工具变量 (只影响T，不直接影响Y)
Z = np.random.normal(size=num_samples)
T = T + 0.3 * Z + np.random.normal(size=num_samples, scale=0.1)
T = (T > 0).astype(int)  # 二值化

# 生成一些额外变量以增加数据的现实感
X1 = np.random.normal(size=num_samples)
X2 = np.random.choice(['A', 'B', 'C'], size=num_samples)

# 创建DataFrame
df = pd.DataFrame({
    'T': T,
    'Y': Y,
    'W': W,
    'M': M,
    'Z': Z,
    'X1': X1,
    'X2': X2
})

print("数据示例:")
print(df.head())

# --------
# 2. 因果图初始化 - 我们将展示多种方法
# --------
print("\n2. 因果图初始化")

# --------- 方法1: 通过 NetworkX DiGraph -----------
print("\n方法1: 通过 NetworkX DiGraph 创建")

try:
    # 创建一个有向图
    G = nx.DiGraph()

    # 添加节点
    G.add_nodes_from(['T', 'Y', 'W', 'M', 'Z'])

    # 添加边
    G.add_edges_from([
        ('W', 'T'),  # 混杂因素→处理
        ('W', 'Y'),  # 混杂因素→结果
        ('W', 'M'),  # 混杂因素→中介
        ('T', 'M'),  # 处理→中介
        ('M', 'Y'),  # 中介→结果
        ('T', 'Y'),  # 处理→结果 (直接效应)
        ('Z', 'T')   # 工具变量→处理
    ])

    # 使用NetworkX图创建 CausalModel 而不是直接初始化 CausalGraph
    model_nx = dowhy.CausalModel(
        data=df,
        treatment='T',
        outcome='Y',
        graph=G  # 传递NetworkX图
    )
    
    # 获取底层的CausalGraph
    causal_graph_nx = model_nx._graph

    # 使用networkx绘制图形
    try:
        plt.figure(figsize=(8, 6))
        pos = nx.spring_layout(G, seed=42)
        nx.draw(G, pos, with_labels=True, node_color='lightblue', 
                node_size=2000, arrowsize=20, font_size=16, font_weight='bold')
        plt.title("通过 NetworkX 创建的因果图")
        plt.savefig('causal_graph_nx.png')
        plt.close()
        print("因果图已保存为 'causal_graph_nx.png'")
    except Exception as e:
        print(f"绘制图形时出错: {e}")
except Exception as e:
    print(f"通过NetworkX创建CausalModel时出错: {e}")

# --------- 方法2: 通过 GML 字符串 -----------
print("\n方法2: 通过 GML 字符串创建")

try:
    # 注意：在最新版本中，节点需要有 'label' 属性而不是 'name'
    gml_string = """
    graph [
      directed 1
      node [
        id 0
        label "T"
      ]
      node [
        id 1
        label "Y"
      ]
      node [
        id 2
        label "W"
      ]
      node [
        id 3
        label "M"
      ]
      node [
        id 4
        label "Z"
      ]
      edge [
        source 2
        target 0
      ]
      edge [
        source 2
        target 1
      ]
      edge [
        source 2
        target 3
      ]
      edge [
        source 0
        target 3
      ]
      edge [
        source 3
        target 1
      ]
      edge [
        source 0
        target 1
      ]
      edge [
        source 4
        target 0
      ]
    ]
    """

    # 从GML字符串创建CausalModel
    model_gml = dowhy.CausalModel(
        data=df,
        treatment='T',
        outcome='Y',
        graph=gml_string
    )
    
    causal_graph_gml = model_gml._graph
    print("成功从GML字符串创建CausalModel")
except Exception as e:
    print(f"从GML字符串创建CausalModel时出错: {e}")
    # 降级到使用networkx方法
    if 'model_nx' in locals():
        model_gml = model_nx
        causal_graph_gml = causal_graph_nx
        print("降级为使用之前的NetworkX图")

# --------- 方法3: 通过 变量角色 (最常用方法) -----------
print("\n方法3: 通过 CausalModel 和变量角色创建")

try:
    model = dowhy.CausalModel(
        data=df,
        treatment='T',
        outcome='Y',
        common_causes=['W'],
        instruments=['Z'],
        effect_modifiers=['X1']
    )

    print("通过变量角色创建的因果模型已初始化")

    # 可视化模型图
    try:
        model.view_model(file_name="causal_model_roles.png")
        print("因果模型已保存为 'causal_model_roles.png'")
    except Exception as e:
        print(f"可视化模型时出错: {e}")
except Exception as e:
    print(f"通过变量角色创建CausalModel时出错: {e}")

# --------
# 3. 分析因果图属性 
# --------
print("\n3. 分析因果图属性")

try:
    # 使用来自变量角色的图
    graph_from_model = model._graph

    # D-分离检验:
    # 检查T和Y在给定W的情况下是否d-分离
    is_dsep = graph_from_model.check_dseparation(['T'], ['Y'], ['W'])
    print(f"'T'和'Y'在给定'W'的情况下是否d-分离: {is_dsep}")
    
    # 检查T和Y是否不受条件限制地d-分离
    is_dsep_uncond = graph_from_model.check_dseparation(['T'], ['Y'], [])
    print(f"'T'和'Y'不受条件限制是否d-分离: {is_dsep_uncond}")
    
    # 检查Z和Y是否在给定T的情况下d-分离
    is_dsep_z_y = graph_from_model.check_dseparation(['Z'], ['Y'], ['T'])
    print(f"'Z'和'Y'在给定'T'的情况下是否d-分离: {is_dsep_z_y}")
except Exception as e:
    print(f"执行d-分离检查时出错: {e}")

# 检查有效的后门路径集
try:
    # 尝试不同版本的API
    try:
        is_valid_backdoor = graph_from_model.check_valid_backdoor_set(['T'], ['Y'], ['W'])
        print(f"'W'是否为'T'到'Y'的有效后门集: {is_valid_backdoor}")
    except AttributeError:
        try:
            # 尝试其他可能的方法名
            is_valid_backdoor = graph_from_model.check_backdoor(['T'], ['Y'], ['W'])
            print(f"'W'是否为'T'到'Y'的有效后门集: {is_valid_backdoor}")
        except Exception as e2:
            print(f"检查后门集时出错 (替代方案也失败): {e2}")
except Exception as e:
    print(f"检查后门集时出错: {e}")

# --------
# 4. 识别因果效应
# --------
print("\n4. 识别因果效应")

# 识别因果效应(这里标识出可能的估计策略)
try:
    identified_estimand = model.identify_effect()
    print("\n识别的估计量:")
    print(identified_estimand)
except Exception as e:
    print(f"识别因果效应时出错: {e}")
    identified_estimand = None

# --------
# 5. 估计因果效应
# --------
print("\n5. 估计因果效应")

if identified_estimand is not None:
    # 尝试不同的估计方法
    # 线性回归
    try:
        estimate_lr = model.estimate_effect(
            identified_estimand,
            method_name="backdoor.linear_regression",
            control_value=0,
            treatment_value=1
        )
        print("\n后门调整 (线性回归) 估计结果:")
        print(estimate_lr)
    except Exception as e:
        print(f"线性回归估计出错: {e}")
        estimate_lr = None

    # 倾向得分匹配
    try:
        estimate_psm = model.estimate_effect(
            identified_estimand,
            method_name="backdoor.propensity_score_matching",
            control_value=0,
            treatment_value=1
        )
        print("\n后门调整 (倾向得分匹配) 估计结果:")
        print(estimate_psm)
    except Exception as e:
        print(f"倾向得分匹配估计出错: {e}")
        estimate_psm = None

    # 工具变量
    try:
        estimate_iv = model.estimate_effect(
            identified_estimand,
            method_name="iv.instrumental_variable",
            control_value=0,
            treatment_value=1,
        )
        print("\n工具变量估计结果:")
        print(estimate_iv)
    except Exception as e:
        print(f"工具变量估计出错: {e}")
        estimate_iv = None
else:
    print("未能识别因果效应，跳过估计步骤")

# --------
# 6. 反驳测试
# --------
print("\n6. 反驳测试")

# 尝试使用前面的线性回归估计进行反驳
if identified_estimand is not None and 'estimate_lr' in locals() and estimate_lr is not None:
    # 1. 加入随机变量测试
    try:
        refutation_random = model.refute_estimate(
            identified_estimand, estimate_lr,
            method_name="random_common_cause"
        )
        print("\n加入随机变量测试结果:")
        print(refutation_random)
    except Exception as e:
        print(f"加入随机变量测试出错: {e}")

    # 2. 调换结果和处理(排除性测试)
    try:
        refutation_placebo = model.refute_estimate(
            identified_estimand, estimate_lr,
            method_name="placebo_treatment_refuter"
        )
        print("\n调换结果和处理测试结果:")
        print(refutation_placebo)
    except Exception as e:
        print(f"调换结果和处理测试出错: {e}")

    # 3. 数据子集测试
    try:
        refutation_subset = model.refute_estimate(
            identified_estimand, estimate_lr,
            method_name="data_subset_refuter",
            subset_fraction=0.8
        )
        print("\n数据子集测试结果:")
        print(refutation_subset)
    except Exception as e:
        print(f"数据子集测试出错: {e}")
else:
    print("没有可用的估计结果来进行反驳测试")

print("\n-------------------------------------------")
print("DoWhy 完整工作流程示例已完成")
print("-------------------------------------------") 