[tool.poetry]
name = "dowhy"

#
# 0.0.0 is standard placeholder for poetry-dynamic-versioning
# any changes to this should not be checked in
#
version = "0.0.0"
description = "DoWhy is a Python library for causal inference that supports explicit modeling and testing of causal assumptions"
authors = ["PyWhy Community <<EMAIL>>"]
maintainers = []
license = "MIT"
documentation = "https://py-why.github.io/dowhy"
repository = "https://github.com/py-why/dowhy"
classifiers = [
    'Development Status :: 4 - Beta',
    'License :: OSI Approved :: MIT License',
    'Programming Language :: Python :: 3.8',
    'Programming Language :: Python :: 3.9',
    'Programming Language :: Python :: 3.10',
    'Programming Language :: Python :: 3.11',
    'Programming Language :: Python :: 3.12',
]
keywords = [
    'causality',
    'machine-learning',
    'causal-inference',
    'statistics',
    'graphical-model',
]
include = ['docs', 'tests', 'CONTRIBUTING.md', 'LICENSE']
readme = 'README.rst'
[[tool.poetry.source]]
name = "torch-cpu"
url = "https://download.pytorch.org/whl/torch/"
priority = "explicit"


[build-system]
# Cython<3 currently required due to build fails of certain dependencies like econml
# see: https://github.com/py-why/EconML/blob/e0271fdde8d94a70a9f67a07e1d0a5a8e964d3f5/pyproject.toml#L94
requires = ["poetry-core>=1.0.0", "cython<3"]
build-backend = "poetry.core.masonry.api"

[tool.poetry-dynamic-versioning]
enable = true
vcs = "git"
metadata = false
format = "{base}"

[tool.poetry-dynamic-versioning.substitution]
files = ["dowhy/__init__.py"]

#
# Dependency compatibility notes:
# * numba (imported by econml) requires python <3.11
#
[tool.poetry.dependencies]
python = ">=3.8,<3.13"
cython = "<3.0"
scipy = [
    {version = ">=1.7", python = "<3.9"},
    {version = ">=1.10", python = ">=3.9"}
]
statsmodels = ">=0.13.5"
numpy = [
    {version = "<2.0", python = "<3.9"},
    {version = ">1.0", python = ">=3.9"}
]
pandas = [
    {version = "<2.0", python = "<3.9"},
    {version = ">1.0", python = ">=3.9"}
]
networkx = [
    {version = ">=3.3", python = ">=3.10"},
    {version = ">=2.8.5", python = "<3.10"}
]
sympy = ">=1.10.1"
scikit-learn = ">1.0"
pydot = { version = "^1.4.2", optional = true }
joblib = ">=1.1.0"
pygraphviz = { version = ">=1.9", optional = true }
econml = ">=0.15.1"
tqdm = ">=4.64.0"
causal-learn = ">=*******"
numba = [
    {version = "<0.59", python = "<3.9"},
    {version = ">=0.59", python = ">=3.9"}
]

#Plotting Extra
matplotlib = { version = ">=3.5.3", optional = true }
cvxpy = "<1.5"

[tool.poetry.extras]
pygraphviz = ["pygraphviz"]
pydot = ["pydot"]
plotting = ["matplotlib"]
econml = ["econml"]

[tool.poetry.group.dev.dependencies]
poethepoet = ">=0.24.4"
flake8 = ">=5.0.4"
black = { version = ">=22.6", extras = ["jupyter"] }
isort = ">=5.10.1"
pytest = ">=7.1.2"
pytest-cov = ">=4.1.0"
pytest-split = ">=0.8.0"
nbformat = ">=5.4.0"
jupyter = ">=1.0.0"
flaky = ">=3.7.0"
keras = ">=2.9.0"
xgboost = ">=2.0.2"
mypy = ">=1.7.1"
torch = "==2.4.1"
torchvision = ">=0.13.1"
pytorch-lightning = [
    {version = "<2.0", python = ">=3.8,<3.11"},
    {version = ">=2.0", python=">=3.11"}
]

[tool.poetry.group.docs]
optional = true

[tool.poetry.group.docs.dependencies]
#
# Dependencies for Documentation Generation
#
docutils = "<0.21"
sphinx_design = ">=0.3.0"
sphinx = ">=5.3.0"
sphinxcontrib-googleanalytics = { git = "https://github.com/sphinx-contrib/googleanalytics.git", branch = "master" }
nbsphinx = ">=0.8.9"
sphinx-rtd-theme = ">=1.0.0"
pydata-sphinx-theme = ">=0.9.0"
ipykernel = ">=6.15.1"
sphinx-copybutton = "0.5.0"
seaborn = ">=0.12.1"
cdt =">=0.6.0"

#
# Versions defined for security reasons
#
# https://github.com/py-why/dowhy/security/dependabot/1 - CVE-2022-34749
nbconvert = { version = "7.0.0rc3", allow-prereleases = true }

[tool.pytest.ini_options]
markers = [
    "advanced: not be to run each time. only on package updates.",
    "notebook: jupyter notebook tests",
    "econml: a marker to mark tests requiring econml",
    "focused: a debug marker to focus on specific tests",
]

[tool.poe.tasks]
# stop the build if there are Python syntax errors or undefined names
_flake8Errors = "flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics"
_flake8Warnings = "flake8 . --count --exit-zero --statistics"
_black = 'black .'
_isort = 'isort .'
_black_check = 'black --check .'
_isort_check = 'isort --check .'

# testing tasks
test = "pytest -v -m 'not advanced and not econml' --durations=0 --durations-min=60.0"
test_no_notebooks= "pytest -v -m 'not advanced and not econml and not notebook' --durations=0 --durations-min=60.0"
test_durations = "poetry run poe test --store-durations"
test_econml = "pytest -v -m 'econml' --durations=0 --durations-min=60.0"
test_advanced = "pytest -v"
test_focused = "pytest -v -m 'focused'"

[tool.poe.tasks.format]
sequence = ['_black', '_isort']
ignore_fail = 'return_non_zero'

[tool.poe.tasks.format_check]
sequence = ['_black_check', '_isort_check']
ignore_fail = 'return_non_zero'

[tool.poe.tasks.lint]
sequence = ['_flake8Errors', '_flake8Warnings']
ignore_fail = 'return_non_zero'

[tool.poe.tasks.verify]
sequence = ['lint', 'format_check', 'test']
ignore_fail = "return_non_zero"

[tool.black]
line-length = 120
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
(
      __pycache__
    | \.github
)
'''

[tool.pylint]
max-line-length = 120
disable = ["W0511"]

[tool.isort]
profile = 'black'
multi_line_output = 3
line_length = 120
py_version = 38
