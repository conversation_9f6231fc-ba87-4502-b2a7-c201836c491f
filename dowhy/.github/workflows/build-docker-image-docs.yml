name: Build Docker image (docs)

on:
  push:
    branches: [main]
    paths: ['docs/Dockerfile']
  pull_request:
    branches: [main]
    paths: ['docs/Dockerfile']

jobs:
  build-and-push-docker-image:
    runs-on: ubuntu-latest
    permissions:
      packages: write
    steps:
      - uses: actions/checkout@v4
        name: Checkout repository

      - uses: pmorelli92/github-container-registry-build-push@2.2.1
        name: Build and Publish latest service image
        with:
          github-push-secret: ${{secrets.GITHUB_TOKEN}}
          docker-image-name: dowhy-docs-generation
          docker-image-tag: latest # optional
          dockerfile-path: ./docs/Dockerfile # optional
