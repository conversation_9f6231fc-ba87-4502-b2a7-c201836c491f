"""
DoWhy 因果图完整示例
==================
这个脚本专注于 DoWhy 中因果图的创建和分析：
1. 多种方式初始化因果图
2. 可视化因果图
3. 分析因果图属性（节点、边、关系）
4. 执行 d-分离测试
5. 验证后门、前门路径
"""

import dowhy
import networkx as nx
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from dowhy.causal_graph import CausalGraph

# --------
# 1. 准备样例数据 (非常小的数据集，足以演示所有概念)
# --------
print("1. 准备样例数据")
df = pd.DataFrame({
    'T': [0, 1, 0, 1, 1],  # 处理变量 (二元)
    'Y': [1, 3, 2, 4, 5],  # 结果变量
    'W': [1, 2, 1, 3, 2],  # 混杂变量/共同原因
    'M': [0, 2, 0, 3, 2],  # 中介变量
    'Z': [1, 1, 0, 0, 1]   # 工具变量
})

print("数据示例:")
print(df)
print("\n变量含义:")
print("T = 处理变量")
print("Y = 结果变量")
print("W = 混杂变量/共同原因")
print("M = 中介变量")
print("Z = 工具变量")

# --------
# 2. 用不同方式创建因果图
# --------
print("\n2. 创建因果图的不同方式")

# 方法1：使用 NetworkX DiGraph 创建，然后传递给 CausalModel
print("\n方法1: 通过 NetworkX DiGraph 创建后传递给 CausalModel")
try:
    # 创建一个有向图
    G = nx.DiGraph()
    
    # 添加节点
    nodes = ['T', 'Y', 'W', 'M', 'Z']
    G.add_nodes_from(nodes)
    
    # 添加反映因果关系的边
    edges = [
        ('W', 'T'),  # 混杂变量→处理
        ('W', 'Y'),  # 混杂变量→结果
        ('W', 'M'),  # 混杂变量→中介
        ('T', 'M'),  # 处理→中介
        ('M', 'Y'),  # 中介→结果
        ('T', 'Y'),  # 处理→结果 (直接效应)
        ('Z', 'T')   # 工具变量→处理
    ]
    G.add_edges_from(edges)
    
    # 使用 NetworkX 图创建 CausalModel
    model1 = dowhy.CausalModel(
        data=df,
        treatment='T',
        outcome='Y',
        graph=G  # 直接使用 NetworkX 图
    )
    
    # 获取 CausalGraph
    cg1 = model1._graph
    
    print("成功通过 NetworkX DiGraph 创建了 CausalModel")
    
    # 基本图属性
    print(f"  节点数: {len(cg1._graph.nodes())}")
    print(f"  边数: {len(cg1._graph.edges())}")
    
except Exception as e:
    print(f"通过 NetworkX DiGraph 创建 CausalModel 时出错: {e}")

# 方法2：从 GML 字符串创建
print("\n方法2: 从 GML 字符串创建")
try:
    # 注意：在最新版本中，节点需要有 'label' 属性而不是 'name'
    gml_string = """
    graph [
      directed 1
      node [ id 0 label "T" ]
      node [ id 1 label "Y" ]
      node [ id 2 label "W" ]
      node [ id 3 label "M" ]
      node [ id 4 label "Z" ]
      node [ id 5 label "U" ]
      edge [ source 2 target 0 ]
      edge [ source 2 target 1 ]
      edge [ source 2 target 3 ]
      edge [ source 0 target 3 ]
      edge [ source 3 target 1 ]
      edge [ source 0 target 1 ]
      edge [ source 4 target 0 ]
      edge [ source 5 target 0 ]
      edge [ source 5 target 1 ]
    ]
    """
    
    # 创建一个临时文件用于保存GML字符串
    with open("causal_graph.gml", "w") as f:
        f.write(gml_string)
    
    # 从GML字符串初始化CausalModel
    model2 = dowhy.CausalModel(
        data=df,
        treatment='T',
        outcome='Y',
        graph=gml_string
    )
    
    cg2 = model2._graph
    print("成功从GML字符串创建了CausalModel")
    
except Exception as e:
    print(f"从GML字符串创建CausalModel时出错: {e}")

# 方法3：通过变量角色初始化
print("\n方法3: 通过变量角色初始化 CausalModel")
try:
    model3 = dowhy.CausalModel(
        data=df,
        treatment='T',
        outcome='Y',
        common_causes=['W'],
        instruments=['Z'],
        mediator=['M']
    )
    
    cg3 = model3._graph
    print("成功通过变量角色创建了CausalModel")
    
    # 尝试可视化模型
    try:
        model3.view_model(file_name="causal_model_from_roles.png")
        print("因果模型已保存为 'causal_model_from_roles.png'")
    except Exception as e:
        print(f"可视化模型时出错: {e}")
        
except Exception as e:
    print(f"通过变量角色创建CausalModel时出错: {e}")

# --------
# 3. 分析因果图属性和关系
# --------
print("\n3. 分析因果图属性和关系")

# 使用前面创建的图
try:
    # 优先使用方法3创建的图
    if 'cg3' in locals():
        graph_for_analysis = cg3
    elif 'cg2' in locals():
        graph_for_analysis = cg2
    elif 'cg1' in locals():
        graph_for_analysis = cg1
    else:
        raise Exception("没有可用的因果图")
    
    print("\n图的基本信息:")
    print(f"  节点: {list(graph_for_analysis._graph.nodes())}")
    print(f"  边: {list(graph_for_analysis._graph.edges())}")
    
    # 检查特定节点的关系
    target_node = 'T'
    print(f"\n节点 '{target_node}' 的关系:")
    
    # 获取父节点
    parents = list(graph_for_analysis._graph.predecessors(target_node))
    print(f"  父节点 (Parents): {parents}")
    
    # 获取子节点
    children = list(graph_for_analysis._graph.successors(target_node))
    print(f"  子节点 (Children): {children}")
    
    # 检查图中是否有这些节点，再获取祖先和后代
    if target_node in graph_for_analysis._graph.nodes():
        # 获取祖先节点 (所有可以到达该节点的节点)
        ancestors = list(nx.ancestors(graph_for_analysis._graph, target_node))
        print(f"  祖先节点 (Ancestors): {ancestors}")
        
        # 获取后代节点 (所有可以从该节点到达的节点)
        descendants = list(nx.descendants(graph_for_analysis._graph, target_node))
        print(f"  后代节点 (Descendants): {descendants}")
    
except Exception as e:
    print(f"分析图属性时出错: {e}")

# --------
# 4. 执行 d-分离测试
# --------
print("\n4. 执行 d-分离测试")

try:
    # 确保所有要测试的节点都在图中
    nodes_in_graph = set(graph_for_analysis._graph.nodes())
    required_nodes = {'T', 'Y', 'W', 'Z'}
    
    if required_nodes.issubset(nodes_in_graph):
        # 测试1: T 和 Y 在给定 W 的情况下是否 d-分离 (后门路径)
        test1 = graph_for_analysis.check_dseparation(['T'], ['Y'], ['W'])
        print(f"测试1: T 和 Y 在给定 W 的情况下是否 d-分离? {test1}")
        print("  解释: 如果 True，表示控制 W 后，T 对 Y 的效应可以被识别；如果有未观测的混杂，会是 False")
        
        # 测试2: T 和 Y 在不给定任何条件的情况下是否 d-分离
        test2 = graph_for_analysis.check_dseparation(['T'], ['Y'], [])
        print(f"测试2: T 和 Y 在不给定任何条件的情况下是否 d-分离? {test2}")
        print("  解释: 如果是 True，表示 T 和 Y 没有任何关联；通常应该是 False，因为我们关心的就是 T 到 Y 的因果效应")
        
        # 测试3: Z 和 Y 在给定 T 的情况下是否 d-分离 (工具变量条件)
        test3 = graph_for_analysis.check_dseparation(['Z'], ['Y'], ['T'])
        print(f"测试3: Z 和 Y 在给定 T 的情况下是否 d-分离? {test3}")
        print("  解释: 对于一个有效的工具变量 Z，给定 T 后，Z 和 Y 应该是 d-分离的")
        
        # 如果 'M' 在图中，测试4: M 和 W 在给定 T 的情况下是否 d-分离
        if 'M' in nodes_in_graph:
            test4 = graph_for_analysis.check_dseparation(['M'], ['W'], ['T'])
            print(f"测试4: M 和 W 在给定 T 的情况下是否 d-分离? {test4}")
    else:
        missing_nodes = required_nodes - nodes_in_graph
        print(f"d-分离测试所需的节点 {missing_nodes} 不在图中")
    
    # 分析多条路径，手动追踪 d-分离
    print("\n手动分析 T 和 Y 之间的路径:")
    # 直接路径
    print("  路径1: T -> Y (直接因果效应)")
    # 如果图中有 M
    if 'M' in nodes_in_graph:
        print("  路径2: T -> M -> Y (中介效应)")
    # 通过混杂变量 W 的路径
    print("  路径3: T <- W -> Y (混杂偏差)")
    print("  控制 W 后，路径3被阻断，但路径1仍然开放")
    if 'M' in nodes_in_graph:
        print("  控制 W 后，路径3被阻断，但路径1和路径2仍然开放")
    
except Exception as e:
    print(f"执行d-分离测试时出错: {e}")

# --------
# 5. 验证后门和前门标准
# --------
print("\n5. 验证后门和前门标准")

try:
    if required_nodes.issubset(nodes_in_graph):
        # 后门标准检验
        print("\n后门标准检验:")
        try:
            backdoor_valid = graph_for_analysis.check_valid_backdoor_set(['T'], ['Y'], ['W'])
            print(f"  'W' 是否为 'T' 到 'Y' 的有效后门调整集? {backdoor_valid}")
            print("  解释: True 表示控制 W 可以去除 T 和 Y 之间的混杂偏差")
        except Exception as e:
            print(f"  后门检验出错: {e}")
            # 代替方案：使用API版本不同的情况
            print("  尝试替代方案进行后门测试:")
            try:
                # 尝试列表而不是元组
                backdoor_valid = graph_for_analysis.check_backdoor(['T'], ['Y'], ['W'])
                print(f"  'W' 是否为 'T' 到 'Y' 的有效后门调整集? {backdoor_valid}")
            except Exception as e2:
                print(f"  替代方案也失败: {e2}")
        
        # 前门标准检验 (如果 M 在图中)
        print("\n前门标准检验:")
        if 'M' in nodes_in_graph:
            try:
                frontdoor_valid = graph_for_analysis.check_valid_frontdoor_set(['T'], ['Y'], ['M'])
                print(f"  'M' 是否为 'T' 到 'Y' 的有效前门调整集? {frontdoor_valid}")
                print("  解释: True 表示即使有未观测混杂，通过中介 M 也可以识别 T 对 Y 的因果效应")
            except Exception as e:
                print(f"  前门检验出错: {e}")
                # 代替方案
                try:
                    # 尝试列表而不是元组
                    frontdoor_valid = graph_for_analysis.check_frontdoor(['T'], ['Y'], ['M'])
                    print(f"  'M' 是否为 'T' 到 'Y' 的有效前门调整集? {frontdoor_valid}")
                except Exception as e2:
                    print(f"  替代方案也失败: {e2}")
        else:
            print("  'M' 不在图中，无法进行前门测试")
    else:
        print("验证后门和前门标准所需的节点不在图中")
            
    # 可视化多种因果结构模式
    print("\n三种基本因果结构模式:") 
    print("  1. 链 (Chain): A -> B -> C")
    print("     A 和 C 在给定 B 的情况下是 d-分离的")
    
    print("  2. 分叉 (Fork): A <- B -> C")
    print("     A 和 C 在给定 B 的情况下是 d-分离的") 
    
    print("  3. 对撞 (Collider): A -> B <- C")
    print("     A 和 C 在不给定任何条件下是 d-分离的")
    print("     但在给定 B 或 B 的任何后代的情况下，A 和 C 不再 d-分离")

except Exception as e:
    print(f"验证后门和前门标准时出错: {e}")

# --------
# 6. do-算子（手术）演示
# --------
print("\n6. do-算子（手术）演示")

try:
    print("\ndo(T=t) 手术:")
    print("  在因果图上执行 do(T=t) 手术会移除所有指向 T 的边")
    print("  这反映了我们在干预 T 时，T 不再受其原因 (如 W, Z) 的影响")
    
    # 尝试不同的do操作方法
    try:
        # 尝试使用do_surgery方法
        intervened_graph = graph_for_analysis.do_surgery(nodes=['T'])
        print("  使用 do_surgery(nodes=['T']) 成功")
    except Exception as e:
        print(f"  do_surgery 方法失败: {e}")
        try:
            # 尝试使用do操作方法
            intervened_graph = graph_for_analysis.do(nodes=['T'])
            print("  使用 do(nodes=['T']) 成功")
        except Exception as e2:
            print(f"  do 方法也失败: {e2}")
            # 手动创建干预图
            print("  将手动演示do操作的效果")
            intervened_graph = None
    
    if intervened_graph is not None:
        # 比较原图和干预后的图
        print("\n原图边:")
        print(f"  {list(graph_for_analysis._graph.edges())}")
        print("干预后的图边:")
        print(f"  {list(intervened_graph._graph.edges())}")
        
        # 检查指向 T 的边是否被移除
        t_parents_before = list(graph_for_analysis._graph.predecessors("T"))
        t_parents_after = list(intervened_graph._graph.predecessors("T"))
        print(f"\nT 的父节点 (干预前): {t_parents_before}")
        print(f"T 的父节点 (干预后): {t_parents_after}")
    else:
        print("\ndo 操作的手动演示:")
        print("  原图中 T 的父节点: ", list(graph_for_analysis._graph.predecessors("T")))
        print("  do(T=t) 操作会移除这些父节点到 T 的所有边，使 T 只受干预值的影响")
    
except Exception as e:
    print(f"执行 do-算子演示时出错: {e}")

print("\n-------------------------------------------")
print("DoWhy 因果图完整示例已完成")
print("-------------------------------------------") 