








import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

import dowhy


dataset = pd.read_csv('https://raw.githubusercontent.com/Sid-darthvader/DoWhy-The-Causal-Story-Behind-Hotel-Booking-Cancellations/master/hotel_bookings.csv')
dataset.head()


dataset.columns








# Total stay in nights
dataset['total_stay'] = dataset['stays_in_week_nights']+dataset['stays_in_weekend_nights']
# Total number of guests
dataset['guests'] = dataset['adults']+dataset['children'] +dataset['babies']
# Creating the different_room_assigned feature
dataset['different_room_assigned']=0
slice_indices =dataset['reserved_room_type']!=dataset['assigned_room_type']
dataset.loc[slice_indices,'different_room_assigned']=1
# Deleting older features
dataset = dataset.drop(['stays_in_week_nights','stays_in_weekend_nights','adults','children','babies'
                        ,'reserved_room_type','assigned_room_type'],axis=1)
dataset.columns





dataset.isnull().sum() # Country,Agent,Company contain 488,16340,112593 missing entries 
dataset = dataset.drop(['agent','company'],axis=1)
# Replacing missing countries with most freqently occuring countries
dataset['country']= dataset['country'].fillna(dataset['country'].mode()[0])


dataset = dataset.drop(['reservation_status','reservation_status_date','arrival_date_day_of_month'],axis=1)
dataset = dataset.drop(['arrival_date_year'],axis=1)
dataset = dataset.drop(['distribution_channel'], axis=1)


# Replacing 1 by True and 0 by False for the experiment and outcome variables
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(1,True)
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(0,False)
dataset['is_canceled']= dataset['is_canceled'].replace(1,True)
dataset['is_canceled']= dataset['is_canceled'].replace(0,False)
dataset.dropna(inplace=True)
print(dataset.columns)
dataset.iloc[:, 5:20].head(100)





# 筛选出 deposit_type 为 "No Deposit" 的数据行
# 这是为了只考虑没有支付押金的预订
dataset = dataset[dataset.deposit_type == "No Deposit"]

# 使用 groupby 对 'deposit_type' 和 'is_canceled' 进行分组，并计算每组的数量
# 这是为了查看在没有支付押金的情况下，预订是如何被取消或保留的
dataset.groupby(['deposit_type', 'is_canceled']).count()



# 使用 deep=True 创建数据集的深度副本并将其存储在 dataset_copy 中
# 这样做是为了保留原始数据集的完整副本，以便后续可以对其进行比较或恢复
dataset_copy = dataset.copy(deep=True)









# 初始化一个变量以存储所有迭代中计数的总和
counts_sum = 0

# 进行 1 到 10000 的循环，每次随机抽取 1000 个观察值
for i in range(1, 10000):
    # 初始化一个变量以存储当前迭代中相同值的计数
    counts_i = 0
    
    # 随机从数据集中抽取 1000 个观察值，并将其存储在 rdf 中
    rdf = dataset.sample(1000)
    
    # 计算 'is_canceled' 和 'different_room_assigned' 取相同值的观察数
    # shape[0] 会给出行数，即满足条件的观察数
    counts_i = rdf[rdf["is_canceled"] == rdf["different_room_assigned"]].shape[0]
    
    # 将当前迭代的计数添加到总和中
    counts_sum += counts_i

# 计算 10000 次迭代中相同值出现的平均次数
# 这将给出 'is_canceled' 和 'different_room_assigned' 取相同值的预期概率
counts_sum / 10000






# 初始化一个变量以存储所有迭代中计数的总和
counts_sum = 0

# 进行 1 到 10000 的循环，每次从没有预订更改的观察中随机抽取 1000 个样本
for i in range(1, 10000):
    # 初始化一个变量以存储当前迭代中相同值的计数
    counts_i = 0
    
    # 从数据集中筛选出 "booking_changes" 为 0 的观察，然后随机抽取 1000 个样本
    # 这是为了只考虑那些没有进行预订更改的情况
    rdf = dataset[dataset["booking_changes"] == 0].sample(1000)
    
    # 计算 'is_canceled' 和 'different_room_assigned' 在这些情况下取相同值的观察数
    counts_i = rdf[rdf["is_canceled"] == rdf["different_room_assigned"]].shape[0]
    
    # 将当前迭代的计数添加到总和中
    counts_sum += counts_i

# 计算 10000 次迭代中相同值出现的平均次数
# 这将给出 'is_canceled' 和 'different_room_assigned' 在没有预订更改的情况下取相同值的预期概率
counts_sum / 10000









# 计算当存在预订更改（"booking_changes" > 0）时，'is_canceled' 和 'different_room_assigned' 取相同值的预期概率。结果显示这个预期概率为 66.4%。
counts_sum = 0

# 进行 1 到 10000 的循环，每次从有预订更改的观察中随机抽取 1000 个样本
for i in range(1, 10000):
    # 初始化一个变量以存储当前迭代中相同值的计数
    counts_i = 0
    
    # 从数据集中筛选出 "booking_changes" 大于 0 的观察，然后随机抽取 1000 个样本
    # 这是为了只考虑那些有进行预订更改的情况
    rdf = dataset[dataset["booking_changes"] > 0].sample(1000)
    
    # 计算 'is_canceled' 和 'different_room_assigned' 在这些情况下取相同值的观察数
    counts_i = rdf[rdf["is_canceled"] == rdf["different_room_assigned"]].shape[0]
    
    # 将当前迭代的计数添加到总和中
    counts_sum += counts_i

# 计算 10000 次迭代中相同值出现的平均次数
# 这将给出 'is_canceled' 和 'different_room_assigned' 在有预订更改的情况下取相同值的预期概率
counts_sum / 10000












import pygraphviz
causal_graph = """digraph {
different_room_assigned[label="Different Room Assigned"];
is_canceled[label="Booking Cancelled"];
booking_changes[label="Booking Changes"];
previous_bookings_not_canceled[label="Previous Booking Retentions"];
days_in_waiting_list[label="Days in Waitlist"];
lead_time[label="Lead Time"];
market_segment[label="Market Segment"];
country[label="Country"];
U[label="Unobserved Confounders",observed="no"];
is_repeated_guest;
total_stay;
guests;
meal;
hotel;
U->{different_room_assigned,required_car_parking_spaces,guests,total_stay,total_of_special_requests};
market_segment -> lead_time;
lead_time->is_canceled; country -> lead_time;
different_room_assigned -> is_canceled;
country->meal;
lead_time -> days_in_waiting_list;
days_in_waiting_list ->{is_canceled,different_room_assigned};
previous_bookings_not_canceled -> is_canceled;
previous_bookings_not_canceled -> is_repeated_guest;
is_repeated_guest -> {different_room_assigned,is_canceled};
total_stay -> is_canceled;
guests -> is_canceled;
booking_changes -> different_room_assigned; booking_changes -> is_canceled; 
hotel -> {different_room_assigned,is_canceled};
required_car_parking_spaces -> is_canceled;
total_of_special_requests -> {booking_changes,is_canceled};
country->{hotel, required_car_parking_spaces,total_of_special_requests};
market_segment->{hotel, required_car_parking_spaces,total_of_special_requests};
}"""





# 使用 dowhy 库创建一个因果模型
# 这里我们为模型提供了几个关键参数：
# - data：包含观察数据的数据集
# - graph：代表因果关系的因果图，其中换行符被替换为空格，以确保格式正确
# - treatment：我们关注的处理变量，即是否分配了与客户预订相同的房间（"different_room_assigned"）
# - outcome：我们关注的结果变量，即预订是否被取消（"is_canceled"）

model = dowhy.CausalModel(
        data=dataset, 
        graph=causal_graph.replace("\n", " "), 
        treatment="different_room_assigned", 
        outcome='is_canceled'
)

# 调用 view_model 方法以可视化因果模型
# 这将展示一个因果图，帮助我们更好地理解变量之间的关系
model.view_model()









#Identify the causal effect
identified_estimand = model.identify_effect()
print(identified_estimand)





estimate = model.estimate_effect(identified_estimand, 
                                 method_name="backdoor.propensity_score_weighting",target_units="ate")
# ATE = Average Treatment Effect
# ATT = Average Treatment Effect on Treated (i.e. those who were assigned a different room)
# ATC = Average Treatment Effect on Control (i.e. those who were not assigned a different room)
print(estimate)











refute1_results=model.refute_estimate(identified_estimand, estimate,
        method_name="random_common_cause")
print(refute1_results)








refute2_results=model.refute_estimate(identified_estimand, estimate,
        method_name="placebo_treatment_refuter")
print(refute2_results)











refute3_results=model.refute_estimate(identified_estimand, estimate,
        method_name="data_subset_refuter")
print(refute3_results)








#!pip install xgboost


# plot feature importance using built-in function
from xgboost import XGBClassifier
from xgboost import plot_importance
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from matplotlib import pyplot
# split data into X and y
X = dataset_copy
y = dataset_copy['is_canceled']
X = X.drop(['is_canceled'],axis=1)
# One-Hot Encode the dataset
X = pd.get_dummies(X)
# split data into train and test sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=26)
# fit model no training data
model = XGBClassifier()
model.fit(X_train, y_train)
# make predictions for test data and evaluate
y_pred = model.predict(X_test)
predictions = [round(value) for value in y_pred]
accuracy = accuracy_score(y_test, predictions)
print("Accuracy: %.2f%%" % (accuracy * 100.0))
print(classification_report(y_test, predictions))


# plot feature importance
plot_importance(model,max_num_features=20)
pyplot.show()


# Execute this code to hide all warnings
from IPython.display import HTML
HTML('''<script>
code_show_err=false;
function code_toggle_err() {
 if (code_show_err){
 $('div.output_stderr').hide();
 } else {
 $('div.output_stderr').show();
 }
 code_show_err = !code_show_err
}
$( document ).ready(code_toggle_err);
</script>
To toggle on/off output_stderr, click <a href="javascript:code_toggle_err()">here</a>.''')



