








# 导入所需的库：NumPy用于数值计算，Pandas用于数据处理，NetworkX用于图论模型，dowhy用于因果推断
import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

# 生成1000个从-5到5均匀分布的随机数作为变量X的值
X = np.random.uniform(low=-5, high=5, size=1000)

# 生成变量Y，它是变量X的0.5倍加上一个均值为0、标准差为1的正态分布噪声
Y = 0.5 * X + np.random.normal(loc=0, scale=1, size=1000)

# 生成变量Z，它是变量Y的2倍加上一个均值为0、标准差为1的正态分布噪声
Z = 2 * Y + np.random.normal(loc=0, scale=1, size=1000)

# 生成变量W，它是变量Z的3倍加上一个均值为0、标准差为1的正态分布噪声
W = 3 * Z + np.random.normal(loc=0, scale=1, size=1000)

# 将所有生成的变量存储在一个Pandas DataFrame中
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))






# 使用gcm库中的InvertibleStructuralCausalModel类创建一个因果模型。
# 这里，nx.DiGraph表示一个有向图，其中('X', 'Y'), ('Y', 'Z'), ('Z', 'W')定义了因果关系：X -> Y -> Z -> W。
causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z'), ('Z', 'W')]))

# 使用auto模块的assign_causal_mechanisms函数自动为模型中的每一条因果关系分配一个因果机制。
# 这里，数据DataFrame用于帮助算法确定最合适的因果机制。
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 使用fit函数拟合模型，这一步将使用数据来估计模型中的各个参数。
gcm.fit(causal_model, data)








# 从X的正常分布（均匀分布，范围在-5到5）中随机抽取一个样本
X = np.random.uniform(low=-5, high=5)

# 计算Y的值。这里，我们故意将Y的噪声设置为5，这是一个异常高的值。
# Y通常是X的0.5倍加上一个正态分布的噪声，但在这里，噪声被设置为5。
Y = 0.5 * X + 5

# 计算Z的值，它是Y的2倍（这里没有额外的噪声）
Z = 2 * Y

# 计算W的值，它是Z的3倍（这里没有额外的噪声）
W = 3 * Z

# 创建一个只包含这一个异常样本的Pandas DataFrame
# 这个数据框只包含一个样本，其中包含X、Y、Z和W的值。
anomalous_data = pd.DataFrame(data=dict(X=[X], Y=[Y], Z=[Z], W=[W]))





anomalous_data.iloc[0]['W'],data['W'].mean()





# 使用gcm库的attribute_anomalies函数来计算异常归因分数。
# 这里，'causal_model'是先前创建和拟合的因果模型，
# 'W'是我们关心的目标节点，
# 'anomalous_data'包含了异常样本。
attribution_scores = gcm.attribute_anomalies(causal_model, 'W', anomaly_samples=anomalous_data)
attribution_scores





import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()















import networkx as nx, numpy as np, pandas as pd
from dowhy import gcm
from scipy.stats import halfnorm


X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = np.maximum(X, Y) + np.random.normal(loc=0, scale=0.5, size=1000)
W = Z + halfnorm.rvs(size=1000, loc=0.1, scale=0.2)
data_old = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))


X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = X + Y + np.random.normal(loc=0, scale=0.5, size=1000)
W = Z + halfnorm.rvs(size=1000, loc=0.1, scale=0.2)
data_new = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))





causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X', 'Z'), ('Y', 'Z'), ('Z', 'W')]))  # (X, Y) -> Z -> W
gcm.auto.assign_causal_mechanisms(causal_model, data_old)


import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()





attributions = gcm.distribution_change(causal_model, data_old, data_new, 'W')
attributions

















To see how the method works, let us generate some data following the example above:


import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

X = abs(np.random.normal(loc=0, scale=5, size=1000))
Y = X + abs(np.random.normal(loc=0, scale=1, size=1000))
Z = Y + abs(np.random.normal(loc=0, scale=1, size=1000))
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)


import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()





contributions = gcm.intrinsic_causal_influence(causal_model, 'Z')
contributions














import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm


X = np.random.normal(loc=0, scale=1, size=1000)
Z = np.random.normal(loc=0, scale=1, size=1000)
Y = X + 3 * Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Z', 'Y')]))  # X -> Y <- Z
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)


import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()





parent_relevance, noise_relevance = gcm.parent_relevance(causal_model, target_node="Y")
parent_relevance, noise_relevance





from sklearn.linear_model import LinearRegression
from dowhy.gcm.util.general import variance_of_deviations


mdl = LinearRegression()
mdl.fit(data[['X', 'Z']].to_numpy(), Y)
relevance = gcm.feature_relevance_distribution(mdl.predict, data[['X', 'Z']].to_numpy(), subset_scoring_func=variance_of_deviations)
relevance





single_observation = np.array([[2, 1]])





from dowhy.gcm.util.general import means_difference


relevance = gcm.feature_relevance_sample(mdl.predict, data[['X', 'Z']].to_numpy(), baseline_samples=single_observation, subset_scoring_func=means_difference)
relevance






