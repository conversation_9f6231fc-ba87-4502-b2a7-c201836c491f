{"cells": [{"cell_type": "markdown", "id": "9fd851ab-e30e-4586-95d1-9494d427f374", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "77dac9d2-9a63-4249-a79f-37a9b73d25bd", "metadata": {}, "source": ["# Treatment Featurization Examples \n", "\n", "Many EconML estimators model treatment effects as linear with respect to the treatment. However, there are times when one may want to impose a particular form of non-linearity in the treatment effect. For example, one may believe that the outcome is linear with respect to treatment squared, or more generally, linear with respect to some featurization of the treatment. We enable users to pass such a featurization via the `treatment_featurizer` argument. This notebook walks through some examples where treatment featurization may be useful. \n", "\n", "Additional context and problem setup can be found in the docs at [this link](https://econml.azurewebsites.net/spec/api.html#linear-in-treatment-cate-estimators).\n", "\n", "The EconmL SDK supports treatment featurization for many estimators, including:\n", "\n", "* Estimators given unconfoundedness\n", "    * LinearDML\n", "    * SparseLinearDML\n", "    * KernelDML\n", "    * CausalForestDML\n", "    * DMLOrthoForest\n", "    * NonParamDML\n", "    * DML\n", "    \n", "* Estimators in an IV setting\n", "    * OrthoIV\n", "    * DMLIV\n", "    * NonParamDMLIV\n", "    * DRIV\n", "    * LinearDRIV\n", "    * SparseLinearDRIV\n", "    * ForestDRIV\n", "    \n", "\n", "**Notebook contents:**\n", "1. Treatment Featurization under Unconfoundedness\n", "2. Treatment Featurization for IV methods "]}, {"cell_type": "markdown", "id": "59180b1d-d4b3-4418-954c-d43e8302d342", "metadata": {}, "source": ["# Imports "]}, {"cell_type": "code", "execution_count": 1, "id": "8d05cc1a-1285-4a27-a657-0291f9e2dbbb", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# EconML imports\n", "from econml.dml import LinearDML\n", "from econml.iv.dml import OrthoIV\n", "\n", "# Generic ML imports\n", "from sklearn.preprocessing import PolynomialFeatures # featurizer class\n", "from sklearn.ensemble import RandomForestRegressor"]}, {"cell_type": "markdown", "id": "b2681963-6c74-4af8-8b54-59959e166e5b", "metadata": {}, "source": ["# Basic examples "]}, {"cell_type": "markdown", "id": "8974cf05-3c37-43a1-8443-294ec2a8107f", "metadata": {}, "source": ["## Treatment Featurization under Unconfoundedness "]}, {"cell_type": "markdown", "id": "bf676590-382d-41c3-932b-f82cbba62a06", "metadata": {}, "source": ["### Simulate data \n", "\n", "We generate data where the outcome depends on the square of the treatment."]}, {"cell_type": "code", "execution_count": 2, "id": "b149e603-675a-4dea-a977-a58b1a2edea9", "metadata": {}, "outputs": [], "source": ["n = 2000\n", "d_w = 5\n", "W = np.random.normal(size = (n, d_w), scale = 5)\n", "T = np.random.uniform(low = 0, high = 10, size = (n, 1)) + 0.5*W[:, [0]]\n", "epsilon = np.random.normal(size = (n, 1))*20\n", "Y = 0.5*T**2 + W[:, [1]] + epsilon\n", "\n", "test_T = np.arange(0, 10, step = 0.1).reshape(-1, 1)\n", "\n", "featurizer = PolynomialFeatures(degree=2, interaction_only=False, include_bias=False)"]}, {"cell_type": "markdown", "id": "172c60b5-5e15-412c-9af4-b11ef7f33612", "metadata": {}, "source": ["### Run estimation \n", "\n", "Here we run two estimators, one with treatment featurization and one without treatment featurization for comparison"]}, {"cell_type": "code", "execution_count": 3, "id": "1c484dd3-4b2f-469d-b418-6267f1db6226", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "           <td></td>           <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0</th>        <td>0.061</td>      <td>0.289</td>  <td>0.21</td>   <td>0.834</td>  <td>-0.506</td>    <td>0.628</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0^2</th>      <td>0.507</td>      <td>0.024</td> <td>21.467</td>   <td>0.0</td>    <td>0.461</td>    <td>0.554</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot \\psi(T) + g(X, W) + \\epsilon$<br/>where $\\psi(T)$ is the output of the `treatment_featurizer<br/>and for every outcome $i$ and featurized treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = X' coef_{ij} + cate\\_intercept_{ij}$<br/>Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                          CATE Intercept Results                         \n", "=========================================================================\n", "                    point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------------\n", "cate_intercept|T0            0.061  0.289   0.21  0.834   -0.506    0.628\n", "cate_intercept|T0^2          0.507  0.024 21.467    0.0    0.461    0.554\n", "-------------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot \\psi(T) + g(X, W) + \\epsilon$\n", "where $\\psi(T)$ is the output of the `treatment_featurizer\n", "and for every outcome $i$ and featurized treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = X' coef_{ij} + cate\\_intercept_{ij}$\n", "Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["est = LinearDML(treatment_featurizer = featurizer)\n", "est.fit(Y=Y, T=T, W=W)\n", "\n", "bad_est = LinearDML()\n", "bad_est.fit(Y=Y, T=T, W=W)\n", "\n", "est.summary()"]}, {"cell_type": "markdown", "id": "bf292edf-70bc-4720-ad6a-7a62787ea9b6", "metadata": {}, "source": ["### Plot effects "]}, {"cell_type": "code", "execution_count": 4, "id": "18630b8d-541a-4c18-8a50-00ede8fe9a0e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Treatment Effect vs Treatment value')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "\n", "# Plot point estimates\n", "plt.plot(\n", "    test_T,\n", "    est.effect(T0 = 0, T1=test_T),\n", "    label = 'Estimate with Featurized Treatment',\n", "    linewidth=3\n", ")\n", "\n", "plt.plot(\n", "    test_T,\n", "    bad_est.effect(T0 = 0, T1=test_T),\n", "    label = 'Estimate without Featurized Treatment'\n", ")\n", "\n", "plt.plot(\n", "    test_T,\n", "    0.5*test_T**2,\n", "    linestyle='--',\n", "    linewidth=3,\n", "    label='Ground Truth',\n", ")\n", "\n", "# Plot confidence intervals\n", "lb, ub = est.effect_interval(T0 = np.zeros(shape=(100, 1)), T1=test_T)\n", "\n", "plt.fill_between(\n", "    test_T.squeeze(),\n", "    lb.squeeze(),\n", "    ub.squeeze(),\n", "    alpha = 0.4, \n", ")\n", "\n", "lb, ub = bad_est.effect_interval(T0 = np.zeros(shape=(100, 1)), T1=test_T)\n", "\n", "plt.fill_between(\n", "    test_T.squeeze(),\n", "    lb.squeeze(),\n", "    ub.squeeze(),\n", "    alpha = 0.4\n", ")\n", "\n", "plt.legend()\n", "plt.xlabel('Treatment value')\n", "plt.ylabel('Treatment effect')\n", "plt.title('Treatment Effect vs Treatment value')"]}, {"cell_type": "markdown", "id": "ab4e4de4-0efd-4d52-a623-21bf0c25e076", "metadata": {}, "source": ["## Treatment Featurization for IV methods "]}, {"cell_type": "markdown", "id": "7f98173a-4a39-4514-b1c6-63fc4e5251ff", "metadata": {}, "source": ["### Simulate data "]}, {"cell_type": "code", "execution_count": 5, "id": "e47f53c1-9053-4fe4-8131-9f8535a74eee", "metadata": {}, "outputs": [], "source": ["n = 2000\n", "d_w = 5\n", "W = np.random.normal(size = (n, d_w), scale = 5)\n", "unobserved_confounder = np.random.normal(size = (n, 1)) * 5\n", "Z = np.random.normal(loc = 3, scale = 5, size = (n, 1))\n", "T = np.random.uniform(low = 0, high = 10, size = (n, 1)) + 0.5*W[:, [0]] + Z + unobserved_confounder\n", "epsilon = np.random.normal(size = (n, 1)) * 50\n", "Y = 0.5*T**2 + W[:, [1]] + unobserved_confounder + epsilon \n", "\n", "test_T = np.arange(0, 10, step = 0.1).reshape(-1, 1)\n", "\n", "featurizer = PolynomialFeatures(degree=2, interaction_only=False, include_bias=False)"]}, {"cell_type": "markdown", "id": "dc351445-eae6-4c59-ba8c-3f8769aaaa36", "metadata": {}, "source": ["### Run estimation \n", "\n", "Here we run two estimators, one with treatment featurization and one without treatment featurization for comparison.\n", "\n", "Note that OrthoIV requires that the dimension of the featurized treatment be less than or equal to the dimension of the instrument. So in the case where we have a featurized treatment that has higher dimension than our instrument, we can either featurize the instrument ourselves before passing it to the estimator or we can set `projection=True`, which will  fit efficient instruments, along with passing a flexible `model_t_xwz`. In principle we recommend the latter approach."]}, {"cell_type": "code", "execution_count": 6, "id": "58b5c8d3-3c24-4613-8035-306bd7bf098f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "           <td></td>           <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0</th>       <td>-0.731</td>      <td>0.539</td> <td>-1.356</td>  <td>0.175</td>  <td>-1.787</td>    <td>0.326</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0^2</th>      <td>0.537</td>      <td>0.031</td> <td>17.467</td>   <td>0.0</td>    <td>0.477</td>    <td>0.598</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot \\psi(T) + g(X, W) + \\epsilon$<br/>where $\\psi(T)$ is the output of the `treatment_featurizer<br/>and for every outcome $i$ and featurized treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = X' coef_{ij} + cate\\_intercept_{ij}$<br/>Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                          CATE Intercept Results                         \n", "=========================================================================\n", "                    point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------------\n", "cate_intercept|T0           -0.731  0.539 -1.356  0.175   -1.787    0.326\n", "cate_intercept|T0^2          0.537  0.031 17.467    0.0    0.477    0.598\n", "-------------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot \\psi(T) + g(X, W) + \\epsilon$\n", "where $\\psi(T)$ is the output of the `treatment_featurizer\n", "and for every outcome $i$ and featurized treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = X' coef_{ij} + cate\\_intercept_{ij}$\n", "Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["est = OrthoIV(\n", "    model_t_xwz=RandomForestRegressor(), \n", "    projection=True, \n", "    treatment_featurizer = featurizer\n", ")\n", "est.fit(Y=Y, T=T, W=W, Z=Z)\n", "\n", "bad_est = OrthoIV()\n", "bad_est.fit(Y=Y, T=T, W=W, Z=Z)\n", "\n", "est.summary()"]}, {"cell_type": "code", "execution_count": 7, "id": "efb9dfb0-dc61-4b7b-af05-a29abf7e5bd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Treatment Effect vs Treatment value')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***********************************/SmzVtsRsFVvArS5K9lkbq01pzs8LC/sQePf+Jcu4qMKjItubhDXQCszN7GtqIPk2+dO+lzO/sC/O1QK3vqnUNut1pMbFtayIVLirBaEtdPpRQsKc6icq4DewL3c5yJxiy8lFIWrXVIKXUOgNZ6ik0fQoiU0l0LDTvBP4W/oeKZNp+WbsxuFa0A8+y9zFzMDvVdfexr6KHHO/ofLr6wm0DEOyRvK81k54KCD1DtfpuLim6jdArREH2BEC8dbeetE51Dku5NCjZV5HPJimKyE7ypdEmOjXMW5JGbIdtxxWK8Ga/twAZgj1LqSeAPwEDxpbX+3ziPTQgxnfq6oH47uKbQoqkj0LQXjj5zOm1+3YehcOmZj8uWbfRvFS6dvpgJIeKkucfLO/U9I0JI+wUiPnZ1P8VbnY8yL2M1N5ffO+T+9blXsyHvmkmfNxiO8GZNJy8daxuxnLmyLIcrV5dQnJ3YGeIsu4X183LP+niIyYqlxysf6AQuwWiwV9F/pfASYiYI+qBpN3QcY8zdd8eiNbQdhqN/iqbNl8Gmj0Px6jNPhrc7jEiI/EWyrY9IeW0uH/sbemjt9Y96fygSYI/zL7zZ+Ts8YScA1e63afAepjx95cDjJtv3FNGavaec/O1w64jZtXl56Vy1poyFhYndbifNrFg9x8GK0mxMCdzPcbYYr/AqVkrdBRzgdMHVL/Xj7oU420UixhY9ze8YPVmT1VljbO/TdQIyCowZrrkbzjxtPqMgmjK/QLb1ESmvyxPgnQYnzWOEn4Z1iH09f+P1jt/iCnUMuS8vbQ6hyOiF2kS01hxtcfHXQy0jir2CTCtXrC5lzZychDawKwWLi7KoKpc+rjMxXuFlBrIYWnD1k8JLiFTmrDeytHy9k39uT4OxvU/7EbDlTF/afHapMcPlmHwjsRCJ1tMX5J0GJw3d3lHvj+gwB3tf4rWO3+AMDl2+z7YUckHh+6lyXI5ZTT6/qq7Tw18OtlDXOXRf1EyrmUtWlnBuRT7mBM80lTpsbJgvfVzTYbzviGat9dcTNhIhxJnzOqFhh1E8TZa7zejhmu60eUe5UXBlT8MVj0LEWY83yIHGHk51jZ42D1Dn2cdfW/+TzsDQn7MMcy7nF7yP9blXYTFN/uempdfH3w62cLhl6IUvVouJC5cUsnVJIbYEzzRl2y2sn59LeZ70cU2X8QovWQMQYqYI+Y3m9/YjRiP8ZMQjbV4pYymxbO30ZHoJEWcuX5ADjb3UdnombIW0mKxDii67KYvNBTdzTt71WE2Tb3Dv8gR4/nDriD0VzUpx7sJ8Ll5RnPDkd5vFxJq5Rh6X9HFNr/G+kpcmbBRCiKnR2ii2mvZCaPQelDHFI21emaBgMZRWGs3zQqQ4jz/EgcYeTnZ4iIyxn6Imgkmdnmmam76CJZnnUu89wKb8d7Mp78ZJp82DUey9dLSd7Se7CA+r9qrKHVyxqpT8zMQu7ZkULCvNZs0cR0JzwM4mYxZeWuuuRA5ECDFJPY3GLJXXObnnxSNt3mQx4iBK1oAtsVuTCDEVHn+Ig029nGh3j1lwHXdv57WOR6h0XMbG/BuG3H9F6aexmtJJN0/+DxVvIMyr1e28XtMxYmuh5SXZXL6qhDm507C/6STNz89g7TxHwnPAzjaya6UQM42v1yi4nPWTe144ALWvR9PmPVC6FpZfbTS9T5XZGt3WZ9X0bIQtRJz1BYyCq6Zt7IKrxrODVzt+TYuvGgB3Vzdrc68kzXR6g2lHWvGkzx0IRXijpoNXqttHZHEtyM/gitWlCY+GACjKtrF+fi6FWYndQPtsJYWXEDNFKGBEQ7Qdmlwf14i0+eXRtPn5Ux+LxQ7FK41tfSxylZNIfR5/iEPN4xdcxz07eG1QwdXPF3bT7KtmfsaaKZ07GI6wo7aLF4+24/GHhtxXmmPn8lUlrCjNTvjehtl2C+skADXhpPASItVpDR3VRghqcPRL20d/3rC0+dwFZ542b800tvUpXA5m+e9DpL6YlhQ9O3it4xFafEM3izerNDbkXsPmgpuntIF1OKLZVdfNi0fbRoSfFmRauWxVCZVzHQndxBqkcT7Z5H9OIVKZq9WYrerrjP05WhuzYkefnr60eVu2EQlRsERS5sWM4PaHODROwQVQ497Jy+2/otVfM+R2i7KyLvdqNue/l+y0gkmfO6I1e+udvHCkbcTWQo70NC5ZUcyG+XkJz+KymBTLS7NZNSeHNLP8HCeLFF5CpCK/28jj6q6d3POGpM0XwvqPwJz1U0+bT88zrlDMXyQp82JGcPmCHGzqpXaMqxQHa/XVDCm6LMrK+tyrpzzDFdGa/Y09vHC4jXb30LT5TJuFdy0r4tyF+QkvepSCRYWZVJXnkm6VxPlkk8JLiFQSDkHLPmg9YPRmxaqnAY48De2HpydtPrMouq3PGfSBCZFAvT4j+LSuc/TgU631iB6qc/Ku5+2uxwjpEOtzrzqjgutQUy/PHW6lzTW04EpPM7NtWRFbFhUkJZ6hPC+dteW5ODLkSsVUIYWXEKmiswYad0HAE/tzpjttPmeOsaSYUza15wuRYM6+AAebesdMmg/rEAd7XuTNrj/wnrlfochWMXCfzZzBTXO/TLGtgkxL3qTPrbXmcLOL54+00twzNEfPFk2bv2BJYVL2NSzKtrFuXi5F2XKlYqqRwkuIZHO3G31cnvbYnzPdafO5842CK6toas8XIsG6PAEONPaMuZdiKBJgX8/feKvzUXpCbQC83vE7bpr7pSGPW5i5ftLn1lpzpMXF84dbaRpWcFktJs5fXMCFSwrJsCb+V6wjPY218xyyxU8Kk8JLiGQJeIwZrs6aiR/bb3ja/IILYekU0+aVgryFxpJi+uT/2hciGdpcPg429dLsHH2nhkDEy57uP7O9+3HcoaE54LV9e/GH+7CZp1aUDBRcR1ppGnb+NLNiy6ICti4tIjPB2/sAZNrMVM51sLAwM+GxFGJypPASItEiYWjZb7xFQhM/HqY3bd5kNq5OLFkD9pzJP1+IJGhyejnY1Ev7sB6qft6wi13dT7Gj60l8kaGbTKebczg3/92ck3vdlIqu/oLrhSNtNDqHzrClmRXnVuSzbVlRUhLf7WkmVs+RaIiZRAovIRKp66Qxy+V3TfxYGJk2X7YWlk0xbd5kiabMrwarLEOImaG+q4+DTT10eYKj3h/RYV5u/yW7nc8QiAwtirIs+ZyX/x7W5V49pc2rI1pzuLmXF460jejhspgU5y1MXsGVZlasLMtheWm2REPMMFJ4CZEInk6jH8vVEtvjpzNt3mIzCq7iVZA2+V8+QiRaJKKp7fRwqLmXXu/4s8ImZabZVz2k6MpNK2Fz/i1UOi7FYpr8hSYRrTnY1MuLR9po6U2tgstiUiwtyWLVnBxsFomGmImk8BIinoJeaNwNndWMesnVcP1p88f+bDTbn0nafFqGsYdi0Qowy6XkIvWFwhFq2j0caenF4x89TmW0Hq0tBbdS17ePQut8thTcwqqcizCpyRclEa3Z19DDS0fbRsRCpJkV5y0s4MKlheQkoeAyKVhcnMWaOQ7J4prhpPASIh4iESM9vvkdY7lwItOZNm/LNvq3CpdOPcdLiATyBcMcb3NztMWFPzRyH1KtNaf69vNW16N0+uv55OKfYlanf31VZKzj/fO+QUXGWtQUwoLDESNp/qWjbXQOS5pPMys2LzKuUkzGDJdSUFGQSWW5g6wkNO2L6SdfRSGmm/MU1G+PvY9rSNp8gTHDNXfD5NPm7Q4jEiJ/kWzrI2YEjz/EkZZeato8hEaJmdc6wjH3W7zZ+QeafccGbj/Y+xJVjssGPlZKTSkWIhSOsOtUN68ca6e7b2gPmdViYnN0hitZBc+Cggwqyx1JmWET8SOFlxDTxdsN9TugtzG2x09X2nxGgREJkVcx6SELkQzOvgCHmns51dk36rY+oUiAA70v8nbX/9IVaBh2r6LDf+qMzh8IRdhe28Vr1e30+ob2kNnTTJy/uJDzFxckJYcLjLT5qnIHuRlTDEIWKU0KLyHOVMgPTXug/UhsfVzD0+ZXXA8Lt04+bT671Jjhcsyd0rCFSLTWXh+HmnpHXCHYzxt2scf5Z3Z2/RFP2DnkPrOyUJlzKecWvJcC69S+572BMG+e6OSNmg76AkN7yDKsZi5YUsiWRQVJSZoHmJNrp6o8l/xMKbhmMym8hJiqSMQotpr3GsXXRLxOOPaX02nzSy6HxRcbxddkOOYZG1dnl0xl1EIklNaaU119HG7uHTMSAuCkZw+PNXyDoB629Y4pg/W517Ap/8Yp7aMIxsbZrx/v5O2TnSN6yLJtFi5cWsi5C/OTdpVgmcNOZbmDwizZ3udsIIWXEFPR02gUUF7nxI+djrR5pYwrHMvWTi00VYgEC4YjnJjgCsXBSu1LhnycbSlgU96NrM29Crs5c0pj6PIEeLW6nV113SN6yHIz0ti2tIhzFuQlLQer1GFjzVwHxdkS83I2kcJLiMnw9Rh9XD31Ez92eNp8+SZYNsm0eWWCgsXGDJfdMeVhC5Eo3kCYo60uqltdBMOjN8wf9+wkP62MAtu8gdvTzdmsy72Sk569nFfwHlbnXIRZTa2pvLnHy6vVHexrcI7oISvKsnHRsiLWzsvFnKSkdym4zm5SeAkRi1DAiIZoO2RkbY1neNp86VpYPsm0eZMFCpcZKfO2rDMbuxAJ0O0JcKTFRV2nZ9SG+WDEx4GeF9ne/QRdgQbW5FzK9XPuGvKYi4pu49LiT0xpr0GtNbWdfbx8rI1jre4R95fnpXPRsiJWluVgStJehlJwCZDCS4jxaQ0dx4wQ1NDoDcEDpiNt3myNbuuzCtLSz2zsQiRAo9PLkeZeWntH73N0h7rY1f00e5zP4A33Dtx+qPdl3lV0G9lphQO3pU1xW58jzb28fKyd+m7viPsXF2Vy0bJiFhclb/PoMoed1XNzpOASgBReQozN1WIUUn1d4z9uOtLmLfZoyvxKsMgVTSK1hcIRajs9HGlxjbmlT6uvhh1df+SQ62XCeuhjbKYM1uZeiUlN/VdQMBxh7yknrx5vp8M9NPRUAavn5LBtWRHlecnbl3ROrp01c6VpXgwlhZcQw/ld0LADuuvGf5zW0HY4mjbfOLW0eWtmNGV+GZjlx1Gktr5AiGOtbo63uQmMkjAPUO16i+1dT3DKu3/EfQ5LMRvzb2St44oR2/7EyhsI8/bJTt6o6cTtH1rQmU2KDfNz2bqkiMLs5BU7c/PSWTMnhwIpuMQo5H96IfqFg9CyD1oPGsuG4znTtHlbtpHBVbBEUuZFyutw+zna4qK+a/TA08F2dj85ouiaY1/BefnvZln2lintoQjGFYqv13Swq7abQHho0WdPM3FuRQHnLylIWsq7UjAvL4M1c3Mk+FSMSwovIQA6jkPjLgj2jf+4UdPmzzOa4WORnhdNmV84+T0YhUigSMTI3zra6qLTPfp+o4GIF6tpaC/ixvybqO17B4WJFdkXsin/Ruamr5jyOOq7+njteAcHGnsYXvM50tM4f3EBmyrykxZ6qpSxtc/qOQ4c6bK1j5iYFF7i7OZuN/q4PO0TPK7N6OFq2jO1tPnMIiODK3fexI8VIon6N6yubnPhDYy2YXWEk3172dn1JO3+Wj61+KEhG1YvydzIRUW3sybnYnLSiqY0hv6G+deOd1DbOfKPoZIcG1uXFlFV7sCSpBljswkWFmaxak6ObF4tJkW+W8TZKeCBhp3GUuF4vN1w7K+n0+aXXgGLLo79isOcOcaSYk7ZmY9ZiDjq8gQ42uLiVJeH8CjtW/5wH/t7nmOX8090BU7vR3rM9SYrc7YOfKyUifMLbp3SGPyhMLvqunmjppMuz8hZtiXFWVy4pJClxVlJu0LRYlIsKcliZWkO6dbkzLKJmS1uhZdSah7wK6AUiAA/0Vp/XymVD/wOqABqgVu11t3xGocQQ4RD0HoAWvZDZPSrsYAzT5vPnW/McGUWTvxYIZKkfznxWKtrxJWB/Tr9Dezqfor9vc8TiIyMa2j11QwpvKbC2RfgrROdbK/twhccWvWZFKwtz+XCpYWUOZIXsWK1mFhWksWykuykLWuK2SGeM14h4J+01ruVUtnALqXU34A7gOe11vcrpe4B7gG+FMdxCGHoOgENuyAwMlxxwPC0+XmbYGmMafNKGb1bZVVGL5cQKcobMJYTj7ePvpwY0WGOu7ezq/tP1PbtHXG/zZRBpeNyzsm7lvwpblgNcKrTw+s1nRxs6hnRtG9PM3HewgI2LypIau9UhtXM8tJslhRnJW1rITG7xK3w0lo3A83R911KqcPAXOBG4F3Rh/0SeAkpvEQ8eTqNPi5369iPOZO0eZPZuDqxZA3Yc6Zv3EJMs7ZeH8da3TR0j3914h7nn3m29Ucjbi+wzuOcvOtYk3PJlOMgwhHNgaYe3jjeMWrgaUGmlfOXFLJhfm7SNq0GyLZbWFmWw6LCTExJ2lpIzE4J6fFSSlUA64G3gZJoUYbWulkpVZyIMYizUNBrJM53VhuZW6OJhKF+ezRt3gmFy2FFjGnz5jTj8SWrwZq8kEYhxhMMR6jt8FDd5sbZFxxxv9Z6RL/UqpyLeKHt54S0H4WJJVmbOCfveioy1k25t8rtD7Gjtou3T3TS6xu5zL+oMJPzFxeyoiw7aVv6ABRkWVlVlkN5XnrS+sjE7Bb3wksplQU8Bvyj1ro31m9kpdSdwJ0A8+dPYssVISKRaB/XPiObazSjps1/KLa0eYsNildC8SrjfSFSkLMvQHWbm5MdHkKjbFYdiHg52PMSu51Pc23ZP1JqXzJwX7o5m41516EwsT7vGhxpU//7uMnp5Y2aTvY1OAkNm2YzmxTrynM5f0lBUvu3wEiZX1WWQ3GObOsj4kvpsWYCpuPgSqUBfwL+qrX+bvS2o8C7orNdZcBLWuvl4x1n48aNeufOnXEbp5hFuuuM1Hm/a/T7R0ubX36tMWs10R8FaRnRbX1WGLNdQqSYcERT39VHdZubdtfoeye2+U6y2/kMB3tfHGiWX+u4kmvKPj+t4zjY1MObJzqpGyUOIstm4byF+Zy7MJ/sJAWegtG4v6Agk5Vl2RJ6KqaVUmqX1nrjaPfF86pGBfwMONxfdEU9CdwO3B/994/xGoM4i/R1GQVXb9PYj5lq2rwtO7qtz1Kjn0uIFOPyBY3ZrXYP/lG28glG/BxxvcYe5zM0eo+MuP+Y+02u1J/GrM6sCHL5gmyv7WL7yS5coywnluelc/7iAtbMTV7+FkCaWbGkOIvlpdlkWCVVSSRWPL/jLgA+AuxXSu2N3vYVjILr90qpvwNOAbfEcQxitgv6jFDTjqNj93ENT5tfczPM3zxx2rzdYURC5C2UbX1EyolENA3dXo63u2jpGX12q8N/ir3Ov7C/53l8kZFX8xZYy1mfey2VjkumXHRpranr7OOtk50cbOwlPOzn0KRgzVwHFywuZF5+cnshM21mlpVks7goC6tFfqZFcsTzqsbXMDaJH82l8TqvOEtEIkYh1bTXuCJxNFNNm88shNJKyKuY7lELccZcviA17R5OtLtHZF4N1hNs46cnPz3idhMWlmefz4a8a5iXvmbKDeT+UJi99U7ePtFFS69vxP3ZNgvnLsxn08L8pO2f2C8/M40VpTnMz8+QKxRF0skcq5h5ehqMKxF9PaPf73XCsb+cTptfcjksvtgovsaTXWqkzDumnkskRDzEMrsV0eEhG1A70oqZn145sGF1bloJ63KvpspxOZmW3CmPpbXXx/baLnbXdY+6rDk/P4MtiwtYPScnqcuJSsGc3HRWlmZLw7xIKVJ4iZnD6zS2+empH/3+qabNO+YZM1zZJdM9YiHOSI83SE372L1b/nAfh12vsNf5V5ZmnccFhe8fcv+GvGtJt+SwPvdqKjLWoibqZxxDKBLhYFMv2092cbLDM+L+NLNibXkumxcVMCc3uVcnWkyKRUWZLC/NTmrjvhBjkcJLpL5QAJr3Glcj6lGWVoanzZdvgmUTpM0rZURIlK2NLZVeiAQJhSOc6uqjpt0z6pWJWmsavYfZ1/M3DrteHbgysS/cw/kFtw4prlbmbD2j7Xy6PAF21Haxs64bj39ks3xBppXNiwrYMD8v6fsWZtrMLC02Eualf0ukMim8ROrSGtqPGj1aoZE9JFNKm1cmKFhszHDZHfEbuxCT1OH2U9Pmpq6rb9TcLU+omwM9L/BOz7N0BhpG3O8OddLur6PYvvCMxhGOaA4397KjtovqtpEN+SYFK8tyOHdhPouLspIadgpQlG1jeUk25Xnp0r8lZgQpvERq6m02tvnxjrJ/+kDa/F+MPq9Y0uZNFihcZuR12bLiN24hJsEXDFPb6aGmzUOPd7RU+QjH3dvZ1/Mcx93biRAe8ZhC63zW5l7JmpyLybBM/Y+JLk+AnXVd7KrtxjXK7FaO3cKmhflsXJCf1L0T4XT+1vLSbPIzJX9LzCxSeInU4us18ricp0bepyPGkuPRwWnzHx4/bd5sNQJPS1ZDmjTYiuSLRDRNPV5OtHtocnrH3TMRFM+3/Yzu4NB8OqspnZXZ21ibewVz7MunfGViKBLhcLOLHbVdHB9ldksBS0uyOLeigOWl2ZiTPKOUbjUNLCfa0yRTT8xMUniJ1BAOQvM+aDtozGgNNlra/MaPj582b7FHU+ZXgkX+IhbJ5+wLcKLDQ22HZ9QYCG/YhTvURZFtwcBtSikqHZfySsd/A1Cevpq1jstZkXMhVtPUm9jbXD521Xaz+1Q3nsDIWbRsu4WNC/LYWJFPXgokuhdmWVlems28PImDEDOfFF4i+Tqqjc2sgyO3FqHrBBz5U+xp89bMaMr8MjDLt7dILl8wTF1nHyc73HR5Ri4lhnWIE+5d7O99nuPutym1L+G2Bd8Z8phKx2UEIz4qcy+nwDr1qBN/KMyBxh521nZT1zXyZ61/dmtTRT4rSnOSPrtlNsH8fFlOFLOP/GYSyeNuM/q4PB0j7xueNl95C8w7b+y0eXsOlFRCwRJJmRdJFYloGp1eTnaMvpSotabVX8P+nhc41PsSfeHTeXSN3iN0BhqHFFg5aYW8q/iOKY1Fa019t5dddV2809BDYJRICkd6GucsyOOcBXkpMbuVZbewtDiLRUWZ2CyynChmHym8ROL53dC4y5jFGs7dDseeiT1tPj0PyqqMbX2SfHWVOLt1uP2c7PBwqrNv1MwtV7CDg70vsb/nBToCdaMeo8y+DF94jA3eJ8HlC7LnlJNddd20u0dGUpgUrCjNYeOCPJaVZif9ykSloMxhZ2lJNnMc9in3rAkxE0jhJRInHILW/dByACLDrpryOqH6r8YMWCxp81nFRsp87ry4D1uIsbj9IWo7PNR2euj1jrwSsN8Tjfdz2PUaMLKTPsuSz5qcS6h0XEqhbZwrcycQikQ40uxi96lujrW6Rm3aL8yysXFBHuvn56ZEuKjNYmJxcRZLirPIssmvI3F2kO90kRidNUYfV2DYlVMBt5HDVfua0US/4AJYesXYafM5c40Mrpyy+I9ZiFH4Q2FOdfZR29k3asDpaNLNDgYXXWnKxrLs86l0XMqCjKohW/1MhtaaJqePXae62dfgpG+URnmrxUTlXAcbF+QxPz8jJWaTirJtLC3Okr0TxVlJCi8RX54OI3PL3Tr09pDPSJo/8aKRTF++CZZdaTTQjyZ3vpEyn1kY9yELMVw4omns9lLbOVbfVoQG7yEO9r5EWIe4tuwfh9y/xnExu51PU5FRxRrHpSzL2oLNPMHeoePo9QbZW+9kT303rb2jF38VBZmcsyCPNXNzUqJXymoxsbAwgyVF2Tgykj/bJkSySOEl4iPQZ/RxdR4fens4CHWvGbNcgQnS5pUJ8hcaM1zpeYkZtxBRWmtaen3UdvRR3z0yTV5rTZv/BId6X+ZQ7yv0htoBMGHhkuK/I918etZ2jn05n138S7LTxvjDIgaBUIRDzb3sOdXN8Tb3KIuWRqP8hvm5rJ+fR2GWbcrnmk6FWVaWRGe3LGa58EUIKbzE9IqEofUgtOwziqzBtzdsh2N/BZ9z/LR5kxkKlkLpmok3uBZimrW5fJzq7ONUV9+oeVudgUYO9b7M4d6XR926J0KIavfbVDkuG7hNKTWloiuiNSfaPeytd3KgafSrEtPMijVzHKyfn8eiosykN8qDzG4JMR4pvMT06a6Fhp3gH3RV1qhp8x8aPW3enGYUZCWrwTr1ZRghJqvbE6C208Oprj48/pF9UgA7u55kX89ztPprRr3fbspmZc5WVue8i/L0lVMei9aa5h4fe+udvNPgxOUbvWl/UWEm6+fnsXpOTsqkuBdn21hSnMW8/Iyk54AJkaqk8BJnrq/L6ONyNZ++bTJp8xYbFK+E4lXG+0IkQE9fkLouD3WdfWMWN4PVew+MKLrSlJ1l2ZtZmXMRizLXY1ZTn93p8gTY1+Bkb72TtjGa9ouybKyfn8u6ebnkpkDmFhjb+CwszGJxUWZKXCkpRKqTwktMXdAHTbuh45hRaPWLNW0+LcMoxIqWG7NdQsRZjzdIfVcfdZ19o25K7Qp2cMT1OmEdYnPBe4fctzJ7G0dcr2NWFhZnbmJVzkUsydpEmmnqe4C6/SH2N/bwTr2TU6OkyQNk2iysLXewbl4uc3PTU+KqRJOCObnpLC7OktwtISZJCi8xeZGIkSjftBfCgdO39zTA0Weg7ZCRNr/mZpi/eWTavC07uq3PUqOfS4g46i+2TnX14ewbWWz1Bjs46nqdI67XaPAeAsBuymJT/o2Y1env3cVZm7i27Assy9qM3Zw15fF4A2EONfewr6GHmnb3qHlbaWbFqrIc1s/PY3FRVsos2+VmpLGwMJOFhZkps7wpxEwjhZeYHGc9NOwA3+ltTmJOm0/PNUJP8xbKtj4irnr6gtR3j11sOQMt0WLrDZp8R0bc74u4qfXsZXHWxoHb0ky2IQ3zk+EPhTnS4mJfQw/HWl2ER6m2TAqWFmezdl4uK8uyUyICAowisKIwk0WFmRSkyJWSQsxkUniJ2HidRsHV0zD0tljS5jMLjUiIvIoEDlicbbo9gYFia7QUea01b3c9xuHeV2gZo0FeYWJBRhUrci6kLH3ZGY0nEIpwtNXF/gYnR1tdBMOjBUDAgoIM1pbnUjnXQWaKpLf3b+GzqDCLuXnpKTPjJsRskBo/5SJ1hQLGTFb7EeMKRYg9bT671JjhcswdeVwhpkG7y099dx8N3V7cEzTIK6Wo8ewcUXQZxdZaVuRcwPKs88mwOKY8nkAowrFWFweaejjS7CIQHhn/ADA3N52qcgeVcx0p0yQPp5cSKwoySbemxoybELONFF5idFpD+1Gj6Ar5jNtGpM1vhGVXjUybd8wzNq7OKk74sMXsFolo2lx+GqLF1vAtciI6TH3fQY653yLbUjCiQX551vmc6tuPCQsLM9ezIvsClmSfR4Y5Z8pj6p/ZOtDYw9GWsYutkhwblXMdVM3NpTA7dZbs0q0mFhRksrAgk7zM1CkChZitpPASI/U2GfEQ3m7j4xFp81Ww/JqhafNKGRldZWshIz854xazUigcobnHR313H01O34gQ0UDER61nD8fcb3HcvR1vuBeA3LQyzst/z5Ar7lbkXIjdnMWSrPOwmzOnPCZfMMzRFmNm69g4y4hFWTYqozNbJTlTv/pxullMivK8dCoKMymTqxKFSCgpvMRpvl6jj8t5yvg4lrR5ZYKCxUYPl33qSzRCDOYLhmno9tLo9NLa4yM0rBndE+qm2r2davdb1Hr2EtKBEcdwBpvpCJyiyLZg4LYsSz5rHJdMaUx9/hCHW3o50NjL8Xb3qA3yAIVZNirn5rBmroPSnNQpapSCUoedhQWZlOely/Y9QiSJFF7CmNFqfsfY6kdHYkubN1mgcJmRw2Wb+qX1QvTr8QZp6O6jsdtLpycwJBquXyDi5bf1/5dG71EYdbdCyLIUsCxrM8uyt5BvnXNGY3L2BTjU3Muhpl5qOz2jRj8AFGUby4hr5jooybalTLEFxl6JCwoyWVCQIREQQqQAKbzOZlpDR7URghr0Rvu6DsORZ6C3wVhK3Ph3RuZW/y8SsxWKVhgFV1rqLJ2ImScS0bS7/QMzW8Ob40ORAGEdwmY+fZWs1ZSOL+xheNFVaF3A0uzzWJa1hTL7EtTwsN4YaW30kB1u7uVQcy8N3d4xH1vmsLN6joM1c3IoTqFlRDCa5BcUZLCgIJOsFLlSUghhkJ/Is5Wr1YiB6Os0Pp4obd5ih5JVULQSLNKAK6bGFwzT3OOjyemlyekd0RvlCnZw3LODGvcOaj17uaDwA2wpuGXIY5ZmnUtXVyPz0lezNPs8lmZtJs9aNuUxRbSmrrNvoNjq8oxctuw3Ly+d1XMcrJ6Tk3KZVll2CwvyM1hQkJFSV0oKIYaSwuts43cbfVzdtcbHE6XNWzOjKfPLwCzfLmLynH0BGp3eUZcQIzpMo/cIJzw7qXHvpNV/Yshza9w7RhRe5+a/m/MKbj6jKxH9wTDVbW4ON/dytNU14urIfiYFi4qyWFWWw6qyHHLSU2trq0ybmXn5GVQUZJIvVyQKMSPIb9KzRTgELfug9YDRND8kbT4dVlwHFVtPb1Jtz4GSSihYIinzYlKC4Qgt0Vmt5h7fiKLGG3ZR7X6bE+6dnPTsxhfxjHmsvnAvYR0asnVPpiVvSuPq9gQ40uriSHMvJzo8YzbHWy0mlhVnsWpODstLclIuzyrTZqY8z5jZKkyxWTchxMSk8DobdNZA4y4jCmKitPmM/GjK/MLTfV1CTMDZF6DJ6aO5x0u7yz9mEzpAV6CRp5u/N+p9JizMz1jN4qxNLM46lwLr1MN3wxFNfVcfR1pcHGnppc3lH/Ox2XYLK0tzWFmWw+KizJS74q+/2Jqfn0FRCmWACSEmTwqv2czdbhRYnvZo2vzzUPvq6bT5JZcbM1tghJ2WVkHuvOSOWcwI/lCY1h4/TT1eWobNammt6Q42cdKzh5Oe3VxV+lmyLKez3crsS7GbsvFFXABkWwpYlLmRxVkbqchYN6SZfrLc/hDVrS6OtrqobnXjDY6+hAhGc/yK0hxWlmUzJzcdU4r9oZFltzAvL535+Rkp108mhJg6Kbxmo0AfNO40ZromSpvPmWvMcOVMvTlZzH5aG1cgtvT4aO7x0TWsV8sbdlHn2Udt3x5OeHbTE2wduG+550IqB2VnmZSZc/NvwqTMLM7cRJFtwZTjFyJa09Dt5Viri2OtLhq7vWOETBihoYuLslhems2K0uyUbEB3pKcxLz+deXkZkiIvxCwlhddsEgkbPVzN+4x4iLrX4fjfRk+bz51vpMxnFiZ3zCJl9fqCtEYLrdZe35ArEEORII3eQ5zs20utZy/NvmrGytU66dk9pPACuKDw/VMflzdIdZubY60ujreNP6uVY7ewvDSHFaXZLC7KwmpJrSVEgIIsK+V56czLzyDHnlrN+0KI6SeF12zRddLo4/I6x06bVybIX2jMcKVPrUFZzF6+YJjWXh8tPT5aen14/GMXNM+3/ZTdzqfHvN9qSmdBRhULMzewMHPDGY0rGI5Q2+Ghus1NdZuL1t6xe7VMCubnZ7C8JJtlpdkplRzfz6SgJMdOeV46c/PSybDKf8NCnE3kJ36m6+sy+rh6m8ZOmzeZoWAplK4BW3ayRyxSRDAcoc1lLB+29vpw9gUH7tM6Qpu/lrq+ffjDHrYWfWjIcxdkVA0pvBQmyuxLqchcx8LMDcxNXzHkSsTJiGhNS4+PmnY3x9vcnOzwjNgyaLAcu4WlJdksLc5iaXF2yl2FCJBmVszJTWdubjpzctNTcuZNCJEYUnjNVEEvNO6GjmNGBtdoafMWqzHjVbIarFNvWBazQygcocMdoKXXKLS6PYGBqw+1jtDuP8Up735OefZxyntgYLPpNGXj/MJbMavTy2DzM6vIt5ZTkbGWisx1LMiowm6e+tZR3Z6AUWi1u6lpc+MZI1cLwKwUCwoyWFaSzbKSbEpyUmuLnn7GlYjpzM3NoDjbhsmUemMUQiSeFF4zTSRiFFrN70D7kdHT5tPSoXglFK86ncslzjqhcIROT4DWXh9tvX463P4hhVab/ySn+g5wqm8/9d6DA4XWcEHtp9lbTXnGqoHbMsw5fHLRf015bG5/iJpokXWiwzNuWjxAUZaNJSVZLCvOYmFhavZqKWVskD0n1055bgaODOnXEkKMJIXXTOI8ZaTOtx2GI0+PTJu35RizW0XLwSz/6Z9tQuEI7W4/7S4/rb1+Ot1j52lpNP9z6ksEImPvRZhhzmVBRiXzM6rOaEseAI8/xMkODyc6PJxod4+bqQWQaTWzuDiLJUVZLCnOSskrEMEIW53jsDMnN52yXDs2S+otcwohUosUXjOBtxvqd0DT3kFp8xlG2vzCbcZsV8ma0/1c4qzgD4XpcBszWu0u/5ClQ1/YQ5P3CPXeQzR4D7Ih91pW5mwdeK5JmSlPX8UJz66B2zLMDuZnrGFeRiULMqootM6f8hKe2x+itsPDyehbS69v3MenmRULCzNZVGgUWqUOe8rlavXLz0yjzGH0ahVmWVNymVMIkbqk8EplIb9RZJ16C479ZWTafM4cIyYib6Fs63MW8PhDtLv8tLv9tPX66fEazfBaa3qCbTR4D9HoPUyD9xBt/loGxzsUWOcNKbwAlmadh9WUzvxooVVgnTflIqLHG6S20zNQbE00o2VWinn56SwuymJxURbl+elYUvR72GYxUeqwU+awU+ZIT8nmfSHEzCGFVyqKRKDjKJx81djAenjafMGiaMG1INkjFXGitaa7L0i7y+jNanf5R+x5WOfZx27nn2jwHsYd6hr3eA3egyNu25B3LRvyrp3S2DrdAaPQ6vRQ29k3YY+WSUF5XgaLCjNZVJTF/PyMlOzTAmOsBVk2yhx2Sh12CjJlVksIMX2k8Eo1PY1w8mU49MeRafMla4wMLsfU968TqckXDNPpCdARLbQ6PQFCYY3WGmewBWewhYWZ64c8xxN2csT1+qjHU5goti2kPGMV89JXU56+cspjC0UiNHV7qevqo7azj1OdnnGvOgRjRmtuXjoLCzNZWJjJgoKMlO5/yrJbjEIrx05Jjj1li0IhxMwnhVeq8PUYSfPv/G5k2nz5JiirMvZTFDNeJKJxeoN0uv10uAN0uP24fCHA2Hqn2XuMJt8xmrxHafYdoy/cg0XZ+Kdlf8CkThcvc9NXDLxvNaUzx76C8oxVzE1fwVz7iinveejyBTnV1cepzj5OdfXR6PSOm6MFxnY88/IzqCgwCq1UntGC08uHJTnGrFaWTf4rFEIkhvxvk2yhgJHHtfthI/zU54TCZbDyOlh4kVF8ZeRPdBSRwtz+EF3uAB0eP53uAN2eAKGIRusI9d5DtPiqafIeo9l3DGewZdRjhLSfdn8tJfbFA7flWIq4pvQfKLMvpdA2f0hRFqtQOEJzj49TXX3Ud/dR39VH96Ag1bGkp5mZn59BRWEmFQUZzM1L3R4tAItZUZxtMwqtHLvsgyiESBopvJJFa2g/Cjt/BgefAE+bsa3P+g8bs1yllWDPSfYoxST1Lxl2e4yZrC5PAF8wgj/ch1ImrCb7oEcrHm34Ov6IZ9xj2kyZlNmXEdZDl/eUUqzNvSLmsUW0pssdoL67j4ZuLw3dfTT1+AhPMJsFkJ9pZUF+BgsKjGXDomxbyl51CMYMXGG2leJsY0YrP8MqAaZCiJQghVcy9DbDjp8ay4r9afOb7oQ174WySrBmJnuEIga+YJjuvgBdnoAxk9UXwOMP0xfqodV/glbfCVp8x2n119AVaOLasn+kynHZwPOVUpTal1DX987AbWZlodi2iDL7UuakL2eOfTn51jkoNbnZJK01Pd4gDd1eGp3GW0N3H75gZMLnWkyK8rx05udnMD8/k3n56WSn+ObNgwut4hwbhZmSFC+ESE1SeCWS3w27fwU7HoKuGiN/a8PtxixXaRWk2Sc+hkiKvkCI7r4g3R6j0OovsnqD7TR5j9HqP0Gb7wSt/hO4Qh2jHqPVVwODCi8wIh1y0oqYY19GmX0ZRbYKLKbJFTn9RVaj00tTtMhq7PZO2ADfryDTyrz8DOblpTMvP4NShz2llw3ByP0qyrYNFFoyoyWEmCmk8EqEcAgOPgavfx9aDxoJ82s/AJs+YSwpWqTfJFVoren1hujuC+D0ni60nP4evGEX+dY5Qx6/s/sp3u56bMLjKkz4wyOXFDfl3zip8UW0psPtp7nHR7PTS1OPjyand0TUxFgyrGbm5WVQnpdOefTfzBnQWJ5hNVOUbTPesmzkZqRJxIMQYkZK/f9xZ7rjL8BL/2ps9ZOWAavfA1s+C2VrwSyf/mTyBcP0eIM4+4I4+wJGbpanlzZvPe2BOjr8dbT762j31+IKdTI3fQW3LfjOkGMU2xaOOK5FWSmyVVBiW0ipfSkl9kUU2SpIM01u30x/MExLr4/mHp/xr9NLS6+PYHjiniwwrtybm5vO3Lx05uYahVbeDChYlILc9DQKo0VWYbZNrjoUQswa8r9ZvDTvg7/da2RymSzG9j5b/wnK1knKfIKFwhF6vEGjyOr/ty+ANxChN9jOru6n6PDX0xGowxlsHfM47f46tNZDCpcy+xIqMtZTbK+gxLaYEvsiCqzlk7rCMBzRdLr9tPT6aO310dLrp7XXN2Eo6WD2NBNzotvYzMm1Mzc3g4Isa0o3wPezWkwUZFmNIivLRkGWlTSz/IwIIWYnKbymm/MUPPtVOPIn48rFxZfCRV8yQlBnwC/BmSwUjtDrCw0UWT19Aep7mznlrqXT30inv55LSz6OWZ3uoQrpIG/FsFRoVmnkpc3BF3GTbs4euL3ANo8PzP9GTOOLaE23J0Brr582l1Fktbn8tLn8MV1Z2C87GvY5x5FOWW46cxxGPMJMKLJMCnIz0ijIslGQaaUw20ZOijfuCyHEdJLCa7r0dcPz/wzv/MbYY7FiK1z8f2DB5mSPbNbxh8L0eo0Cq9cXpKm3g9reUzR56un0N9IdaKQr0EhXoImgHro584a8aym0zR/4ODetBLNKI6yN7CqFiXzrHAptCyi0zqfItoBiWwV51jkxz2IFwxE6PQHaXUaB1e7yD7xNFEQ6mElBUbaNMkc6pdGgzzKHPeWvMBws226hINNKQZaN/Ewr+ZlWzNIEL4Q4i0nhdaaCPnjlW/D2f0HADfPOg0vvhYoLkz2yGS0c0bh9IXp9QXq8Aep7W6jrrcemi7GpvCGP/Y/qv8MT7o7puJ2BhiGFl0mZuaT478gw51BonU++tTymqwq11vT6QnS4jS1+jK1+ArS7/XR7AsReXhkc6WmU5NgGtqwpybFTnG3DMoOW3LLsFvIzjOKqIMtKXoY1pdPrhRAiGaTwmqpwCLb/BF59APo6oXQtXHYfLLkk2SObMcIRjdsfotcboMXdzameBupdjTS6G2n3NeMMtOAMttITbCWkjX6nq0s/x7rcq4YcJ886B4939MLLbsqiwFZOgXUeBdZyCm0jNxbfmHf9qM+NaI3LF6LT46fLHaDTE6Azuo9ih9sfc5P7YNk2C8U5Nopz7JRk2ynJMSIR0q2pu4/hcEoZM1n5GVbyorNYUmQJIURspPCarEgE9v8env869DZC4XK44Qew4tpkjywl+YJhnF4f9T3N1PU2ocM28tMqcPtD9AXCaA1PNX2HA70vxHS87kDziNuKbQsJRPrIs86hwDqXPOtc8qNvGebx0//9wbCRzxUNQu3P6Or/dyrFlcLoYxrImYrGIMy0AguMYFJHRhp5GVbyMtLIy7SSm542o2bihBAilUjhFSutofpv8Oz/gY5jkFsB7/05rHnPWd007w2EaXS1c6SjmiZ3K619xlunr51ufzu9wQ7coW40RmL66pyLuWHOF4ccI9OSN9qhh0g355CbVjrqY68s/fSoz9Fa4/GHcEavYnT2GU333X1GUdXtCeINxpZ/NeqY0swUZlmjV+KdzpiaiVflKQVZNguOdKPIys1IIzcjbUb1kwkhxEwghVcsTr0Ff/2ysZl1dpkxw7XuQ2CaWbMXsQpGgnT7uml2tdPiaafZ3U57Xwdtfe2YSefystvwBMJ4AyHCEdjV/Seebf1RTMd2BUemujvSiklTdhxpxdG3UnLTSsi1lkTfL8VuHrqNUkT3L1MGcUWvZOztv5ox+tbrC05pxmqwDKuZgsz+viWjQbwwy0ZhppWMGZotlWkz40hPG3jLzbCSY7fILJYQQiTAzPzNkSgt+40srpoXID0frv4mnPOxGZU0H9ERXAEXvf5enH4nroCLjSWb8QbD+INh+gJhjnQd5aeHvkNPoBtXwElf2DXm8XLTSqjKfN+Q27ItBROOI9OcR05a0ag9Vutzr2ZD7rUopfCHwrh9Idz+EG5XiDp/iAM+Ny5fNy5fKPoWxO0PMYkLBMdkMSly+5fSMq0DzeH978+0pcF+JmU0u+fY08iJFlg5dmNGSwosIYRInqQUXkqpq4DvA2bgIa31/ckYx5g6a+CF/wcHHwdbNlxyL2z+VMI3r9Za4w156Qv14Ql6Bt4qCyuxW07v6+jyu/j2zu/Q4+ulN1pk9QZ7cQd68YTcA8t8/b60/Mkh0Qitvh6O9eyLaUzuUPeIENHctFLK01eRZckny5JPjqWQrLQCMk0FpOk8VMSBP2jCGwjh8YV5+Vg7ff4QnkAIjz+MJ2AUWh5/6IxnqIazWUzGslm61ehVSk/DkWElPyON3EwrWTbLjMi/Gku61US2LY1su4WcdOPfbHsa2TaL7F0ohBApKOGFl1LKDPwncDnQAOxQSj2ptT6U6LGAkbnU5PQSxI3F24htx49RB/8XzGnozZ8msvF2ItYswt52It5WIpEIRRlFOGyOIcfZ27aX3kAvwXCQYMR4C4QD+MN+gpEg/rB/4OP3Ln0vFY6KIc+/89k76fJ10Rfy4g32Gf+G+tCjBBN8dd3D5FrmEghH8AcjuIMeHj8+cQhoP1/YTYbl9PgzzI5hj1Ckm3PIMOViNzmwqRzSVA5p5GKO5LCjtpNASOMLRfAGw/gCFgj+Pe3BCPXBMN5gGG8gTCAcAbqjb9Mvw2qOzugMndkZ/GZPm5kzVoNlWM1k2Sxk2S1GYRUttLLslhnXSyaEEGe7ZMx4nQsc11qfAFBK/Ra4EUhK4VXf1cd7v/MU8+b9gJNZ0SW2eWXGv61Pw9NPj3jOcvNHWWi7FKvFhNViwmJS/NV5L92hkzGds6VlDnPtASIadEQTikTY3X4Iv+6J6fkvV9fjMFmJaI3WEIpEUJjRjN4obtLpmMnApDMxRTL5w+5aCDkIhiMEwxEC4RBmy6cJBzIIBDMIBuy49HgFy8grC6eLxaQGiowsm2VQwWHM4uRE359NRYfVYiLLZibTZiEz+pr7/82yWSRwVAghZpFkFF5zgfpBHzcA5w1/kFLqTuBOgPnz5w+/e9r0BcJ8wPwC9aqVk2TE9Jx3GrvY2d0w5LaMihDm9NjO+fg7dYRcQ2MOMhebMY3SOqYjaeiIFSI2dPTtzyfaifiHXm1mcbwbdBo6bEdH7OhwBoTT0eF0jBXd09oAGN7HNbL36kyZFNjTzKSnGUVFhtVMhtVCptVMhvV0oZE56H2bxZTymzhPhtkE6VYLGWlmMmynX7/xui1k2MyzpoAUQggxsWQUXqP9Vh2xnqa1/gnwE4CNGzdOb+PPMM/l3ESfdqFD9eghZ1KgFWAaeF9jQoftI44R7puPDmegtRm0BbQJrS0QsYC2GO9rC0TSiPhLRjzf2/gh0KC1FSLWaLFlZXjRNJZQz8apvPQxWUxqYEbPajZhs5iwp5mxWUzY0szY+/9NM///7d17jFxlHcbx73NmZnsBBZSL2hKLUqyIoVwCCBEIEIJBLRhJQDGg/GMiCAgiGqOQmKgJKRqjeMECiQi1FSOYhoJIAmpFrlJqNRKoUEAoGuQS6O7M/PzjfXfntJRuFzvnzDLPJ23OZeec97f7Zneffd9z5jCrVTCj2WDWSNqe3Urrb7QQVSalB1PPmvgapM959kiDWSNNZrXS+hthqtPMzLafOoLXemDP0vZc4Mka6gBgvzk7ccuXTgTSG6B2u8HLY+mC75dHO7wy1p24ZumVdodX8rVLG8e6bGx32Nge//h56Xqnsd4xY+0uo3k6b6zTZawdtLtBdyRNEQZ5GRDsA5QSaKRf7kLkfxPbhUDqLRuFaEgUhWgU0BjfVxQ0G6JZpO3W+HajoNUQzaJgpCFaOVy18v+RZjGU01vNhlKwbDaY2eotZ+ZwNbNVMLPZW3+jhkozM+ufOoLX3cB8SXsBTwCnAp+ooY4tKgpNTHv1S7vTZWO7y2i7txztdHrrpcA22o5ecOt06XQnP/8waxai1VQvROYgOdLcdHtGs7ec0WwMbdg0M7NqVR68IqIt6WxgJWkebUlErKm6jjo1GwXNRsEOM6Z+bLcbjHW7tDvjgSzodCOHsnShfrsbtPP+TqRluxN083pvmZ6XGJHWuxETF+wHQbebRuAi8r2VpWnYze+2VHkGOY/Qjb9NQ1GkdUkT+wulkFsojdKlfWl0rig2XTaKNDrXKG23Gmn0rtVINze08iieR6HMzGyQ1fI+XhGxAlhRR9vTXVGIGUWDafqm6WZmZkPNt1OZmZmZVcTBy8zMzKwiDl5mZmZmFXHwMjMzM6uIg5eZmZlZRRy8zMzMzCri4GVmZmZWEQcvMzMzs4o4eJmZmZlVxMHLzMzMrCIOXmZmZmYVcfAyMzMzq4iDl5mZmVlFHLzMzMzMKqKIqLuGSUnaAPyzz83sCjzb5zbs9XHfDCb3y+By3wwm98vg2t59886I2G1LH5gWwasKku6JiIPrrsNezX0zmNwvg8t9M5jcL4Oryr7xVKOZmZlZRRy8zMzMzCri4NXz47oLsNfkvhlM7pfB5b4ZTO6XwVVZ3/gaLzMzM7OKeMTLzMzMrCIOXoCkEyT9XdLDki6uux4DSXtKul3SWklrJJ1bd022KUkNSfdL+k3dtVgiaWdJyyX9LX/vfKDumiyRdH7+WfaQpOskzay7pmEkaYmkZyQ9VNr3Fkm3SvpHXu7SzxqGPnhJagDfBz4E7AucJmnfeqsyoA1cEBHvBQ4DPud+GTjnAmvrLsI28V3g5ohYAOyP+2cgSJoDfB44OCL2AxrAqfVWNbSuBk7YbN/FwG0RMR+4LW/3zdAHL+AQ4OGIeCQiRoHrgUU11zT0IuKpiLgvr79A+gUyp96qbJykucCJwJV112KJpDcDRwI/BYiI0Yh4rtairKwJzJLUBGYDT9Zcz1CKiDuA/2y2exFwTV6/BjipnzU4eKVf5o+XttfjX/ADRdI84ADgrppLsZ7vABcB3ZrrsJ53ARuAq/IU8JWSdqi7KIOIeAK4DHgMeAr4b0TcUm9VVrJHRDwF6Y9+YPd+NubgBdrCPt/qOSAk7Qj8EjgvIp6vux4DSR8GnomIe+uuxTbRBA4EroiIA4CX6POUiW2bfM3QImAv4B3ADpJOr7cqq4uDVxrh2rO0PRcPAQ8ESS1S6Lo2Im6oux6bcATwUUnrSFPzx0j6Wb0lGeln2fqIGB8ZXk4KYla/44BHI2JDRIwBNwCH11yT9Twt6e0AeflMPxtz8IK7gfmS9pI0Qrrg8caaaxp6kkS6VmVtRCyuux7riYgvR8TciJhH+n75XUT4r/eaRcS/gMclvSfvOhb4a40lWc9jwGGSZuefbcfiGx8GyY3AGXn9DODX/Wys2c+TTwcR0ZZ0NrCSdKfJkohYU3NZlkZVPgWslvRA3veViFhRX0lmA+8c4Nr8R+QjwKdrrseAiLhL0nLgPtId2/fjd7GvhaTrgKOBXSWtB74OfAv4haSzSCH5lL7W4HeuNzMzM6uGpxrNzMzMKuLgZWZmZlYRBy8zMzOzijh4mZmZmVXEwcvMzMysIkP/dhJm1h+S3kp64CzA24AO6ZE2AIfkZ6NO9ZxHA6MR8cftUWMV7Ug6k/Rw5LP/33OZ2fTn4GVmfRER/wYWAki6BHgxIi4b/7ikZkS0p3jao4EXgb4GrwrbMbMh46lGM6uMpKslLZZ0O/BtSe+WdLOkeyXdKWlBft1HJN2VH/b8W0l75IelfxY4X9IDkj6Yz3eFpNslPSLpKElLJK2VdHWp3eMlrZJ0n6Rl+RmgSFon6dK8f7WkBVtqp3SeIh+zc2nfw7m+V9X8Gp//x0vbL5bWvyjpbkkPSrp0u33RzWygOHiZWdX2AY6LiAtI7959TkQcBFwI/CC/5vfAYflhz9cDF0XEOuCHwOURsTAi7syv3QU4BjgfuAm4HHgf8H5JCyXtCnw1t3kgcA/whVI9z+b9VwAXbqUdIqJLepzIyQCSDgXWRcTTW6p5W78gko4H5gOHkEYJD5J05LYeb2bTh6cazaxqyyKik0edDgeWpcfXATAjL+cCS/MDa0eAR7dyvpsiIiStBp6OiNUAktYA8/K59gX+kNsZAVaVjh9/APu9wMe2of6lwNeAq0jPqlz6Omre3PH5//15e0dSELtjCucws2nAwcvMqvZSXhbAcxGxcAuv+R6wOCJuzBe6X7KV823My25pfXy7Sbqo/9aIOG2S4zts28/EVcDeknYDTgK+MYWa2+SZhvyw5JG8X8A3I+JH29C+mU1jnmo0s1pExPPAo5JOgRREJO2fP7wT8EReP6N02AvAm6bY1J+AIyTtnduZLWmfSY55zXYiPeD2V8BiYG2+iWBrNZetAw7K64uAVl5fCXymdO3ZHEm7T1KjmU1DDl5mVqdPAmdJ+guwhhRGII0WLZN0J/Bs6fU3ASdvftH71kTEBuBM4DpJD5KC2IJJDpusnaXA6fSmGbdWc9lPgKMk/Rk4lDz6FxG3AD8HVuUp0+VMPWCa2TSg9MebmZmZmfWbR7zMzMzMKuLgZWZmZlYRBy8zMzOzijh4mZmZmVXEwcvMzMysIg5eZmZmZhVx8DIzMzOriIOXmZmZWUX+B2UYOt3+dCL4AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "\n", "# Plot point estimates\n", "plt.plot(\n", "    test_T,\n", "    est.effect(T0 = 0, T1=test_T),\n", "    label = 'Estimate with Featurized Treatment',\n", "    linewidth=3\n", ")\n", "\n", "plt.plot(\n", "    test_T,\n", "    bad_est.effect(T0 = 0, T1=test_T),\n", "    label = 'Estimate without Featurized Treatment'\n", ")\n", "\n", "plt.plot(\n", "    test_T,\n", "    0.5*test_T**2,\n", "    linestyle='--',\n", "    linewidth=3,\n", "    label='Ground Truth',\n", ")\n", "\n", "# Plot confidence intervals\n", "lb, ub = est.effect_interval(T0 = np.zeros(shape=(100, 1)), T1=test_T)\n", "\n", "plt.fill_between(\n", "    test_T.squeeze(),\n", "    lb.squeeze(),\n", "    ub.squeeze(),\n", "    alpha = 0.4, \n", ")\n", "\n", "lb, ub = bad_est.effect_interval(T0 = np.zeros(shape=(100, 1)), T1=test_T)\n", "\n", "plt.fill_between(\n", "    test_T.squeeze(),\n", "    lb.squeeze(),\n", "    ub.squeeze(),\n", "    alpha = 0.4\n", ")\n", "\n", "plt.legend()\n", "plt.xlabel('Treatment value')\n", "plt.ylabel('Treatment effect')\n", "plt.title('Treatment Effect vs Treatment value')"]}, {"cell_type": "markdown", "id": "a64e5cba-ea4b-494f-b34f-87ee9596cb96", "metadata": {}, "source": ["## Applied example"]}, {"cell_type": "markdown", "id": "f0cb71c5-b3f6-453c-a059-b8e76f71d91d", "metadata": {}, "source": ["Suppose you are a healthcare researcher interested in optimizing the dosage of a particular medicine, using observational data collected that measured dosage and wellbeing for patients of various attributes. \n", "\n", "You believe that your treatment affects patient outcome in a quadratic manner. You also believe that different customers will respond differently to your treatment. \n", "\n", "You can frame your data as follows: Let X be a patient characteristic that affects how they respond to changes in treatment. Let T be the dosage of your treatment. Let Y be the patient's wellbeing. Let W be other confounding variables that affect the treatment and outcome simultaneously."]}, {"cell_type": "markdown", "id": "c9f04927-a58c-49d0-b52b-6a5373f21730", "metadata": {}, "source": ["### Data simulation "]}, {"cell_type": "markdown", "id": "392defb7-6161-4589-ac72-7e808a59690f", "metadata": {}, "source": ["We define a synthetic dataset as follows:\n", "\n", "Note that while the outcome still varies linearly with featurized treatment, we use a non linear operation on X to define effect heterogeneity. In other words, if X>0.5, then the outcome depends on 2\\*T - T^2, while if X<0.5, the outcome depends on 4\\*(2*T - T^2)"]}, {"cell_type": "code", "execution_count": 8, "id": "613bb31c-f8f8-4cf8-9b0e-1048579d1ac0", "metadata": {}, "outputs": [], "source": ["n = 10000\n", "d_w = 5\n", "d_x = 1\n", "W = np.random.normal(size = (n, d_w), scale = 5)\n", "X = np.random.normal(size = (n, d_x), scale = 5)\n", "T = np.random.uniform(low = 0, high = 10, size = (n, 1)) + 0.5*W[:, [0]] + 0.5*X[:, [0]]\n", "epsilon = np.random.normal(size = (n, 1))*20\n", "\n", "Y = np.where(X[:, [0]]>0.5, 1, 4) * (2*T - T**2) + X[:, [0]]+ W[:, [1]] + epsilon\n", "\n", "\n", "featurizer = PolynomialFeatures(degree=2, interaction_only=False, include_bias=False)"]}, {"cell_type": "markdown", "id": "43548ee0-a0a3-44db-b94f-608a363c7290", "metadata": {}, "source": ["Since we believe effect heterogeneity is nonlinear, we use CausalForestDML."]}, {"cell_type": "code", "execution_count": 9, "id": "f1ccec7f-58d1-464e-96cb-86246753f494", "metadata": {}, "outputs": [], "source": ["from econml.dml import CausalForestDML"]}, {"cell_type": "code", "execution_count": 10, "id": "61bf3283-0e9f-44f4-b0ed-40cf0bbdc5fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.causal_forest.CausalForestDML at 0x1ad429413a0>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# est = LinearDML(model_y=RandomForestRegressor(min_samples_leaf=20, random_state=123),\n", "est = CausalForestDML(model_y=RandomForestRegressor(min_samples_leaf=20, random_state=123),\n", "                model_t=RandomForestRegressor(min_samples_leaf=20, random_state=123),\n", "                treatment_featurizer = featurizer,\n", "                cv=5,\n", "                random_state=123)\n", "est.fit(Y=Y.flatten(), T=T, X=X, W=W)"]}, {"cell_type": "markdown", "id": "7c94fa1e-14bb-43d3-978a-9a08daee8140", "metadata": {}, "source": ["Observe treatment effects for different x values as treatment varies."]}, {"cell_type": "code", "execution_count": 11, "id": "55af9377-5403-4b7f-bafe-0faebfcbe63e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["test_T = np.arange(-5, 5, step = 0.1).reshape(-1, 1)\n", "\n", "x_vals= [-1, 0, 1, 2]\n", "\n", "for x_val in x_vals:\n", "    plt.figure(figsize=(10, 6))\n", "    \n", "    xtest = np.ones(test_T.shape) * x_val\n", "    # Plot point estimates\n", "    plt.plot(\n", "        test_T,\n", "        est.effect(xtest, T0 = 0, T1=test_T),\n", "        label = 'Estimate with Featurized Treatment',\n", "        linewidth=3\n", "    )\n", "\n", "    plt.plot(\n", "        test_T,\n", "        est.marginal_effect(test_T, xtest),\n", "        label = 'Marginal Effect',\n", "        linewidth=3\n", "    )\n", "\n", "    plt.plot(\n", "        test_T,\n", "        np.where(xtest>0.5, 1, 4) * (2*test_T - test_T**2),\n", "        linestyle='--',\n", "        linewidth=3,\n", "        label='Ground Truth',\n", "    )\n", "\n", "    # Plot confidence intervals\n", "    lb, ub = est.effect_interval(xtest, T0 = 0, T1=test_T)\n", "\n", "    plt.fill_between(\n", "        test_T.squeeze(),\n", "        lb.squeeze(),\n", "        ub.squeeze(),\n", "        alpha = 0.4, \n", "    )\n", "\n", "\n", "    plt.legend()\n", "    plt.xlabel('Treatment value')\n", "    plt.ylabel('Treatment effect')\n", "    plt.title(f'Treatment Effect vs Treatment value at X={x_val}')"]}, {"cell_type": "markdown", "id": "6677b86a-dedb-4353-acac-6967b04f6753", "metadata": {}, "source": ["We gather the marginal effect for existing samples and prescribe interventions (i.e. changes in treatment dosage.)"]}, {"cell_type": "code", "execution_count": 12, "id": "40bdacc2-ad96-43d5-af2d-edd0dc4eb1a0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>X</th>\n", "      <th>marginal effect</th>\n", "      <th>lb</th>\n", "      <th>ub</th>\n", "      <th>true</th>\n", "      <th>presciption</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1.964775</td>\n", "      <td>-30.157065</td>\n", "      <td>-32.856818</td>\n", "      <td>-27.457311</td>\n", "      <td>-31.657195</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-4.976109</td>\n", "      <td>38.821192</td>\n", "      <td>34.239351</td>\n", "      <td>43.403033</td>\n", "      <td>35.247105</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1.222882</td>\n", "      <td>-15.234570</td>\n", "      <td>-18.565181</td>\n", "      <td>-11.903959</td>\n", "      <td>-20.269921</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3.991654</td>\n", "      <td>-16.428203</td>\n", "      <td>-19.629034</td>\n", "      <td>-13.227373</td>\n", "      <td>-15.480751</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.296386</td>\n", "      <td>9.529916</td>\n", "      <td>6.753388</td>\n", "      <td>12.306444</td>\n", "      <td>5.865886</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3.957914</td>\n", "      <td>2.373906</td>\n", "      <td>-3.624517</td>\n", "      <td>8.372328</td>\n", "      <td>-1.632855</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>-1.128893</td>\n", "      <td>-4.281499</td>\n", "      <td>-7.800553</td>\n", "      <td>-0.762444</td>\n", "      <td>-6.282670</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0.878929</td>\n", "      <td>-7.576779</td>\n", "      <td>-10.937542</td>\n", "      <td>-4.216015</td>\n", "      <td>-8.584140</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>4.884313</td>\n", "      <td>-26.683832</td>\n", "      <td>-29.480043</td>\n", "      <td>-23.887622</td>\n", "      <td>-26.062601</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5.683501</td>\n", "      <td>-28.384068</td>\n", "      <td>-35.917722</td>\n", "      <td>-20.850415</td>\n", "      <td>-27.269113</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>-2.777241</td>\n", "      <td>-6.122264</td>\n", "      <td>-8.284679</td>\n", "      <td>-3.959850</td>\n", "      <td>-8.034091</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>1.627892</td>\n", "      <td>-9.720055</td>\n", "      <td>-12.813256</td>\n", "      <td>-6.626854</td>\n", "      <td>-11.640772</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>0.911899</td>\n", "      <td>-21.524285</td>\n", "      <td>-29.571710</td>\n", "      <td>-13.476861</td>\n", "      <td>-22.671561</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>8.081129</td>\n", "      <td>-18.425315</td>\n", "      <td>-21.145556</td>\n", "      <td>-15.705074</td>\n", "      <td>-17.765141</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2.269164</td>\n", "      <td>-14.051747</td>\n", "      <td>-16.566518</td>\n", "      <td>-11.536975</td>\n", "      <td>-15.527673</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>-7.350547</td>\n", "      <td>-16.000470</td>\n", "      <td>-20.505561</td>\n", "      <td>-11.495379</td>\n", "      <td>-21.748643</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>-2.434966</td>\n", "      <td>5.838954</td>\n", "      <td>3.360944</td>\n", "      <td>8.316963</td>\n", "      <td>4.131746</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>-4.899031</td>\n", "      <td>-27.065199</td>\n", "      <td>-28.375405</td>\n", "      <td>-25.754992</td>\n", "      <td>-31.926394</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>-8.004931</td>\n", "      <td>-21.797776</td>\n", "      <td>-25.303549</td>\n", "      <td>-18.292004</td>\n", "      <td>-24.396402</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>0.541819</td>\n", "      <td>-9.815485</td>\n", "      <td>-11.743268</td>\n", "      <td>-7.887703</td>\n", "      <td>-9.831051</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>1.744513</td>\n", "      <td>-17.864399</td>\n", "      <td>-23.221449</td>\n", "      <td>-12.507348</td>\n", "      <td>-20.683919</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>-7.234710</td>\n", "      <td>-25.571708</td>\n", "      <td>-29.451154</td>\n", "      <td>-21.692262</td>\n", "      <td>-32.635177</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>11.396015</td>\n", "      <td>-27.233582</td>\n", "      <td>-31.179588</td>\n", "      <td>-23.287575</td>\n", "      <td>-26.410036</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>-6.389070</td>\n", "      <td>1.632913</td>\n", "      <td>-1.395815</td>\n", "      <td>4.661641</td>\n", "      <td>-2.780295</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>-4.058979</td>\n", "      <td>28.186649</td>\n", "      <td>21.637661</td>\n", "      <td>34.735638</td>\n", "      <td>27.327685</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>1.891756</td>\n", "      <td>-24.242139</td>\n", "      <td>-28.101735</td>\n", "      <td>-20.382543</td>\n", "      <td>-21.184927</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>-1.209522</td>\n", "      <td>65.134330</td>\n", "      <td>60.299782</td>\n", "      <td>69.968878</td>\n", "      <td>62.266306</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>-0.377739</td>\n", "      <td>-45.713667</td>\n", "      <td>-48.575870</td>\n", "      <td>-42.851464</td>\n", "      <td>-52.309354</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>-2.444687</td>\n", "      <td>-41.562128</td>\n", "      <td>-44.204142</td>\n", "      <td>-38.920114</td>\n", "      <td>-43.265879</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>-5.138705</td>\n", "      <td>24.112798</td>\n", "      <td>19.301931</td>\n", "      <td>28.923665</td>\n", "      <td>20.922958</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>3.156487</td>\n", "      <td>-12.615076</td>\n", "      <td>-14.749285</td>\n", "      <td>-10.480868</td>\n", "      <td>-12.605709</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>-5.297919</td>\n", "      <td>-41.742057</td>\n", "      <td>-48.958655</td>\n", "      <td>-34.525460</td>\n", "      <td>-44.828540</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>9.490741</td>\n", "      <td>-22.905279</td>\n", "      <td>-27.982578</td>\n", "      <td>-17.827981</td>\n", "      <td>-24.506200</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>-0.468496</td>\n", "      <td>-13.087916</td>\n", "      <td>-15.059780</td>\n", "      <td>-11.116053</td>\n", "      <td>-16.690441</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>-2.552019</td>\n", "      <td>-42.842586</td>\n", "      <td>-46.251459</td>\n", "      <td>-39.433712</td>\n", "      <td>-48.007278</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>-1.978348</td>\n", "      <td>25.197274</td>\n", "      <td>17.974794</td>\n", "      <td>32.419755</td>\n", "      <td>22.420934</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>1.272495</td>\n", "      <td>7.836774</td>\n", "      <td>2.145505</td>\n", "      <td>13.528043</td>\n", "      <td>3.882322</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>4.693579</td>\n", "      <td>10.312457</td>\n", "      <td>4.319765</td>\n", "      <td>16.305148</td>\n", "      <td>9.861466</td>\n", "      <td>Increase treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>-1.505240</td>\n", "      <td>-9.599493</td>\n", "      <td>-12.319979</td>\n", "      <td>-6.879008</td>\n", "      <td>-14.602760</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>-1.965708</td>\n", "      <td>-87.758610</td>\n", "      <td>-93.213076</td>\n", "      <td>-82.304145</td>\n", "      <td>-86.759201</td>\n", "      <td>Decrease treatment</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            X  marginal effect         lb         ub       true  \\\n", "0   -1.964775       -30.157065 -32.856818 -27.457311 -31.657195   \n", "1   -4.976109        38.821192  34.239351  43.403033  35.247105   \n", "2   -1.222882       -15.234570 -18.565181 -11.903959 -20.269921   \n", "3    3.991654       -16.428203 -19.629034 -13.227373 -15.480751   \n", "4    2.296386         9.529916   6.753388  12.306444   5.865886   \n", "5    3.957914         2.373906  -3.624517   8.372328  -1.632855   \n", "6   -1.128893        -4.281499  -7.800553  -0.762444  -6.282670   \n", "7    0.878929        -7.576779 -10.937542  -4.216015  -8.584140   \n", "8    4.884313       -26.683832 -29.480043 -23.887622 -26.062601   \n", "9    5.683501       -28.384068 -35.917722 -20.850415 -27.269113   \n", "10  -2.777241        -6.122264  -8.284679  -3.959850  -8.034091   \n", "11   1.627892        -9.720055 -12.813256  -6.626854 -11.640772   \n", "12   0.911899       -21.524285 -29.571710 -13.476861 -22.671561   \n", "13   8.081129       -18.425315 -21.145556 -15.705074 -17.765141   \n", "14   2.269164       -14.051747 -16.566518 -11.536975 -15.527673   \n", "15  -7.350547       -16.000470 -20.505561 -11.495379 -21.748643   \n", "16  -2.434966         5.838954   3.360944   8.316963   4.131746   \n", "17  -4.899031       -27.065199 -28.375405 -25.754992 -31.926394   \n", "18  -8.004931       -21.797776 -25.303549 -18.292004 -24.396402   \n", "19   0.541819        -9.815485 -11.743268  -7.887703  -9.831051   \n", "20   1.744513       -17.864399 -23.221449 -12.507348 -20.683919   \n", "21  -7.234710       -25.571708 -29.451154 -21.692262 -32.635177   \n", "22  11.396015       -27.233582 -31.179588 -23.287575 -26.410036   \n", "23  -6.389070         1.632913  -1.395815   4.661641  -2.780295   \n", "24  -4.058979        28.186649  21.637661  34.735638  27.327685   \n", "25   1.891756       -24.242139 -28.101735 -20.382543 -21.184927   \n", "26  -1.209522        65.134330  60.299782  69.968878  62.266306   \n", "27  -0.377739       -45.713667 -48.575870 -42.851464 -52.309354   \n", "28  -2.444687       -41.562128 -44.204142 -38.920114 -43.265879   \n", "29  -5.138705        24.112798  19.301931  28.923665  20.922958   \n", "30   3.156487       -12.615076 -14.749285 -10.480868 -12.605709   \n", "31  -5.297919       -41.742057 -48.958655 -34.525460 -44.828540   \n", "32   9.490741       -22.905279 -27.982578 -17.827981 -24.506200   \n", "33  -0.468496       -13.087916 -15.059780 -11.116053 -16.690441   \n", "34  -2.552019       -42.842586 -46.251459 -39.433712 -48.007278   \n", "35  -1.978348        25.197274  17.974794  32.419755  22.420934   \n", "36   1.272495         7.836774   2.145505  13.528043   3.882322   \n", "37   4.693579        10.312457   4.319765  16.305148   9.861466   \n", "38  -1.505240        -9.599493 -12.319979  -6.879008 -14.602760   \n", "39  -1.965708       -87.758610 -93.213076 -82.304145 -86.759201   \n", "\n", "           presciption  \n", "0   Decrease treatment  \n", "1   Increase treatment  \n", "2   Decrease treatment  \n", "3   Decrease treatment  \n", "4   Increase treatment  \n", "5   Increase treatment  \n", "6   Decrease treatment  \n", "7   Decrease treatment  \n", "8   Decrease treatment  \n", "9   Decrease treatment  \n", "10  Decrease treatment  \n", "11  Decrease treatment  \n", "12  Decrease treatment  \n", "13  Decrease treatment  \n", "14  Decrease treatment  \n", "15  Decrease treatment  \n", "16  Increase treatment  \n", "17  Decrease treatment  \n", "18  Decrease treatment  \n", "19  Decrease treatment  \n", "20  Decrease treatment  \n", "21  Decrease treatment  \n", "22  Decrease treatment  \n", "23  Increase treatment  \n", "24  Increase treatment  \n", "25  Decrease treatment  \n", "26  Increase treatment  \n", "27  Decrease treatment  \n", "28  Decrease treatment  \n", "29  Increase treatment  \n", "30  Decrease treatment  \n", "31  Decrease treatment  \n", "32  Decrease treatment  \n", "33  Decrease treatment  \n", "34  Decrease treatment  \n", "35  Increase treatment  \n", "36  Increase treatment  \n", "37  Increase treatment  \n", "38  Decrease treatment  \n", "39  Decrease treatment  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ns = 40\n", "eff = est.marginal_effect(T[:ns], X[:ns])\n", "lb, ub = est.marginal_effect_inference(T[:ns], X[:ns]).conf_int(alpha=.01)\n", "true = (.5 + X[:ns, [0]]) * (np.ones((ns,1)) - 2 * T[:ns])\n", "true = np.where(X[:ns, [0]]>0.5, 1, 4) * (np.ones((ns,1)) - 2 * T[:ns])\n", "df = (\n", "    pd.DataFrame({'X': X[:ns, 0], 'marginal effect': eff[:, 0], 'lb': lb[:, 0], 'ub': ub[:, 0], 'true': true[:, 0]})\n", "    .assign(\n", "        presciption = lambda df: df['marginal effect'].gt(0).map({True: 'Increase treatment', False: 'Decrease treatment'})\n", "    )\n", ")\n", "\n", "df"]}, {"cell_type": "markdown", "id": "b8e78869-8584-46cc-a1df-c6c3c7df199e", "metadata": {}, "source": ["Marginal effect vs ground truth"]}, {"cell_type": "code", "execution_count": 13, "id": "75990523-4d08-42df-b95a-2bed264c6489", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "plt.errorbar(\n", "    df['X'], df['marginal effect'], \n", "    yerr=[df['marginal effect'] - df['lb'], df['ub'] - df['marginal effect']], \n", "    fmt='o', alpha = 0.8, label = 'Estimated Marginal Effect')\n", "plt.scatter(df['X'], df['true'], marker='x', color='green', label = 'True Marginal Effect')\n", "\n", "plt.xlabel('X')\n", "plt.ylabel('Marginal Effect')\n", "plt.title('Marginal Effect vs X')\n", "\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "af46336d-012f-4481-b133-e30c1221a456", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.9.7 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "vscode": {"interpreter": {"hash": "a079fe92cd2338bcf6346e1127f9bc22d82c11ed4ab30d98a7d2e9f22c56f37f"}}}, "nbformat": 4, "nbformat_minor": 5}