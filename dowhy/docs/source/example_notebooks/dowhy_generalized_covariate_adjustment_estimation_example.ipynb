{"cells": [{"cell_type": "markdown", "id": "d5433f53-63d7-4ed7-95aa-8dea0b2b0065", "metadata": {}, "source": ["# Causal Effect Estimation with the Generalized Adjustment Criterion - Example Notebook\n", "\n", "The backdoor criterion, while very useful, is not complete, e.g. there are some valid adjustment sets which do not satisfy the backdoor criterion. This notebook gives an example where the backdoor criterion finds no adjustment sets, but by using the generalized adjustment algorithm one can still use covariate adjustment to estimate the causal effect."]}, {"cell_type": "code", "execution_count": 1, "id": "63a631d8-b14b-4647-b74d-e898cc2bf540", "metadata": {}, "outputs": [], "source": ["from dowhy import CausalModel\n", "import dowhy.datasets\n", "import pandas as pd\n", "import networkx as nx\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 44, "id": "68c136aa-a91d-46a7-8934-a1092bc17d60", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>L</th>\n", "      <th>V4</th>\n", "      <th>V5</th>\n", "      <th>X1</th>\n", "      <th>V1</th>\n", "      <th>V3</th>\n", "      <th>V2</th>\n", "      <th>X2</th>\n", "      <th>Y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.285453</td>\n", "      <td>-0.634205</td>\n", "      <td>-0.829879</td>\n", "      <td>1</td>\n", "      <td>-0.689395</td>\n", "      <td>-0.159391</td>\n", "      <td>1.472056</td>\n", "      <td>1</td>\n", "      <td>0.983598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.765150</td>\n", "      <td>1.840856</td>\n", "      <td>0.364065</td>\n", "      <td>1</td>\n", "      <td>-0.195452</td>\n", "      <td>-0.343018</td>\n", "      <td>-0.397037</td>\n", "      <td>0</td>\n", "      <td>-0.286198</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.424812</td>\n", "      <td>0.670598</td>\n", "      <td>-0.693397</td>\n", "      <td>0</td>\n", "      <td>0.481983</td>\n", "      <td>1.232096</td>\n", "      <td>0.764908</td>\n", "      <td>0</td>\n", "      <td>-0.169075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.633208</td>\n", "      <td>-0.772012</td>\n", "      <td>-1.144497</td>\n", "      <td>0</td>\n", "      <td>-1.039779</td>\n", "      <td>0.682182</td>\n", "      <td>0.891705</td>\n", "      <td>1</td>\n", "      <td>0.335345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.568951</td>\n", "      <td>0.064867</td>\n", "      <td>-0.194460</td>\n", "      <td>0</td>\n", "      <td>1.816467</td>\n", "      <td>0.704929</td>\n", "      <td>0.175386</td>\n", "      <td>0</td>\n", "      <td>0.866810</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          L        V4        V5  X1        V1        V3        V2  X2  \\\n", "0 -0.285453 -0.634205 -0.829879   1 -0.689395 -0.159391  1.472056   1   \n", "1 -0.765150  1.840856  0.364065   1 -0.195452 -0.343018 -0.397037   0   \n", "2  0.424812  0.670598 -0.693397   0  0.481983  1.232096  0.764908   0   \n", "3  1.633208 -0.772012 -1.144497   0 -1.039779  0.682182  0.891705   1   \n", "4 -0.568951  0.064867 -0.194460   0  1.816467  0.704929  0.175386   0   \n", "\n", "          Y  \n", "0  0.983598  \n", "1 -0.286198  \n", "2 -0.169075  \n", "3  0.335345  \n", "4  0.866810  "]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define the graph\n", "treatments = [\"X1\", \"X2\"]\n", "outcome = \"Y\"\n", "variables = [\"V1\", \"V2\", \"V3\", \"V4\", \"V5\"]\n", "edges = [\n", "    (\"V5\", \"X1\"),\n", "    (\"V4\", \"X1\"),\n", "    (\"X1\", \"V1\"),\n", "    (\"V1\", \"V2\"),\n", "    (\"V2\", \"X2\"),\n", "    (\"X2\", \"Y\"),\n", "    (\"X1\", \"V3\"),\n", "    (\"V3\", \"Y\"),\n", "    (\"L\", \"V3\"),\n", "    (\"L\", \"V2\"),\n", "]\n", "graph = nx.Di<PERSON>raph(edges)\n", "data = dowhy.datasets.linear_dataset_from_graph(\n", "    graph, \n", "    treatments=treatments, \n", "    outcome=outcome, \n", "    treatments_are_binary=True,\n", "    outcome_is_binary=False,\n", "    num_samples=10_000\n", ")\n", "data['df'].head()"]}, {"cell_type": "markdown", "id": "1ce53578-ed47-4116-820b-d9ace13b6ddc", "metadata": {}, "source": ["Since this data was syntheticly generated, we have access to the true ATE from the random edge weights assigned to the given causal graph."]}, {"cell_type": "code", "execution_count": 47, "id": "e5b6c8a6-eb0c-43c4-b8b6-332f20041a95", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/latex": ["$\\displaystyle 0.750699626768534$"], "text/plain": ["0.7506996267685339"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"ate\"]"]}, {"cell_type": "markdown", "id": "abedc954-dbdc-4b44-a6eb-c395fb8e12a8", "metadata": {}, "source": ["We now drop \"L\" from the dataset, as L will be unobserved."]}, {"cell_type": "code", "execution_count": 50, "id": "f25eefb9-f0a4-4d14-bfe7-e4a13bb0f66c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>V4</th>\n", "      <th>V5</th>\n", "      <th>X1</th>\n", "      <th>V1</th>\n", "      <th>V3</th>\n", "      <th>V2</th>\n", "      <th>X2</th>\n", "      <th>Y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.634205</td>\n", "      <td>-0.829879</td>\n", "      <td>1</td>\n", "      <td>-0.689395</td>\n", "      <td>-0.159391</td>\n", "      <td>1.472056</td>\n", "      <td>1</td>\n", "      <td>0.983598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.840856</td>\n", "      <td>0.364065</td>\n", "      <td>1</td>\n", "      <td>-0.195452</td>\n", "      <td>-0.343018</td>\n", "      <td>-0.397037</td>\n", "      <td>0</td>\n", "      <td>-0.286198</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.670598</td>\n", "      <td>-0.693397</td>\n", "      <td>0</td>\n", "      <td>0.481983</td>\n", "      <td>1.232096</td>\n", "      <td>0.764908</td>\n", "      <td>0</td>\n", "      <td>-0.169075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.772012</td>\n", "      <td>-1.144497</td>\n", "      <td>0</td>\n", "      <td>-1.039779</td>\n", "      <td>0.682182</td>\n", "      <td>0.891705</td>\n", "      <td>1</td>\n", "      <td>0.335345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.064867</td>\n", "      <td>-0.194460</td>\n", "      <td>0</td>\n", "      <td>1.816467</td>\n", "      <td>0.704929</td>\n", "      <td>0.175386</td>\n", "      <td>0</td>\n", "      <td>0.866810</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         V4        V5  X1        V1        V3        V2  X2         Y\n", "0 -0.634205 -0.829879   1 -0.689395 -0.159391  1.472056   1  0.983598\n", "1  1.840856  0.364065   1 -0.195452 -0.343018 -0.397037   0 -0.286198\n", "2  0.670598 -0.693397   0  0.481983  1.232096  0.764908   0 -0.169075\n", "3 -0.772012 -1.144497   0 -1.039779  0.682182  0.891705   1  0.335345\n", "4  0.064867 -0.194460   0  1.816467  0.704929  0.175386   0  0.866810"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["data['df'] = data['df'].drop(columns='L', inplace=False)  # L is unobserved\n", "df=data['df']\n", "df.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "23dfda9f-0399-4725-8a6b-ae2b27c97570", "metadata": {}, "outputs": [], "source": ["model = CausalModel(data=data[\"df\"],\n", "                    treatment=data[\"treatment_name\"], outcome=data[\"outcome_name\"],\n", "                    graph=data[\"gml_graph\"])"]}, {"cell_type": "code", "execution_count": 54, "id": "fe402e90-58fe-48e3-a90f-3fc0d76ec734", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.view_model()\n", "from IPython.display import Image, display\n", "display(Image(filename=\"causal_model.png\"))"]}, {"cell_type": "markdown", "id": "2d605c9c-c5df-4de8-93d2-ddc5e75a817f", "metadata": {}, "source": ["The backdoor criterion requires the adjustment set **Z** to (1) contain no descendents of **X** and (2) to d-separate all backdoor paths from **X** to **Y**. In this case, in the above graph, the backdoor criterion would require the adjustment set to include L (since V2 and V3 are descendents of **X**). But if L is unobserved, then we cannot control for L, and so the backdoor criterion finds no adjustment sets.\n", "\n", "The generalized adjustment criterion given in <PERSON> et al. (2018) however is complete, and is able to identify that in fact {V1, V2} is a valid adjustment set. One can use this adjustment criterion to identify an estimand by using the 'identify_effect()' method in the CausalModel class, which gives a minimal adjustment set which meets the general adjustment criterion. One can then use convariate adjustment to estimate the causal effect given this estimad."]}, {"cell_type": "code", "execution_count": 57, "id": "f6236abe-37d4-403d-b7ae-15302bf75887", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimand type: EstimandType.NONPARAMETRIC_ATE\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor\n", "No such variable(s) found!\n", "\n", "### Estimand : 2\n", "Estimand name: iv\n", "Estimand expression:\n", " ⎡                                  -1⎤\n", " ⎢    d        ⎛    ∂              ⎞  ⎥\n", "E⎢─────────(Y)⋅⎜─────────([X₁  X₂])⎟  ⎥\n", " ⎣d[V₅  V₄]    ⎝∂[V₅  V₄]          ⎠  ⎦\n", "Estimand assumption 1, As-if-random: If U→→Y then ¬(U →→{V5,V4})\n", "Estimand assumption 2, Exclusion: If we remove {V5,V4}→{X1,X2}, then ¬({V5,V4}→Y)\n", "\n", "### Estimand : 3\n", "Estimand name: frontdoor\n", "No such variable(s) found!\n", "\n", "### Estimand : 4\n", "Estimand name: general_adjustment\n", "Estimand expression:\n", "    d                \n", "─────────(E[Y|V1,V2])\n", "d[X₁  X₂]            \n", "Estimand assumption 1, Unconfoundedness: If U→{X1,X2} and U→Y then P(Y|X1,X2,V1,V2,U) = P(Y|X1,X2,V1,V2)\n", "\n"]}], "source": ["identified_estimand= model.identify_effect(proceed_when_unidentifiable=True)\n", "print(identified_estimand)"]}, {"cell_type": "markdown", "id": "8e253d81-a1f2-4a85-8b08-0e1ab448eafa", "metadata": {}, "source": ["### Computing the Estimate\n", "\n", "We can now use the covariate adjustment set identified by the general adjustment criterion used in the identify_effect() method. We can compare this to the ground truth ATE of the data from above."]}, {"cell_type": "code", "execution_count": 60, "id": "137ba024-a199-4b01-854e-736acbcc85e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Causal Estimate ***\n", "\n", "## Identified estimand\n", "Estimand type: EstimandType.NONPARAMETRIC_ATE\n", "\n", "### Estimand : 1\n", "Estimand name: general_adjustment\n", "Estimand expression:\n", "    d                \n", "─────────(E[Y|V1,V2])\n", "d[X₁  X₂]            \n", "Estimand assumption 1, Unconfoundedness: If U→{X1,X2} and U→Y then P(Y|X1,X2,V1,V2,U) = P(Y|X1,X2,V1,V2)\n", "\n", "## Realized estimand\n", "b: Y~X1+X2+V1+V2\n", "Target units: ate\n", "\n", "## Estimate\n", "Mean value: 0.7697765002235358\n", "\n"]}], "source": ["linear_estimate = model.estimate_effect(identified_estimand,\n", "                                        method_name=\"general_adjustment.linear_regression\",\n", "                                        control_value=(0,0),\n", "                                        treatment_value=(1,1),\n", "                                        method_params={'need_conditional_estimates': False})\n", "print(linear_estimate)"]}, {"cell_type": "code", "execution_count": null, "id": "c4c51fee-38a7-455a-b8b7-35a4469a6929", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}