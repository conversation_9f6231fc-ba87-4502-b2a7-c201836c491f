{"cells": [{"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["<img src=\"https://www.microsoft.com/en-us/research/uploads/prod/2020/05/Recomendation.png\" width=\"400\">\n", "\n", "# Recommendation A/B Testing: Experimentation with Imperfect Compliance\n", "\n", "An online business would like to test a new feature or offering of their website and learn its effect on downstream revenue. Furthermore, they would like to know which kind of users respond best to the new version. We call the user-specific effect a **heterogeneous treatment effect**. \n", "\n", "Ideally, the business would run A/B tests between the old and new versions of the website. However, a direct A/B test might not work because the business cannot force the customers to take the new offering. Measuring the effect in this way will be misleading since not every customer exposed to the new offering will take it.\n", "\n", "The business also cannot look directly at existing data as it will be biased: the users who use the latest website features are most likely the ones who are very engaged on the website and hence spend more on the company's products to begin with. Estimating the effect this way would be overly optimistic."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["In this customer scenario walkthrough, we show how tools from the [EconML](https://aka.ms/econml)  and [DoWhy](https://github.com/py-why/dowhy) libraries can still use a direct A/B test and mitigate these shortcomings.\n", "\n", "### Summary\n", "\n", "1. [Background](#Background)\n", "2. [Data](#Data)\n", "3. [Create Causal Model and Identify Causal Effect with DoWhy](#Create-Causal-Model-and-Identify-Causal-Effect-with-<PERSON><PERSON><PERSON>)\n", "4. [Get Causal Effects with EconML](#Get-Causal-Effects-with-EconML)\n", "5. [Test Estimate Robustness with <PERSON><PERSON><PERSON>](#Test-Estimate-Rob<PERSON>ness-with-<PERSON><PERSON><PERSON>)\n", "    1. [Add Random Common Cause](#Add-Random-Common-Cause)\n", "    2. [Add Unobserved Common Cause](#Add-Unobserved-Common-Cause)\n", "    3. [Replace Treatment with a Random (Placebo) Variable](#Replace-Treatment-with-a-Random-(Placebo)-Variable)\n", "    4. [Remove a Random Subset of the Data](#Remove-a-Random-Subset-of-the-Data)\n", "6. [Understand Treatment Effects with EconML](#Understand-Treatment-Effects-with-EconML)\n", "7. [Make Policy Decisions with EconML](#Make-Policy-Decisions-with-EconML)\n", "8. [Conclusions](#Conclusions)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Background\n", "\n", "<img src=\"https://cdn.pixabay.com/photo/2013/07/13/12/18/boeing-159589_640.png\" width=\"450\">\n", "\n", "In this scenario, a travel website would like to know whether joining a membership program compels users to spend more time engaging with the website and purchasing more products. \n", "\n", "A direct A/B test is infeasible because the website cannot force users to become members. Likewise, the travel company can’t look directly at existing data, comparing members and non-members, because the customers who chose to become members are likely already more engaged than other users. \n", "\n", "**Solution:** The company had run an earlier experiment to test the value of a new, faster sign-up process. Instrumental variable (IV) estimators can exploit this experimental nudge towards membership as an instrument that generates random variation in the likelihood of membership. This is known as an **intent-to-treat** setting: the intention is to give a random group of users the \"treatment\" (access to the easier sign-up process), but not all users will actually take it. \n", "\n", "The EconML and DoWhy libraries complement each other in implementing this solution. On one hand, the DoWhy library can help [build a causal model, identify the causal effect](#Create-Causal-Model-and-Identify-Causal-Effect-with-DoWhy) and [test causal assumptions](#Test-Estimate-Robustness-with-DoWhy). On the other hand, EconML's `IntentToTreatDRIV` estimator can [estimate heterogeneous treatment effects](#Get-Causal-Effects-with-EconML) by taking advantage of the fact that not every customer who was offered the easier sign-up became a member to learn the effect of membership rather than the effect of receiving the quick sign-up. Furthermore, EconML provides users tools to [understand causal effects](#Understand-Treatment-Effects-with-EconML) and [make causal policy decisions](#Make-Policy-Decisions-with-EconML)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# Some imports to get us started\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "# Utilities\n", "import os\n", "import urllib.request\n", "import numpy as np\n", "import pandas as pd\n", "from networkx.drawing.nx_pydot import to_pydot\n", "from IPython.display import Image, display\n", "\n", "# Generic ML imports\n", "import lightgbm as lgb\n", "from sklearn.preprocessing import PolynomialFeatures\n", "\n", "# DoWhy imports \n", "import dowhy\n", "from dowhy import CausalModel\n", "\n", "# EconML imports\n", "from econml.iv.dr import LinearIntentToTreatDRIV\n", "from econml.cate_interpreter import SingleTreeCateInterpreter, \\\n", "                                    SingleTreePolicyInterpreter\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Data\n", "\n", "The data* is comprised of:\n", " * Features collected in the 28 days prior to the experiment (denoted by the suffix `_pre`)\n", " * Experiment variables (whether the use was exposed to the easier signup -> the instrument, and whether the user became a member -> the treatment)\n", " * Variables collected in the 28 days after the experiment (denoted by the suffix `_post`).\n", "\n", "Feature Name | Details \n", ":--- |: --- \n", "**days_visited_exp_pre** |#days a user visits the attractions pages \n", "**days_visited_free_pre** | #days a user visits the website through free channels (e.g. domain direct) \n", "**days_visited_fs_pre** | #days a user visits the flights pages \n", "**days_visited_hs_pre** | #days a user visits the hotels pages \n", "**days_visited_rs_pre** | #days a user visits the restaurants pages \n", "**days_visited_vrs_pre** | #days a user visits the vacation rental pages \n", "**locale_en_US** | whether the user access the website from the US \n", "**os_type** | user's operating system (windows, osx, other) \n", "**revenue_pre** | how much the user spent on the website in the pre-period \n", "**easier_signup** | whether the user was exposed to the easier signup process \n", "**became_member** | whether the user became a member \n", "**days_visited_post** | #days a user visits the website in the 28 days after the experiment \n", "\n", "\n", "**To protect the privacy of the travel company's users, the data used in this scenario is synthetically generated and the feature distributions don't correspond to real distributions. However, the feature names have preserved their names and meaning.*"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# Import the sample AB data\n", "file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/RecommendationAB/ab_sample.csv\"   \n", "ab_data = pd.read_csv(file_url)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"slideshow": {"slide_type": "slide"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>days_visited_exp_pre</th>\n", "      <th>days_visited_free_pre</th>\n", "      <th>days_visited_fs_pre</th>\n", "      <th>days_visited_hs_pre</th>\n", "      <th>days_visited_rs_pre</th>\n", "      <th>days_visited_vrs_pre</th>\n", "      <th>locale_en_US</th>\n", "      <th>revenue_pre</th>\n", "      <th>os_type_osx</th>\n", "      <th>os_type_windows</th>\n", "      <th>easier_signup</th>\n", "      <th>became_member</th>\n", "      <th>days_visited_post</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>25</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10</td>\n", "      <td>25</td>\n", "      <td>27</td>\n", "      <td>10</td>\n", "      <td>27</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>2.26</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>18</td>\n", "      <td>14</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0.03</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17</td>\n", "      <td>0</td>\n", "      <td>23</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>418.77</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>24</td>\n", "      <td>9</td>\n", "      <td>22</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>18</td>\n", "      <td>1</td>\n", "      <td>1.54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   days_visited_exp_pre  days_visited_free_pre  days_visited_fs_pre  \\\n", "0                     1                      9                    7   \n", "1                    10                     25                   27   \n", "2                    18                     14                    8   \n", "3                    17                      0                   23   \n", "4                    24                      9                   22   \n", "\n", "   days_visited_hs_pre  days_visited_rs_pre  days_visited_vrs_pre  \\\n", "0                   25                    6                     3   \n", "1                   10                   27                    27   \n", "2                    4                    5                     2   \n", "3                    2                    3                     1   \n", "4                    2                    3                    18   \n", "\n", "   locale_en_US  revenue_pre  os_type_osx  os_type_windows  easier_signup  \\\n", "0             1         0.01            0                1              0   \n", "1             0         2.26            0                0              0   \n", "2             1         0.03            0                1              0   \n", "3             1       418.77            0                1              0   \n", "4             1         1.54            0                0              0   \n", "\n", "   became_member  days_visited_post  \n", "0              0                  1  \n", "1              0                 15  \n", "2              0                 17  \n", "3              0                  6  \n", "4              0                 12  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data sample\n", "ab_data.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [], "source": ["# Define estimator inputs\n", "Z = ab_data['easier_signup'] # nudge, or instrument\n", "T = ab_data['became_member'] # intervention, or treatment\n", "Y = ab_data['days_visited_post'] # outcome of interest\n", "X_data = ab_data.drop(columns=['easier_signup', 'became_member', 'days_visited_post']) # features"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["The data was generated using the following undelying treatment effect function:\n", "\n", "$$\n", "\\text{treatment_effect} = 0.2 + 0.3 \\cdot \\text{days_visited_free_pre} - 0.2 \\cdot \\text{days_visited_hs_pre} + \\text{os_type_osx}\n", "$$\n", "\n", "The interpretation of this is that users who visited the website before the experiment and/or who use an iPhone tend to benefit from the membership program, whereas users who visited the hotels pages tend to be harmed by membership. **This is the relationship we seek to learn from the data.**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [], "source": ["# Define underlying treatment effect function \n", "TE_fn = lambda X: (0.2 + 0.3 * X['days_visited_free_pre'] - 0.2 * X['days_visited_hs_pre'] + X['os_type_osx']).values\n", "true_TE = TE_fn(X_data)\n", "\n", "# Define the true coefficients to compare with\n", "true_coefs = np.zeros(X_data.shape[1])\n", "true_coefs[[1, 3, -2]] = [0.3, -0.2, 1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Causal Model and Identify Causal Effect with DoWhy\n", "\n", "We define the causal assumptions of the intent-to-treat setting with <PERSON><PERSON><PERSON>. For example, we can include features we believe are instruments and features we think will influence the heterogeneity of the effect. With these assumptions defined, <PERSON><PERSON><PERSON> can identify the causal effect for us."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["feature_names = X_data.columns.tolist()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Define nuissance estimators\n", "lgb_T_XZ_params = {\n", "    'objective' : 'binary',\n", "    'metric' : 'auc',\n", "    'learning_rate': 0.1,\n", "    'num_leaves' : 30,\n", "    'max_depth' : 5\n", "}\n", "\n", "lgb_Y_X_params = {\n", "    'metric' : 'rmse',\n", "    'learning_rate': 0.1,\n", "    'num_leaves' : 30,\n", "    'max_depth' : 5\n", "}\n", "model_T_XZ = lgb.LGBMClassifier(**lgb_T_XZ_params)\n", "model_Y_X = lgb.LGBMRegressor(**lgb_Y_X_params)\n", "flexible_model_effect = lgb.LGBMRegressor(**lgb_Y_X_params)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# initiate an EconML cate estimator\n", "est = LinearIntentToTreatDRIV(model_t_xwz=model_T_XZ, model_y_xw=model_Y_X,\n", "                            flexible_model_effect=flexible_model_effect,\n", "                            featurizer=PolynomialFeatures(degree=1, include_bias=False))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# fit through dowhy\n", "test_customers = X_data.iloc[:1000]\n", "est_dw=est.dowhy.fit(Y, T, Z=Z, X=X_data, outcome_names=[\"days_visited_post\"], treatment_names=[\"became_member\"],\n", "                     feature_names=feature_names, instrument_names=[\"easier_signup\"], target_units=test_customers,\n", "                     inference=\"statsmodels\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x576 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize causal graph\n", "plt.figure(figsize=(10,8))\n", "est_dw.view_model() "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimand type: nonparametric-ate\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor\n", "Estimand expression:\n", "       d                                                                      \n", "────────────────(Expectation(days_visited_post|days_visited_vrs_pre,days_visit\n", "d[became_member]                                                              \n", "\n", "                                                                              \n", "ed_exp_pre,days_visited_hs_pre,days_visited_rs_pre,revenue_pre,days_visited_fr\n", "                                                                              \n", "\n", "                                                                     \n", "ee_pre,os_type_windows,days_visited_fs_pre,os_type_osx,locale_en_US))\n", "                                                                     \n", "Estimand assumption 1, Unconfoundedness: If U→{became_member} and U→days_visited_post then P(days_visited_post|became_member,days_visited_vrs_pre,days_visited_exp_pre,days_visited_hs_pre,days_visited_rs_pre,revenue_pre,days_visited_free_pre,os_type_windows,days_visited_fs_pre,os_type_osx,locale_en_US,U) = P(days_visited_post|became_member,days_visited_vrs_pre,days_visited_exp_pre,days_visited_hs_pre,days_visited_rs_pre,revenue_pre,days_visited_free_pre,os_type_windows,days_visited_fs_pre,os_type_osx,locale_en_US)\n", "\n", "### Estimand : 2\n", "Estimand name: iv\n", "Estimand expression:\n", "Expectation(Derivative(days_visited_post, [easier_signup])*Derivative([became_\n", "member], [easier_signup])**(-1))\n", "Estimand assumption 1, As-if-random: If U→→days_visited_post then ¬(U →→{easier_signup})\n", "Estimand assumption 2, Exclusion: If we remove {easier_signup}→{became_member}, then ¬({easier_signup}→days_visited_post)\n", "\n", "### Estimand : 3\n", "Estimand name: frontdoor\n", "No such variable found!\n", "\n"]}], "source": ["identified_estimand = est_dw.identified_estimand_\n", "print(identified_estimand)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Get Causal Effects with EconML\n", "\n", "To learn a linear projection of the treatment effect, we use the `LinearIntentToTreatDRIV` EconML estimator. For a more flexible treatment effect function, use the `IntentToTreatDRIV` estimator instead. \n", "\n", "The model requires to define some nuissance models (i.e. models we don't really care about but that matter for the analysis): the model for how the outcome $Y$ depends on the features $X$ (`model_Y_X`) and the model for how the treatment $T$ depends on the instrument $Z$ and features $X$ (`model_T_XZ`). Since we don't have any priors on these models, we use generic boosted tree estimators to learn them. "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Causal Estimate ***\n", "\n", "## Identified estimand\n", "Estimand type: nonparametric-ate\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor\n", "Estimand expression:\n", "       d                                                                      \n", "────────────────(Expectation(days_visited_post|days_visited_vrs_pre,days_visit\n", "d[became_member]                                                              \n", "\n", "                                                                              \n", "ed_exp_pre,days_visited_hs_pre,days_visited_rs_pre,revenue_pre,days_visited_fr\n", "                                                                              \n", "\n", "                                                                     \n", "ee_pre,os_type_windows,days_visited_fs_pre,os_type_osx,locale_en_US))\n", "                                                                     \n", "Estimand assumption 1, Unconfoundedness: If U→{became_member} and U→days_visited_post then P(days_visited_post|became_member,days_visited_vrs_pre,days_visited_exp_pre,days_visited_hs_pre,days_visited_rs_pre,revenue_pre,days_visited_free_pre,os_type_windows,days_visited_fs_pre,os_type_osx,locale_en_US,U) = P(days_visited_post|became_member,days_visited_vrs_pre,days_visited_exp_pre,days_visited_hs_pre,days_visited_rs_pre,revenue_pre,days_visited_free_pre,os_type_windows,days_visited_fs_pre,os_type_osx,locale_en_US)\n", "\n", "## Realized estimand\n", "b: days_visited_post~became_member+days_visited_vrs_pre+days_visited_exp_pre+days_visited_hs_pre+days_visited_rs_pre+revenue_pre+days_visited_free_pre+os_type_windows+days_visited_fs_pre+os_type_osx+locale_en_US | days_visited_exp_pre,days_visited_free_pre,days_visited_fs_pre,days_visited_hs_pre,days_visited_rs_pre,days_visited_vrs_pre,locale_en_US,revenue_pre,os_type_osx,os_type_windows\n", "Target units: Data subset provided as a data frame\n", "\n", "## Estimate\n", "Mean value: 2.074836810507913\n", "Effect estimates: [-1.74309260e+00  5.53465593e+00  3.67174621e+00 -8.69405764e-02\n", "  2.49205011e+00  6.53811401e+00 -9.27156048e-01 -2.03494478e+00\n", " -1.58974243e+00  6.60355284e-01 -2.35253192e+00 -9.72266006e-01\n", "  2.58512829e+00 -4.28449527e+00  3.53941978e+00  2.01005015e+00\n", "  8.55532638e+00 -3.89445854e+00  6.49937040e+00  5.97849997e+00\n", "  6.01222833e-01  8.69152554e+00  8.49341505e-01 -4.38862542e-01\n", "  2.69974073e+00 -1.07172634e+00  3.83412079e+00  1.34634769e+00\n", "  3.97833032e+00  5.42847073e+00 -1.19288892e+00  2.10402278e+00\n", "  2.08726067e+00  1.56419260e+00  3.63830156e+00 -1.01578151e+00\n", " -3.02499000e+00  2.18876746e+00 -1.43604094e+00  5.89519266e+00\n", " -4.44503657e-01  3.55396415e+00 -4.28027219e+00  6.12424113e+00\n", " -1.94476700e+00  1.08536153e-01 -3.36965727e+00  6.33474174e+00\n", "  5.89486409e+00 -1.38630934e+00  6.73856605e-01  1.85980353e+00\n", "  3.69212314e+00  4.24501313e+00  4.70614279e+00  3.30653739e+00\n", "  6.10412585e+00  4.09982762e+00  1.62321176e+00  4.20534679e+00\n", " -2.13333488e-03 -7.97476772e-01  4.24218332e+00  1.83376652e+00\n", "  7.02336492e+00  1.45619212e+00  5.73407744e+00  2.44412507e+00\n", "  2.26878288e+00  2.13634127e+00  3.23433510e+00  3.95106838e+00\n", "  1.60719511e+00 -1.85150207e+00  1.17771791e+00 -1.67746289e+00\n", "  3.45943014e+00 -1.28928124e+00  6.34299463e+00 -2.14774544e+00\n", " -1.17201152e+00  8.09765933e+00  1.83124154e+00  3.95896807e+00\n", "  2.16741726e+00  1.82690318e+00  3.59654887e+00  3.21166170e+00\n", "  3.12268300e+00 -1.25797987e-02  3.52929338e+00  2.26757870e+00\n", " -4.71185895e-01  8.27256803e-01  4.13499096e+00  1.62878679e+00\n", " -1.10086813e+00 -9.70798823e-01  2.02758406e-01  1.28683768e+00\n", "  4.51709657e+00  1.05840050e+00  6.87684925e+00  4.43733264e+00\n", "  1.06629793e+00  4.19063540e+00  6.52544128e+00  5.72281224e+00\n", "  3.22966990e+00 -2.81420032e+00  4.99916055e+00  8.05158036e+00\n", "  7.67863330e+00 -3.46375509e-02  1.30546854e+00  1.05531219e+00\n", " -1.26010025e+00  7.91691211e-01 -2.36538221e+00  6.73795194e+00\n", "  4.89705680e+00  1.93764755e+00  3.50080228e+00 -3.95106313e+00\n", "  7.78051059e+00  2.81160250e+00  5.55741787e+00  2.50008057e+00\n", "  5.88765140e+00  5.76020139e+00  2.15759457e-01  3.83114064e+00\n", "  3.35538095e+00  4.57464892e+00  1.73651262e+00  2.20478312e-01\n", "  3.59258894e+00  6.52812764e+00 -9.18804011e-01  9.72567765e-01\n", "  4.56181090e+00  4.45009046e+00  9.87813083e-01 -1.32559925e+00\n", "  7.56776255e+00 -1.95094181e+00  6.53097736e+00 -1.10594109e+00\n", "  6.21188278e-01  3.70136780e+00 -1.23360221e+00  5.20526542e+00\n", "  3.12794715e+00  6.54476985e+00  7.75956636e-01  2.38163644e+00\n", "  3.36417138e+00  4.26756508e+00 -3.04866742e+00  1.96070583e+00\n", "  7.35077271e+00  3.08845773e-01  7.23580460e+00  1.34716185e+00\n", "  2.18156021e+00  4.83274103e+00 -2.34998026e+00  3.35537618e+00\n", "  5.55369276e+00  2.74221800e-01  6.18384351e+00  5.94164635e+00\n", "  4.03024538e+00 -3.74984445e-01  3.84850361e+00  9.82501156e-01\n", "  8.77008493e+00  5.56976699e+00  6.25675975e+00 -1.02349503e+00\n", "  6.05053434e+00 -3.38384566e-01 -1.50995724e+00 -4.45531012e+00\n", "  4.79286266e+00  3.62620644e+00  5.05075984e+00 -3.81138355e+00\n", " -4.39662442e+00  4.74418953e+00 -1.11632755e+00  2.22967461e+00\n", "  4.22320361e+00  8.89930991e-01  5.50984790e+00  5.28657271e+00\n", "  1.93668179e+00  5.16460815e+00  1.48803005e+00  3.16128421e+00\n", " -2.85838153e+00  3.45563312e+00  1.91304882e+00  6.87287368e-01\n", "  1.21567485e+00  5.13325916e+00  2.30208699e+00  2.60595527e+00\n", " -4.66029855e-01  6.72281877e+00  3.17388410e+00  9.80905414e-01\n", "  1.70294587e+00  3.93991122e+00  1.35473574e+00  4.93767675e+00\n", "  7.31608033e+00 -5.30408826e-02  4.48173291e+00  4.61592086e+00\n", "  6.57130629e-01  7.06942280e+00  2.08104238e+00  1.96786944e+00\n", "  4.77461409e+00 -2.62570989e+00  2.15465388e+00  6.18481952e-01\n", "  2.41600923e+00  5.68683208e+00  3.57537757e+00  3.50248852e+00\n", " -1.28753691e+00 -2.80029980e+00  3.99450642e+00  9.92538123e-01\n", "  1.01213435e-01  7.81927442e+00  1.25738051e+00  4.16983403e+00\n", " -6.81940715e-01 -4.42587106e+00  5.74988975e+00  1.68593438e+00\n", "  6.69688320e+00  5.21616476e+00 -2.27260936e+00  1.13831168e+00\n", "  5.36413952e+00  2.83253418e-01  4.88560931e+00 -1.88996135e+00\n", "  3.18349182e+00 -1.02463348e+00  6.06684750e+00  4.20634750e+00\n", "  1.62840431e+00  5.63158806e+00  4.99040472e+00 -1.10327523e+00\n", "  3.00969651e+00  3.03817784e-01  3.87607353e+00  1.08792523e+00\n", "  4.92775808e-01  7.49203732e+00 -3.02149323e-01 -2.02578596e+00\n", " -2.22250377e+00  1.89698906e+00 -1.09787805e+00  2.31600416e+00\n", " -3.65366226e+00  2.66605037e+00 -1.46510115e-01  1.21847490e+00\n", " -2.88910050e+00  4.06739838e+00  9.15531012e-01  7.04984898e+00\n", " -3.51882824e-01  3.66044140e+00 -5.98493435e-01  6.82966626e+00\n", " -2.69842544e+00  1.07744986e+00  1.79752059e+00  7.79385014e+00\n", " -4.56509202e+00  5.80269337e+00 -6.56688906e-01  5.88851860e+00\n", "  3.93658994e+00 -1.69590535e+00  5.20126161e+00  6.82804500e+00\n", "  7.16098922e+00  6.51613242e+00  1.73272127e+00  8.89407848e-01\n", " -2.04866376e-01  4.56717995e+00 -1.05419237e+00 -7.86606424e-01\n", "  3.83260536e+00  2.12661458e+00  4.01653937e+00  5.44296894e+00\n", "  2.72182931e+00  2.75573638e+00  2.90579391e+00  4.43073324e+00\n", " -1.66402361e+00 -1.97191416e+00 -1.28059779e+00  2.88480188e+00\n", "  2.51965402e+00 -1.36302697e-01 -7.80022805e-01 -1.49424695e+00\n", " -1.34282981e+00  3.44975636e+00 -3.03072104e-01 -2.45459527e+00\n", " -3.00629481e-01  2.29090581e+00  1.56543593e+00  3.35750472e+00\n", "  2.33223505e+00 -3.82989563e+00  4.88021332e+00  7.56731917e-01\n", "  4.25582832e+00  5.98434505e+00  3.01986691e+00 -6.77442677e-01\n", " -1.64953867e+00  5.66357995e+00  5.64428003e-01  5.10242590e+00\n", "  8.26971905e+00  4.97946101e+00  1.29196275e+00  7.76284555e-01\n", "  3.33445074e+00  1.30853736e+00 -1.53525283e+00  5.36833802e+00\n", "  1.15992695e+00  2.81928116e+00 -4.07206472e+00 -2.50815511e+00\n", "  8.78792298e+00  6.50547578e+00  5.01930927e+00  5.41797922e-01\n", " -9.09773180e-01 -2.39454791e-01  4.96199723e+00 -4.35634019e-01\n", "  3.80466959e+00  3.86120839e+00  2.03636643e+00  2.81371356e+00\n", "  3.36389949e+00  6.24676032e+00  2.21810552e+00 -9.70461750e-01\n", "  4.39036661e+00  8.75240453e+00  1.16978502e+00  7.93325547e-01\n", "  1.95900018e+00  2.11067965e+00  4.67378254e+00 -9.81738213e-01\n", "  5.62730702e+00  2.28988281e+00  6.39554347e-01  3.56780655e+00\n", "  2.55972508e+00  7.74507056e+00 -1.70026139e+00  4.79636075e+00\n", "  4.58045725e+00  3.92461050e+00 -4.50476384e-01  3.64823178e-01\n", "  5.25977278e+00  2.71290477e+00 -2.73514000e+00  7.35033429e-01\n", " -8.93039089e-01  4.47635864e+00 -4.00819110e-01  9.84948255e-01\n", " -1.25187326e-02  7.43522358e+00  2.30060347e+00  1.38459891e+00\n", "  1.77678620e+00  2.99902550e+00  2.88012576e+00  1.60279207e+00\n", " -2.17076889e+00  4.02771144e+00  1.76082775e+00  4.28513483e+00\n", "  4.28349578e+00  5.65144489e+00  1.82377346e+00  3.54960094e-01\n", "  2.81727581e+00 -4.52598385e-01 -6.03200625e-01  2.60658700e+00\n", "  3.08520007e+00  2.15407701e+00 -1.42112638e+00  3.21798527e+00\n", "  5.61414647e+00 -5.47711654e-01  4.36176805e+00  1.63973179e+00\n", "  1.58738126e+00  5.88689122e+00  8.97612240e-01  5.72790582e+00\n", " -2.08776274e+00  8.15370684e+00 -1.11019576e+00  4.31710187e+00\n", "  4.79763806e+00  3.53119218e+00  2.80709783e+00  3.73105939e+00\n", "  1.45150046e+00 -2.01269415e+00  1.57131277e+00  6.72753596e+00\n", "  2.32073341e+00  1.17117638e+00  1.73436699e+00  4.16817944e-01\n", "  2.05029100e+00 -3.51544594e+00  3.54557735e+00 -4.34142231e+00\n", "  4.06513401e+00  5.99370062e+00 -3.70340315e+00  3.05069363e-01\n", "  3.62131945e+00  5.91110995e+00  3.60378283e+00  7.90079958e+00\n", " -9.82519600e-01 -1.47391313e+00 -3.70396976e+00 -1.75277144e+00\n", "  8.22746049e+00  6.25546949e+00 -3.27053389e+00  5.80854238e+00\n", " -5.38139284e-01  7.33055362e+00  3.45144595e+00  1.25728783e+00\n", " -2.25877957e+00  2.94036380e+00  7.16332361e+00 -1.89523849e+00\n", "  4.59896257e+00  3.24588494e+00 -8.27676346e-01 -1.64001795e+00\n", " -3.39468343e-01  3.16084694e+00 -4.02755628e+00  2.68044647e+00\n", " -3.52309942e+00  5.44565273e+00  2.64071028e-01  3.48066706e+00\n", "  2.71503355e+00  6.64645821e+00  6.03453338e+00 -4.10295028e-01\n", "  2.19121161e+00 -2.57672250e+00  5.39241076e-02  5.10824345e+00\n", "  3.26779962e+00 -1.25823539e-01 -2.69624903e-01 -1.02591192e+00\n", " -1.65936525e-01 -3.68833403e+00  2.60254511e+00  2.33586105e+00\n", " -1.98761269e-01  7.92978882e-02  1.35104743e+00 -1.47352587e+00\n", "  8.19287501e+00  4.36825745e+00  5.62348538e+00 -3.02310464e+00\n", " -1.24952469e+00  3.60869767e+00 -7.14943836e-01  1.72827688e+00\n", " -1.08401619e+00  9.71797607e-01 -3.67172373e+00  2.87376017e+00\n", "  4.02261559e+00  4.40185980e+00  6.08622771e+00  2.52161723e+00\n", "  5.02390682e+00  2.05971964e+00  6.63443395e+00  7.86739483e-01\n", "  4.05075295e+00  7.14878181e+00 -1.06458487e+00  2.25387880e+00\n", "  3.78587974e+00  5.86594420e+00  5.28191092e+00  1.56829712e+00\n", "  1.11992377e+00  6.25970542e+00  4.59518376e+00 -4.73532804e-02\n", "  9.91925106e-01  1.78780084e+00 -1.67704486e+00  2.57846392e+00\n", "  5.01363080e+00  3.62578165e+00  2.98383041e+00 -3.54004556e+00\n", "  1.35146165e+00  1.84404206e+00  3.48719456e-01  5.49275762e+00\n", " -1.24477657e-01  5.71975488e+00 -7.58653139e-01  5.63162877e+00\n", "  1.26831897e+00  4.86859077e+00 -1.13386422e+00  1.06421474e-01\n", "  8.53634114e-01  3.72871105e+00  2.95248652e+00  1.55466009e-01\n", "  4.68613518e+00  3.15019073e+00  4.34200117e+00 -1.30058519e+00\n", "  2.00072905e+00  2.88969282e+00 -1.06451735e+00 -1.82511376e+00\n", "  3.84745924e+00  7.22350673e-01 -2.19343278e+00  3.08092161e-01\n", "  3.71253968e+00 -1.39914348e+00  1.54079973e+00  7.60225010e+00\n", "  6.67954964e+00  7.14655416e+00 -4.19495837e+00  4.88257788e+00\n", " -2.51365816e+00  1.77547611e+00  3.46728670e+00  3.31741550e+00\n", "  8.14876747e-02  8.11447031e+00  6.09301853e+00  1.24339832e+00\n", "  2.38910718e+00  6.81992598e+00 -7.40858729e-01  6.05346401e+00\n", "  7.75580654e-01  3.12830439e+00  6.51307396e+00 -3.09787098e+00\n", "  5.52236505e+00  2.12292849e+00 -1.58532072e+00 -7.10785546e-01\n", "  2.18485629e+00  2.46401673e+00  8.20218100e+00 -2.86368324e-01\n", "  1.96456943e-01 -3.27360049e-01 -1.76152094e+00 -4.13507498e+00\n", "  2.77048361e+00  3.40322662e+00  2.44251620e+00  2.67490436e+00\n", " -1.99694213e+00  3.99706804e+00  4.26671123e+00  4.76257264e+00\n", " -6.93027569e-01 -3.05010862e+00  3.05740918e+00  2.13801727e+00\n", "  4.34430442e+00  1.89650356e+00  2.38504437e+00  4.95921476e+00\n", "  3.17285628e+00  3.12211963e+00  5.46801748e+00  2.73860789e+00\n", "  1.76167846e+00 -1.51200597e+00 -7.96905771e-01  1.35456411e+00\n", "  9.29340211e-01  3.49435329e+00  4.23603904e+00  6.69448510e+00\n", "  5.10603232e+00 -2.36369230e+00  8.37297836e+00  1.22940278e-01\n", "  2.19571073e+00  3.69819816e+00  6.74460063e+00 -3.74331119e+00\n", "  8.92857533e-01  4.59329727e+00  3.48895907e+00  4.37231542e+00\n", "  2.93079746e+00 -2.97465690e+00  1.61162351e+00  1.99255762e+00\n", "  5.40951089e+00 -9.98206039e-01  1.43407717e+00  3.83693457e+00\n", "  4.19456558e-01  4.94522100e-01  6.07062543e-01  1.04986790e+00\n", "  3.09447525e+00  1.97291718e+00  3.46325689e-01  2.44618465e+00\n", "  1.64297716e+00  8.07359838e-01  7.74146481e-02  3.04319821e-01\n", "  2.17399157e+00 -3.76197035e+00  1.31547331e+00  2.77034800e+00\n", "  2.82394908e+00  2.03193459e-01  5.17072376e-01  4.97024954e+00\n", " -2.38117209e+00 -1.31368064e+00 -3.74253696e+00 -1.33734460e+00\n", "  7.50410766e+00 -2.29343424e+00 -8.28129164e-01  1.02559807e+00\n", "  4.72529662e+00  7.49061575e+00 -2.16372322e+00  3.00583132e+00\n", "  3.47987760e+00 -6.46683474e-02 -9.11975347e-01  4.28485556e+00\n", "  1.75228884e+00 -1.40813589e+00  3.63239066e+00  5.11789487e-01\n", "  1.41743073e+00  3.88809859e+00 -3.54327130e-01  3.74289817e+00\n", "  6.25828786e+00  5.13827132e+00  3.64615060e+00  4.38494964e+00\n", "  3.34938791e+00 -1.03516982e+00  1.74261092e+00  1.62080770e+00\n", "  6.44783537e-01  3.84808165e-01  6.66597093e+00  6.58042866e+00\n", "  1.65316580e+00  4.36179208e+00  4.96272829e+00 -1.32003578e+00\n", " -3.37267784e-01  1.17435888e+00  6.32191868e+00 -2.19374986e+00\n", "  6.98513892e+00 -2.41678836e+00  7.36416922e+00  4.11466970e+00\n", "  2.00458399e+00  6.91096947e+00 -2.81991921e+00  5.10144395e+00\n", "  3.61381266e+00  4.63366500e+00 -1.03542313e+00 -1.49851299e+00\n", " -1.59144759e-01  3.90583528e+00  6.22492672e+00  1.20704979e-01\n", "  4.61311085e+00  2.31444040e+00 -1.48449492e+00  1.20596862e-01\n", "  8.08987223e+00  6.60641742e+00 -2.00176137e+00 -2.34127540e+00\n", "  5.35472054e+00 -6.95845533e-01  7.35014465e+00 -1.99873366e+00\n", "  2.02638264e+00  5.62287844e+00  9.01788090e-01  3.05411553e+00\n", "  4.88057954e+00  5.98858828e+00 -3.14881269e+00  1.36720169e-01\n", " -1.03873249e+00 -1.55201233e+00 -5.88124947e-01  7.88855367e+00\n", " -9.11510636e-01  6.97647326e+00  7.99263959e+00 -2.19542887e+00\n", "  7.62333863e-01  4.71366269e+00  5.26937330e+00  7.48340869e-01\n", "  6.25446602e+00 -9.29526957e-01  1.67602852e+00  3.53900851e+00\n", " -9.89236796e-01  5.72960088e-01 -4.96209121e+00  1.41808646e-01\n", "  4.38174259e+00  3.23980166e+00  1.57245128e+00 -2.09363206e-01\n", "  1.23547521e+00  3.79249825e+00  1.37442484e+00  6.56595315e+00\n", "  4.19975970e-01  4.76295795e+00  8.03273165e+00  3.85206502e+00\n", " -3.14809072e+00 -1.42281734e+00  4.53843319e+00 -3.09966441e-01\n", "  2.73278787e+00 -3.23098446e+00 -2.07087082e+00 -1.06979572e-01\n", "  1.60618511e+00 -2.95772871e+00  3.81466244e+00  9.21786010e-01\n", " -1.35387289e+00 -7.03129229e-01  3.79559591e-01  5.77799097e+00\n", "  2.94330351e+00 -2.38336168e-01  2.40932306e-01  7.94506684e+00\n", "  3.17267188e+00 -1.49882027e+00  3.55261410e+00 -1.50667458e+00\n", "  2.99165436e+00  3.75253621e+00 -1.12006173e+00 -8.35947935e-01\n", " -9.02295356e-01  6.26190996e-01 -3.41116453e-01 -3.20073873e+00\n", "  6.66413349e+00  1.81441271e+00 -1.39927597e-02 -6.30747430e-01\n", "  4.67113793e+00  7.21881314e-01 -2.10520878e+00  7.04350250e-01\n", "  3.09285720e+00  2.92249368e+00  7.56353546e+00 -1.13274244e+00\n", "  5.17376347e+00  9.24948945e+00 -3.09396390e+00  2.26610772e+00\n", "  4.74759828e+00  4.91781123e+00  1.23358925e+00 -2.02618829e+00\n", "  1.75889103e+00  4.17112041e+00  6.97209339e+00  3.09761268e+00\n", "  5.89551465e+00  4.45768950e+00  1.64791986e+00  1.44191054e+00\n", "  5.76985139e-01  1.42627610e+00  9.66448598e-01  7.53556981e+00\n", "  4.10629551e+00 -1.39509766e+00 -3.29797971e-01  1.40107822e+00\n", "  5.15825742e+00 -2.75657454e+00  7.20215059e-01 -2.07694148e+00\n", "  1.59247633e+00  2.59882260e+00  2.15560971e+00  6.77307070e+00\n", "  4.47166256e+00 -2.53766184e+00  4.40442171e+00  3.52260241e+00\n", " -2.40405287e-01  2.78356222e+00  1.79254816e+00  3.22573393e+00\n", "  2.80867711e+00  2.66260613e+00  1.87084207e+00  6.39594723e-01\n", " -2.55020546e+00 -1.97359748e+00  4.63770438e+00 -3.79032496e+00\n", "  5.34371491e+00  6.78652025e-01  2.05061234e+00  3.16840712e+00\n", "  1.89895555e+00  5.90313510e+00  4.92421932e+00  4.23968004e+00\n", " -2.40522898e+00  5.75356577e+00  3.31254451e-01  4.47200032e+00\n", "  5.07581093e+00  4.51219872e+00  1.56077006e+00 -3.81885967e+00\n", "  3.47850715e+00 -2.16399475e+00  4.44585883e+00 -1.06202980e+00\n", "  4.82758393e+00 -3.06705558e-01  2.22457446e+00  1.89227309e+00\n", " -6.93047420e-01 -2.74082774e+00  4.48112366e+00  3.24373615e+00\n", "  2.40937313e+00  2.72945617e+00  4.06023806e+00  7.34159840e+00\n", "  4.35356041e+00  7.37977906e+00 -1.60024165e+00  3.72050168e+00\n", "  2.91322179e+00 -1.44928107e+00 -7.32480014e-01 -7.98501972e-01\n", "  8.05410850e-01  6.16302382e+00  1.49803296e-01 -3.57438812e+00\n", "  4.41876630e+00  6.57121478e+00  2.35263834e+00 -1.62730164e-01\n", "  6.34172715e+00  5.32343220e+00  6.21613634e+00  6.47585479e+00\n", "  7.58301854e+00  5.06271128e+00 -2.23422979e-01 -3.77726474e-01\n", " -2.21815777e+00  6.62474901e+00 -2.12185513e+00 -4.03188972e+00\n", "  3.93918428e-01  4.70401667e+00 -1.12479614e+00 -7.64482146e-01\n", "  5.46199850e-01 -1.17406273e+00  1.98813303e+00  4.22495335e+00\n", " -3.46998475e+00  5.23831859e+00  3.80043337e+00 -3.94604969e+00\n", "  2.71623267e+00  8.39105247e+00  6.27411909e+00  5.49172457e+00\n", "  1.59829426e-01 -3.69921183e-01  5.74470090e+00 -3.31019029e+00\n", " -2.32851148e+00  9.70311108e-01  5.19089524e+00  1.37619180e+00\n", "  2.07126559e+00  2.44072904e+00 -1.57609213e-01  3.46134772e+00\n", "  3.36619971e+00  2.55965265e+00 -4.18502036e+00  3.32595277e-01\n", "  6.23600406e-01  2.99559994e+00  4.56222143e+00  2.12829124e+00\n", "  7.32010757e-01 -4.55998526e-01 -2.04518168e+00 -2.78017056e+00\n", "  8.94433533e-01 -1.54750416e+00  3.46372078e+00 -4.87762972e+00\n", "  4.01784829e+00  4.65737498e-01  3.56823217e+00  2.98560714e+00\n", "  6.37814649e-02  1.53870397e+00 -2.93768797e+00  5.95941996e+00\n", "  2.62252390e+00  3.15463337e+00 -2.34714284e+00 -1.72444634e+00\n", "  2.58803802e+00  2.29550703e+00  8.93617057e-02  4.57914480e+00]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["lineardml_estimate = est_dw.estimate_\n", "print(lineardml_estimate)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True ATE on test data:  1.9633999999999998\n"]}], "source": ["true_customer_TE = TE_fn(test_customers)\n", "print(\"True ATE on test data: \", true_customer_TE.mean())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Compare learned coefficients with true model coefficients\\\n", "econml_coefs = est_dw.coef_.flatten()\n", "coef_indices = np.arange(econml_coefs.shape[0])\n", "# Calculate error bars\n", "coef_error = np.asarray(est_dw.coef__interval()).reshape(2, coef_indices.shape[0]) # 95% confidence interval for coefficients\n", "coef_error[0, :] = econml_coefs - coef_error[0, :]\n", "coef_error[1, :] = coef_error[1, :] - econml_coefs"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.errorbar(coef_indices, econml_coefs, coef_error, fmt=\"o\", label=\"Learned coefficients\\nand 95% confidence interval\")\n", "plt.scatter(coef_indices, true_coefs, color='C1', label=\"True coefficients\")\n", "plt.xticks(coef_indices, X_data.columns, rotation='vertical')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["We notice that the coefficients estimates are pretty close to the true coefficients for the linear treatment effect function. \n", "\n", "We can also use the `model.summary` function to get point estimates, p-values and confidence intervals. From the table below, we notice that only the **days_visited_free_pre**, **days_visited_hs_pre** and **os_type_osx** features are statistically significant (the confidence interval doesn't contain $0$, p-value < 0.05) for the treatment effect. "]}, {"cell_type": "code", "execution_count": 16, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "            <td></td>            <th>point_estimate</th> <th>stderr</th>  <th>zstat</th>  <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>days_visited_exp_pre</th>       <td>0.001</td>      <td>0.007</td>  <td>0.133</td>   <td>0.894</td>   <td>-0.01</td>    <td>0.012</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_free_pre</th>      <td>0.286</td>      <td>0.007</td> <td>38.483</td>    <td>0.0</td>    <td>0.274</td>    <td>0.298</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_fs_pre</th>       <td>-0.008</td>      <td>0.007</td> <td>-1.206</td>   <td>0.228</td>  <td>-0.019</td>    <td>0.003</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_hs_pre</th>       <td>-0.189</td>      <td>0.007</td> <td>-28.043</td>   <td>0.0</td>   <td>-0.201</td>   <td>-0.178</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_rs_pre</th>        <td>0.001</td>      <td>0.007</td>  <td>0.13</td>    <td>0.897</td>   <td>-0.01</td>    <td>0.012</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_vrs_pre</th>       <td>-0.0</td>       <td>0.007</td> <td>-0.054</td>   <td>0.957</td>  <td>-0.011</td>    <td>0.011</td> \n", "</tr>\n", "<tr>\n", "  <th>locale_en_US</th>              <td>-0.021</td>      <td>0.113</td> <td>-0.185</td>   <td>0.853</td>  <td>-0.207</td>    <td>0.165</td> \n", "</tr>\n", "<tr>\n", "  <th>revenue_pre</th>                <td>-0.0</td>        <td>0.0</td>  <td>-1.255</td>   <td>0.209</td>   <td>-0.0</td>      <td>0.0</td>  \n", "</tr>\n", "<tr>\n", "  <th>os_type_osx</th>                <td>0.961</td>      <td>0.139</td>  <td>6.931</td>    <td>0.0</td>    <td>0.733</td>    <td>1.189</td> \n", "</tr>\n", "<tr>\n", "  <th>os_type_windows</th>            <td>0.013</td>      <td>0.138</td>  <td>0.091</td>   <td>0.927</td>  <td>-0.215</td>    <td>0.24</td>  \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>0.482</td>      <td>0.27</td>  <td>1.786</td>  <td>0.074</td>   <td>0.038</td>    <td>0.926</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                            Coefficient Results                             \n", "============================================================================\n", "                      point_estimate stderr  zstat  pvalue ci_lower ci_upper\n", "----------------------------------------------------------------------------\n", "days_visited_exp_pre           0.001  0.007   0.133  0.894    -0.01    0.012\n", "days_visited_free_pre          0.286  0.007  38.483    0.0    0.274    0.298\n", "days_visited_fs_pre           -0.008  0.007  -1.206  0.228   -0.019    0.003\n", "days_visited_hs_pre           -0.189  0.007 -28.043    0.0   -0.201   -0.178\n", "days_visited_rs_pre            0.001  0.007    0.13  0.897    -0.01    0.012\n", "days_visited_vrs_pre            -0.0  0.007  -0.054  0.957   -0.011    0.011\n", "locale_en_US                  -0.021  0.113  -0.185  0.853   -0.207    0.165\n", "revenue_pre                     -0.0    0.0  -1.255  0.209     -0.0      0.0\n", "os_type_osx                    0.961  0.139   6.931    0.0    0.733    1.189\n", "os_type_windows                0.013  0.138   0.091  0.927   -0.215     0.24\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept          0.482   0.27 1.786  0.074    0.038    0.926\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["est_dw.summary(feature_names=X_data.columns)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"slideshow": {"slide_type": "skip"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# How close are the predicted treatment effect to the true treatment effects for 1000 users?\n", "plt.scatter(true_customer_TE, est_dw.effect(test_customers), label=\"Predicted vs True treatment effect\")\n", "plt.xlabel(\"True treatment effect\")\n", "plt.ylabel(\"Predicted treatment effect\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Estimate Robustness with <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Random Common Cause\n", "\n", "How robust are our estimates to adding another confounder? We use DoWhy to test this!"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Refute: Add a Random Common Cause\n", "Estimated effect:2.074836810507913\n", "New effect:2.0655457813564473\n", "\n"]}], "source": ["res_random = est_dw.refute_estimate(method_name=\"random_common_cause\", num_simulations=5)\n", "print(res_random)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Unobserved Common Cause\n", "\n", "How robust are our estimates to unobserved confounders? Since we assume we have a valid instrument, adding an unobserved confounder should not affect the estimates much. We use <PERSON><PERSON>hy to test this!"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Refute: Add an Unobserved Common Cause\n", "Estimated effect:2.074836810507913\n", "New effect:2.285224693713481\n", "\n"]}], "source": ["res_unobserved = est_dw.refute_estimate(method_name=\"add_unobserved_common_cause\",\n", "                                       confounders_effect_on_treatment=\"binary_flip\", confounders_effect_on_outcome=\"linear\",\n", "                                       effect_strength_on_treatment=0.05, effect_strength_on_outcome=0.5)\n", "print(res_unobserved)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Replace Treatment with a Random (Placebo) Variable\n", "\n", "What happens our estimates if we replace the treatment variable with noise? Ideally, the average effect would be $0$. We use DoWhy to investigate!"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Refute: Use a Placebo Treatment\n", "Estimated effect:2.074836810507913\n", "New effect:-112.27353652608205\n", "p value:0.045937003218721184\n", "\n"]}], "source": ["res_placebo = est_dw.refute_estimate(method_name=\"placebo_treatment_refuter\", placebo_type=\"permute\", \n", "                                     num_simulations=2)\n", "print(res_placebo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["While the \"New effect\" is not zero, the p-value is greater than 0.05 which means that we cannot reject the null hypothesis that $0$ is under the average treatment effect distribution. Increasing `num_simulations` should produce a \"New effect\" closer to $0$. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove a Random Subset of the Data\n", "\n", "Do we recover similar estimates on subsets of the data? This speaks to the ability of our chosen estimator to generalize well. We use <PERSON><PERSON><PERSON> to investigate this!"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Refute: Use a subset of data\n", "Estimated effect:2.074836810507913\n", "New effect:2.0718850819612706\n", "p value:0.3507294961332309\n", "\n"]}], "source": ["# Removing a random subset of the data\n", "res_subset = est_dw.refute_estimate(method_name=\"data_subset_refuter\", subset_fraction=0.8, \n", "                                    num_simulations=2)\n", "print(res_subset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The \"New effect\" is close to the estimated effect from the original dataset and the p-value is greater than 0.05. Thus, we cannot reject the null hypothesis that the estimated effect is under the average treatment effect distribution. "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Understand Treatment Effects with EconML\n", "\n", "EconML includes interpretability tools to better understand treatment effects. Treatment effects can be complex, but oftentimes we are interested in simple rules that can differentiate between users who respond positively, users who remain neutral and users who respond negatively to the proposed changes.\n", "\n", "The EconML `SingleTreeCateInterpreter` provides interperetability by training a single decision tree on the treatment effects outputted by the any of the EconML estimators. In the figure below we can see in dark red users who respond negatively to the membership program and in dark green users who respond positively."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************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", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\n", "intrp.interpret(est_dw, test_customers)\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=X_data.columns, fontsize=12)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Make Policy Decisions with EconML\n", "\n", "Interventions usually have a cost: incetivizing a user to become a member can be costly (e.g by offering a discount). Thus, we would like to know what customers to target to maximize the profit from their increased engagement. This is the **treatment policy**. \n", "\n", "The EconML library includes policy interpretability tools such as `SingleTreePolicyInterpreter` that take in a treatment cost and the treatment effects to learn simple rules about which customers to target profitably. "]}, {"cell_type": "code", "execution_count": 23, "metadata": {"slideshow": {"slide_type": "fragment"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["intrp = SingleTreePolicyInterpreter(risk_level=0.05, max_depth=2, min_samples_leaf=10)\n", "intrp.interpret(est_dw, test_customers, sample_treatment_costs=0.2)\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=X_data.columns, fontsize=12)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Conclusions\n", "\n", "In this notebook, we have demonstrated the power of using EconML and DoWhy to:\n", "\n", "* Get valid causal insights in seemingly impossible scenarios\n", "* Test causal assumptions and investigate the robustness of the resulting estimates\n", "* Intepret individual-level treatment effects\n", "* Build policies around the learned effects\n", "\n", "To learn more about what EconML can do for you, visit the [website](https://aka.ms/econml), [GitHub page](https://github.com/py-why/EconML) or [docummentation](https://econml.azurewebsites.net/).\n", "\n", "To learn more about what <PERSON><PERSON><PERSON> can do for you, visit the [GitHub page](https://github.com/py-why/dowhy) or [documentation](https://www.pywhy.org/dowhy/)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}