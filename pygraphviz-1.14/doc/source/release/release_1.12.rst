pygraphviz 1.12
===============
We're happy to announce the release of pygraphviz 1.12!

Enhancements
------------

- Add Python 3.12 support (`#486 <https://github.com/pygraphviz/pygraphviz/pull/486>`_).

Bug Fixes
---------

- Copy edges keys when copying a graph (`#473 <https://github.com/pygraphviz/pygraphviz/pull/473>`_).
- Windows Manual Installation Fix (`#468 <https://github.com/pygraphviz/pygraphviz/pull/468>`_).

Documentation
-------------

- Windows Manual Installation Fix (`#468 <https://github.com/pygraphviz/pygraphviz/pull/468>`_).
- Use pydata-sphinx-theme (`#494 <https://github.com/pygraphviz/pygraphviz/pull/494>`_).
- Add version switcher (`#495 <https://github.com/pygraphviz/pygraphviz/pull/495>`_).
- Move release notes (`#506 <https://github.com/pygraphviz/pygraphviz/pull/506>`_).

Maintenance
-----------

- Drop Python 3.8 support per SPEC 0 (`#465 <https://github.com/pygraphviz/pygraphviz/pull/465>`_).
- Update release process (`#464 <https://github.com/pygraphviz/pygraphviz/pull/464>`_).
- Test on Python 3.12.0-beta.2 (`#466 <https://github.com/pygraphviz/pygraphviz/pull/466>`_).
- Update pre-commit (`#467 <https://github.com/pygraphviz/pygraphviz/pull/467>`_).
- Use label-check and attach-next-milestone-action (`#469 <https://github.com/pygraphviz/pygraphviz/pull/469>`_).
- Drop Python 3.9 support (`#487 <https://github.com/pygraphviz/pygraphviz/pull/487>`_).
- Update documentation building requirements (`#488 <https://github.com/pygraphviz/pygraphviz/pull/488>`_).
- Add pyproject.toml (`#471 <https://github.com/pygraphviz/pygraphviz/pull/471>`_).
- Use dependabot (`#496 <https://github.com/pygraphviz/pygraphviz/pull/496>`_).
- Bump actions/setup-python from 3 to 5 (`#501 <https://github.com/pygraphviz/pygraphviz/pull/501>`_).
- Bump scientific-python/attach-next-milestone-action from f94a5235518d4d34911c41e19d780b8e79d42238 to bc07be829f693829263e57d5e8489f4e57d3d420 (`#500 <https://github.com/pygraphviz/pygraphviz/pull/500>`_).
- Bump actions/checkout from 3 to 4 (`#498 <https://github.com/pygraphviz/pygraphviz/pull/498>`_).
- Bump conda-incubator/setup-miniconda from 2 to 3 (`#499 <https://github.com/pygraphviz/pygraphviz/pull/499>`_).
- Bump webfactory/ssh-agent from 0.5.4 to 0.8.0 (`#497 <https://github.com/pygraphviz/pygraphviz/pull/497>`_).
- Update the year (`#502 <https://github.com/pygraphviz/pygraphviz/pull/502>`_).
- Update license information (`#504 <https://github.com/pygraphviz/pygraphviz/pull/504>`_).
- Stop building pdf of the docs (`#503 <https://github.com/pygraphviz/pygraphviz/pull/503>`_).
- Use trusted publisher for PyPI uploads (`#505 <https://github.com/pygraphviz/pygraphviz/pull/505>`_).

Other
-----

- Update INSTALL.txt (`#484 <https://github.com/pygraphviz/pygraphviz/pull/484>`_).

Contributors
------------

6 <USER> <GROUP> to this release (alphabetically):

- `@dependabot[bot] <https://github.com/apps/dependabot>`_
- Eli Shalom (`@elishapiiro <https://github.com/elishapiiro>`_)
- Jarrod Millman (`@jarrodmillman <https://github.com/jarrodmillman>`_)
- Javier Rodrigo López (`@Javiolonchelo <https://github.com/Javiolonchelo>`_)
- Moamen Abdelrazek (`@moemen95 <https://github.com/moemen95>`_)
- Ross Barnowski (`@rossbar <https://github.com/rossbar>`_)

3 reviewers added to this release (alphabetically):

- Dan Schult (`@dschult <https://github.com/dschult>`_)
- Jarrod Millman (`@jarrodmillman <https://github.com/jarrodmillman>`_)
- Ross Barnowski (`@rossbar <https://github.com/rossbar>`_)

_These lists are automatically generated, and may not be complete or may contain duplicates._
