{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Deep IV: Use Case and Examples\n", "\n", "Deep IV uses deep neural networks in a two-stage instrumental variable (IV) estimation of causal effects, as described in [this ICML publication](http://proceedings.mlr.press/v70/hartford17a/hartford17a.pdf) or in the `econml` [specification](https://econml.azurewebsites.net/spec/estimation/deepiv.html).  In the EconML SDK, we have implemented Deep IV estimation on top of the Keras framework for building and training neural networks.  In this notebook, we'll demonstrate how to use the SDK to apply Deep IV to synthetic data.\n", "\n", "### Data\n", "\n", "Deep IV works in settings where we have several different types of observations:\n", "* Covariates, which we will denote with `X`\n", "* Instruments, which we will denote with `Z`\n", "* Treatments, which we will denote with `T`\n", "* Responses, which we will denote with `Y`\n", "\n", "The main requirement is that `Z` is a set of valid instruments; in particular `Z` should affect the responses `Y` only through the treatments `T`.  We assume that `Y` is an arbitrary function of `T` and `X`, plus an additive error term, and that `T` is an arbitrary function of `Z` and `X`.  Deep IV then allows us to estimate `Y` given `T` and `X`.\n", "\n", "### Estimation\n", "\n", "To do this, the Deep IV estimator uses a two-stage approach that involves solving two subproblems:\n", "1. It estimates the *distribution* of the treatment `T` given `Z` and `X`, using a mixture density network.\n", "2. It estimates the dependence of the response `Y` on `T` and `X`.\n", "\n", "Both of these estimates are performed using neural networks.  See the paper for a more complete description of the setup and estimation approach.\n", "\n", "### Using the SDK\n", "\n", "In the `econml` package, our Deep IV estimator is built on top of the Keras framework; we support either the Tensorflow or the Theano backends.  There are three steps to using the `DeepIV`:\n", "\n", "1. Construct an instance.  \n", "    * The `m` and `h` arguments to the initializer specify deep neural network models for estimating `T` and `Y` as described above.  They are each *functions* that take two Keras inputs and return a Keras model (the inputs are `z` and `x` in the case of `m` and the output's shape should match `t`'s; the inputs are `t` and `x` in the case of `h` and the output's shape should match `y`'s).  Note that the `h` function will be called multiple times, but should reuse the same weights - see below for a concrete example of how to achieve this using the Keras API.\n", "    * The `n_samples`, `use_upper_bound_loss`, and `n_gradient_samples` arguments together determine how the loss for the response model will be computed.\n", "        * If `use_upper_bound_loss` is `False` and `n_gradient_samples` is zero, then `n_samples` samples will be averaged to approximate the response - this will provide an unbiased estimate of the correct loss only in the limit as the number of samples goes to infinity.\n", "        * If `use_upper_bound_loss` is `False` and `n_gradient_samples` is nonzero, then we will average `n_samples` samples to approximate the response a first time and average `n_gradient_samples` samples to approximate it a second time - combining these allows us to provide an unbiased estimate of the true loss.\n", "        * If `use_upper_bound_loss` is `True`, then `n_gradient_samples` must be `0`; `n_samples` samples will be used to get an unbiased estimate of an upper bound of the true loss - this is equivalent to adding a regularization term penalizing the variance of the response model (see the `econml` specification linked above for a derivation of this fact).\n", "2. Call `fit` with training samples of `Y`, `T`, `X`, and `Z`; this will train both sub-models.\n", "3. Call `effect` or `predict` depending on what output you want.  `effect` calculates the difference in outcomes based on the features and two different treatments, while `predict` predicts the outcome based on a single treatment.\n", "\n", "The remainder of this notebook will walk through a concete example."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using TensorFlow backend.\n"]}], "source": ["from econml.iv.nnet import DeepIV\n", "import keras\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Synthetic data\n", "\n", "To demonstrate the Deep IV approach, we'll construct a synthetic dataset obeying the requirements set out above.  In this case, we'll take `X`, `Z`, `T` to come from the following distribution: "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["n = 5000\n", "\n", "# Initialize exogenous variables; normal errors, uniformly distributed covariates and instruments\n", "e = np.random.normal(size=(n,))\n", "x = np.random.uniform(low=0.0, high=10.0, size=(n,))\n", "z = np.random.uniform(low=0.0, high=10.0, size=(n,))\n", "\n", "# Initialize treatment variable\n", "t = np.sqrt((x+2) * z) + e\n", "\n", "# Show the marginal distribution of t\n", "plt.hist(t)\n", "plt.xlabel(\"t\")\n", "plt.show()\n", "\n", "plt.scatter(z[x < 1], t[x < 1], label='low X')\n", "plt.scatter(z[(x > 4.5) * (x < 5.5)], t[(x > 4.5) * (x < 5.5)], label='moderate X')\n", "plt.scatter(z[x > 9], t[x > 9], label='high X')\n", "plt.legend()\n", "plt.xlabel(\"z\")\n", "plt.ylabel(\"t\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here, we'll imagine that `Z` and `X` are causally affecting `T`; as you can see in the plot above, low or high values of `<PERSON>` drive moderate values of `T` and moderate values of `Z` cause `T` to have a bi-modal distribution when `X` is high, but a unimodal distribution centered on 0 when `X` is low.  The instrument is positively correlated with the treatment and treatments tend to be bigger at high values of x. The instrument has higher power at higher values of x "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Outcome equation \n", "y = t*t / 10 - x*t / 10 + e\n", "\n", "# The endogeneity problem is clear, the latent error enters both treatment and outcome equally\n", "plt.scatter(t,z, label ='raw data')\n", "tticks = np.arange(-2,12)\n", "yticks2 = tticks*tticks/10 - 0.2 * tticks\n", "yticks5 = tticks*tticks/10 - 0.5 * tticks\n", "yticks8 = tticks*tticks/10 - 0.8 * tticks\n", "plt.plot(tticks,yticks2, 'r--', label = 'truth, x=2')\n", "plt.plot(tticks,yticks5, 'g--', label = 'truth, x=5')\n", "plt.plot(tticks,yticks8, 'y--', label = 'truth, x=8')\n", "plt.xlabel(\"t\")\n", "plt.ylabel(\"y\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`Y` is a non-linear function of `T` and `X` with no direct dependence on `Z` plus additive noise (as required).  We want to estimate the effect of particular `T` and `X` values on `Y`.\n", "\n", "The plot makes it clear that looking at the raw data is highly misleading as to the treatment effect. Moreover the treatment effects are both non-linear and heterogeneous in x, so this is a hard problem!\n", "\n", "## Defining the neural network models\n", "\n", "Now we'll define simple treatment and response models using the Keras `Sequential` model built up of a series of layers.  Each model will have an `input_shape` of 2 (to match the sums of the dimensions of `X` plus `Z` in the treatment case and `T` plus `X` in the response case)."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["treatment_model = keras.Sequential([keras.layers.Dense(128, activation='relu', input_shape=(2,)),\n", "                                    keras.layers.Dropout(0.17),\n", "                                    keras.layers.Dense(64, activation='relu'),\n", "                                    keras.layers.Dropout(0.17),\n", "                                    keras.layers.Dense(32, activation='relu'),\n", "                                    keras.layers.Dropout(0.17)])\n", "\n", "response_model = keras.Sequential([keras.layers.Dense(128, activation='relu', input_shape=(2,)),\n", "                                   keras.layers.Dropout(0.17),\n", "                                   keras.layers.Dense(64, activation='relu'),\n", "                                   keras.layers.Dropout(0.17),\n", "                                   keras.layers.Dense(32, activation='relu'),\n", "                                   keras.layers.Dropout(0.17),\n", "                                   keras.layers.Dense(1)])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we'll instantiate the `DeepIV` class using these models.  Defining the response model *outside* of the lambda passed into constructor is important, because (depending on the settings for the loss) it can be used multiple times in the second stage and we want the same weights to be used every time."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["keras_fit_options = { \"epochs\": 30,\n", "                      \"validation_split\": 0.1,\n", "                      \"callbacks\": [keras.callbacks.EarlyStopping(patience=2, restore_best_weights=True)]}\n", "\n", "deepIvEst = DeepIV(n_components = 10, # number of gaussians in our mixture density network\n", "                   m = lambda z, x : treatment_model(keras.layers.concatenate([z,x])), # treatment model\n", "                   h = lambda t, x : response_model(keras.layers.concatenate([t,x])),  # response model\n", "                   n_samples = 1, # number of samples to use to estimate the response\n", "                   use_upper_bound_loss = False, # whether to use an approximation to the true loss\n", "                   n_gradient_samples = 1, # number of samples to use in second estimate of the response (to make loss estimate unbiased)\n", "                   optimizer='adam', # Keras optimizer to use for training - see https://keras.io/optimizers/ \n", "                   first_stage_options = keras_fit_options, # options for training treatment model\n", "                   second_stage_options = keras_fit_options) # options for training response model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fitting and predicting using the model\n", "Now we can fit our model to the data:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train on 4500 samples, validate on 500 samples\n", "Epoch 1/30\n", "4500/4500 [==============================] - 2s 380us/step - loss: 1.6199 - val_loss: 0.8568\n", "Epoch 2/30\n", "4500/4500 [==============================] - 1s 116us/step - loss: 1.1357 - val_loss: 0.6315\n", "Epoch 3/30\n", "4500/4500 [==============================] - 1s 117us/step - loss: 0.9836 - val_loss: 0.7512\n", "Epoch 4/30\n", "4500/4500 [==============================] - 1s 118us/step - loss: 0.8963 - val_loss: 0.7189\n", "Train on 4500 samples, validate on 500 samples\n", "Epoch 1/30\n", "4500/4500 [==============================] - 3s 774us/step - loss: 4.8558 - val_loss: 3.0255\n", "Epoch 2/30\n", "4500/4500 [==============================] - 1s 183us/step - loss: 5.1271 - val_loss: 2.9335\n", "Epoch 3/30\n", "4500/4500 [==============================] - 1s 187us/step - loss: 5.0416 - val_loss: 3.1960\n", "Epoch 4/30\n", "4500/4500 [==============================] - 1s 198us/step - loss: 5.3328 - val_loss: 3.0213\n"]}], "source": ["deepIvEst.fit(Y=y,T=t,X=x,Z=z)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now we can create a new set of data and see whether our predicted effect matches the true effect `T*T-X*X`:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************/HzCz2mvmc5B90ccFqvoCnsehzEDo+MWvIEhWIwWHr5mKCaDIMxixGaeRrjFhIwPh4QIZGs5zSVbsTftIbepiCH1e6F1A+xcCjsh3BTGQ/G5al7CmQddedA1GGKzVW92IDVCfq9y9zSXqizg5hJoPAiNh45N8AOYwiBxKAy9DpJHqN578vCzT/6SUs0tbHpWCcnRkUBCgfpczRnT4e3gnUPvsLRoKTXOGrKisnhk0iPMz5nfa4GaVk8r6yrWcUPeDZiNZu4ovIPBjsFMSpmkEygvMANmhHC+6Ap08WHph7y27zWKW4uJs8Vxa/6t3Jx/c69h71fiaVM18RuKVK+34QA0HYLWI2q1rKNYo1TSU3QGONLBkabKIESlqUiniOT+UykzGAR3M3TUQHuNKvrWVqm21gqV8NVe1fv5LZEQn6t6/glDVAOdOBSis86P28bnViOBTc9D3W6wRcOVP1UjAs1ZUdZWxuv7X2dZ8TJcfhfjksaxqHAR09On9+rp1zpreW3fa7x18C3cfjfvLXyPbIcOle4LtMuoj5FSsqF6A38t+iufVX2G2WBmbtZcbi+4nREJI87+g/1d0HwYWg4f27dWKKFoPXIsMuZ4rFGqpLI9AezxEBZzbLNFgdWhImesEWAOV71ok617s6jCawazCrn8ciMbDKoGOuiHgFdl7Qa6VEPqc6tyzl0d4O1UkTeeViV27lbV+LuawNmofPqd9SrH43iEASJTVTZvTKYK743OVKOjuBz1TH3ZS3zrblWMLrEQJn4XRtzSfwT2IiIog6yvWs/r+1/nX1X/wmQwMTdrLosKF1EYV9jr2kZ3I09seYKVh1f2zM99Z/h3yIvJC5H1lz5aEC4gh9sO8/r+13mv+D1cfhfD4oZxa/6tzB08lzDTeV75ytOuQh/bK1U2bGet2jsbwNUIziblU3c3g99zDl90dKblLLE6wB4H4XEQFgsRiWqzJ0JUiorKikxW+wsRNXK0JPWBlXBwJSx8To04qneoSLGsqQPLNXeeaPW0sqx4GX878DcqOytPOWKWUtLkaSI+LB6Xz8WC9xYwO2M2iwoX6ZXKLgBaEEKA0+fk/ZL3eWP/G5S0lRBpiWRBzgJuGnITOdE5F94gn1sJSFe72ns7VY/e61QjEb9H7QNeNQII+tVo4PjfhMEIwqhGDkararyNFjXSMIepvTUCLBFqb4tWo5GLpd586xFY/UuVeOZqVMfSxsHc30K6zm49G6SUbK/fzt8P/p2Pyj7CG/QyNnEstxfczqyMWb3CQn1BH6vLVvPa3tfwBDwsW7AMgzDgD/oxGQbMFGbI0YIQQqSUbK3bypsH32RN+Rr8QT+jE0ZzQ94NzMmaQ7hZuyTOK8Ggmnyu3KJyD6q2QuF1amLY3QovTFfhpplXQN7VIc8y7680e5p5v+R93jn0DqVtpUSYI7g2+1puzr+ZITFDel3b1tXG24feZmnRUupd9Qx2DOauwrtYkLtAC0EI0IJwkXD0P9FbB9+irL0Mu9nOnKw5LMxdyOiE0TqK4kzxe1UEks+l1hWQEp4YqiaqQY1UUsfA6G+qTXNO+IN+1let572S9/i44mP8QT+jEkZxQ94NzM2ae0Ln5mhW8arDq3jo04eYmDyRO4fdyZS0KTp0NIRoQbjIkFKyrX4by4qXsbpsNW6/m8yoTK7NvpZrs69lUKQukdxDMKAmoyMS1fsNf1JlQ5qK1SR70K9CQ+9dp86vf1pNoA8aB/FDLh53VT/mQPMBlpcs54PSD2jyNBFri2Ve9jxuyL2B3JjcXtf6g34+rfyUN/a/wYSUCdwz4h58QR+lraW9Ezk1IUMLwkWMy+diddlqlpcsZ0udep6xiWOZlz2PqzOvvvSLdQV8KvP36DoBe5epKqMt5Sr0tLVCRRf9pEidf+tuqC9SUUfxed2hp4U6P+A8U9NZw8qylawoXcGhlkOYhImpg6ayMHchU9OmnlAyotHdyLuH3uXNg29S66wlKTyJe0bcw20Ft4XoCTSnQgtCP6Gqs4oPSj9gRekKDrcdxmQwcUXqFczJmsOM9BlEWPpB0TQplQvH3aJ69q7ucNOh16mw1r3vwq6/K7dORw101Kr7flWvzn/wU9jz9olhp2Pv1JE/fUyju5E15WtYdXgV2+q3ATAyYSTzs+czJ2sOMbaYU977w3U/ZF3FOiYmT+S2gtuYnj5dzw9cpGhB6GdIKdnfvJ8PSj9gVdkq6lx1WAwWpqRN4aqsq5g2aNoJq0SdN3we1WP3dqqcgq5Ole+QdaUKHa3appK3uo6LWOpqh5teUQ34xmdUJM+X+dEeVRl00wuw9S/HwkyPJteNvBXMNjUprGsDXTAa3Y2sLV/LR+UfsaVuC0EZJDc6l2sGX8PcwXNJj0w/4Z5Obycry1by9wN/5/Hpj5MemU5xSzEGg0Enk/UDdOmKix0p1WYwQDCAcDYy1BTJ0Jwb+V9Z89nZtJfVjTv5qPpfrKtYh1kYmeTIY5Yjn2kRg4kTJlWsLbFAZf1ueUWFkQa8KtzU74Fxd0PGRBVr/+FDqt6/z60EwOeEG1+C3NlqQZi/fetEG+9aAYOnqkncbUtUApw1Um02h/L1A2RMgtmPQli0yjs4mntwdEWxifep7VRoMehzKjoq+PjIx6w9spbt9duRSLKisrhv5H3MyZxzwrwAgMfvYeXhlfzjyD/YWL0RX9DHkJghNHuaSY9MP+k9mv7NwBkhvDJX1cYxmLpj64VKRlr4Z3X+L9eqXrIwqA0BOTNgTvcibq98Q/WaJYBU8foF82Dmr9T5p8cq33jQr7JxgwG47C6Y9Z/gdcH/y1bHZbC7IZVw5c9g5sMqg/f3J8nSnP0owSt+wK7ilaxZ+T3+ER5OtdmEkJLRXV1MS5/JtEkPkeN2Il64UtX3MVnU3mxTi74PvVYlZK1+WOUNmGxqb7HDZYshaZgqHVH6ybFcAkt3VnNMll4Yvp8SCAbY07SHf1b8k48rPqa4tRiAITFDmJ05m9kZs8mNzu2JcpNSUtZexs6GnURbo5mePh2nz8nk1yeTHJ7M7MzZXJV5FaMSRunIuH6IHiF8mewZkJCvGuOjDXJCwbHz8UNUz/ZoYy/lsSgXUG4Or1O9FgYlKBHHxbOnT1D7nkQuoyq6BiqRa8I9x44Lg3qdOUmdt0bBvMdV+QijWe1NFkgchkEYGJ01i9EL3+SnRjMH3XWsa9jGx3Vf8GTDBp5cfj1pEWlMmfsQU9OmMj55/Il5DknD4M5lp/7bOAbBmDvO5q+quYho9bSyoXoDn1V9xmdVn9HS1YJBGBibOJaHxj3EjIwZJ7iDluxdwqaaTexu3E1rl6rKO23QNKanT8dutrNswTKyorK0CAwQBs4I4RKkzlnHp1Wf8mnFp2yq3YTb78ZsMDM2aSyTUiYxOXUy+bH5Ov77EsUX8LGzYScbazbyefXn7G7cjUTisDqYkjaFK9Ou5Iq0K7Aarexv3s/uxt3sbtyN2+/mjzP/CMB9H91Hg7uBEfEjGJUwilEJo8iOzta/mUsMPak8wPAGvGyt28pnVZ+xsWYjh1oOARBtjWZ88ngmJE9gfPJ4sh3ZurfXT/EH/exv3s/m2s1srt3MtrptuP1ujMLIsPhhTEmdwqTUSdjN9h530JNbn2TJ3iX4pR+ApPAkRieO5ndX/g4hBIFgAKPO27jk0YIwwGlwNfB5zedsqtnE5trN1DhVJm+MNYbLki5jbNJYxiSOIT82H7NBL0l4MeL2u9nTuIcd9TvYWreV7fXbcfldAOQ4chifPJ4hsUPwB/3UdNawt2kve5v24vQ5WXPTGpLtyawtX8vepr0Mjx/OiPgRJIR/xXKhmksSLQiaHqSUVHZUsqVuC1vqtrC1bitVnWqxmTBTGMPihjEyYSQj40cyImEEieGJX/GJmvONlJKKjgp2Ne5id8NudjXsYn/z/l49+4TwBCwGC26/m/+c9J8Mjx/Oh6Uf8vN//RyTwUR+TD4j4kcwPH44MzNm9l2YsqbfoQVBc1rqnHVsb9jOjvod7GrYRVFzEf6ganwSwhIYFjeMwrhCCmILKIgtINmerF1N54mgDHKk/Qj7W/azr3Ef2+u3c7DlYE/v3yiMZDuymZY+jUhzJH/Y9oeee5PCk8iKyuLBMQ8yOnE0Hd4OWjwtpEak6qQwzSnRgqA5I7oCXRQ1FbG3aS/7mvaxp3EPh9sOI7vXRIiyRJEXk8eQmCHkxeSR7cgmx5Fz6ZfZOAeklNS56thQtYG9TXspbi2msqOSJk8Tge6FgozC2PMawCAMpNhTeGD0A8zPmU+Ht4PPaz4nIzKDjKiM87++hmZAoAVBc864fC4Othxkf/N+DrQc4GDLQYpbint6sgCxtliyorLIjMokMyqT9Mj0nq1flN04C6SU+IN+zEYzUkpWHl7JgZYDHGk/QlVnFY3uRgzCgNPnpNPXecL9YaYwpqZN5Z4R95AVlcWKwytIj0wnLSKNZHuyntPRnHe0IGj6hKAMUuOsobS1lNI2tZW1lVHeXk6Tp6nXtVGWKFIjUkmxp5BsTyYpPIkkexIJYQnEh8UTHxZPlCUq5K6oWmctTZ4mOrwdtHe10+5tJ8IcwdVZV9PiaeG/v/hvSltLaetqo8PXgdvvxmF1EGeLo7qzupdAApgMJlLCU5icNpmc6BxaPC0UxBaQH5NPoj1RN/iaC44WBM0Fp9PbSUVHBRUdFVR2VlLdWU11ZzU1zhrqnHV0+E5cD9okTMTYYoixxeCwOnBYHDisDiLMEdgtdiLMEYSbwgkzhWEz2bAarBiEgSBBYm2xGA1GqjuqqXPX4fa5cfqdOH1OkLAgdwEBGeCN/W+wr2kfLr8Lt9+N2+8mwhzBosJFOH1O3j70NvWu+l52GYURiSQogyfYbDVaibPFURBbQEpECjajjSxHFgUxBWQ5srCZbH32N9ZozgYtCJoLjj/oxxvw4g146Qp0ERcWh8lgosHVQGVnJa2eVmpdtTS5m2hyNzEochAd3g7lbuk4gsfnwRPw0BXowhf0nbQx7gssBgthpjAiLZFEWiKJs8WREH5sFBMfFk9cWByJ4YkkhidiNVoviF0azflCl674Eh6/B6MwYjKYQu6iOFOklARkQPmupZ+gDBKQAaxGK1ajFV/AR6O7Eb/04w+qzRf0kWpPJdoWTVtXG7sadhGQgV7nxyePJ9mezJH2I3xU/hHegBdPwIPHr7bFwxeT7cjm85rPeWHXC3QFuno1+E/PfJohMUN4++DbPPb5Yyc04CuuX0FmVCYflH7A41sfP+G51t68lsTwRP68489sqN6AzWjraZjDzeH8Zc5fkEiWlyzni9ovMBvMWIwWzAZzT5lwiaTaWY3b58ZmtKlRhNGK1WTFZDBhFEYsRgsWgwWL0YLNZOu5LsIcQZgpTCdmaTTdDBhBuG3FbZS0lQDKHSCEYErqFP44S6XwL1i2gAZXA0IIDMKAQDAjYwaPTn4UgIXLFuL0OxGInrT+OVlz+PFlPwbghuU3EOiu/nk0Mmd+9nzuHXkvXYEublx+I1IqF4REIqXk9oLbWTx8Mc2eZhYuW0hABgjIgGrwgwG+P+b7LB6+mIqOCua9O++EZ3p44sPcVqCe6+b3bz7h/G+m/Ib5OfMpbi3me2u/d8L5J6c/SbI9mfL2cp7a9hSg3CFHG82FuQvBoa4NBAPYTXZirbGYjWbVeBtVxEt+bD53D78bi9GC1WjtabSjrSoCaXbmbIbEDFGNtcmKzaga7VhbLAD/NurfuH/U/acU6kWFi1hUuOi0/74ajebcGTCCsKhwEU2epp4eclAGyYzK7Dk/N2subd62Xo12Qeyx4ncTUibg9rt7esFBGexVKCzHkdOrhyyE6MkKNWCgMLYQhAorNGBACEFaZBoANqONq7OuxiiMGIRB7Q0GhsUPA8BhdfDA6Ad6nxcGxiSOASDFnsKjkx/t6RGbDWbMBjND44YCkB+Tz1+v+SsmgwmTMGEymLAYLMSFxQEwKXUSW+7YgtlgPmkNm8tTLufylMtP+bcdHj+c4fGnXr1sUOSg0y4RquvmaDQXB3oOQaPRaC5xvu4cQki6ZkKI3wkh9gshdgkh3hVC6OwmjUajCTGhGquvAYZLKUcCB4FfhMgOjUaj0XQTEkGQUn4kZXfVLvgcOLWDWaPRaDQXhIthNu87wMpQG6HRaDQDnT6LMhJC/ANIPsmph6WU73Vf8zDgB5ae5nPuA+4DyMjI6ANLNRqNRgN9KAhSytmnOy+EuAu4FpglTxPqJKV8AXgBVJTReTVSo9FoND2EJA9BCDEX+DkwTUrp+qrrNRqNRtP3hGoO4U9AJLBGCLFDCPFciOzQaDQaTTf9KjFNCNEAlJ/l7fFA43k0pz+gn3lgoJ95YHAuz5wppfzKBbX7lSCcC0KILV8nU+9SQj/zwEA/88DgQjzzxRB2qtFoNJqLAC0IGo1GowEGliC8EGoDQoB+5oGBfuaBQZ8/84CZQ9BoNBrN6RlIIwSNRqPRnIYBIQhCiLlCiANCiGIhxL+H2p6+RgiRLoT4WAhRJITYK4T4YahtuhAIIYxCiO1CiBWhtuVCIISIFkK81V1K8oQNUAAAA49JREFUvkgIMSnUNvU1Qogfd/+m9wghXhdC2EJt0/lGCPGKEKJeCLHnuGOxQog1QohD3fuYvvjuS14QhBBG4BngG0AhcLsQojC0VvU5fuAnUsqhwOXAAwPgmQF+CBSF2ogLyFPAKillATCKS/zZhRBpwA+AcVLK4YARuC20VvUJfwHmfunYvwNrpZR5wNru9+edS14QgAlAsZSyVErpBd4AFoTYpj5FSlkjpdzW/boD1VCkhdaqvkUIMQiYB7wUalsuBEKIKOBK4GUAKaVXStkaWqsuCCYgTAhhAsKB6hDbc96RUn4KNH/p8AJgSffrJcDCvvjugSAIaUDFce8rucQbx+MRQmQBY4BNobWkz3kS+BkQ/KoLLxGygQbg1W432UtCCHuojepLpJRVwO+BI0AN0Cal/Ci0Vl0wkqSUNaA6fEBiX3zJQBAEcZJjAyK0SggRAbwN/EhK2R5qe/oKIcS1QL2UcmuobbmAmICxwLNSyjGAkz5yI1wsdPvNFwCDgVTALoS4I7RWXVoMBEGoBNKPez+IS3CY+WWEEGaUGCyVUr4Tanv6mCuA64QQZSiX4EwhxF9Da1KfUwlUSimPjvzeQgnEpcxs4LCUskFK6QPeASaH2KYLRZ0QIgWge1/fF18yEAThCyBPCDFYCGFBTUItD7FNfYoQQqB8y0VSyidCbU9fI6X8hZRykJQyC/Xvu05KeUn3HKWUtUCFECK/+9AsYF8ITboQHAEuF0KEd//GZ3GJT6Qfx3Lgru7XdwHv9cWXhGQ9hAuJlNIvhHgQWI2KSnhFSrk3xGb1NVcAi4DdQogd3cd+KaX8MIQ2ac4/3weWdnd0SoFvh9iePkVKuUkI8RawDRVJt51LMGNZCPE6MB2IF0JUAo8AvwXeFELcjRLGm/vku3Wmskaj0WhgYLiMNBqNRvM10IKg0Wg0GkALgkaj0Wi60YKg0Wg0GkALgkaj0Wi60YKg0Zwj3VVHvxdqOzSac0ULgkZz7kQDWhA0/R4tCBrNufNbIEcIsUMI8btQG6PRnC06MU2jOUe6K8qu6K7Rr9H0W/QIQaPRaDSAFgSNRqPRdKMFQaM5dzqAyFAbodGcK1oQNJpzRErZBKzvXvhdTypr+i16Ulmj0Wg0gB4haDQajaYbLQgajUajAbQgaDQajaYbLQgajUajAbQgaDQajaYbLQgajUajAbQgaDQajaYbLQgajUajAeB/APRWMTsLCg8zAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["n_test = 500\n", "for i, x in enumerate([2, 5, 8]):\n", "    t = np.linspace(0,10,num = 100)\n", "    y_true = t*t / 10 - x*t/10\n", "    y_pred = deepIvEst.predict(t, np.full_like(t, x))\n", "    plt.plot(t, y_true, label='true y, x={0}'.format(x),color='C'+str(i))\n", "    plt.plot(t, y_pred, label='pred y, x={0}'.format(x),color='C'+str(i),ls='--')\n", "plt.xlabel('t')\n", "plt.ylabel('y')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can see that despite the fact that the response surface varies with x, our model was able to fit the data reasonably well. Where is does worst is where the instrument has the least power, which is in the low x case.  There it fits a straight line rather than a quadratic, which suggests that the regularization at least is perfoming well.  "]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}}, "nbformat": 4, "nbformat_minor": 2}