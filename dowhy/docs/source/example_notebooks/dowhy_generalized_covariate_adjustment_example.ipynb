{"cells": [{"cell_type": "markdown", "id": "09e62260-c006-4749-a13e-f4abe935f0a4", "metadata": {}, "source": ["# Generalized Adjustment Criterion Example Notebook\n", "\n", "The backdoor criterion, while very useful, is not complete, e.g. there are some valid adjustment sets which do not satisfy the backdoor criterion. This is pointed out in <PERSON><PERSON><PERSON><PERSON> et al. (2010), where figure 1(b) depicts a very simple such example, see below. "]}, {"cell_type": "code", "execution_count": 1, "id": "85e0bb8e-0a2e-4754-9f84-df62d5b1f651", "metadata": {}, "outputs": [], "source": ["from dowhy import CausalModel\n", "import pandas as pd\n", "from IPython.display import Image, display"]}, {"cell_type": "markdown", "id": "748f6533-a470-4449-ae58-9306fb69fc55", "metadata": {}, "source": ["## Figure 1(b), <PERSON><PERSON><PERSON><PERSON> et al. (2010)"]}, {"cell_type": "code", "execution_count": 4, "id": "edcf5392-3203-48ca-be87-4fd48579d397", "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************************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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Figure 1(b):\n"]}, {"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Random data\n", "treatment = \"X\"\n", "outcome = \"Y\"\n", "variables = [\"Z\"]\n", "causal_graph = \"digraph{Z;X;Y; X->Z;X->Y}\"\n", "columns = list(treatment) + list(outcome) + list(variables)\n", "df = pd.DataFrame(columns=columns)\n", "\n", "# Causal Model Initialization\n", "causal_model = CausalModel(df, treatment, outcome, graph=causal_graph)\n", "\n", "# View graph\n", "causal_model.view_model()\n", "print(\"Figure 1(b):\")\n", "display(Image(filename=\"causal_model.png\"))"]}, {"cell_type": "markdown", "id": "660e4129-76af-4344-a661-17cdc50fb6ee", "metadata": {}, "source": ["The backdoor criterion requires the adjustment set **Z** contain no descendents of the treatment set **X**, so the set {Z} clearly does not meet the backdoor criterion. However, {Z} is a valid adjustment set since\n", "\n", "$$P(\\mathbf{y}|do(\\mathbf{x})) = \\sum_z P(\\mathbf{y}|\\mathbf{x},z)P(z).$$\n", "\n", "In this example, the empty set is a valid backdoor set though, so the backdoor criterion still identifies a valid adjustment set. Next, we will use an example from <PERSON> et al. (2018) which illustrates a case when the backdoor criterion is unable to identify any adjustment sets, and then we will  use <PERSON><PERSON><PERSON>'s implementation of the generalized adjustment criterion to identify an adjustment set instead. "]}, {"cell_type": "markdown", "id": "daa4a57a-1a7d-47aa-8439-43c59dd77064", "metadata": {}, "source": ["## Example 8, <PERSON><PERSON> et al. (2018)\n", "\n", "<PERSON><PERSON><PERSON> uses the approach laid out in <PERSON><PERSON> et al. (2018) Definition 4 + Theorem 7, which gives a generalized adjustment criterion. Consider the following graph given in Example 8 of the paper."]}, {"cell_type": "code", "execution_count": 6, "id": "73ba1ba6-d46d-4af4-a838-be829511c094", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Projects\\dowhy\\dowhy\\causal_model.py:583: UserWarning: 1 variables are assumed unobserved because they are not in the dataset. Configure the logging level to `logging.WARNING` or higher for additional details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Graph with no backdoor set:\n"]}, {"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Random data\n", "treatment = [\"X1\", \"X2\"]\n", "outcome = \"Y\"\n", "variables = [\"V1\", \"V2\", \"V3\", \"V4\", \"V5\"]\n", "hidden_variables = [\"L\"]\n", "causal_graph = \"\"\"graph[directed 1 node[id \"X1\" label \"X1\"]\n", "                node[id \"X2\" label \"X2\"]\n", "                node[id \"Y\" label \"Y\"]\n", "                node[id \"V1\" label \"V1\"]\n", "                node[id \"V2\" label \"V2\"]\n", "                node[id \"V3\" label \"V3\"]\n", "                node[id \"V4\" label \"V4\"]\n", "                node[id \"V5\" label \"V5\"]\n", "                node[id \"L\" label \"L\"]\n", "                edge[source \"V5\" target \"X1\"]\n", "                edge[source \"V4\" target \"X1\"]\n", "                edge[source \"X1\" target \"V1\"]\n", "                edge[source \"V1\" target \"V2\"]\n", "                edge[source \"V2\" target \"X2\"]\n", "                edge[source \"X2\" target \"Y\"]\n", "                edge[source \"X1\" target \"V3\"]\n", "                edge[source \"V3\" target \"Y\"]\n", "                edge[source \"L\" target \"V3\"]\n", "                edge[source \"L\" target \"V2\"]]\n", "                \"\"\"\n", "columns = list(treatment) + list(outcome) + list(variables)\n", "df = pd.DataFrame(columns=columns)\n", "\n", "# Causal Model Initialization\n", "causal_model = CausalModel(df, treatment, outcome, graph=causal_graph)\n", "\n", "# View graph\n", "causal_model.view_model()\n", "from IPython.display import Image, display\n", "print(\"Graph with no backdoor set:\")\n", "display(Image(filename=\"causal_model.png\"))"]}, {"cell_type": "markdown", "id": "2d1f3dad-a589-40b4-b54f-7bd6408caa59", "metadata": {}, "source": ["The backdoor criterion requires the adjustment set **Z** to (1) contain no descendents of **X** and (2) to d-separate all backdoor paths from **X** to **Y**. In this case, in the above graph, the backdoor criterion would require the adjustment set to include L (since V2 and V3 are descendents of **X**). But if L is unobserved, then we cannot control for L, and so the backdoor criterion finds no adjustment sets.\n", "\n", "The generalized adjustment criterion given in <PERSON> et al. (2018) however is complete, and is able to identify that in fact {V1, V2} is a valid adjustment set. One can use this adjustment criterion to identify an estimand by using the 'identify_effect()' method in the CausalModel class, which gives a minimal adjustment set which meets the general adjustment criterion."]}, {"cell_type": "code", "execution_count": 16, "id": "5865e9e2-b22d-4e25-b065-aae47b498f6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimand type: EstimandType.NONPARAMETRIC_ATE\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor\n", "No such variable(s) found!\n", "\n", "### Estimand : 2\n", "Estimand name: iv\n", "Estimand expression:\n", " ⎡                                  -1⎤\n", " ⎢    d        ⎛    ∂              ⎞  ⎥\n", "E⎢─────────(Y)⋅⎜─────────([X₁  X₂])⎟  ⎥\n", " ⎣d[V₅  V₄]    ⎝∂[V₅  V₄]          ⎠  ⎦\n", "Estimand assumption 1, As-if-random: If U→→Y then ¬(U →→{V5,V4})\n", "Estimand assumption 2, Exclusion: If we remove {V5,V4}→{X1,X2}, then ¬({V5,V4}→Y)\n", "\n", "### Estimand : 3\n", "Estimand name: frontdoor\n", "No such variable(s) found!\n", "\n", "### Estimand : 4\n", "Estimand name: general_adjustment\n", "Estimand expression:\n", "    d                \n", "─────────(E[Y|V2,V1])\n", "d[X₁  X₂]            \n", "Estimand assumption 1, Unconfoundedness: If U→{X1,X2} and U→Y then P(Y|X1,X2,V2,V1,U) = P(Y|X1,X2,V2,V1)\n", "\n"]}], "source": ["# Causal Identification\n", "identified_estimand = causal_model.identify_effect()\n", "print(identified_estimand)"]}, {"cell_type": "markdown", "id": "35b0c550-f69c-4edd-b190-73f90382a13d", "metadata": {}, "source": ["**We see here that while the backdoor estimand found no adjustment set, the general_adjustment estimand successfully found the set {V1, V2}!**"]}, {"cell_type": "markdown", "id": "5852f515-5cbf-4e53-a5b5-f606e6b29f27", "metadata": {}, "source": ["## References\n", "\n", "1. <PERSON><PERSON>, <PERSON>, and <PERSON>. \"On the validity of covariate adjustment for estimating causal effects.\" In *Proceedings of UAI 2010*, pages 527–536, 2010.\n", "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>. \"Complete graphical characterization and construction of adjustment sets in Markov equivalence classes of ancestral graphs.\" *Journal of Machine Learning Research*, vol. 18, no. 220, pp. 1–62, 2018. [Online]. Available: [http://jmlr.org/papers/v18/16-319.html](http://jmlr.org/papers/v18/16-319.html)"]}, {"cell_type": "code", "execution_count": null, "id": "e498f727-5ab5-466e-969e-35c531890e46", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}