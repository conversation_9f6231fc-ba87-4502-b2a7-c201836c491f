{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Metalearners Estimators: Use Cases and Examples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Metalearners are binary treatment CATE estimators that model the response surfaces, $Y(0)$ and $Y(1)$, separately. To account for a heterogeneous propensity of treatment $P(T\\mid X)$, the two modeled responses $E(Y(0)\\mid X)$ and $E(Y(1)\\mid X)$ are weighted in different ways in the final CATE estimation. For a detailed overview of these methods, see [this paper](https://arxiv.org/abs/1706.03461). \n", "\n", "The EconML SDK implements the following `metalearners`:\n", "\n", "* <PERSON>-<PERSON><PERSON>\n", "\n", "* <PERSON><PERSON><PERSON><PERSON>\n", "\n", "* <PERSON><PERSON><PERSON><PERSON>\n", "\n", "* DomainAdaptation-Learner\n", "\n", "* <PERSON><PERSON>ly<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "In this notebook, we compare the performance of these four CATE estimatiors on synthetic data and semi-synthetic data.\n", "\n", "**Notebook contents:**\n", "\n", "1. Example usage with synthetic data\n", "\n", "2. Example usage with semi-synthetic data"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Main imports\n", "from econml.metalearners import TLearner, SLearner, XLearner, DomainAdaptationLearner\n", "\n", "# Helper imports \n", "import numpy as np\n", "from numpy.random import binomial, multivariate_normal, normal, uniform\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier, GradientBoostingRegressor\n", "import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Example Usage with Synthetic Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1. DGP"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We use the data generating process (DGP) from [<PERSON><PERSON><PERSON> et al.](https://arxiv.org/abs/1706.03461). The DGP is described by the following equations:\n", "\n", "$\n", "Y = \\mu_1(x) \\cdot T + \\mu_0(x) \\cdot (1-T) + \\epsilon \\\\\n", "T \\sim Bern(e(x)), \\; e(x) = P(T=1|X=x)\n", "$\n", "\n", "where \n", "\n", "$\n", "\\mu_0(x) = x^T\\beta,\\; with \\;\\beta\\sim Unif([-3, 3]^d),\\; X_i \\sim N(0, \\Sigma)\\\\\n", "\\mu_1(x) = \\mu_0(x) + 8 \\mathbb{I}(x_2>0.1) => CATE(x) = 8 \\mathbb{I}(x_2>0.1)\n", "$\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Define DGP\n", "def generate_data(n, d, controls_outcome, treatment_effect, propensity):\n", "    \"\"\"Generates population data for given untreated_outcome, treatment_effect and propensity functions.\n", "    \n", "    Parameters\n", "    ----------\n", "        n (int): population size\n", "        d (int): number of covariates\n", "        controls_outcome (func): untreated outcome conditional on covariates\n", "        treatment_effect (func): treatment effect conditional on covariates\n", "        propensity (func): probability of treatment conditional on covariates\n", "    \"\"\"\n", "    # Generate covariates\n", "    X = multivariate_normal(np.zeros(d), np.diag(np.ones(d)), n)\n", "    # Generate treatment\n", "    T = np.apply_along_axis(lambda x: binomial(1, propensity(x), 1)[0], 1, X)\n", "    # Calculate outcome\n", "    Y0 = np.apply_along_axis(lambda x: controls_outcome(x), 1, X)\n", "    treat_effect = np.apply_along_axis(lambda x: treatment_effect(x), 1, X)\n", "    Y = Y0 + treat_effect * T\n", "    return (Y, T, X)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# controls outcome, treatment effect, propensity definitions\n", "def generate_controls_outcome(d):\n", "    beta = uniform(-3, 3, d)\n", "    return lambda x: np.dot(x, beta) + normal(0, 1)\n", "treatment_effect = lambda x: (1 if x[1] > 0.1 else 0)*8\n", "propensity = lambda x: (0.8 if (x[2]>-0.5 and x[2]<0.5) else 0.2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# DGP constants and test data\n", "d = 5\n", "n = 1000\n", "n_test = 250\n", "controls_outcome = generate_controls_outcome(d)\n", "X_test = multivariate_normal(np.zeros(d), np.diag(np.ones(d)), n_test)\n", "delta = 6/n_test\n", "X_test[:, 1] = np.arange(-3, 3, delta)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["Y, T, X = generate_data(n, d, controls_outcome, treatment_effect, propensity)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2. Train Estimators"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Instantiate T learner\n", "models = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "T_learner = TLearner(models=models)\n", "# Train T_learner\n", "T_learner.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "T_te = T_learner.effect(X_test)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Instantiate S learner\n", "overall_model = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "S_learner = SLearner(overall_model=overall_model)\n", "# Train S_learner\n", "S_learner.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "S_te = S_learner.effect(X_test)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Instantiate X learner\n", "models = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "propensity_model = RandomForestClassifier(n_estimators=100, max_depth=6, \n", "                                                  min_samples_leaf=int(n/100))\n", "X_learner = XLearner(models=models, propensity_model=propensity_model)\n", "# Train X_learner\n", "X_learner.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "X_te = X_learner.effect(X_test)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Instantiate Domain Adaptation learner\n", "models = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "final_models = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "propensity_model = RandomForestClassifier(n_estimators=100, max_depth=6, \n", "                                                  min_samples_leaf=int(n/100))\n", "DA_learner = DomainAdaptationLearner(models=models,\n", "                                     final_models=final_models,\n", "                                     propensity_model=propensity_model)\n", "# Train DA_learner\n", "DA_learner.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "DA_te = DA_learner.effect(X_test)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The sklearn.ensemble.forest module is  deprecated in version 0.22 and will be removed in version 0.24. The corresponding classes / functions should instead be imported from sklearn.ensemble. Anything that cannot be imported from sklearn.ensemble is now part of the private API.\n"]}], "source": ["# Instantiate Doubly <PERSON>\n", "from econml.dr import DRLearner\n", "outcome_model = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "pseudo_treatment_model = GradientBoostingRegressor(n_estimators=100, max_depth=6, min_samples_leaf=int(n/100))\n", "propensity_model = RandomForestClassifier(n_estimators=100, max_depth=6, \n", "                                                  min_samples_leaf=int(n/100))\n", "\n", "DR_learner = DRLearner(model_regression=outcome_model, model_propensity=propensity_model,\n", "                       model_final=pseudo_treatment_model, cv=5)\n", "# Train DR_learner\n", "DR_learner.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "DR_te = DR_learner.effect(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3. Visual Comparisons"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 504x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["### Comparison plot of the different learners\n", "plt.figure(figsize=(7, 5))\n", "plt.plot(X_test[:, 1], np.apply_along_axis(treatment_effect, 1, X_test), color='black', ls='--', label='Baseline')\n", "plt.scatter(X_test[:, 1], T_te, label=\"T-learner\")\n", "plt.scatter(X_test[:, 1], S_te, label=\"S-learner\")\n", "plt.scatter(X_test[:, 1], DA_te, label=\"DA-learner\")\n", "plt.scatter(X_test[:, 1], X_te, label=\"X-learner\")\n", "plt.scatter(X_test[:, 1], DR_te, label=\"DR-learner\")\n", "plt.xlabel('$x_1$')\n", "plt.ylabel('Treatment Effect')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualization of bias distribution\n", "expected_te = np.apply_along_axis(treatment_effect, 1, X_test)\n", "plt.violinplot([np.abs(T_te - expected_te), \n", "                np.abs(S_te - expected_te),\n", "                np.abs(DA_te - expected_te),\n", "                np.abs(X_te - expected_te),\n", "                np.abs(DR_te - expected_te)\n", "               ], showmeans=True)\n", "plt.ylabel(\"Bias distribution\")\n", "plt.xticks([1, 2, 3, 4, 5], ['T-learner', 'S-learner', 'DA-learner', 'X-learner', 'DR-learner'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Example Usage with Semi-synthetic Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1. DGP\n", "\n", "We use the Response Surface B from [<PERSON> (2011)](https://www.tandfonline.com/doi/pdf/10.1198/jcgs.2010.08162) to generate sythetic outcome surfaces from real-world covariates and treatment assignments (Infant Health Development Program data). Since the original data was part of a randomized trial, a subset of the treated infants (those with non-white mothers) has been removed from the data in order to mimic the observational data setting. For more details, see [<PERSON> (2011)](https://www.tandfonline.com/doi/pdf/10.1198/jcgs.2010.08162).\n", "\n", "\n", "The DGP is described by the following equations:\n", "\n", "$\n", "Y(0) = e^{(X+W)\\beta} + \\epsilon_0, \\;\\epsilon_0 \\sim N(0, 1)\\\\\n", "Y(1) = X\\beta - \\omega + \\epsilon_1, \\;\\epsilon_1 \\sim N(0, 1)\\\\\n", "$\n", "\n", "where $X$ is a covariate matrix, $W$ is a constant matrix with entries equal to $0.5$ and $w$ is a constant calculated such that the CATT equals $4$."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from econml.data.dgps import ihdp_surface_B"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["Y, T, X, expected_te = ihdp_surface_B()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2. Train Estimators"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# T-learner\n", "T_learner.fit(Y, T, X=X)\n", "T_te = T_learner.effect(X)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# S-learner\n", "S_learner.fit(Y, T, X=X)\n", "S_te = S_learner.effect(X)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# X-learner\n", "X_learner.fit(Y, T, X=X)\n", "X_te = X_learner.effect(X)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Domain adaptation learner\n", "DA_learner.fit(Y, T, X=X)\n", "DA_te = DA_learner.effect(X)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Doubly robust learner\n", "DR_learner.fit(Y, T, X=X)\n", "DR_te = DR_learner.effect(X)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3. Visual Comparisons"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualization of bias distribution\n", "plt.violinplot([np.abs(T_te - expected_te), \n", "                np.abs(S_te - expected_te),\n", "                np.abs(DA_te - expected_te),\n", "                np.abs(X_te - expected_te),\n", "                np.abs(DR_te - expected_te)\n", "               ], showmeans=True)\n", "plt.ylabel(\"Bias distribution\")\n", "plt.xticks([1, 2, 3, 4, 5], ['T-learner', 'S-learner', 'DA-learner', 'X-learner', 'DR-learner'])\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}