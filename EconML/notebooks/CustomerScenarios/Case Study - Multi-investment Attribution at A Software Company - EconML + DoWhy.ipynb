{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<img src=\"https://www.microsoft.com/en-us/research/uploads/prod/2020/05/Attribution.png\" width=\"400\">\n", "\n", "<h1 align=\"left\">Multi-investment Attribution: Distinguish the Effects of Multiple Outreach Efforts</h1>\n", "\n", "A startup that sells software would like to know whether its multiple outreach efforts were successful in attracting new customers or boosting consumption among existing customers. They would also like to distinguish the effects of several incentives on different kinds of customers. In other words, they would like to learn the **heterogeneous treatment effect** of each investment on customers' software usage. \n", "\n", "In an ideal world, the startup would run several randomized experiments where each customer would receive a random assortment of investments. However, this can be logistically prohibitive or strategically unsound: the startup might not have the resources to design such experiments or they might not want to risk losing out on big opportunities due to lack of incentives.\n", "\n", "In this customer scenario walkthrough, we show how tools from the [EconML](https://aka.ms/econml) and [DoWhy](https://github.com/py-why/dowhy) libraries can use historical investment data to learn the effects of multiple investments."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary\n", "\n", "1. [Background](#Background)\n", "2. [Data](#Data)\n", "3. [Create Causal Model and Identify Causal Effect with DoWhy](#Create-Causal-Model-and-Identify-Causal-Effect-with-<PERSON><PERSON><PERSON>)\n", "4. [Get Causal Effects with EconML](#Get-Causal-Effects-with-EconML)\n", "5. [Understand Treatment Effects with EconML](#Understand-Treatment-Effects-with-EconML)\n", "6. [Make Policy Decisions with EconML](#Make-Policy-Decisions-with-EconML)\n", "7. [Test Estimate Robustness with <PERSON><PERSON><PERSON>](#Test-Estimate-Rob<PERSON>ness-with-<PERSON><PERSON><PERSON>)\n", "    1. [Add Random Common Cause](#Add-Random-Common-Cause)\n", "    2. [Add Unobserved Common Cause](#Add-Unobserved-Common-Cause)\n", "    3. [Replace Treatment with a Random (Placebo) Variable](#Replace-Treatment-with-a-Random-(Placebo)-Variable)\n", "    4. [Remove a Random Subset of the Data](#Remove-a-Random-Subset-of-the-Data)\n", "8. [Conclusions](#Conclusions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Background\n", "\n", "<img src=\"https://get.pxhere.com/photo/update-software-upgrade-laptop-computer-install-program-screen-system-repair-data-development-electronic-load-pc-process-progress-support-technical-load-1565823.jpg\" width=\"400\">\n", "\n", "In this scenario, a startup that sells software provides two types of incentives to its customers: technical support and discounts. A customer might be given one, both or none of these incentives. \n", "\n", "The startup has historical data on these two investments for 2,000 customers, as well as how much revenue these customers generated in the year after the investments were made. They would like to use this data to learn the optimal incentive policy for each existing or new customer in order to maximize the return on investment (ROI).\n", "\n", "The startup faces two challenges: 1) the dataset is biased because historically the larger customers received the most incentives and 2) the observed outcome combines effects from two different investments. Thus, they need a causal model that can accommodate multiple concurrent interventions. \n", "\n", "**Solution:** \n", "The EconML and DoWhy libraries complement each other in providing a complete solution. On one hand, the DoWhy library can help [build a causal model, identify the causal effect](#Create-Causal-Model-and-Identify-Causal-Effect-with-<PERSON><PERSON><PERSON>) and [test causal assumptions](#Test-Estimate-Robustness-with-<PERSON><PERSON><PERSON>). \n", "\n", "On the other hand, EconML's `<PERSON>ubly <PERSON> Learner` estimator can [jointly estimates the effects of multiple discrete treatments](#Get-Causal-Effects-with-EconML). The model uses flexible functions of observed customer features to filter out spurious correlations in existing data and deliver the causal effect of each intervention on revenue.\n", "\n", "\n", "Furthermore, EconML provides users tools to [understand causal effects](#Understand-Treatment-Effects-with-EconML) and [make causal policy decisions](#Make-Policy-Decisions-with-EconML)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Some imports to get us started\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "# Utilities\n", "import os\n", "import urllib.request\n", "import numpy as np\n", "import pandas as pd\n", "from networkx.drawing.nx_pydot import to_pydot\n", "from IPython.display import Image, display\n", "\n", "# Generic ML imports\n", "from xgboost import XGBRegressor, XGBClassifier\n", "\n", "# EconML imports\n", "from econml.dr import LinearDRLearner\n", "\n", "# DoWhy imports \n", "import dowhy\n", "from dowhy import CausalModel\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data\n", "\n", "The data* contains ~2,000 customers and is comprised of:\n", "\n", "* Customer features: details about the industry, size, revenue, and technology profile of each customer.\n", "* Interventions: information about which incentive was given to a customer.\n", "* Outcome: the amount of product the customer bought in the year after the incentives were given.\n", "\n", "Feature Name | Type | Details \n", ":--- |:--- |:--- \n", "**Global Flag** | W | whether the customer has global offices\n", "**Major Flag** | W | whether the customer is a large consumer in their industry (as opposed to SMC - Small Medium Corporation - or SMB - Small Medium Business)\n", "**SMC Flag** | W | whether the customer is a Small Medium Corporation (SMC, as opposed to major and SMB)\n", "**Commercial Flag** | W | whether the customer's business is commercial (as opposed to public secor)\n", "**IT Spend** | W | \\\\$ spent on IT-related purchases \n", "**Employee Count** | W | number of employees\n", "**PC Count** | W | number of PCs used by the customer\n", "**Size** | X | customer's size given by their yearly total revenue \n", "**Tech Support** | T | whether the customer received tech support (binary)\n", "**Discount** | T | whether the customer was given a discount (binary)\n", "**Revenue** | Y | \\\\$ Revenue from customer given by the amount of software purchased\n", "\n", "**To protect the privacy of the startup's customers, the data used in this scenario is synthetically generated and the feature distributions don't correspond to real distributions. However, the feature names have preserved their names and meaning.*"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import the sample multi-attribution data\n", "file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/ROI/multi_attribution_sample.csv\"\n", "multi_data = pd.read_csv(file_url)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Global Flag</th>\n", "      <th>Major Flag</th>\n", "      <th>SMC Flag</th>\n", "      <th>Commercial Flag</th>\n", "      <th>IT Spend</th>\n", "      <th>Employee Count</th>\n", "      <th>PC Count</th>\n", "      <th>Size</th>\n", "      <th>Tech Support</th>\n", "      <th>Discount</th>\n", "      <th>Revenue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>45537</td>\n", "      <td>26</td>\n", "      <td>26</td>\n", "      <td>152205</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>17688.36300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>20842</td>\n", "      <td>107</td>\n", "      <td>70</td>\n", "      <td>159038</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>14981.43559</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>82171</td>\n", "      <td>10</td>\n", "      <td>7</td>\n", "      <td>264935</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32917.13894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>30288</td>\n", "      <td>40</td>\n", "      <td>39</td>\n", "      <td>77522</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14773.76855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>25930</td>\n", "      <td>37</td>\n", "      <td>43</td>\n", "      <td>91446</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>17098.69823</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Global Flag  Major Flag  SMC Flag  Commercial Flag  IT Spend  \\\n", "0            1           0         1                0     45537   \n", "1            0           0         1                1     20842   \n", "2            0           0         0                1     82171   \n", "3            0           0         0                0     30288   \n", "4            0           0         1                0     25930   \n", "\n", "   Employee Count  PC Count    <PERSON>ze  Tech Support  Discount      Revenue  \n", "0              26        26  152205             0         1  17688.36300  \n", "1             107        70  159038             0         1  14981.43559  \n", "2              10         7  264935             1         1  32917.13894  \n", "3              40        39   77522             1         1  14773.76855  \n", "4              37        43   91446             1         1  17098.69823  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data sample\n", "multi_data.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Define estimator inputs\n", "T_bin = multi_data[\n", "    [\"Tech Support\", \"Discount\"]\n", "]  # multiple interventions, or treatments\n", "Y = multi_data[\"Revenue\"]  # amount of product purchased, or outcome\n", "X = multi_data[[\"Size\"]]  # heterogeneity feature\n", "W = multi_data.drop(\n", "    columns=[\"Tech Support\", \"Discount\", \"Revenue\", \"Size\"]\n", ")  # controls\n", "confounder_names = [\"Global Flag\", \"Major Flag\", \"SMC Flag\", \"Commercial Flag\", \"IT Spend\", \"Employee Count\", \"PC Count\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We investigate below whether the number of investments given is correlated with the size of the customer. We note that the average customer size is larger for more incentives given. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Tech Support</th>\n", "      <th>Discount</th>\n", "      <th>Size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>70943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>96466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>108978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>171466</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Tech Support  Discount    Size\n", "0             0         0   70943\n", "1             0         1   96466\n", "2             1         0  108978\n", "3             1         1  171466"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Average customer size per incentive combination\n", "multi_data[[\"<PERSON><PERSON>\", \"Tech Support\", \"Discount\"]].groupby(\n", "    by=[\"Tech Support\", \"Discount\"], as_index=False\n", ").mean().astype(int)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The data was generated using the following underlying treatment effect function:\n", "\n", "$$\n", "\\text{treatment_effect(Size)} = (5,000 + 2\\% \\cdot \\text{Size}) \\cdot I_\\text{Tech Support} + (5\\% \\cdot \\text{Size}) \\cdot I_\\text{Discount}\n", "$$\n", "\n", "Therefore, the treatment effect depends on the customer's size as follows: tech support provides an consumption boost of \\$5,000 + 2\\% Size and a discount provides an consumption boost of 5\\% Size.**This is the relationship we seek to learn from the data.**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Define underlying treatment effect function\n", "TE_fn = lambda X: np.hstack([5000 + 2 / 100 * X, 5 / 100 * X])\n", "true_TE = TE_fn(X)\n", "\n", "# Define true coefficients for the three treatments\n", "# The third coefficient is just the sum of the first two since we assume an additive effect\n", "true_coefs = [2 / 100, 5 / 100, 7 / 100]\n", "true_intercepts = [5000, 0, 5000]\n", "treatment_names = [\"Tech Support\", \"Discount\", \"Tech Support & Discount\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Causal Model and Identify Causal Effect with DoWhy\n", "\n", "We define the causal assumptions of the multi-intervention setting with <PERSON><PERSON><PERSON>. For example, we can include features we believe influence both the treatments* and the outcome (`common_causes`) and features we think will influence the heterogeneity of the effect (`effect_modifiers`). With these assumptions defined, <PERSON><PERSON><PERSON> can identify the causal effect for us."]}, {"cell_type": "markdown", "metadata": {}, "source": ["*The `DoWhy` and `EconML` estimators require multiple binary treatments to be given as a list of discrete treatments $T$ that correspond to different types of interventions. Thus, we first map the binary interventions tech support and discount into one categorical variable:*\n", "\n", "Tech support| Discount| Treatment encoding| Details\n", ":--- |:--- |:--- |:---\n", "0 | 0 | 0 | no incentive\n", "1 | 0 | 1 | tech support only\n", "0 | 1 | 2 | discount only\n", "1 | 1 | 3 | both incentives"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Transform T to one-dimensional array with consecutive integer encoding\n", "def treat_map(t):\n", "    return np.dot(t, 2 ** np.arange(t.shape[0]))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["T = np.apply_along_axis(treat_map, 1, T_bin).astype(int)\n", "multi_data[\"discrete_T\"] = T"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# initiate an EconML cate estimator\n", "est = LinearDRLearner(\n", "    model_regression=XGBRegressor(learning_rate=0.1, max_depth=3),\n", "    model_propensity=XGBClassifier(learning_rate=0.1, max_depth=3, objective=\"multi:softprob\"),\n", "    random_state=1,\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:dowhy.causal_model:Causal Graph not provided. <PERSON><PERSON><PERSON> will construct a graph based on data inputs.\n", "INFO:dowhy.causal_graph:If this is observed data (not from a randomized experiment), there might always be missing confounders. Adding a node named \"Unobserved Confounders\" to reflect this.\n", "INFO:dowhy.causal_model:Model to find the causal effect of treatment ['discrete_T'] on outcome ['Revenue']\n", "WARNING:dowhy.causal_identifier:If this is observed data (not from a randomized experiment), there might always be missing confounders. Causal effect cannot be identified perfectly.\n", "INFO:dowhy.causal_identifier:Continuing by ignoring these unobserved confounders because proceed_when_unidentifiable flag is True.\n", "INFO:dowhy.causal_identifier:Instrumental variables for treatment and outcome:[]\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~discrete_T+Major Flag+IT Spend+SMC Flag+Size+PC Count+Global Flag+Employee Count+Commercial Flag | Size\n"]}], "source": ["# fit through dowhy\n", "test_customers = X.iloc[:1000].values\n", "est_dw = est.dowhy.fit(Y, T, X=X, W=W, outcome_names=[\"Revenue\"], treatment_names=[\"discrete_T\"], feature_names=[\"Size\"],\n", "               confounder_names=confounder_names, target_units=test_customers)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"dot\" with args ['-Tpng', 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmpz73o722o'] returned code: 1\n", "\n", "stdout, stderr:\n", " b''\n", "b''\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:dowhy.causal_graph:Warning: Pygraphviz cannot be loaded. Check that graphviz and pygraphviz are installed.\n", "INFO:dowhy.causal_graph:Using Matplotlib for plotting\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize causal graph\n", "try:\n", "    # Try pretty printing the graph. Requires pydot and pygraphviz\n", "    display(\n", "        Image(to_pydot(est_dw._graph._graph).create_png())\n", "    )\n", "except:\n", "    # Fall back on default graph view\n", "    est_dw.view_model() "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimand type: nonparametric-ate\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor1 (<PERSON><PERSON><PERSON>)\n", "Estimand expression:\n", "      d                                                                       \n", "─────────────(Expectation(Revenue|Employee Count,Global Flag,IT Spend,Commerci\n", "d[discrete_T]                                                                 \n", "\n", "                                           \n", "al Flag,PC Count,SMC Flag,Size,Major Flag))\n", "                                           \n", "Estimand assumption 1, Unconfoundedness: If U→{discrete_T} and U→Revenue then P(Revenue|discrete_T,Employee Count,Global Flag,IT Spend,Commercial Flag,PC Count,SMC Flag,Size,Major Flag,U) = P(Revenue|discrete_T,Employee Count,Global Flag,IT Spend,Commercial Flag,PC Count,SMC Flag,Size,Major Flag)\n", "\n", "### Estimand : 2\n", "Estimand name: iv\n", "No such variable found!\n", "\n", "### Estimand : 3\n", "Estimand name: frontdoor\n", "No such variable found!\n", "\n"]}], "source": ["identified_estimand = est_dw.identified_estimand_\n", "print(identified_estimand)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Causal Effects with EconML"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To get causal effects, we use EconML's `LinearDRLearner`* estimator. The estimator takes as input the outcome of interest $Y$ (amount of product purchased), a discrete treatment $T$ (interventions given), heterogeneity features $X$ (here, customer's size) and controls $W$ (all other customer features).\n", "\n", "The LinearDRLearner also requires two auxiliary models to model the relationships $T\\sim (W, X)$ (`model_propensity`) and $Y \\sim (W, X)$(`model_regression`). These can be generic, flexible classification and regression models, respectively.  \n", "\n", "**This estimator assumes a linear relationship between the treatment effect and a transformation of the features $X$ (e.g. a polynomial basis expansion). For more generic forms of the treatment effect, see the `DRLearner` estimator.*"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Causal Estimate ***\n", "\n", "## Identified estimand\n", "Estimand type: nonparametric-ate\n", "\n", "## Realized estimand\n", "b: Revenue~discrete_T+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n", "Target units: \n", "\n", "## Estimate\n", "Mean value: 7640.913924714072\n", "Effect estimates: [ 8437.71899247  8577.15928753 10738.18796098 ...  5575.01973432\n", "  6148.71869743  8451.26916896]\n", "\n"]}], "source": ["lineardml_estimate = est_dw.estimate_\n", "print(lineardml_estimate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When training the model, `<PERSON><PERSON><PERSON>` automatically calculates the average treatment effect (ATE) on a test sample. In the case above, we asked that `<PERSON><PERSON><PERSON>` calculate the ATE for $T=1$ which corresponds to a customer getting tech support only. Let's see how this compares to the true ATE:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True ATE on test data:  7275.564040000001\n"]}], "source": ["true_customer_TE = TE_fn(test_customers)\n", "print(\"True ATE on test data: \", true_customer_TE[:, 0].mean())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that the two values are close which is a good starting point for our algorithm. In a future section, we see how the calculated heterogeneous treatment effects compare with the true ones. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Understand Treatment Effects with EconML\n", "\n", "We can obtain a summary of the coefficient values as well as confidence intervals by calling the `summary` function on the fitted model for each treatment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Investment: Tech Support\n", "True treatment effect: 5000 + 0.02*Size\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>   <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>Size</th>      <td>0.02</td>       <td>0.012</td> <td>1.722</td>  <td>0.085</td>   <td>0.001</td>    <td>0.04</td>  \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>    <td>5331.688</td>    <td>847.626</td> <td>6.29</td>    <td>0.0</td>  <td>3937.468</td> <td>6725.908</td>\n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                   Coefficient Results                   \n", "=========================================================\n", "     point_estimate stderr zstat pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "Size           0.02  0.012 1.722  0.085    0.001     0.04\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate  stderr zstat pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept       5331.688 847.626  6.29    0.0 3937.468 6725.908\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Investment: Discount\n", "True treatment effect: 0 + 0.05*Size\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>   <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>Size</th>      <td>0.052</td>      <td>0.012</td> <td>4.364</td>   <td>0.0</td>    <td>0.032</td>    <td>0.071</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>     <td>349.579</td>    <td>850.063</td> <td>0.411</td>  <td>0.681</td> <td>-1048.65</td> <td>1747.809</td>\n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                   Coefficient Results                   \n", "=========================================================\n", "     point_estimate stderr zstat pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "Size          0.052  0.012 4.364    0.0    0.032    0.071\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate  stderr zstat pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept        349.579 850.063 0.411  0.681 -1048.65 1747.809\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Investment: Tech Support & Discount\n", "True treatment effect: 5000 + 0.07*Size\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>   <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>Size</th>      <td>0.073</td>      <td>0.012</td> <td>6.271</td>   <td>0.0</td>    <td>0.054</td>    <td>0.093</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>    <td>4909.669</td>    <td>851.759</td> <td>5.764</td>   <td>0.0</td>   <td>3508.65</td> <td>6310.687</td>\n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                   Coefficient Results                   \n", "=========================================================\n", "     point_estimate stderr zstat pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "Size          0.073  0.012 6.271    0.0    0.054    0.093\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate  stderr zstat pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept       4909.669 851.759 5.764    0.0  3508.65 6310.687\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(est_dw._d_t[0]):\n", "    print(f\"Investment: {treatment_names[i]}\")\n", "    print(f\"True treatment effect: {true_intercepts[i]} + {true_coefs[i]}*Size\")\n", "    display(est_dw.summary(T=i + 1, feature_names=[\"Size\"]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the summary panels, we see that the learned coefficients/intercepts are close to the true coefficients/intercepts and the p-values are small for most of these. \n", "\n", "We further use the `coef_, coef__interval` and the `intercept_, intercept__interval` methods to obtain the learned coefficient values and build confidence intervals. We compare the true and the learned coefficients through the plots below."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare learned coefficients with true model coefficients\n", "# Aggregate data\n", "coef_indices = np.arange(est_dw._d_t[0])\n", "coefs = np.hstack([est_dw.coef_(T=i) for i in 1 + coef_indices])\n", "intercepts = np.hstack([est_dw.intercept_(T=i) for i in 1 + coef_indices])\n", "\n", "# Calculate coefficient error bars for 95% confidence interval\n", "coef_error = np.hstack([est_dw.coef__interval(T=i) for i in 1 + coef_indices])\n", "coef_error[0, :] = coefs - coef_error[0, :]\n", "coef_error[1, :] = coef_error[1, :] - coefs\n", "\n", "# Calculate intercept error bars for 95% confidence interval\n", "intercept_error = np.vstack(\n", "    [est_dw.intercept__interval(T=i) for i in 1 + coef_indices]\n", ").T\n", "intercept_error[0, :] = intercepts - intercept_error[0, :]\n", "intercept_error[1, :] = intercept_error[1, :] - intercepts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Plot coefficients\n", "plt.figure(figsize=(6, 5))\n", "ax1 = plt.subplot(2, 1, 1)\n", "plt.errorbar(\n", "    coef_indices,\n", "    coefs,\n", "    coef_error,\n", "    fmt=\"o\",\n", "    label=\"Learned values\\nand 95% confidence interval\",\n", ")\n", "plt.scatter(coef_indices, true_coefs, color=\"C1\", label=\"True values\", zorder=3)\n", "plt.xticks(coef_indices, treatment_names)\n", "plt.setp(ax1.get_xticklabels(), visible=False)\n", "plt.title(\"Coefficients\")\n", "plt.legend(loc=(1.05, 0.65))\n", "plt.grid()\n", "\n", "# Plot intercepts\n", "plt.subplot(2, 1, 2)\n", "plt.errorbar(coef_indices, intercepts, intercept_error, fmt=\"o\")\n", "plt.scatter(coef_indices, true_intercepts, color=\"C1\", zorder=3)\n", "plt.xticks(coef_indices, treatment_names)\n", "plt.title(\"Intercepts\")\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Make Policy Decisions with EconML\n", "\n", "Investments such as tech support and discounts come with an associated cost. Thus, we would like to know what incentives to give to each customer to maximize the profit from their increased engagement. This is the **treatment policy**.\n", "\n", "In this scenario, we define a cost function as follows:\n", "* The cost of `tech support` scales with the number of PCs a customer has. You can imagine that if the software product needs tech support to be installed on each machine, there is a cost (\\\\$100 here) per machine.\n", "* The cost of `discount` is a fixed \\\\$7,000. Think of this as giving the customer the first \\\\$7,000 worth of product for free.\n", "* The cost of `tech support` and `discount` is the sum of the cost of each of these. Note that this might not be the case in every business application: it is possible that managing multiple incentive programs can add overhead. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define cost function\n", "def cost_fn(multi_data):\n", "    t1_cost = multi_data[[\"PC Count\"]].values * 100\n", "    t2_cost = np.ones((multi_data.shape[0], 1)) * 7000\n", "    return np.hstack([t1_cost, t2_cost, t1_cost + t2_cost])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We use the model's `const_marginal_effect` method to find the counterfactual treatment effect for each possible treatment. We then subtract the treatment cost and choose the treatment which the highest return. That is the recommended policy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get roi for each customer and possible treatment\n", "potential_roi = est_dw.const_marginal_effect(X=X.values) - cost_fn(multi_data)\n", "# Add a column of 0s for no treatment\n", "potential_roi = np.hstack([np.zeros(X.shape), potential_roi])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_treatments = np.array([\"None\"] + treatment_names)\n", "recommended_T = np.argmax(potential_roi, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ax1 = sns.scatterplot(\n", "    x=X.values.flatten(),\n", "    y=multi_data[\"PC Count\"].values,\n", "    hue=all_treatments[recommended_T],\n", "    hue_order=all_treatments,\n", "    cmap=\"Dark2\",\n", "    s=40,\n", ")\n", "plt.legend(title=\"Investment Policy\")\n", "plt.setp(\n", "    ax1,\n", "    xlabel=\"Customer Size\",\n", "    ylabel=\"PC Count\",\n", "    title=\"Optimal Investment Policy by Customer\",\n", ")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We compare different policies: the optimal policy we learned, the current policy, and the policy under which each customer is given all incentives. We note that the optimal policy has a much higher ROI than the alternatives. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["roi_current = potential_roi[np.arange(X.shape[1]), T].sum()\n", "roi_optimal = potential_roi[np.arange(X.shape[1]), recommended_T].sum()\n", "roi_bothT = potential_roi[:, -1].sum()\n", "all_rois = np.array([roi_optimal, roi_current, roi_bothT])\n", "Y_baseline = (Y - est_dw.effect(X=X.values, T1=T)).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Policy</th>\n", "      <th>ROI ($)</th>\n", "      <th>ROI (% of baseline Y)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Optimal</td>\n", "      <td>9.107992e+06</td>\n", "      <td>59.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Current</td>\n", "      <td>6.801621e+06</td>\n", "      <td>44.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>All Investments</td>\n", "      <td>9.601709e+05</td>\n", "      <td>6.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Policy       ROI ($)  ROI (% of baseline Y)\n", "0          Optimal  9.107992e+06                   59.7\n", "1          Current  6.801621e+06                   44.6\n", "2  All Investments  9.601709e+05                    6.3"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(\n", "    {\n", "        \"Policy\": [\"Optimal\", \"Current\", \"All Investments\"],\n", "        \"ROI ($)\": all_rois,\n", "        \"ROI (% of baseline Y)\": np.round(all_rois / Y_baseline * 100, 1),\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Estimate Robustness with <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Random Common Cause\n", "\n", "How robust are our estimates to adding an uncorrelated confounder? We use DoWhy to test this!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~discrete_T+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag+w_random | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:11] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:11] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "Refute: Add a Random Common Cause\n", "Estimated effect:7640.913924714072\n", "New effect:7879.781233669907\n", "\n"]}], "source": ["res_random = est_dw.refute_estimate(method_name=\"random_common_cause\", num_simulations=10)\n", "print(res_random)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Unobserved Common Cause\n", "\n", "How robust are our estimates to unobserved confounders? Since we assume unconfoundedness, adding an unobserved confounder might bias the estimates. We use <PERSON><PERSON><PERSON> to show this!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~discrete_T+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:14] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:14] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "Refute: Add an Unobserved Common Cause\n", "Estimated effect:7640.913924714072\n", "New effect:3383.8433082673646\n", "\n"]}], "source": ["res_unobserved = est_dw.refute_estimate(method_name=\"add_unobserved_common_cause\",\n", "    confounders_effect_on_treatment=\"binary_flip\", confounders_effect_on_outcome=\"linear\",\n", "    effect_strength_on_treatment=0.3, effect_strength_on_outcome=0.5)\n", "print(res_unobserved)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Replace Treatment with a Random (Placebo) Variable\n", "\n", "What happens our estimates if we replace the treatment variable with noise? Ideally, the average effect would be close to $0$. We use DoWhy to investigate!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_refuters.placebo_treatment_refuter:Refutation over 5 simulated datasets of permute treatment\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~placebo+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:17] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:17] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~placebo+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:18] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:18] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~placebo+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:19] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:19] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~placebo+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:20] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:20] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~placebo+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:21] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:21] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:dowhy.causal_refuters.placebo_treatment_refuter:We assume a Normal Distribution as the sample has less than 100 examples.\n", "                 Note: The underlying distribution may not be Normal. We assume that it approaches normal with the increase in sample size.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refute: Use a Placebo Treatment\n", "Estimated effect:7640.913924714072\n", "New effect:348.68481077875134\n", "p value:0.20213997629264546\n", "\n"]}], "source": ["res_placebo = est_dw.refute_estimate(\n", "    method_name=\"placebo_treatment_refuter\", placebo_type=\"permute\", \n", "    num_simulations=5\n", ")\n", "print(res_placebo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["While the \"New effect\" is not zero, the p-value is greater than 0.05 which means that we cannot reject the null hypothesis that $0$ is under the average treatment effect distribution. Increasing `num_simulations` should produce a \"New effect\" closer to $0$. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove a Random Subset of the Data\n", "\n", "Do we recover similar estimates on subsets of the data? This speaks to the ability of our chosen estimator to generalize well. We use <PERSON><PERSON><PERSON> to investigate this!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_refuters.data_subset_refuter:Refutation over 0.8 simulated datasets of size 1600.0 each\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~discrete_T+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:24] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:24] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~discrete_T+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:25] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:25] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: Revenue~discrete_T+Employee Count+Global Flag+IT Spend+Commercial Flag+PC Count+SMC Flag+Size+Major Flag | Size\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[15:47:25] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n", "[15:47:26] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.3.0/src/learner.cc:1061: Starting in XGBoost 1.3.0, the default evaluation metric used with the objective 'multi:softprob' was changed from 'merror' to 'mlogloss'. Explicitly set eval_metric if you'd like to restore the old behavior.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:dowhy.causal_refuters.data_subset_refuter:We assume a Normal Distribution as the sample has less than 100 examples.\n", "                 Note: The underlying distribution may not be Normal. We assume that it approaches normal with the increase in sample size.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refute: Use a subset of data\n", "Estimated effect:7640.913924714072\n", "New effect:8079.900946029998\n", "p value:0.17820728594431112\n", "\n"]}], "source": ["# Removing a random subset of the data\n", "res_subset = est_dw.refute_estimate(\n", "    method_name=\"data_subset_refuter\", subset_fraction=0.8, \n", "    num_simulations=3)\n", "print(res_subset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The \"New effect\" is close to the estimated effect from the original dataset and the p-value is greater than 0.05. Thus, we cannot reject the null hypothesis that the estimated effect is under the average treatment effect distribution. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Conclusions\n", "\n", "In this notebook, we have demonstrated the power of using `EconML` and `DoWhy` to:\n", "\n", "* Learn the effects of multiple concurrent interventions\n", "* Interpret the resulting individual-level treatment effects\n", "* Build investment policies around the learned effects\n", "* Test causal assumptions and investigate the robustness of the estimates\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). \n", "\n", "To learn more about what <PERSON><PERSON><PERSON> can do for you, visit the [GitHub page](https://github.com/py-why/dowhy) or [documentation](https://www.pywhy.org/dowhy/)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "2e5c6628eef985e7fd2fa2aad22c988c5b8aa1d2648cf9c51c543a2a2637c546"}, "kernelspec": {"display_name": "Python 3.6.6 64-bit", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}}, "nbformat": 4, "nbformat_minor": 4}