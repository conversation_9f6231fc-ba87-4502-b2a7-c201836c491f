





# 导入 NetworkX 库，用于创建和操作图结构
import networkx as nx
# 创建一个有向图（DiGraph）以表示因果关系
# 这里，'X' 导致 'Y'，而 'Y' 导致 'Z'
causal_graph = nx.DiGraph([('X', 'Y'), ('Y', 'Z')])

# 设定节点位置，以实现水平展示
pos = {'X': [0, 0], 'Y': [1, 0], 'Z': [2, 0]}






from dowhy import gcm

# 利用之前创建的因果图（causal_graph）来创建一个结构性因果模型（Structural Causal Model）
# 这个模型会包含更多关于数据生成过程的信息
causal_model = gcm.StructuralCausalModel(causal_graph)



import matplotlib.pyplot as plt
# 使用 networkx 和 matplotlib 绘制因果图
plt.figure(figsize=(2, 0.5))
nx.draw(causal_graph, pos, with_labels=True,  node_color='skyblue', font_size=14, font_color='black')
plt.show()





import numpy as np, pandas as pd

# 生成 1000 个来自正态分布的随机数作为 X 的值，均值为 0，标准差为 1
X = np.random.normal(loc=0, scale=1, size=1000)

# 根据 X 的值和一些随机噪声生成 Y 的值
# Y 是 2 倍的 X 加上来自正态分布的随机噪声
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)

# 根据 Y 的值和一些随机噪声生成 Z 的值
# Z 是 3 倍的 Y 加上来自正态分布的随机噪声
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)

# 将 X, Y, Z 的数据合并为一个 Pandas DataFrame
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

# 显示数据的前几行
data.head()






# 这一行代码使用 gcm.auto.assign_causal_mechanisms() 
# 函数来自动分配因果机制到前面创建的 causal_model 中。函数使用输入的数据集 data 来推断这些因果机制。
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 这里的目标是基于观察到的数据自动找出如何从一个变量（或一组变量）预测另一个变量的函数关系，这样我们就能完善我们的因果模型。

# 总结：这一步是在因果模型的每个节点上自动设置因果机制，使模型能用于进一步的因果分析。





causal_model.set_causal_mechanism('X', gcm.EmpiricalDistribution())
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
causal_model.set_causal_mechanism('Z', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))








gcm.fit(causal_model, data)








# 使用 'interventional_samples' 函数来生成干预样本
# 我们在这里模拟对 Y 变量进行干预，将其设为 2.34
# 并且希望抽取 1000 个样本来观察这种干预下其他变量（这里是 Z）的表现
samples = gcm.interventional_samples(causal_model,  # 使用之前创建的结构因果模型（SCM）
                                     {'Y': lambda y: 2.34 },  # 干预字典，设置 Y = 2.34
                                     num_samples_to_draw=1000)  # 抽取 1000 个样本
# 显示前几行的干预样本数据
samples.head()







