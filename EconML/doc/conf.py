# -*- coding: utf-8 -*-
#
# Configuration file for the Sphinx documentation builder.
#
# This file does only contain a selection of the most common options. For a
# full list see the documentation:
# http://www.sphinx-doc.org/en/main/config

# -- Path setup --------------------------------------------------------------

# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.
#
import os
import sys
import econml
sys.path.insert(0, os.path.abspath('econml'))


# -- Project information -----------------------------------------------------

project = 'econml'
copyright = '2023, PyWhy contributors'
author = 'PyWhy contributors'
version = econml.__version__
release = econml.__version__

# -- General configuration ---------------------------------------------------

# If your documentation needs a minimal Sphinx version, state it here.
#
# needs_sphinx = '1.0'

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.autosummary',
    'sphinx.ext.doctest',
    'sphinx.ext.intersphinx',
    'sphinx.ext.napoleon',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.mathjax',
    'sphinx.ext.viewcode',
    'sphinx.ext.inheritance_diagram',
]
inheritance_graph_attrs = dict(rankdir="TB", size='"7.0, 10.0"',
                               fontsize=12, ratio='auto',
                               bgcolor='"#ffffff"', center='true', style='solid')
inheritance_node_attrs = dict(shape='ellipse', fontsize=12,
                              fontname="monspace", height=0.75)

napoleon_use_param = False
# TODO: enable type aliases
# napoleon_preprocess_types = True  # needed for type aliases to work
# napoleon_type_aliases = {
#     "array_like": ":term:`array_like`",
#     "ndarray": "~numpy.ndarray",
#     "RandomState": ":class:`~numpy.random.RandomState`",
#     "DataFrame": ":class:`~pandas.DataFrame`",
#     "Series": ":class:`~pandas.Series`",
# }

autosummary_generate = True
autodoc_default_options = {'members': None,
                           'show-inheritance': None,
                           'inherited-members': None,
                           'member-order': 'groupwise'}

mathjax3_config = {
    'tex': {
        'macros': {
            'vec': [r'{\bf #1}', 1],
            'ldot': [r'\left\langle #1, #2 \right\rangle', 2],
            'E': r'\mathbb{E}',
            'T': r'\mathcal{T}',
            'argmin': r'\mathrm{argmin}',
            'Var': r'\mathrm{Var}'
        }
    }
}

# Add any paths that contain templates here, relative to this directory.
templates_path = ['_templates']

# The suffix(es) of source filenames.
# You can specify multiple suffix as a list of strings:
#
# source_suffix = ['.rst', '.md']
source_suffix = '.rst'

# The root toctree document.
root_doc = 'index'

# The language for content autogenerated by Sphinx. Refer to documentation
# for a list of supported languages.
#
# This is also used if you do content translation via gettext catalogs.
# Usually you set "language" from the command line for these cases.
language = "en"

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This pattern also affects html_static_path and html_extra_path.
exclude_patterns = []

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = None


# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
#
html_theme = 'sphinx_rtd_theme'

# Theme options are theme-specific and customize the look and feel of a theme
# further.  For a list of options available for each theme, see the
# documentation.
#
html_theme_options = {
    'collapse_navigation': False
}

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
# html_static_path = ['_static']
html_extra_path = ['map.svg', 'Causal-Inference-User-Guide-v4-022520.pdf', "spec/img"]

# Custom sidebar templates, must be a dictionary that maps document names
# to template names.
#
# The default sidebars (for documents that don't match any pattern) are
# defined by theme itself.  Builtin themes are using these templates by
# default: ``['localtoc.html', 'relations.html', 'sourcelink.html',
# 'searchbox.html']``.
#
# html_sidebars = {}

html_logo = 'econml-logo-inverse.png'
html_favicon = 'econml.ico'

# -- Options for HTMLHelp output ---------------------------------------------

# Output file base name for HTML help builder.
htmlhelp_basename = 'econmldoc'


# -- Options for LaTeX output ------------------------------------------------

latex_elements = {
    # The paper size ('letterpaper' or 'a4paper').
    #
    # 'papersize': 'letterpaper',

    # The font size ('10pt', '11pt' or '12pt').
    #
    # 'pointsize': '10pt',

    # Additional stuff for the LaTeX preamble.
    #
    # 'preamble': '',

    # Latex figure (float) alignment
    #
    # 'figure_align': 'htbp',
}

# Grouping the document tree into LaTeX files. List of tuples
# (source start file, target name, title,
#  author, documentclass [howto, manual, or own class]).
latex_documents = [
    (root_doc, 'econml.tex', 'econml Documentation',
     'PyWhy contributors', 'manual'),
]


# -- Options for manual page output ------------------------------------------

# One entry per manual page. List of tuples
# (source start file, name, description, authors, manual section).
man_pages = [
    (root_doc, 'econml', 'econml Documentation',
     [author], 1)
]


# -- Options for Texinfo output ----------------------------------------------

# Grouping the document tree into Texinfo files. List of tuples
# (source start file, target name, title, author,
#  dir menu entry, description, category)
texinfo_documents = [
    (root_doc, 'econml', 'econml Documentation',
     author, 'econml', 'One line description of project.',
     'Miscellaneous'),
]


# -- Options for Epub output -------------------------------------------------

# Bibliographic Dublin Core info.
epub_title = project

# The unique identifier of the text. This can be a ISBN number
# or the project homepage.
#
# epub_identifier = ''

# A unique identification for the text.
#
# epub_uid = ''

# A list of files that should not be packed into the epub file.
epub_exclude_files = ['search.html']


# -- Extension configuration -------------------------------------------------

# -- Options for intersphinx extension ---------------------------------------

# Example configuration for intersphinx: refer to the Python standard library.
intersphinx_mapping = {'python': ('https://docs.python.org/3', None),
                       'numpy': ('https://numpy.org/doc/stable/', None),
                       'sklearn': ('https://scikit-learn.org/stable/', None),
                       'matplotlib': ('https://matplotlib.org/stable/', None),
                       'shap': ('https://shap.readthedocs.io/en/stable/', None),
                       'dowhy': ('https://www.pywhy.org/dowhy/v0.8/', None),
                       'statsmodels': ('https://www.statsmodels.org/stable/', None)}


# -- Options for todo extension ----------------------------------------------

# If true, `todo` and `todoList` produce output, else they produce nothing.
todo_include_todos = False

# -- Options for doctest extension -------------------------------------------
import doctest
doctest_default_flags = (doctest.DONT_ACCEPT_TRUE_FOR_1 |
                         doctest.ELLIPSIS |
                         doctest.IGNORE_EXCEPTION_DETAIL |
                         doctest.NORMALIZE_WHITESPACE)


def exclude_entity(app, what, name, obj, skip, opts):
    # we can document otherwise excluded entities here by returning False
    # or skip otherwise included entities by returning True
    if name in ["_RLearner", "_OrthoLearner", "_crossfit"]:
        return False
    return None


def setup(app):
    app.connect('autodoc-skip-member', exclude_entity)
    ()
