





from IPython.display import display, Image
display(Image(filename="/home/<USER>/OneDrive/Productive/Causality/img/1.png"))





from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/navigation.png"))

















from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/microservice-architecture1.png"))





from IPython.display import Image
Image('https://www.pywhy.org/dowhy/v0.10.1/_images/example_notebooks_gcm_rca_microservice_architecture_1_0.png', width=500)








import pandas as pd

normal_data = pd.read_csv("/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/rca_microservice_architecture_latencies.csv")
normal_data.head()





axes = pd.plotting.scatter_matrix(normal_data, figsize=(10, 10), c='#ff0d57', alpha=0.2, hist_kwds={'color':['#1E88E5']});
for ax in axes.flatten():
    ax.xaxis.label.set_rotation(90)
    ax.yaxis.label.set_rotation(0)
    ax.yaxis.label.set_ha('right')








import networkx as nx
from dowhy import gcm
from dowhy.utils import plot, bar_plot

causal_graph = nx.DiGraph([('www', 'Website'),
                           ('Auth Service', 'www'),
                           ('API', 'www'),
                           ('Customer DB', 'Auth Service'),
                           ('Customer DB', 'API'),
                           ('Product Service', 'API'),
                           ('Auth Service', 'API'),
                           ('Order Service', 'API'),
                           ('Shipping Cost Service', 'Product Service'),
                           ('Caching Service', 'Product Service'),
                           ('Product DB', 'Caching Service'),
                           ('Customer DB', 'Product Service'),
                           ('Order DB', 'Order Service')])


plot(causal_graph, figure_size=[13, 13])








from scipy.stats import halfnorm

causal_model = gcm.StructuralCausalModel(causal_graph)

for node in causal_graph.nodes:
    if len(list(causal_graph.predecessors(node))) > 0:
        causal_model.set_causal_mechanism(node, gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
    else:
        causal_model.set_causal_mechanism(node, gcm.ScipyDistribution(halfnorm))








outlier_data = pd.read_csv("/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/rca_microservice_architecture_anomaly.csv")
outlier_data





outlier_data.iloc[0]['Website']-normal_data['Website'].mean()








gcm.config.disable_progress_bars() # to disable print statements when computing Shapley values

median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.attribute_anomalies,
                        causal_model,
                        normal_data,
                        target_node='Website',
                        anomaly_samples=outlier_data),
    num_bootstrap_resamples=10)





bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')








outlier_data = pd.read_csv("/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/rca_microservice_architecture_anomaly_1000.csv")
outlier_data.head()





outlier_data['Website'].mean() - normal_data['Website'].mean()








import numpy as np

median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    lambda : gcm.distribution_change(causal_model,
                                     normal_data.sample(frac=0.6),
                                     outlier_data.sample(frac=0.6),
                                     'Website',
                                     difference_estimation_func=lambda x, y: np.mean(y) - np.mean(x)),
    num_bootstrap_resamples = 10)

bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')








from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/shifting-resources.png"))





median_mean_latencies, uncertainty_mean_latencies = gcm.confidence_intervals(
    lambda : gcm.fit_and_compute(gcm.interventional_samples,
                                 causal_model,
                                 outlier_data,
                                 interventions = {
                                    "Caching Service": lambda x: x-1,
                                    "Shipping Cost Service": lambda x: x+2
                                 },
                                 observed_data=outlier_data)().mean().to_dict(),
    num_bootstrap_resamples=10)





avg_website_latency_before = outlier_data.mean().to_dict()['Website']
bar_plot(dict(before=avg_website_latency_before, after=median_mean_latencies['Website']),
                  dict(before=np.array([avg_website_latency_before, avg_website_latency_before]), after=uncertainty_mean_latencies['Website']),
                  ylabel='Avg. Website Latency',
                  figure_size=(3, 2),
                  bar_width=0.4,
                  xticks=['Before', 'After'],
                  xticks_rotation=45)








from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/hotel-booking-cancellations.png"))





import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

import dowhy





dataset = pd.read_csv('https://raw.githubusercontent.com/Sid-darthvader/DoWhy-The-Causal-Story-Behind-Hotel-Booking-Cancellations/master/hotel_bookings.csv')
dataset.head()


dataset.columns





# Total stay in nights
dataset['total_stay'] = dataset['stays_in_week_nights']+dataset['stays_in_weekend_nights']
# Total number of guests
dataset['guests'] = dataset['adults']+dataset['children'] +dataset['babies']
# Creating the different_room_assigned feature
dataset['different_room_assigned']=0
slice_indices =dataset['reserved_room_type']!=dataset['assigned_room_type']
dataset.loc[slice_indices,'different_room_assigned']=1
# Deleting older features
dataset = dataset.drop(['stays_in_week_nights','stays_in_weekend_nights','adults','children','babies'
                        ,'reserved_room_type','assigned_room_type'],axis=1)
dataset.columns





dataset.isnull().sum() # Country,Agent,Company contain 488,16340,112593 missing entries
dataset = dataset.drop(['agent','company'],axis=1)
# Replacing missing countries with most freqently occuring countries
dataset['country']= dataset['country'].fillna(dataset['country'].mode()[0])


dataset = dataset.drop(['reservation_status','reservation_status_date','arrival_date_day_of_month'],axis=1)
dataset = dataset.drop(['arrival_date_year'],axis=1)
dataset = dataset.drop(['distribution_channel'], axis=1)


# Replacing 1 by True and 0 by False for the experiment and outcome variables
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(1,True)
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(0,False)
dataset['is_canceled']= dataset['is_canceled'].replace(1,True)
dataset['is_canceled']= dataset['is_canceled'].replace(0,False)
dataset.dropna(inplace=True)
print(dataset.columns)
dataset.iloc[:, 5:20].head(100)


dataset = dataset[dataset.deposit_type=="No Deposit"]
dataset.groupby(['deposit_type','is_canceled']).count()


dataset_copy = dataset.copy(deep=True)





counts_sum=0
for i in range(1,10000):
        counts_i = 0
        rdf = dataset.sample(1000)
        counts_i = rdf[rdf["is_canceled"]== rdf["different_room_assigned"]].shape[0]
        counts_sum+= counts_i
counts_sum/10000





# Expected Count when there are no booking changes
counts_sum=0
for i in range(1,10000):
        counts_i = 0
        rdf = dataset[dataset["booking_changes"]==0].sample(1000)
        counts_i = rdf[rdf["is_canceled"]== rdf["different_room_assigned"]].shape[0]
        counts_sum+= counts_i
counts_sum/10000





# Expected Count when there are booking changes = 66.4%
counts_sum=0
for i in range(1,10000):
        counts_i = 0
        rdf = dataset[dataset["booking_changes"]>0].sample(1000)
        counts_i = rdf[rdf["is_canceled"]== rdf["different_room_assigned"]].shape[0]
        counts_sum+= counts_i
counts_sum/10000











pip install pygraphviz


import pygraphviz
causal_graph = """digraph {
different_room_assigned[label="Different Room Assigned"];
is_canceled[label="Booking Cancelled"];
booking_changes[label="Booking Changes"];
previous_bookings_not_canceled[label="Previous Booking Retentions"];
days_in_waiting_list[label="Days in Waitlist"];
lead_time[label="Lead Time"];
market_segment[label="Market Segment"];
country[label="Country"];
U[label="Unobserved Confounders",observed="no"];
is_repeated_guest;
total_stay;
guests;
meal;
hotel;
U->{different_room_assigned,required_car_parking_spaces,guests,total_stay,total_of_special_requests};
market_segment -> lead_time;
lead_time->is_canceled; country -> lead_time;
different_room_assigned -> is_canceled;
country->meal;
lead_time -> days_in_waiting_list;
days_in_waiting_list ->{is_canceled,different_room_assigned};
previous_bookings_not_canceled -> is_canceled;
previous_bookings_not_canceled -> is_repeated_guest;
is_repeated_guest -> {different_room_assigned,is_canceled};
total_stay -> is_canceled;
guests -> is_canceled;
booking_changes -> different_room_assigned; booking_changes -> is_canceled;
hotel -> {different_room_assigned,is_canceled};
required_car_parking_spaces -> is_canceled;
total_of_special_requests -> {booking_changes,is_canceled};
country->{hotel, required_car_parking_spaces,total_of_special_requests};
market_segment->{hotel, required_car_parking_spaces,total_of_special_requests};
}"""





model= dowhy.CausalModel(
        data = dataset,
        graph=causal_graph.replace("\n", " "),
        treatment="different_room_assigned",
        outcome='is_canceled')
model.view_model()
from IPython.display import Image, display
display(Image(filename="causal_model.png"))





#Identify the causal effect
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)





estimate = model.estimate_effect(identified_estimand,
                                 method_name="backdoor.propensity_score_weighting",target_units="ate")
# ATE = Average Treatment Effect
# ATT = Average Treatment Effect on Treated (i.e. those who were assigned a different room)
# ATC = Average Treatment Effect on Control (i.e. those who were not assigned a different room)
print(estimate)








refute1_results=model.refute_estimate(identified_estimand, estimate,
        method_name="random_common_cause")
print(refute1_results)





refute2_results=model.refute_estimate(identified_estimand, estimate,
        method_name="placebo_treatment_refuter")
print(refute2_results)





refute3_results=model.refute_estimate(identified_estimand, estimate,
        method_name="data_subset_refuter")
print(refute3_results)











import networkx as nx
import matplotlib.pyplot as plt

# Initialize a directed graph
causal_graph = nx.DiGraph()

# Add edges to the graph
causal_graph.add_edge("X", "Y")
causal_graph.add_edge("Y", "Z")

# Define the layout to make it horizontal
pos = {"X": (0, 0), "Y": (1, 0), "Z": (2, 0)}

# Plot the causal graph with the specified layout
nx.draw(causal_graph, pos=pos, with_labels=True, node_size=500, node_color="skyblue", font_size=12, font_color="black", font_weight="bold", arrowsize=20)

# Show the plot
plt.show()






df = pd.read_csv("/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/rca_microservice_architecture_latencies.csv")
df.head()


from dowhy import CausalModel
import networkx as nx
model = CausalModel(
   data=df, # some pandas dataframe
   treatment="Caching Service",
   outcome="Website",
   graph="\n".join(nx.generate_gml(causal_graph))
)





model = CausalModel(
   data=df, # some pandas dataframe
   treatment="Caching Service",
   outcome="Website",
   common_causes=["Product DB", "Customer DB"],
)











import dowhy
from dowhy import CausalModel

import numpy as np
import pandas as pd
import graphviz
import networkx as nx

np.set_printoptions(precision=3, suppress=True)
np.random.seed(0)





def make_graph(adjacency_matrix, labels=None):
    idx = np.abs(adjacency_matrix) > 0.01
    dirs = np.where(idx)
    d = graphviz.Digraph(engine='dot')
    names = labels if labels else [f'x{i}' for i in range(len(adjacency_matrix))]
    for name in names:
        d.node(name)
    for to, from_, coef in zip(dirs[0], dirs[1], adjacency_matrix[idx]):
        d.edge(names[from_], names[to], label=str(coef))
    return d

def str_to_dot(string):
    '''
    Converts input string from graphviz library to valid DOT graph format.
    '''
    graph = string.strip().replace('\n', ';').replace('\t','')
    graph = graph[:9] + graph[10:-2] + graph[-1] # Removing unnecessary characters from string
    return graph





data_mpg = pd.read_csv('http://archive.ics.uci.edu/ml/machine-learning-databases/auto-mpg/auto-mpg.data-original',
                   delim_whitespace=True, header=None,
                   names = ['mpg', 'cylinders', 'displacement',
                            'horsepower', 'weight', 'acceleration',
                            'model year', 'origin', 'car name'])
data_mpg.dropna(inplace=True)
data_mpg.drop(['model year', 'origin', 'car name'], axis=1, inplace=True)
print(data_mpg.shape)
data_mpg.head()





from cdt.causality.graph import LiNGAM, PC, GES

graphs = {}
labels = [f'{col}' for i, col in enumerate(data_mpg.columns)]
functions = {
    'LiNGAM' : LiNGAM,
    'PC' : PC,
    'GES' : GES,
}

for method, lib in functions.items():
    obj = lib()
    output = obj.predict(data_mpg)
    adj_matrix = nx.to_numpy_array(output)
    adj_matrix = np.asarray(adj_matrix)
    graph_dot = make_graph(adj_matrix, labels)
    graphs[method] = graph_dot

# Visualize graphs
for method, graph in graphs.items():
    print("Method : %s"%(method))
    display(graph)





for method, graph in graphs.items():
        if method != "LiNGAM":
            continue
        print('\n*****************************************************************************\n')
        print("Causal Discovery Method : %s"%(method))

        # Obtain valid dot format
        graph_dot = str_to_dot(graph.source)

        # Define Causal Model
        model=CausalModel(
                data = data_mpg,
                treatment='mpg',
                outcome='weight',
                graph=graph_dot)

        # Identification
        identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
        print(identified_estimand)

        # Estimation
        estimate = model.estimate_effect(identified_estimand,
                                        method_name="backdoor.linear_regression",
                                        control_value=0,
                                        treatment_value=1,
                                        confidence_intervals=True,
                                        test_significance=True)
        print("Causal Estimate is " + str(estimate.value))


estimate.value














from cdt.data import load_dataset
data_sachs, graph_sachs = load_dataset("sachs")

data_sachs.dropna(inplace=True)
print(data_sachs.shape)
data_sachs.head()





labels = [f'{col}' for i, col in enumerate(data_sachs.columns)]
adj_matrix = nx.to_numpy_array(graph_sachs)
adj_matrix = np.asarray(adj_matrix)
graph_dot = make_graph(adj_matrix, labels)
display(graph_dot)








from cdt.causality.graph import LiNGAM, PC, GES

graphs = {}
graphs_nx = {}
labels = [f'{col}' for i, col in enumerate(data_sachs.columns)]
functions = {
    'LiNGAM' : LiNGAM,
    'PC' : PC,
    'GES' : GES,
}

for method, lib in functions.items():
    obj = lib()
    output = obj.predict(data_sachs)
    graphs_nx[method] = output
    adj_matrix = nx.to_numpy_array(output)
    adj_matrix = np.asarray(adj_matrix)
    graph_dot = make_graph(adj_matrix, labels)
    graphs[method] = graph_dot

# Visualize graphs
for method, graph in graphs.items():
    print("Method : %s"%(method))
    display(graph)








for method, graph in graphs.items():
        if method != "LiNGAM":
            continue
        print('\n*****************************************************************************\n')
        print("Causal Discovery Method : %s"%(method))

        # Obtain valid dot format
        graph_dot = str_to_dot(graph.source)

        # Define Causal Model
        model=CausalModel(
                data = data_sachs,
                treatment='PIP2',
                outcome='PKC',
                graph=graph_dot)

        # Identification
        identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
        print(identified_estimand)

        # Estimation
        estimate = model.estimate_effect(identified_estimand,
                                        method_name="backdoor.linear_regression",
                                        control_value=0,
                                        treatment_value=1,
                                        confidence_intervals=True,
                                        test_significance=True)
        print("Causal Estimate is " + str(estimate.value))











from cdt.metrics import SHD, SHD_CPDAG, SID, SID_CPDAG
from numpy.random import randint

for method, graph in graphs_nx.items():
    print("***********************************************************")
    print("Method: %s"%(method))
    tar, pred = graph_sachs, graph
    print("SHD_CPDAG = %f"%(SHD_CPDAG(tar, pred)))
    print("SHD = %f"%(SHD(tar, pred, double_for_anticausal=False)))
    print("SID_CPDAG = [%f, %f]"%(SID_CPDAG(tar, pred)))
    print("SID = %f"%(SID(tar, pred)))








import itertools
from numpy.random import randint
from cdt.metrics import SHD, SHD_CPDAG, SID, SID_CPDAG

# Find combinations of pair of methods to compare
combinations = list(itertools.combinations(graphs_nx, 2))

for pair in combinations:
    print("***********************************************************")
    graph1 = graphs_nx[pair[0]]
    graph2 = graphs_nx[pair[1]]
    print("Methods: %s and %s"%(pair[0], pair[1]))
    print("SHD_CPDAG = %f"%(SHD_CPDAG(graph1, graph2)))
    print("SHD = %f"%(SHD(graph1, graph2, double_for_anticausal=False)))
    print("SID_CPDAG = [%f, %f]"%(SID_CPDAG(graph1, graph2)))
    print("SID = %f"%(SID(graph1, graph2)))

















# Import the necessary libraries and functions for this demo
import numpy as np
import pandas as pd
import networkx as nx
from sklearn.ensemble import GradientBoostingRegressor
from dowhy.gcm.falsify import FalsifyConst, falsify_graph, plot_local_insights, run_validations, apply_suggestions
from dowhy.gcm.independence_test.generalised_cov_measure import generalised_cov_based
from dowhy.gcm.util import plot
from dowhy.gcm.util.general import set_random_seed
from dowhy.gcm.ml import SklearnRegressionModel

# Set random seed
set_random_seed(1332)





# Load example graph and data
g_true = nx.read_gml(f"/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/falsify_g_true.gml")
data = pd.read_csv(f"/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/falsify_data_nonlinear.csv")

# Plot true DAG
print("True DAG")
plot(g_true)





result = falsify_graph(g_true, data, plot_histogram=True)
# Summarize the result
print(result)





print(f"Graph is falsifiable: {result.falsifiable}, Graph is falsified: {result.falsified}")





# Simulate a domain expert with knowledge over some of the edges in the system
g_given = g_true.copy()
g_given.add_edges_from(([('X4', 'X1')]))  # Add wrong edge from X4 -> X1
g_given.remove_edge('X2', 'X0')  # Remove true edge from X2 -> X0
plot(g_given)


# Run evaluation and plot the result using `plot=True`
result = falsify_graph(g_given, data, plot_histogram=True)
# Summarize the result
print(result)





# Plot nodes for which violations of LMCs occured
print('Violations of LMCs')
plot_local_insights(g_given, result, method=FalsifyConst.VALIDATE_LMC)








# Load the data and consensus DAG
data_url = "https://raw.githubusercontent.com/FenTechSolutions/CausalDiscoveryToolbox/master/cdt/data/resources/cyto_full_data.csv"
data_sachs = pd.read_csv(data_url)
g_sachs = nx.read_gml('/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/falsify_sachs.gml')


plot(g_sachs)





# Define independence test based on the generalised covariance measure with gradient boosted decision trees as models
def create_gradient_boost_regressor(**kwargs) -> SklearnRegressionModel:
    return SklearnRegressionModel(GradientBoostingRegressor(**kwargs))
def gcm(X, Y, Z=None):
    return generalised_cov_based(X, Y, Z=Z, prediction_model_X=create_gradient_boost_regressor,
                                 prediction_model_Y=create_gradient_boost_regressor)





# Run evaluation for consensus graph and data.
result_sachs = falsify_graph(g_sachs, data_sachs, n_permutations=100,
                              independence_test=gcm,
                              conditional_independence_test=gcm,
                              plot_histogram=True)
print(result_sachs)











result = falsify_graph(g_given, data, plot_histogram=True, suggestions=True)
print(result)





# Plot suggestions
plot_local_insights(g_given, result, method=FalsifyConst.VALIDATE_CM)





# Apply all suggestions (we could exclude suggestions via `edges_to_keep=[('X3', 'X4')])`)
g_given_pruned = apply_suggestions(g_given, result)
# Plot pruned DAG
plot(g_given_pruned)





import numpy as np, pandas as pd
X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





import dowhy.gcm as gcm
# Null hypothesis: x is independent of y given z
p_value = gcm.independence_test(X, Z, conditioned_on=Y, method='kernel')
p_value





# Null hypothesis: x is independent of y
p_value = gcm.independence_test(X, Z, method='kernel')
p_value











import networkx as nx
import matplotlib.pyplot as plt

# Initialize a directed graph
causal_graph = nx.DiGraph()

# Add edges to the graph
causal_graph.add_edge("X", "Y")
causal_graph.add_edge("Y", "Z")

from dowhy.gcm.falsify import falsify_graph
# causal_graph is a networkx digraph
result = falsify_graph(causal_graph, data, show_progress_bar=False)
print(result)





print(f"Graph is falsifiable: {result.falsifiable}, Graph is falsified: {result.falsified}")








from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/pcm.png"))





from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/scm.png"))





import networkx as nx
import matplotlib.pyplot as plt

# Initialize a directed graph
causal_graph = nx.DiGraph()

# Add edges to the graph
causal_graph.add_edge("X", "Y")
causal_graph.add_edge("Y", "Z")

from dowhy import gcm
causal_model = gcm.StructuralCausalModel(causal_graph)





import numpy as np, pandas as pd

X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))
data.head()





gcm.auto.assign_causal_mechanisms(causal_model, data)





causal_model.set_causal_mechanism('X', gcm.EmpiricalDistribution())
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
causal_model.set_causal_mechanism('Z', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))








gcm.fit(causal_model, data)

















import numpy as np, pandas as pd
X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))
data.head()





import networkx as nx
import dowhy.gcm as gcm
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')]))
gcm.auto.assign_causal_mechanisms(causal_model, data) # Automatically assigns additive noise models to non-root nodes
gcm.fit(causal_model, data)





generated_data = gcm.draw_samples(causal_model, num_samples=1000)
generated_data.head()





gcm.divergence.auto_estimate_kl_divergence(data.to_numpy(), generated_data.to_numpy())











from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/pcm.png"))





from scipy.stats import norm
import networkx as nx
from dowhy import gcm
causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X', 'Y')]))
causal_model.set_causal_mechanism('X', gcm.ScipyDistribution(norm))





causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(
    prediction_model=gcm.ml.create_linear_regressor(),
    noise_model=gcm.ScipyDistribution(norm)))





def fit(self, X: np.ndarray, Y: np.ndarray) -> None: ...
def predict(self, X: np.ndarray) -> np.ndarray: ...





import numpy as np, pandas as pd
X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2*X + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y))


最后，我们可以从训练数据中学习这些因果模型的参数。


gcm.fit(causal_model, data)





在某些情况下，可能已知地面真实模型，并且应该使用它们。 假设我们知道我们的关系是线性的，具有系数alpha=2,beta=3
。 让我们利用这些知识，创建一个实现 PredictionModel 接口的自定义预测模型：


import dowhy.gcm.ml.prediction_model
class MyCustomModel(gcm.ml.PredictionModel):
    def __init__(self, coefficient):
        self.coefficient = coefficient
    def fit(self, X, Y):
        # Nothing to fit here, since we know the ground truth.
        pass
    def predict(self, X):
        return self.coefficient * X
    def clone(self):
        return MyCustomModel(self.coefficient)





causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(MyCustomModel(2)))
gcm.fit(causal_model, data)








# Let’s say we have a function that computes Pi using the Monte Carlo method with 1000 trials:

import numpy as np
def compute_pi_monte_carlo():
    trials = 1000
    return 4*(np.random.default_rng().uniform(-1, 1, (trials,))**2+
              np.random.default_rng().uniform(-1, 1, (trials,))**2 <= 1).sum() / trials


compute_pi_monte_carlo()





from dowhy import gcm
median, intervals = gcm.confidence_intervals(compute_pi_monte_carlo,
                                             num_bootstrap_resamples=1000)
median, intervals











import pandas as pd
from scipy.stats import halfnorm
X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = np.maximum(X, Y) + np.random.normal(loc=0, scale=1, size=1000)
data_old = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))
X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = X + Y + np.random.normal(loc=0, scale=1, size=1000)
data_new = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





import networkx as nx
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Z'), ('Y', 'Z')]))  # X -> Z <- Y
gcm.auto.assign_causal_mechanisms(causal_model, data_old)





def f():
    return gcm.distribution_change(causal_model, data_old, data_new, 'Z')





gcm.confidence_intervals(f)





gcm.confidence_intervals(lambda: gcm.distribution_change(causal_model,
                                                         data_old, data_new,
                                                         target_node='Z'))





Z = np.random.normal(loc=0, scale=1, size=1000)
X = 2*Z + np.random.normal(loc=0, scale=1, size=1000)
Y = 3*X + 4*Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(dict(X=X, Y=Y, Z=Z))
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('Z', 'Y'), ('Z', 'X'), ('X', 'Y')]))
gcm.auto.assign_causal_mechanisms(causal_model, data_old)





strength_median, strength_intervals = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.arrow_strength,
                        causal_model,
                        bootstrap_training_data=data,
                        target_node='Y'))
strength_median, strength_intervals





gcm.fit(causal_model, data)
strength_median, strength_intervals = gcm.confidence_intervals(
    gcm.bootstrap_sampling(gcm.arrow_strength,
                           causal_model,
                           target_node='Y'))
strength_median, strength_intervals

















from IPython.display import display, Image
display(Image(url="https://raw.githubusercontent.com/microsoft/dowhy/main/docs/images/dowhy-schematic.png"))























# model is an instance of CausalModel
identified_estimand = model.identify_effect()
print(identified_estimand)





identified_estimand = model.identify_effect(method_name="maximal-adjustment")











# model is an instance of CausalModel
identified_estimand = model.identify_effect()
print(identified_estimand)





# model is an instance of CausalModel
identified_estimand = model.identify_effect()
print(identified_estimand)





# model is an instance of CausalModel
identified_estimand = model.identify_effect(method_name="id-algorithm")
print(identified_estimand)





from dowhy.causal_identifier import identify_effect_id
identified_estimand_id = identify_effect_id(
       graph, treatment_name, outcome_name,
)
# Note that the return type for id_identify_effect is IDExpression and not IdentifiedEstimand
print(identified_estimand)











estimate = model.estimate_effect(identified_estimand,
       method_name="backdoor.linear_regression",
       test_significance=True
)
print(estimate)

















































































