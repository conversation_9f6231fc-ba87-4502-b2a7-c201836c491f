





# 导入用于配置日志级别的字典
import logging.config

# 从DoWhy库中导入CausalModel类，这个类用于创建因果模型
from dowhy import CausalModel  
# 导入Econml估计器，这是用于因果效应估计的一个方法
from dowhy.causal_estimators.econml import Econml
# 导入倾向性评分匹配估计器，这也是用于因果效应估计的一个方法
from dowhy.causal_estimators.propensity_score_matching_estimator import PropensityScoreMatchingEstimator
# 导入因果图类，用于表示和操作因果关系
from dowhy.causal_graph import CausalGraph
# 导入函数式API，用于自动或手动识别因果效应
from dowhy.causal_identifier import (
    BackdoorAdjustment,
    EstimandType,
    identify_effect,
    identify_effect_auto,
    identify_effect_id,
)
# 导入用于进行反驳分析（即，敏感性分析）的函数
from dowhy.causal_refuters import (
    refute_bootstrap,
    refute_data_subset,
    refute_estimate,
)
# 导入一个用于生成线性数据集的函数
from dowhy.datasets import linear_dataset

# 设置默认的日志配置，这里将日志级别设置为“WARN”
DEFAULT_LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "loggers": {
        "": {
            "level": "WARN",
        },
    },
}

# 设置随机种子以生成确定性的数据集，并避免在运行测试时出现问题
import numpy as np
np.random.seed(1)

# 应用日志配置
logging.config.dictConfig(DEFAULT_LOGGING)

# 禁用某些警告输出
# 这里特别禁用了sklearn库中的DataConversionWarning警告
import warnings
from sklearn.exceptions import DataConversionWarning
warnings.filterwarnings(action="ignore", category=DataConversionWarning)






# 设置用于生成数据集的参数
TREATMENT_IS_BINARY = True  # 处理（干预）是否是二进制变量（True 或 False）
BETA = 10  # 因果效应的大小
NUM_SAMPLES = 500  # 样本数量
NUM_CONFOUNDERS = 3  # 混杂变量（也称为共同原因）的数量
NUM_INSTRUMENTS = 2  # 工具变量的数量
NUM_EFFECT_MODIFIERS = 2  # 效应修饰变量（即，能改变处理效应大小的变量）的数量

# 使用给定的参数创建一个线性数据集
data = linear_dataset(
    beta=BETA,
    num_common_causes=NUM_CONFOUNDERS,
    num_instruments=NUM_INSTRUMENTS,
    num_effect_modifiers=NUM_EFFECT_MODIFIERS,
    num_samples=NUM_SAMPLES,
    treatment_is_binary=True,
)

# 创建第二个线性数据集（参数与第一个数据集相同）
data_2 = linear_dataset(
    beta=BETA,
    num_common_causes=NUM_CONFOUNDERS,
    num_instruments=NUM_INSTRUMENTS,
    num_effect_modifiers=NUM_EFFECT_MODIFIERS,
    num_samples=NUM_SAMPLES,
    treatment_is_binary=True,
)

# 获取处理（干预）变量的名称
treatment_name = data["treatment_name"]
print('treatment_name is',treatment_name)
# 获取结果变量的名称
outcome_name = data["outcome_name"]
print('outcome_name is',outcome_name)

# 使用从数据集中获取的信息创建一个因果图对象
graph = CausalGraph(
    treatment_name=treatment_name,  # 处理变量名称
    outcome_name=outcome_name,  # 结果变量名称
    graph=data["gml_graph"],  # 因果图的GML格式表示
    effect_modifier_names=data["effect_modifier_names"],  # 效应修饰变量名称
    common_cause_names=data["common_causes_names"],  # 混杂变量（共同原因）名称
    observed_node_names=data["df"].columns.tolist(),  # 数据集中所有观察到的变量名称
)



graph.view_graph()





# 使用默认的 identify_effect 函数来识别因果效应。
# 这个函数需要因果图、处理变量名称和结果变量名称作为输入。
identified_estimand = identify_effect(graph, 
                                      treatment_name, 
                                      outcome_name)

# 使用 auto_identify_effect 函数，并传入额外的参数。
# 这个函数会自动选择最佳的识别策略。
# 参数 estimand_type 指定了估计量的类型（这里是非参数平均处理效应）。
# 参数 backdoor_adjustment 指定了用于后门调整的方法（这里是效率最高的方法）。
identified_estimand_auto = identify_effect_auto(
    graph,
    treatment_name,
    outcome_name,
    estimand_type=EstimandType.NONPARAMETRIC_ATE,
    backdoor_adjustment=BackdoorAdjustment.BACKDOOR_EFFICIENT,
)

# 使用 id_identify_effect 函数来识别因果效应。
# 这个函数返回的是一个 IDExpression 对象，而不是 IdentifiedEstimand 对象。
# 这通常用于更高级的识别任务。
identified_estimand_id = identify_effect_id(graph, 
                                            treatment_name, 
                                            outcome_name
)  

# 打印第一个识别方法得到的估计量。
print(identified_estimand)



# 打印第2个识别方法得到的估计量。
print(identified_estimand_auto)


# 打印第3个识别方法得到的估计量。
print(identified_estimand_id)





# 使用基础的估算效应函数
# 创建一个倾向性评分匹配估计器（Propensity Score Matching Estimator）
# identified_estimand 是前面识别出来的因果效应估算量。
# 其他参数（如 test_significance、evaluate_effect_strength、confidence_intervals）都设置为默认值。
estimator = PropensityScoreMatchingEstimator(
    identified_estimand=identified_estimand,
    test_significance=None,
    evaluate_effect_strength=False,
    confidence_intervals=False,
).fit(
    # 使用第一个数据集进行拟合
    data=data["df"],
    # 获取效应修饰变量（effect modifiers），这些变量可能会改变处理效应的大小
    effect_modifier_names=graph.get_effect_modifiers(treatment_name, outcome_name),
)

# 使用该估计器来估计因果效应。
# control_value 和 treatment_value 分别设置为 0 和 1，表示处理的两个不同水平。
# target_units 设置为 "ate"，表示我们想要估计平均处理效应（Average Treatment Effect）。
estimate = estimator.estimate_effect(
    data=data["df"],
    control_value=0,
    treatment_value=1,
    target_units="ate",
)

# 使用相同的估计器，但使用第二个数据集来估计因果效应。
second_estimate = estimator.estimate_effect(
    data=data_2["df"],
    control_value=0,
    treatment_value=1,
    target_units="ate",
)

# 打印两次估计的结果
print(estimate)
print("-----------")
print(second_estimate)



# 导入所需的库和模型
from econml.dml import DML
from sklearn.linear_model import LassoCV
from sklearn.preprocessing import PolynomialFeatures
from sklearn.ensemble import GradientBoostingRegressor

# 创建一个 EconML 估计器
# 使用 DML 作为 EconML 的估计方法，并配置相应的模型
# model_y 和 model_t 是用于处理结果（y）和处理（t）的模型，
# 在这里都使用了梯度增强回归（GradientBoostingRegressor）。
# model_final 是用于最终估计的模型，这里使用了 Lasso 回归。
# featurizer 是用于特征工程的，这里使用了一阶多项式特征。
estimator = Econml(
    identified_estimand=identified_estimand,
    econml_estimator=DML(
        model_y=GradientBoostingRegressor(),
        model_t=GradientBoostingRegressor(),
        model_final=LassoCV(fit_intercept=False),
        featurizer=PolynomialFeatures(degree=1, include_bias=True),
    ),
).fit(
    # 使用第一个数据集进行拟合
    data=data["df"],
    # 获取可能会影响处理效应的效应修饰变量（effect modifiers）
    effect_modifier_names=graph.get_effect_modifiers(treatment_name, outcome_name),
)

# 使用拟合好的估计器来估计因果效应
# control_value 和 treatment_value 分别设置为 0 和 1
# target_units 设置为 "ate"，表示我们想要估计平均处理效应（ATE）
estimate_econml = estimator.estimate_effect(
    data=data["df"],
    control_value=0,
    treatment_value=1,
    target_units="ate",
)

# 打印估计结果
print(estimate)






# 使用 refute_estimate 函数执行多种反驳方法。
# 注意：目前这个函数不支持 sensitivity_* 函数。
# 该函数需要数据、已识别的因果效应估算量（estimand）、因果效应估计（estimate）、处理名称和结果名称。
refutation_results = refute_estimate(
    data["df"],
    identified_estimand,
    estimate,
    treatment_name=treatment_name,
    outcome_name=outcome_name,
    refuters=[refute_bootstrap, refute_data_subset],  # 指定要使用的反驳方法
)

# 打印每个反驳方法的结果
for result in refutation_results:
    print(result)

# 或者，你也可以直接执行特定的反驳方法
# 可以将 refute_bootstrap 和 refute_data_subset 替换为其他反驳方法，并添加缺少的参数。

# 使用 bootstrap 方法进行反驳测试
bootstrap_refutation = refute_bootstrap(data["df"], identified_estimand, estimate)
print(bootstrap_refutation)

# 使用数据子集方法进行反驳测试
data_subset_refutation = refute_data_subset(data["df"], identified_estimand, estimate)
print(data_subset_refutation)






# Create Causal Model
causal_model = CausalModel(data=data["df"], treatment=treatment_name, outcome=outcome_name, graph=data["gml_graph"])






identified_estimand_causal_model_api = (
    causal_model.identify_effect()
)  # graph, treatment and outcome comes from the causal_model object

print(identified_estimand_causal_model_api)





estimate_causal_model_api = causal_model.estimate_effect(
    identified_estimand_causal_model_api, method_name="backdoor.propensity_score_matching"
)

print(estimate_causal_model_api)





bootstrap_refutation_causal_model_api = causal_model.refute_estimate(identified_estimand_causal_model_api, estimate_causal_model_api, "bootstrap_refuter")
print(bootstrap_refutation_causal_model_api)

data_subset_refutation_causal_model_api = causal_model.refute_estimate(
    identified_estimand_causal_model_api, estimate_causal_model_api, "data_subset_refuter"
)

print(data_subset_refutation_causal_model_api)



