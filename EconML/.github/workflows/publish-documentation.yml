name: Build and publish the documentation
on:
  workflow_dispatch:
    inputs:
      publish:
        description: 'Whether to publish the documentation (as opposed to just building it)'
        required: false
        default: true
        type: boolean
      environment:
        description: 'Whether to publish to production or test environment'
        required: false
        default: prod
        type: choice
        options: [prod, test]
      ref:
        description: 'The git ref to build the documentation for'
        required: false
        default: ''
        type: string
      use_lkg:
        description: 'Whether to use the last known good versions of dependencies'
        required: false
        default: True
        type: boolean
  # annoyingly, there does not seem to be a way to share these input definitions between triggers
  workflow_call:
    inputs:
      publish:
        description: 'Whether to publish the documentation (as opposed to just building it)'
        required: false
        default: true
        type: boolean
      # choice type only supported for workflow_dispatch, not workflow_call
      environment:
        description: 'Whether to publish to production or test environment'
        required: false
        default: prod
        type: string
      ref:
        description: 'The git ref to build the documentation for'
        required: false
        default: ''
        type: string
      use_lkg:
        description: 'Whether to use the last known good versions of dependencies'
        required: false
        default: True
        type: boolean

jobs:
  create_docs:
    name: Create and publish documentation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        name: Checkout repository
        with:
          ref: ${{ inputs.ref }}
      - uses: actions/setup-python@v4
        name: Setup Python
        with:
          python-version: 3.8 # because of our supported TensorFlow versions, must build on 3.6-3.8
      - run: python -m pip install --upgrade pip && pip install --upgrade setuptools
        name: Ensure latest pip and setuptools
      - run: pip install -e .[all] ${{ inputs.use_lkg && '-r lkg.txt' || '' }}
        name: Install econml[all]
      - run: sudo apt-get -yq install graphviz
        name: Install graphviz
      - run: pip install "sphinx~=4.4.0" "sphinx_rtd_theme~=1.0.0" && python setup.py build_sphinx -W
        name: Build documentation
      - uses: actions/upload-artifact@v3
        name: Upload docs as artifact
        with:
          name: docs
          path: build/sphinx/html/
      - run: |-
          pushd build/sphinx/html
          zip -r docs.zip *
          popd
        name: Zip docs for publishing
        if : ${{ inputs.publish }}
      - uses: azure/login@v1
        name: Login to Azure
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
        if : ${{ inputs.publish }}
      - uses: azure/webapps-deploy@v2
        name: Deploy documentation to Azure web app
        with:
          app-name: ${{ inputs.environment == 'prod' && 'econml' || 'econml-dev' }}
          package: build/sphinx/html/docs.zip
        if: ${{ inputs.publish }}          