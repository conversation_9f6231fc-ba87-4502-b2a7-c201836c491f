# DoWhy v0.13 自定义模型分配指南

## 概述

在DoWhy的图形因果模型(GCM)框架中，可以手动为因果图中的每个节点分配特定的因果机制，而不是依赖自动分配。这种自定义分配能力允许研究者基于领域知识和数据特征来精确控制建模过程。

## 核心概念

### 1. 节点类型与模型选择

#### 根节点（Root Nodes）
- **定义**：在因果图中没有父节点的变量
- **模型类型**：使用随机模型（Stochastic Model）
- **推荐方法**：`ScipyDistribution`

```python
from scipy.stats import norm
import dowhy.gcm as gcm

# 为根节点分配正态分布
causal_model.set_causal_mechanism('X', gcm.ScipyDistribution(norm))
```

#### 非根节点（Non-Root Nodes）
- **定义**：在因果图中有父节点的变量
- **模型类型**：使用条件随机模型（Conditional Stochastic Model）
- **推荐方法**：`AdditiveNoiseModel`

```python
# 为非根节点分配加性噪声模型
causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(prediction_model=LinearRegression()))
```

### 2. 加性噪声模型（Additive Noise Model）

加性噪声模型是非根节点的标准选择，其结构为：
```
Y = f(parents) + noise
```

**核心组件**：
- **预测模型**：定义因果关系 f(parents)
- **噪声模型**：通常为零均值分布

## 自定义预测模型

### 方法一：使用内置模型

DoWhy支持多种内置预测模型：

```python
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor

# 线性回归
causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(LinearRegression()))

# 随机森林
causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(RandomForestRegressor()))
```

### 方法二：创建自定义模型类

通过继承`gcm.ml.PredictionModel`创建完全自定义的模型：

```python
class MyCustomModel(gcm.ml.PredictionModel):
    def __init__(self, coefficient):
        self.coefficient = coefficient

    def fit(self, X, Y):
        # 自定义拟合逻辑
        pass

    def predict(self, X):
        # 自定义预测逻辑
        return self.coefficient * X

# 使用自定义模型
custom_model = MyCustomModel(coefficient=2.5)
causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(custom_model))
```

### 方法三：基于方程式的模型创建

DoWhy还提供基于数学方程式创建因果模型的方法：

```python
# 定义结构方程模型
equations = {
    'X': lambda: np.random.normal(0, 1),
    'Y': lambda X: 2 * X + np.random.normal(0, 0.5),
    'Z': lambda Y: Y**2 + np.random.normal(0, 0.1)
}

causal_model = gcm.StructuralCausalModel.from_equations(equations)
```

## 实际应用示例

### 完整的自定义分配流程

```python
import dowhy.gcm as gcm
import numpy as np
from sklearn.linear_model import LinearRegression
from scipy.stats import norm

# 1. 创建因果图
causal_graph = nx.DiGraph([('X', 'Y'), ('Y', 'Z')])

# 2. 创建图形因果模型
causal_model = gcm.StructuralCausalModel(causal_graph)

# 3. 自定义分配因果机制
# 根节点：使用随机分布
causal_model.set_causal_mechanism('X', gcm.ScipyDistribution(norm))

# 非根节点：使用条件模型
causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(LinearRegression()))

causal_model.set_causal_mechanism('Z', 
    gcm.AdditiveNoiseModel(LinearRegression()))

# 4. 拟合模型
gcm.fit(causal_model, data)
```

## 高级特性

### 1. 噪声模型自定义

```python
from scipy.stats import t

# 使用t分布作为噪声
causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(
        prediction_model=LinearRegression(),
        noise_model=gcm.ScipyDistribution(t, df=3)
    ))
```

### 2. 非线性关系建模

```python
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline

# 多项式回归建模非线性关系
poly_model = Pipeline([
    ('poly', PolynomialFeatures(degree=2)),
    ('linear', LinearRegression())
])

causal_model.set_causal_mechanism('Y', 
    gcm.AdditiveNoiseModel(poly_model))
```

## 最佳实践

### 1. 模型选择原则
- **根据数据特征选择**：连续变量用回归，分类变量用分类器
- **考虑非线性**：复杂关系使用随机森林或神经网络
- **保持简单性**：避免过度复杂化，优先选择可解释模型

### 2. 验证策略
```python
# 模型质量评估
model_quality = gcm.evaluate_causal_model(causal_model, data)
print("模型质量指标:", model_quality)

# 因果强度分析
strengths = gcm.confidence_intervals_of_causal_effects(causal_model, data)
```

### 3. 调试技巧
- 使用`gcm.fit_and_compute`进行增量调试
- 通过可视化检查模型拟合质量
- 对比自动分配与手动分配的结果差异

## 注意事项

1. **一致性检查**：确保手动分配的模型与数据生成过程一致
2. **计算复杂度**：复杂模型可能导致推理时间增长
3. **过拟合风险**：避免模型过于复杂导致泛化能力下降
4. **因果假设**：手动分配需要强因果假设，确保假设合理性

## 总结

DoWhy的自定义模型分配功能提供了高度的灵活性，允许研究者：

- 融入领域专业知识
- 处理特殊的数据分布和关系
- 实现精确的因果建模控制
- 比较不同建模假设的影响

通过合理使用这一功能，可以显著提升因果推理的准确性和可信度。关键在于平衡模型复杂度与可解释性，确保因果假设的合理性。