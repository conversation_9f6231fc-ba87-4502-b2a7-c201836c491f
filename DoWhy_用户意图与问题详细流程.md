# DoWhy用户意图与问题详细流程指南

## 概述

基于DoWhy v0.13框架，本文档详细阐述了四大类用户意图和问题的完整分析流程。每个流程都包含从问题识别到结果应用的完整路径，为因果推断实践提供系统性指导。

---

## 1. 效应估计 (Effect Estimation)

### 流程概述
效应估计是最常见的因果问题，旨在量化一个变量对另一个变量的因果影响强度。

### 详细流程图

```mermaid
flowchart TD
    A[用户问题:<br/>X对Y的因果效应是多少?] --> B{确定估计类型}
    
    B --> C[平均因果效应<br/>ACE]
    B --> D[条件因果效应<br/>CATE]
    B --> E[分布外预测<br/>OOD Prediction]
    
    C --> F[数据收集与预处理]
    D --> F
    E --> F
    
    F --> G[构建因果图DAG]
    G --> H{因果识别}
    H -->|可识别| I[选择估计方法]
    H -->|不可识别| J[重新设计实验<br/>或收集更多数据]
    J --> G
    
    I --> K{方法选择}
    K --> L[随机化实验<br/>RCT]
    K --> M[准实验方法<br/>IV/RDD/DID]
    K --> N[观察性数据方法<br/>匹配/回归]
    
    L --> O[执行估计]
    M --> O
    N --> O
    
    O --> P[敏感性分析]
    P --> Q{结果稳健?}
    Q -->|否| R[调整模型<br/>或方法]
    Q -->|是| S[置信区间计算]
    R --> O
    
    S --> T[结果解释]
    T --> U[报告效应大小<br/>统计显著性<br/>实际意义]
    
    style A fill:#e1f5fe
    style U fill:#c8e6c9
    style H fill:#fff3e0
    style Q fill:#fce4ec
```

### 关键步骤说明

1. **问题明确化**: 精确定义治疗变量(X)和结果变量(Y)，明确目标人群
2. **类型选择**: 根据研究目标选择ACE（群体平均）、CATE（异质性分析）或OOD（泛化能力）
3. **因果识别**: 验证在给定假设下，目标因果效应是否可从数据中唯一确定
4. **方法选择**: 基于数据特征和识别策略选择合适的估计方法
5. **稳健性检查**: 通过多种方法验证结果的可靠性

---

## 2. 量化因果影响 (Quantify Causal Influence)

### 流程概述
深入理解变量间因果作用的路径、强度和机制，解析复杂因果网络中的传导过程。

### 详细流程图

```mermaid
flowchart TD
    A[用户问题:<br/>变量间的因果影响<br/>路径和强度如何?] --> B{分析类型选择}
    
    B --> C[中介分析<br/>Mediation Analysis]
    B --> D[直接箭头强度<br/>Direct Arrow Strength]
    B --> E[内在因果影响<br/>Intrinsic Causal Influence]
    
    C --> F1[识别中介变量M]
    D --> F2[定义目标因果路径]
    E --> F3[确定纯净因果作用]
    
    F1 --> G1[分解总效应]
    F2 --> G2[量化直接效应]
    F3 --> G3[控制混杂影响]
    
    G1 --> H1{中介假设检验}
    G2 --> H2{路径独立性检验}
    G3 --> H3{外生性检验}
    
    H1 -->|通过| I1[估计间接效应<br/>Natural Indirect Effect]
    H1 -->|未通过| J1[重新识别中介路径]
    
    H2 -->|通过| I2[计算路径系数]
    H2 -->|未通过| J2[调整因果图结构]
    
    H3 -->|通过| I3[估计内在影响强度]
    H3 -->|未通过| J3[寻找工具变量]
    
    J1 --> F1
    J2 --> F2
    J3 --> F3
    
    I1 --> K1[中介效应分解:<br/>ADE + ACME = ATE]
    I2 --> K2[路径影响量化]
    I3 --> K3[纯净因果贡献]
    
    K1 --> L[结果综合分析]
    K2 --> L
    K3 --> L
    
    L --> M[因果机制解释]
    M --> N[影响路径可视化<br/>强度排序<br/>政策建议]
    
    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style H1 fill:#fff3e0
    style H2 fill:#fff3e0
    style H3 fill:#fff3e0
```

### 关键步骤说明

1. **路径识别**: 明确因果传导的具体路径和中介变量
2. **假设检验**: 验证中介独立性、路径独立性等关键假设
3. **效应分解**: 将总效应分解为直接效应和间接效应
4. **机制解释**: 提供因果影响的机制性解释和政策含义

---

## 3. "如果...会怎样"分析 (What-If Analysis)

### 流程概述
探索虚拟情景和反事实推理，回答干预和反事实问题。

### 详细流程图

```mermaid
flowchart TD
    A[用户问题:<br/>如果改变X会怎样?<br/>如果当初没有X会怎样?] --> B{问题类型}
    
    B --> C[干预分析<br/>Interventions<br/>do(X=x)]
    B --> D[反事实分析<br/>Counterfactuals<br/>Y_X=x'|X=x,Y=y]
    
    C --> E1[定义干预目标]
    D --> E2[定义反事实情景]
    
    E1 --> F1[构建干预图<br/>G_X̄]
    E2 --> F2[构建反事实模型<br/>SCM]
    
    F1 --> G1{干预可识别?}
    F2 --> G2{反事实可识别?}
    
    G1 -->|是| H1[应用do-calculus]
    G1 -->|否| I1[收集实验数据<br/>或寻找代理变量]
    
    G2 -->|是| H2[三步反事实推理]
    G2 -->|否| I2[强化因果假设<br/>或收集更多信息]
    
    I1 --> F1
    I2 --> F2
    
    H1 --> J1[计算干预分布<br/>P(Y|do(X=x))]
    H2 --> J2[步骤1: 溯因<br/>P(U|X=x,Y=y)]
    
    J2 --> K2[步骤2: 行动<br/>修改X为x']
    K2 --> L2[步骤3: 预测<br/>P(Y_X=x'|X=x,Y=y)]
    
    J1 --> M1[预测干预效果]
    L2 --> M2[计算反事实概率]
    
    M1 --> N{敏感性分析}
    M2 --> N
    
    N -->|稳健| O[政策建议<br/>决策支持]
    N -->|不稳健| P[调整模型假设<br/>收集更多数据]
    
    P --> F1
    P --> F2
    
    O --> Q[情景分析报告<br/>不确定性量化<br/>决策树构建]
    
    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style G1 fill:#fff3e0
    style G2 fill:#fff3e0
    style N fill:#fce4ec
```

### 关键步骤说明

1. **问题区分**: 明确区分干预问题(do-operator)和反事实问题(counterfactual)
2. **可识别性分析**: 验证目标问题在给定图结构下是否可识别
3. **三层次推理**: 对于反事实，执行溯因-行动-预测的三步推理
4. **敏感性检验**: 测试结果对模型假设的敏感程度

---

## 4. 根本原因分析与解释 (Root Cause Analysis & Explanations)

### 流程概述
追溯问题源头，解释异常现象，识别导致观察结果的根本原因。

### 详细流程图

```mermaid
flowchart TD
    A[用户问题:<br/>为什么会出现这个结果?<br/>根本原因是什么?] --> B{分析类型}
    
    B --> C[分布变化归因<br/>Distribution Change]
    B --> D[异常归因<br/>Anomaly Attribution]
    B --> E[特征相关性归因<br/>Feature Relevance]
    B --> F[单元变化归因<br/>Unit Change]
    
    C --> G1[检测分布变化<br/>时间点/群体间]
    D --> G2[识别异常数据点<br/>离群值检测]
    E --> G3[提取重要特征<br/>模型解释]
    F --> G4[锁定变化个体<br/>前后对比]
    
    G1 --> H1[分解变化来源]
    G2 --> H2[异常维度识别]
    G3 --> H3[特征因果路径]
    G4 --> H4[个体轨迹分析]
    
    H1 --> I1{上游变量分析}
    H2 --> I2{多维度筛选}
    H3 --> I3{因果vs相关区分}
    H4 --> I4{个人vs环境因素}
    
    I1 --> J1[变量重要性排序<br/>Shapley值分解]
    I2 --> J2[异常原因定位<br/>多层次诊断]
    I3 --> J3[因果贡献量化<br/>去混杂分析]
    I4 --> J4[个体因果效应<br/>个性化解释]
    
    J1 --> K1[时间序列因果<br/>Granger因果性]
    J2 --> K2[反事实对比<br/>最近邻分析]
    J3 --> K3[机制路径验证<br/>中介分析]
    J4 --> K4[个体轨迹建模<br/>生命历程分析]
    
    K1 --> L{验证因果关系}
    K2 --> L
    K3 --> L
    K4 --> L
    
    L -->|确认| M[根因排序]
    L -->|质疑| N[调整假设<br/>扩大搜索范围]
    
    N --> H1
    N --> H2
    N --> H3
    N --> H4
    
    M --> O[可解释性报告]
    O --> P[根因可视化<br/>影响路径图<br/>改进建议<br/>预警机制]
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style I1 fill:#fff3e0
    style I2 fill:#fff3e0
    style I3 fill:#fff3e0
    style I4 fill:#fff3e0
    style L fill:#fce4ec
```

### 关键步骤说明

1. **问题定位**: 精确定义需要解释的现象或异常
2. **多维度分析**: 从时间、空间、群体等多个维度搜索可能原因
3. **因果验证**: 区分相关性和因果性，避免虚假归因
4. **根因排序**: 基于因果强度和影响范围对根本原因进行排序

---

## 总结

### 四大流程的共同特征

1. **假设导向**: 所有流程都始于明确的因果假设
2. **迭代优化**: 通过反馈机制不断改进模型和假设
3. **稳健性检验**: 强调结果的可靠性和泛化能力
4. **可解释性**: 注重结果的机制性解释和实际应用

### 流程间的关联

- **效应估计**为其他三类分析提供基础的因果量化能力
- **量化因果影响**深化了效应估计的机制理解
- **"如果...会怎样"分析**将因果知识转化为预测和决策能力
- **根本原因分析**提供了因果关系的解释性应用

### 实践建议

1. **循序渐进**: 从简单的效应估计开始，逐步深入复杂分析
2. **假设透明**: 明确记录和检验所有因果假设
3. **多方法验证**: 使用多种方法交叉验证关键结论
4. **领域结合**: 将统计分析与领域知识紧密结合

---

*本文档基于DoWhy v0.13框架编写，为因果推断实践提供系统性指导。建议结合具体应用场景灵活调整流程细节。*