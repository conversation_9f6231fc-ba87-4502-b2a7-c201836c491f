{"data": {"layout-restorer:data": {"main": {"dock": null, "current": "notebook:dowhy_confounder_example.ipynb"}, "down": {"size": 0, "widgets": []}, "left": {"collapsed": true, "visible": false, "widgets": ["filebrowser", "running-sessions", "@jupyterlab/toc:plugin", "extensionmanager.main-view"], "widgetStates": {"jp-running-sessions": {"sizes": [0.16666666666666666, 0.16666666666666666, 0.16666666666666666, 0.16666666666666666, 0.16666666666666666, 0.16666666666666666], "expansionStates": [false, false, false, false, false, false]}, "extensionmanager.main-view": {"sizes": [0.3333333333333333, 0.3333333333333333, 0.3333333333333333], "expansionStates": [false, false, false]}}}, "right": {"collapsed": true, "visible": false, "widgets": ["jp-property-inspector", "debugger-sidebar"], "widgetStates": {"jp-debugger-sidebar": {"sizes": [0.2, 0.2, 0.2, 0.2, 0.2], "expansionStates": [false, false, false, false, false]}}}, "relativeSizes": [0, 1, 0], "top": {"simpleVisibility": true}}, "notebook:dowhy_confounder_example.ipynb": {"data": {"path": "dowhy_confounder_example.ipynb", "factory": "Notebook"}}, "docmanager:recents": {"opened": [{"path": "", "contentType": "directory", "root": "~/Documents/Lei_MBP/Education/Research/Causality/dowhy_zh/docs/source/example_notebooks"}, {"path": "dowhy_confounder_example.ipynb", "contentType": "notebook", "factory": "Notebook", "root": "~/Documents/Lei_MBP/Education/Research/Causality/dowhy_zh/docs/source/example_notebooks"}], "closed": []}}, "metadata": {"id": "default"}}