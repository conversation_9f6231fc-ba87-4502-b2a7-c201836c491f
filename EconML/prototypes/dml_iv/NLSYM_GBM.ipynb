{"cells": [{"cell_type": "code", "execution_count": 37, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression, Lasso, LogisticRegression\n", "from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.preprocessing import PolynomialFeatures, StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "import scipy.special"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# NLSYM DATA"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\preprocessing\\data.py:645: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.partial_fit(X, y)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\base.py:464: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.fit(X, **fit_params).transform(X)\n"]}], "source": ["# Preprocess data\n", "df = pd.read_csv(\"data/card.csv\")\n", "data_filter = df['educ'].values >= 6\n", "T = df['educ'].values[data_filter]\n", "Z = df['nearc4'].values[data_filter]\n", "y = df['lwage'].values[data_filter]\n", "\n", "# Impute missing values with mean, add dummy columns\n", "# I excluded the columns 'weights' as we don't know what it is\n", "X_df = df[['exper', 'expersq']].copy()\n", "X_df['fatheduc'] = df['fatheduc'].fillna(value=df['fatheduc'].mean())\n", "X_df['fatheduc_nan'] = df['fatheduc'].isnull()*1\n", "X_df['motheduc'] = df['motheduc'].fillna(value=df['motheduc'].mean())\n", "X_df['motheduc_nan'] = df['motheduc'].isnull()*1\n", "X_df[['momdad14', 'sinmom14', 'reg661', 'reg662',\n", "        'reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']] = df[['momdad14', 'sinmom14', \n", "        'reg661', 'reg662','reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']]\n", "X_df[['black', 'smsa', 'south', 'smsa66']] = df[['black', 'smsa', 'south', 'smsa66']]\n", "columns_to_scale = ['fatheduc', 'motheduc', 'exper', 'expersq']\n", "scaler = StandardScaler()\n", "X_df[columns_to_scale] = scaler.fit_transform(X_df[columns_to_scale])\n", "X = X_df.values[data_filter]\n", "\n", "true_fn = lambda x: np.zeros(x.shape[0])"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["Index(['exper', 'expersq', 'fatheduc', 'fatheduc_nan', 'motheduc',\n", "       'motheduc_nan', 'momdad14', 'sinmom14', 'reg661', 'reg662', 'reg663',\n", "       'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66',\n", "       'black', 'smsa', 'south', 'smsa66'],\n", "      dtype='object')"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["X_df.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSIS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some hyperparameters"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.006686726847208292\n"]}], "source": ["random_seed = 123459 # random seed for each experiment\n", "N_SPLITS = 10 # number of splits for cross-fitting\n", "COV_CLIP = 20/(X.shape[0]) # covariance clipping in driv\n", "print(COV_CLIP)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some generic non-parametric regressors and classifiers"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"collapsed": false}, "outputs": [], "source": ["from utilities import RegWrapper\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from xgboost import XGBClassifier, XGBRegressor\n", "from xgb_utilities import XGBWrapper\n", "\n", "# XGB forest models for Regression and Classification\n", "model = lambda: XGBWrapper(XGBRegressor(gamma=0.001, n_estimators=100, min_child_weight=50, n_jobs=10),\n", "                           early_stopping_rounds=5, eval_metric='rmse', binary=False)\n", "\n", "model_clf = lambda: RegWrapper(XGBWrapper(XGBClassifier(gamma=0.001, n_estimators=100, min_child_weight=50, n_jobs=10),\n", "                                          early_stopping_rounds=5, eval_metric='logloss', binary=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Some utility functions"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"collapsed": true}, "outputs": [], "source": ["def nuisance_diagnostic(cate, nuisance_model, property_name, property_fn, \n", "                        index_names=None, statistic=np.std, threshold=None):\n", "    std = statistic([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]], axis=0)\n", "    if hasattr(std, '__len__'):\n", "        if threshold is None:\n", "            coefs = np.argmax(std).flatten()\n", "        else:\n", "            coefs = np.argwhere(std >= threshold).flatten()\n", "        if index_names is None:\n", "            index_names = np.arange(std.shape[0])\n", "        for high_var in coefs:\n", "            plt.figure(figsize=(4,3))\n", "            plt.title(\"{}: {}[{}] Across Folds\".format(nuisance_model, property_name, index_names[high_var]))\n", "            plt.plot([property_fn(ns)[high_var] for ns in cate.fitted_nuisances[nuisance_model]])\n", "            plt.xlabel('fold')\n", "            plt.ylabel('property')\n", "            plt.show()\n", "    else:\n", "        plt.figure(figsize=(4,3))\n", "        plt.title(\"{}: {} Across Folds\".format(nuisance_model, property_name))    \n", "        plt.plot([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]])\n", "        plt.xlabel('fold')\n", "        plt.ylabel('property')\n", "        plt.show()\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE via DMLATEIV"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"collapsed": false}, "outputs": [], "source": ["from dml_ate_iv import DMLATEIV\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We need to specify models to be used for each of these residualizations\n", "model_Y_X = lambda: model() # model for E[Y | X]\n", "model_T_X = lambda: model() # model for E[T | X]. We use a regressor since T is continuous\n", "model_Z_X = lambda: model_clf() # model for E[Z | X]. We use a classifier since Z is binary\n", "\n", "dmlate = DMLATEIV(model_Y_X(), model_T_X(), model_Z_X(),\n", "                  n_splits=N_SPLITS, # n_splits determines the number of splits to be used for cross-fitting.\n", "                  binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                  binary_treatment=True # a flag whether to stratify cross-fitting by treatment\n", "                 )"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\model_selection\\_split.py:652: Warning: The least populated class in y has only 8 members, which is too few. The minimum number of members in any class cannot be less than n_splits=10.\n", "  % (min_groups, self.n_splits)), Warning)\n"]}, {"data": {"text/plain": ["<dml_ate_iv.DMLATEIV at 0x1b75873bb70>"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["# We fit DMLATEIV with these models\n", "dmlate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"collapsed": true}, "outputs": [], "source": ["# We call effect() to get the ATE\n", "ta_effect = dmlate.effect()"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.157\n", "Standard error: 0.060\n"]}], "source": ["# Comparison with true ATE\n", "print(\"ATE Estimate: {:.3f}\".format(ta_effect))\n", "print(\"Standard error: {:.3f}\".format(dmlate.std))"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate Interval: (0.041, 0.274)\n"]}], "source": ["# We can call normal_effect_interval to get confidence intervals based\n", "# based on the asympotic normal approximation\n", "lower, upper = dmlate.normal_effect_interval(lower=2.5, upper=97.5)\n", "# Comparison with true ATE\n", "print(\"ATE Estimate Interval: ({:.3f}, {:.3f})\".format(lower, upper))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE and CATE via DMLIV"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from utilities import SelectiveLasso, SeparateModel\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from econml.utilities import hstack\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DMLIV we also need a model for E[T | X, Z]. To allow for heterogeneity in the compliance, i.e.\n", "# T = beta(X)*Z + gamma(X)\n", "# we train a separate model for Z=1 and Z=0. The model for Z=1 learns the\n", "# quantity beta(X) + gamma(X) and the model for Z=0 learns gamma(X).\n", "model_T_XZ = lambda: SeparateModel(model(), model())\n", "\n", "# We now specify the features to be used for heterogeneity. We will fit a CATE model of the form\n", "#      theta(X) = <theta, phi(X)>\n", "# for some set of features phi(X). The featurizer needs to support fit_transform, that takes\n", "# X and returns phi(X). We need to include a bias if we also want a constant term.\n", "dmliv_featurizer = lambda: PolynomialFeatures(degree=1, include_bias=True)\n", "\n", "# Then we need to specify a model to be used for fitting the parameters theta in the linear form.\n", "# This model will minimize the square loss:\n", "#        (Y - E[Y|X] - <theta, phi(X)> * (E[T|X,Z] - E[T|X]))**2\n", "dmliv_model_effect = lambda: LinearRegression(fit_intercept=False)\n", "\n", "\n", "# Potentially with some regularization on theta. Here we use an ell_1 penalty on theta\n", "# If we also have a prior that there is no effect heterogeneity we can use a selective lasso\n", "# that does not penalize the constant term in the CATE model\n", "#dmliv_model_effect = lambda: SelectiveLasso(np.arange(1, X.shape[1]+1), LassoCV(cv=5, fit_intercept=False))\n", "\n", "\n", "# We initialize DMLIV with all these models and call fit\n", "cate = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(), \n", "             dmliv_model_effect(), dmliv_featurizer(),\n", "             n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "             binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "             binary_treatment=True # a flag whether to stratify cross-fitting by treatment\n", "            )"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\model_selection\\_split.py:652: Warning: The least populated class in y has only 8 members, which is too few. The minimum number of members in any class cannot be less than n_splits=10.\n", "  % (min_groups, self.n_splits)), Warning)\n"]}, {"data": {"text/plain": ["<dml_iv.DMLIV at 0x1b7587c1588>"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["cate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAeoAAAFJCAYAAABU5W56AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nYBihBgDAMEINAIBhhBoAAMMINQAAhv1/1BIcThzRyFkAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# To get the CATE at every X we call effect(X)\n", "dml_effect = cate.effect(X)\n", "plt.hist(dml_effect, label='est')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.05854357  0.09525829 -0.09525865  0.02605133  0.0096557  -0.00424164\n", "  0.06109043 -0.03009112 -0.03460873  0.0677044  -0.02577201  0.00583535\n", "  0.07245171 -0.02756375  0.00314271  0.01737084  0.07879138 -0.13341706\n", " -0.0070502  -0.03338776  0.07776467  0.02455005 -0.09374658]\n", "NA\n"]}], "source": ["# To get the parameter theta we call coef_. The first entry is the intercept of the CATE model\n", "print(cate.coef_)\n", "try:\n", "    print(cate.effect_model.lasso_model.alpha_)\n", "    plt.plot(cate.effect_model.lasso_model.alphas_, cate.effect_model.lasso_model.mse_path_)\n", "    plt.show()\n", "except:\n", "    print(\"NA\")"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.026\n"]}], "source": ["# We can average the CATE to get an ATE\n", "print(\"ATE Estimate: {:.3f}\".format(np.mean(dml_effect)))"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAecAAAFXCAYAAACYx4YhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nfxXeAt5FNDGaAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also see how it compares to the true CATE at each target point and calculate MSE\n", "plt.title(\"DMLIV CATE as Function of {}\".format(X_df.columns[np.argmax(np.abs(cate.coef_[1:]))]))\n", "plt.scatter(X[:, np.argmax(np.abs(cate.coef_[1:]))], dml_effect, label='est')\n", "plt.scatter(X[:, np.argmax(np.abs(cate.coef_[1:]))], true_fn(X), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Some Diagnostics of the Fitted Nuisance Models Across Folds"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unavailable\n"]}], "source": ["# The property .fitted_nuisances is a dictionary of the form:\n", "# {'name_of_nuisance': [fitted_model_fold_1, fitted_model_fold_2, ...]}\n", "# then we can access all properties of each of the fitted models for each fold.\n", "# If for instance all nuisances have a linear form we can look at the standard deviation\n", "# of the coefficients of each of the nuisance model across folds to check for stability\n", "try:\n", "    nuisance_diagnostic(cate, 'model_T_XZ', 'coef', lambda ns: ns.coef_.flatten(),\n", "                        [c+\"_0\" for c in X_df.columns] + [c+\"_1\" for c in X_df.columns])\n", "    nuisance_diagnostic(cate, 'model_T_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "    nuisance_diagnostic(cate, 'model_Y_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "code", "execution_count": 56, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unavailable\n"]}], "source": ["try:\n", "    for i in range(N_SPLITS):\n", "        plt.plot([c0 - c1 for c0, c1 in zip(cate.fitted_nuisances['model_T_XZ'][i].model0.coef_.flatten(),\n", "              cate.fitted_nuisances['model_T_XZ'][i].model1.coef_.flatten())])\n", "    \n", "    plt.title(\"Difference in coefficients betwen model0 and model1\")\n", "    plt.show()\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE via DRIV"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper, StatsModelLinearRegression, ConstantModel\n", "from sklearn.dummy import DummyRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DRIV we need a model for predicting E[T*Z | X]. We use a classifier\n", "model_TZ_X = lambda: model()\n", "\n", "# We also need a model for the final regression that will fit the function theta(X)\n", "# If we want to fit an ATE, we simply fit a constant functin theta(X) = theta\n", "# We can do this with a pipeline where the preprocessing step only creates a bias column\n", "# and the regression step fits a linear regression with no intercept.\n", "# To get normal confidence intervals easily we can use a statsmodels linear regression\n", "# wrapped in an sklearn interface\n", "const_driv_model_effect = lambda: ConstantModel()\n", "\n", "# As in OrthoDMLIV we need a perliminary estimator of the CATE.\n", "# We use a DMLIV estimator with no cross-fitting (n_splits=1)\n", "dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=True)\n", "\n", "const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                        dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                        model_TZ_X(), # model for E[T * Z | X]\n", "                        const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                        cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                        n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                        binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                        binary_treatment=True # a flag whether to stratify cross-fitting by treatment\n", "                       )"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\model_selection\\_split.py:652: Warning: The least populated class in y has only 8 members, which is too few. The minimum number of members in any class cannot be less than n_splits=10.\n", "  % (min_groups, self.n_splits)), Warning)\n"]}, {"data": {"text/plain": ["<dr_iv.DRIV at 0x1b75c7d97f0>"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["const_dr_cate.fit(y, T, X, Z, store_final=True)"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\statsmodels\\regression\\linear_model.py:1554: RuntimeWarning: divide by zero encountered in double_scalars\n", "  return self.ess/self.df_model\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>     inf</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>   nan</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>16:53:34</td>     <th>  Log-Likelihood:    </th> <td> -6595.4</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.319e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2990</td>      <th>  BIC:               </th> <td>1.320e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     0</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "    <td></td>       <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th> <td>    0.0413</td> <td>    0.040</td> <td>    1.030</td> <td> 0.303</td> <td>   -0.037</td> <td>    0.120</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>3168.820</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   2.012</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>4187875.949</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td>-4.201</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>186.121</td> <th>  Cond. No.          </th>  <td>    1.00</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.000\n", "Model:                            OLS   Adj. R-squared:                  0.000\n", "Method:                 Least Squares   F-statistic:                       inf\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):                nan\n", "Time:                        16:53:34   Log-Likelihood:                -6595.4\n", "No. Observations:                2991   AIC:                         1.319e+04\n", "Df Residuals:                    2990   BIC:                         1.320e+04\n", "Df Model:                           0                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.0413      0.040      1.030      0.303      -0.037       0.120\n", "==============================================================================\n", "Omnibus:                     3168.820   <PERSON><PERSON><PERSON>-Watson:                   2.012\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          4187875.949\n", "Skew:                          -4.201   Prob(JB):                         0.00\n", "Kurtosis:                     186.121   Cond. No.                         1.00\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is the pipeline, we then look\n", "# at the reg step of the pipeline which is the statsmodel wrapper and then we look\n", "# at the model attribute of the statsmodel wrapper and print the summary()\n", "const_dr_cate.effect_model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Some diagnostics of the fitted nuisance models across folds"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model prel_model_effect max std of coefs: 0.06628579876493147\n"]}], "source": ["# The property .fitted_nuisances is a dictionary of the form:\n", "# {'name_of_nuisance': [fitted_model_fold_1, fitted_model_fold_2, ...]}\n", "# then we can access all properties of each of the fitted models for each fold.\n", "# If for instance all nuisances have a linear form we can look at the standard deviation\n", "# of the coefficients of each of the nuisance model across folds to check for stability\n", "try:\n", "    [print(\"Model {} max std of coefs: {}\".format(name,\n", "                                                  np.max(np.std([ns.coef_ for ns in nuisance_insts], axis=0)))) \n", "     if hasattr(nuisance_insts[0], 'coef_') else None\n", "     for name, nuisance_insts in const_dr_cate.fitted_nuisances.items()]\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unavailable\n"]}], "source": ["try:\n", "    nuisance_diagnostic(const_dr_cate, 'model_TZ_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "    nuisance_diagnostic(const_dr_cate, 'model_TZ_X', 'predict', lambda ns: ns.predict(X), np.arange(X.shape[0]))\n", "    nuisance_diagnostic(const_dr_cate, 'model_T_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "    nuisance_diagnostic(const_dr_cate, 'prel_model_effect', 'effect', lambda ns: ns.effect(X), np.arange(X.shape[0]))\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Projecting CATE to a pre-chosen subset of variables in final model"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4]\n"]}], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We could also fit a projection on a subset of the features by using the\n", "# subset wrapper from our utilities.\n", "\n", "# Example: including everything for expository purposes, but any array_like of indices would work\n", "subset_names = set(['motheduc'])\n", "# list of indices of features X to use in the final model\n", "feature_inds = np.argwhere([(x in subset_names) for x in X_df.columns.values]).flatten()\n", "print(feature_inds)\n", "# Because we are projecting to a low dimensional model space, we can\n", "# do valid inference and we can use statsmodel linear regression to get all\n", "# the hypothesis testing capability\n", "proj_driv_model_effect = lambda: SubsetWrapper(StatsModelLinearRegression(),\n", "                                          feature_inds # list of indices of features X to use in the final model\n", "                                         )"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"collapsed": true}, "outputs": [], "source": ["proj_dr_cate = const_dr_cate.refit_final(proj_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 64, "metadata": {"collapsed": true}, "outputs": [], "source": ["# To get the CATE at every X we call effect(X[:, feature_inds])\n", "proj_dr_effect = proj_dr_cate.effect(X[:, feature_inds])"]}, {"cell_type": "code", "execution_count": 65, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.001</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>   1.538</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td> 0.215</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>16:53:35</td>     <th>  Log-Likelihood:    </th> <td> -6594.7</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.319e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2989</td>      <th>  BIC:               </th> <td>1.321e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     1</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>        <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>    <td>    0.0418</td> <td>    0.040</td> <td>    1.042</td> <td> 0.298</td> <td>   -0.037</td> <td>    0.121</td>\n", "</tr>\n", "<tr>\n", "  <th>motheduc</th> <td>   -0.0502</td> <td>    0.040</td> <td>   -1.240</td> <td> 0.215</td> <td>   -0.129</td> <td>    0.029</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>3179.942</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   2.012</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>4188762.287</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td>-4.231</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>186.137</td> <th>  Cond. No.          </th>  <td>    1.01</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.001\n", "Model:                            OLS   Adj. R-squared:                  0.000\n", "Method:                 Least Squares   F-statistic:                     1.538\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):              0.215\n", "Time:                        16:53:35   Log-Likelihood:                -6594.7\n", "No. Observations:                2991   AIC:                         1.319e+04\n", "Df Residuals:                    2989   BIC:                         1.321e+04\n", "Df Model:                           1                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.0418      0.040      1.042      0.298      -0.037       0.121\n", "motheduc      -0.0502      0.040     -1.240      0.215      -0.129       0.029\n", "==============================================================================\n", "Omnibus:                     3179.942   <PERSON><PERSON><PERSON>-<PERSON>:                   2.012\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          4188762.287\n", "Skew:                          -4.231   Prob(JB):                         0.00\n", "Kurtosis:                     186.137   Cond. No.                         1.01\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is\n", "# an instance of SubsetWrapper, we look at the model of the SubsetWrapper which is \n", "# and instance of the pipeline, we then look at the reg step of the pipeline which is the statsmodel wrapper and\n", "# call summary() of the wrapper (most prob there is a better API for this, but we can go with this for now :)\n", "proj_dr_cate.effect_model.summary(alpha=.05, xname=['const']+list(X_df.columns[feature_inds]))"]}, {"cell_type": "code", "execution_count": 66, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAN4AAAEFCAYAAACM6VnvAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ngBCeQGABhPAEAgsghCcQWID/B7Fm+K5BsoApAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 216x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also evaluate coverage and create prediction intervals using statsmodels attributes\n", "import statsmodels.api as sm\n", "from statsmodels.sandbox.regression.predstd import wls_prediction_std\n", "res = proj_dr_cate.effect_model.model\n", "predictions = res.get_prediction(PolynomialFeatures(degree=1, include_bias=True).fit_transform(X[:, feature_inds]))\n", "frame = predictions.summary_frame(alpha=0.05)\n", "pred = frame['mean']\n", "iv_l = frame['mean_ci_lower']\n", "iv_u = frame['mean_ci_upper']\n", "\n", "fig, ax = plt.subplots(figsize=(3,4))\n", "order = np.argsort(X[:, feature_inds[0]])\n", "ax.plot(X[order, feature_inds[0]], iv_u[order], 'C3--', label=\"Upper 95% CI\")\n", "ax.plot(X[order, feature_inds[0]], iv_l[order], 'C3--', label=\"Lower 95% CI\")\n", "ax.plot(X[order, feature_inds[0]], pred[order], 'C0--.', label=\"Prediction\")\n", "ax.legend(loc='best')\n", "plt.xlabel(\"Mother's Education (scaled)\")\n", "plt.ylabel(\"Treatment Effect\")\n", "#plt.savefig(\"NLSYM_momeduc_linear_projection_2.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random Forest Based CATE and Tree Explainer"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "rf_driv_model_effect = lambda: RandomForestRegressor(n_estimators=5000, max_depth=2, min_impurity_decrease=0.01,\n", "                                                     min_samples_leaf=100, bootstrap=True)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {"collapsed": true}, "outputs": [], "source": ["rf_dr_cate = const_dr_cate.refit_final(rf_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 69, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\matplotlib\\axes\\_axes.py:6521: MatplotlibDeprecationWarning: \n", "The 'normed' kwarg was deprecated in Matplotlib 2.1 and will be removed in 3.1. Use 'density' instead.\n", "  alternative=\"'density'\", removal=\"3.1\")\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAANYAAAEFCAYAAACfPhkbAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRK5CYII=\n", "text/plain": ["<Figure size 216x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "\n", "rf_dr_effect = rf_dr_cate.effect(X)\n", "plt.figure(figsize=(3,4))\n", "sns.distplot(rf_dr_effect)\n", "plt.xlabel(\"Treatment Effect\")\n", "plt.ylabel(\"Frequency\")\n", "plt.savefig(\"NLSYM_hte_distribution_2.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABGoAAAD/CAYAAACpUzDlAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nEREREREREZE8oUCNiIiIiIiIiEie+H/tccJZWV/JsAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import shap\n", "import pandas as pd\n", "\n", "Xdf = pd.DataFrame(X, columns=X_df.columns)\n", "# explain the model's predictions using SHAP values\n", "explainer = shap.TreeExplainer(rf_dr_cate.effect_model)\n", "shap_values = explainer.shap_values(Xdf)\n", "\n", "# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(explainer.expected_value, shap_values[0,:], Xdf.iloc[0,:], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAWMAAAEdCAYAAADZ6gZQAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 288x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(4,4))\n", "shap.summary_plot(shap_values, Xdf, plot_type=\"violin\", max_display=10, show=False, auto_size_plot=False)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXsAAAEXCAYAAABMCOQqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 360x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(5,4))\n", "shap.summary_plot(shap_values, Xdf, plot_type='bar', max_display=10, show=False, auto_size_plot=False)\n", "plt.xlabel(\"mean(|SHAP value|)\\n average impact on model output magnitude\")\n", "plt.savefig(\"NLSYM_shap_summary_bar.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}