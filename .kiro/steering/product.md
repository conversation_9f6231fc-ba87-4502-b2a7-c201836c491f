# Product Overview

This is a comprehensive causal inference research repository focused on exploring and implementing various causal reasoning methods and frameworks. The project serves as both a learning environment and practical implementation space for causal inference techniques.

## Core Purpose

The repository is designed for:
- **Research and experimentation** with causal inference methods
- **Educational content creation** including detailed explanations of causal concepts
- **Practical implementation** of causal analysis workflows
- **Comparative analysis** of different causal inference approaches

## Key Focus Areas

1. **DoWhy Framework**: Extensive exploration of Microsoft's DoWhy library for causal inference
2. **Graphical Causal Models (GCM)**: Deep analysis of graph-based causal modeling approaches
3. **Educational Content**: Comprehensive documentation of causal concepts in both English and Chinese
4. **Multiple Frameworks**: Integration of various causal inference libraries (DoWhy, EconML, CausalML, causal-learn)

## Target Audience

- Researchers working on causal inference
- Students learning causal reasoning concepts
- Data scientists implementing causal analysis
- Academics developing causal inference methodologies

## Project Maturity

This is an active research repository with ongoing development of educational materials, code examples, and comparative analyses of causal inference methods.