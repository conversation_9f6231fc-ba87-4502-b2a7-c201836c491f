


from IPython.display import Image, display
display(Image(url="https://raw.githubusercontent.com/microsoft/dowhy/main/docs/images/dowhy-schematic.png"))





import numpy as np

from dowhy import CausalModel
import dowhy.datasets 








# 引入 dowhy 数据集生成函数，用于生成一个线性的因果模型的数据
data = dowhy.datasets.linear_dataset(
    beta=10,  # 设置treatments（treatment）对结果（outcome）的影响大小为 10
    num_common_causes=5,  # 设置有 5 个common cause（也叫做混杂变量或者控制变量）
    num_instruments=2,  # 设置有 2 个工具变量（instrumental variables）
    num_effect_modifiers=1,  # 设置有 1 个效应修饰变量（effect modifier）
    num_samples=5000,  # 设置样本数量为 5000
    treatment_is_binary=True,  # 设置treatments（treatment）是一个二进制变量（0 或 1）
    stddev_treatment_noise=10,  # 设置treatments（treatment）的噪声的标准差为 10
    num_discrete_common_causes=1  # 设置其中 1 个common cause（common cause）是离散变量
)

# 将生成的数据存储在 DataFrame 中
df = data["df"]
data


print("The Treatment Variable is ：",data["treatment_name"]),
print("The Outcome Variable is ：",data["outcome_name"]),
print("The Cofounder Variables are ：",data["common_causes_names"]),
print("The Instrument Variables are ：",data["instrument_names"])














# With graph
model=CausalModel(
        data = df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        graph=data["gml_graph"]
        )
model.view_model()





# 使用 DoWhy 库的 'identify_effect' 方法来找出可用于估计因果效应的估算量（Estimand）。
# 参数 'proceed_when_unidentifiable=True' 表示即使因果效应不可识别，也继续执行。
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)

# 打印出识别的估算量及其相关属性和假设。
# 这将帮助您了解可以用哪些方法来进行因果效应的估计，以及这些方法需要满足哪些假设。
print(identified_estimand)

# 通过运行这两行代码，您将了解哪些因果估算量可用，以及在使用这些估算量时需要满足哪些假设条件。
# 然后，您可以选择一个或多个估算量进行进一步的因果效应估计。





# 使用 'estimate_effect' 方法来估计处理（treatment）对结果（outcome）的因果效应。
# 这里使用的是通过 'identify_effect' 方法识别出来的估算量（identified_estimand）。
# 使用后门（backdoor）方法，并具体选择倾向性评分分层（propensity_score_stratification）作为估计方法。
causal_estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_stratification")

# 打印出因果效应的估计结果。
# 这将包括平均处理效应（ATE）以及可能的其他统计量，取决于所使用的估计方法。
print(causal_estimate)



estimate = model.estimate_effect(identified_estimand,
       method_name="backdoor.linear_regression",
       test_significance=True
)
print(estimate)


causal_estimate_dmatch = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.distance_matching",
                                             target_units="ate",
                                             method_params={'distance_metric':"minkowski", 'p':2})
print(causal_estimate_dmatch)


causal_estimate_match = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.propensity_score_matching",
                                             target_units="ate")
print(causal_estimate_match)
print("Causal Estimate is " + str(causal_estimate_match.value))


causal_estimate_strat = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.propensity_score_stratification",
                                             target_units="ate")
print(causal_estimate_strat)
print("Causal Estimate is " + str(causal_estimate_strat.value))


causal_estimate_ipw = model.estimate_effect(identified_estimand,
                                           method_name="backdoor.propensity_score_weighting",
                                           target_units = "ate",
                                           method_params={"weighting_scheme":"ips_weight"})
print(causal_estimate_ipw)
print("Causal Estimate is " + str(causal_estimate_ipw.value))


causal_estimate_iv = model.estimate_effect(identified_estimand,
       method_name="iv.instrumental_variable", method_params = {'iv_instrument_name': 'Z0'})
print(causal_estimate_iv)
print("Causal Estimate is " + str(causal_estimate_iv.value))


causal_estimate_regdist = model.estimate_effect(identified_estimand,
       method_name="iv.regression_discontinuity",
       method_params={'rd_variable_name':'Z1',
                      'rd_threshold_value':0.5,
                      'rd_bandwidth': 0.15})
print(causal_estimate_regdist)
print("Causal Estimate is " + str(causal_estimate_regdist.value))









































# 创建一个没有因果图（graph）的因果模型
# 使用 DoWhy 库的 CausalModel 类来实例化一个因果模型对象

# data=df: 指定数据集 df，这是一个包含所有相关变量（处理变量、结果变量、共同原因等）的 DataFrame。
# treatment=data["treatment_name"]: 指定处理变量（treatment variable）的名称。
# outcome=data["outcome_name"]: 指定结果变量（outcome variable）的名称。
# common_causes=data["common_causes_names"]: 指定所有可能的共同原因（common causes，也叫做混杂变量或控制变量）的名称。
# effect_modifiers=data["effect_modifier_names"]: 指定影响修正变量（effect modifiers）的名称，这些变量可能会改变处理对结果的影响。

model= CausalModel(                             
        data=df,                                      
        treatment=data["treatment_name"],             
        outcome=data["outcome_name"],                 
        common_causes=data["common_causes_names"],
        effect_modifiers=data["effect_modifier_names"])  
model.view_model()





# 这一行代码使用 identify_effect 方法来识别因果估计量（estimand）。
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)    

print(identified_estimand)





# 使用 `estimate_effect` 方法来估算 `identified_estimand` 定义的因果效应。
# 这里使用 "backdoor.propensity_score_stratification" 作为估计方法，它是一种倾向得分分层方法。
estimate = model.estimate_effect(identified_estimand, method_name="backdoor.propensity_score_stratification")

# 打印出因果效应的估计结果，这将包括用于估计的表达式和相关假设。
print(estimate)



















































# 使用 `refute_estimate` 方法来对因果效应的估计进行反驳测试。
# "random_common_cause" 是一种反驳方法，它添加一个随机的共同原因到数据中，
# 然后重新进行因果效应的估计，以检查估计是否稳健。
# `show_progress_bar=True` 显示一个进度条，让你知道反驳测试的进度。
res_random = model.refute_estimate(identified_estimand, estimate, 
                                   method_name="random_common_cause", 
                                   show_progress_bar=True)

# 打印出反驳测试的结果。
# 如果因果效应的估计在添加随机共同原因后没有显著改变，那么这通常被视为估计是相对稳健的。
print(res_random)





# 使用 `refute_estimate` 方法进行反驳测试，这次使用 "placebo_treatment_refuter" 作为方法。
# 这个方法将处理（treatment）变量替换为一个无关的替代变量（即安慰剂），然后重新估计因果效应。
# 如果因果效应的估计在使用这个安慰剂后接近于零，那么这通常被视为原始估计是可靠的。
# 参数 `placebo_type="permute"` 指定了如何生成这个安慰剂，这里是通过随机置换原始处理值来生成。
# `show_progress_bar=True` 显示进度条。
res_placebo = model.refute_estimate(identified_estimand, estimate, 
                                    method_name="placebo_treatment_refuter", 
                                    show_progress_bar=True, 
                                    placebo_type="permute")

# 打印出反驳测试的结果。
# 如果在使用安慰剂处理后因果效应的估计接近于零，这通常被认为是原估计的一个好的验证。
print(res_placebo)






# 使用模型、已识别的因果关系和原始估计来执行反驳测试
res_subset = model.refute_estimate(
    identified_estimand,  # 已识别的因果关系
    estimate,  # 使用全量数据得到的因果效应估计
    method_name="data_subset_refuter",  # 指定使用数据子集反驳方法
    show_progress_bar=True,  # 显示进度条
    subset_fraction=0.9,  # 指定使用90%的数据作为子集
    random_seed=1,  # 设置随机种子以确保结果可复现
    n_jobs=-1,  # 使用所有可用的CPU核心来并行计算
    verbose=10  # 设置输出信息的详细程度
)

# 打印出反驳测试的结果
print(res_subset)






# 使用 'add_unobserved_common_cause' 方法进行敏感性分析
res_unobserved = model.refute_estimate(
    identified_estimand,  # 先前识别的因果关系
    estimate,  # 先前计算的因果效应
    method_name="add_unobserved_common_cause",  # 使用的方法名
    confounders_effect_on_treatment="binary_flip",  # 未观察因素对Treatment的影响类型
    confounders_effect_on_outcome="linear",  # 未观察因素对结果的影响类型
    effect_strength_on_treatment=0.01,  # 未观察因素对Treatment的影响强度
    effect_strength_on_outcome=0.02  # 未观察因素对结果的影响强度
)

# 打印新的因果效应估计
print(res_unobserved)






res_unobserved_range=model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                     confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear",
                                    effect_strength_on_treatment=np.array([0.001, 0.005, 0.01, 0.02]), effect_strength_on_outcome=0.01)
print(res_unobserved_range)





res_unobserved_range=model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                           confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear",
                                           effect_strength_on_treatment=[0.001, 0.005, 0.01, 0.02], 
                                           effect_strength_on_outcome=[0.001, 0.005, 0.01,0.02])
print(res_unobserved_range)





res_unobserved_auto = model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                           confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear")
print(res_unobserved_auto)



