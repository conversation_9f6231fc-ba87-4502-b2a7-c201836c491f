


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import math
import dowhy
from dowhy import CausalModel
import dowhy.datasets, dowhy.plotter

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'INFO',
        },
    }
}

logging.config.dictConfig(DEFAULT_LOGGING)





# 通过随机选择，rvar 变量被设置为 1 或 0。这决定了是否有因果关系。
rvar = 1 if np.random.uniform() > 0.5 else 0

# 使用 dowhy 库中的 xy_dataset 函数生成一个数据集。
# effect 参数设置为 rvar，表示是否有因果效应（1 表示有，0 表示无）。
# num_common_causes 设置为 1，表示有一个共同原因（即一个混杂变量）。
# sd_error 设置为 0.2，表示生成数据的标准差。
data_dict = dowhy.datasets.xy_dataset(10000, effect=rvar, 
                                      num_common_causes=1, 
                                      sd_error=0.2)

# 从生成的数据字典中获取 DataFrame。
df = data_dict['df']

# 打印 DataFrame 的前几行，仅显示 "Treatment"（治疗），"Outcome"（结果）和 "w0"（共同原因）列。
print(df[["Treatment", "Outcome", "w0"]].head())



# 使用 dowhy 库的 plotter 模块中的 plot_treatment_outcome 函数
# 来绘制治疗变量和结果变量之间的关系。
# 第一个参数是 DataFrame 中代表治疗的列。
# 第二个参数是 DataFrame 中代表结果（或响应）的列。
# 第三个参数是 DataFrame 中代表时间的列（如果有的话）。
dowhy.plotter.plot_treatment_outcome(df[data_dict["treatment_name"]], 
                                     df[data_dict["outcome_name"]],
                                     df[data_dict["time_val"]])






# 创建一个因果模型对象
# 数据集是 df，处理（或干预）变量是在 data_dict["treatment_name"] 中定义的，
# 结果变量是在 data_dict["outcome_name"] 中定义的，
# 共同原因（或混杂变量）是在 data_dict["common_causes_names"] 中定义的，
# 工具变量（如果有的话）是在 data_dict["instrument_names"] 中定义的。
model= CausalModel(                                                                                                                      
        data=df,  # 数据集                                                                                                                        
        treatment=data_dict["treatment_name"],  # 处理变量                                                                                          
        outcome=data_dict["outcome_name"],  # 结果变量                                                                                              
        common_causes=data_dict["common_causes_names"],  # 共同原因                                                                                  
        instruments=data_dict["instrument_names"])  # 工具变量（如果有的话）                                                                                      

# 可视化因果模型
# 使用 "dot" 布局来显示因果图
model.view_model(layout="dot")






# 使用 'identify_effect' 方法来识别用于估计因果效应的估算量（Estimand）。
# 参数 'proceed_when_unidentifiable=True' 表示即使因果效应不可识别，也继续执行。
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)

# 打印出识别的估算量及其相关属性和假设。
# 这将帮助您了解可以用哪些方法来进行因果效应的估计，以及这些方法需要满足哪些假设。
print(identified_estimand)






# 使用 'estimate_effect' 方法和线性回归（"backdoor.linear_regression"）来估计因果效应。
# 这里的 'identified_estimand' 是之前识别步骤的输出。
estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.linear_regression")

# 打印出通过线性回归得到的因果效应估计值。
print(estimate)

# 使用 DoWhy 的 'plot_causal_effect' 方法来绘制因果效应。
# 这将绘制一个散点图，显示处理变量与结果之间的关系，以及线性回归的斜率（即因果效应）。
dowhy.plotter.plot_causal_effect(estimate, df[data_dict["treatment_name"]], df[data_dict["outcome_name"]])









res_random=model.refute_estimate(identified_estimand, estimate, method_name="random_common_cause")
print(res_random)





res_placebo=model.refute_estimate(identified_estimand, estimate,
        method_name="placebo_treatment_refuter", placebo_type="permute")
print(res_placebo)





res_subset=model.refute_estimate(identified_estimand, estimate,
        method_name="data_subset_refuter", subset_fraction=0.9)
print(res_subset)




