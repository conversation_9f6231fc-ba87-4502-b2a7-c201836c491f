{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<img src=\"https://www.microsoft.com/en-us/research/uploads/prod/2020/05/Segmentation.png\" width=\"400\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Customer Segmentation: Estimate Individualized Responses to Incentives\n", "\n", "Nowadays, business decision makers rely on estimating the causal effect of interventions to answer what-if questions about shifts in strategy, such as promoting specific product with discount, adding new features to a website or increasing investment from a sales team. However, rather than learning whether to take action for a specific intervention for all users, people are increasingly interested in understanding the different responses from different users to the two alternatives. Identifying the characteristics of users having the strongest response for the intervention could help make rules to segment the future users into different groups. This can help optimize the policy to use the least resources and get the most profit.\n", "\n", "In this case study, we will use a personalized pricing example to explain how the [EconML](https://aka.ms/econml) and [DoWhy](https://github.com/py-why/dowhy) libraries could fit into this problem and provide robust and reliable causal solutions.\n", "\n", "### Summary\n", "\n", "1. [Background](#background)\n", "2. [Data](#data)\n", "3. [Create Causal Model and Identify Causal Effect with DoWhy](#identify)\n", "4. [Get Causal Effects with EconML](#estimate)\n", "5. [Test Estimate Robustness with <PERSON><PERSON><PERSON>](#robustness)\n", "    1. [Add Random Common Cause](#random-common-cause)\n", "    2. [Add Unobserved Common Cause](#unobserved-common-cause)\n", "    3. [Replace Treatment with a Random (Placebo) Variable](#placebo-variable)\n", "    4. [Remove a Random Subset of the Data](#subset)\n", "6. [Understand Treatment Effects with EconML](#interpret)\n", "7. [Make Policy Decisions with EconML](#policy)\n", "8. [Conclusions](#conclusion)\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Background <a id=\"background\"></a>\n", "\n", "<img src=\"https://cdn.pixabay.com/photo/2018/08/16/11/59/radio-3610287_960_720.png\" width=\"400\" />\n", "\n", "The global online media market is growing fast over the years. Media companies are always interested in attracting more users into the market and encouraging them to buy more songs or become members. In this example, we'll consider a scenario where one experiment a media company is running is to give small discount (10%, 20% or 0) to their current users based on their income level in order to boost the likelihood of their purchase. The goal is to understand the **heterogeneous price elasticity of demand** for people with different income level, learning which users would respond most strongly to a small discount. Furthermore, their end goal is to make sure that despite decreasing the price for some consumers, the demand is raised enough to boost the overall revenue.\n", "\n", "The EconML and DoWhy libraries complement each other in implementing this solution. On one hand, the DoWhy library can help [build a causal model, indentify the causal effect](#identify) and [test causal assumptions](#robustness). On the other hand, EconML’s `DML` based estimators can be used to take the discount variation in existing data, along with a rich set of user features, to [estimate heterogeneous price sensitivities](#estimate) that vary with multiple customer features. Then, the `SingleTreeCateInterpreter` provides a [presentation-ready summary](#interpret) of the key features that explain the biggest differences in responsiveness to a discount, and the `SingleTreePolicyInterpreter` recommends a [policy](#policy) on who should receive a discount in order to increase revenue (not only demand), which could help the company to set an optimal price for those users in the future."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Some imports to get us started\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "# Utilities\n", "import os\n", "import urllib.request\n", "import numpy as np\n", "import pandas as pd\n", "from networkx.drawing.nx_pydot import to_pydot\n", "from IPython.display import Image, display\n", "\n", "# Generic ML imports\n", "from sklearn.preprocessing import PolynomialFeatures\n", "from sklearn.ensemble import GradientBoostingRegressor\n", "\n", "# EconML imports\n", "from econml.dml import LinearDML, CausalForestDML\n", "from econml.cate_interpreter import SingleTreeCateInterpreter, SingleTreePolicyInterpreter\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data <a id=\"data\"></a>\n", "\n", "\n", "The dataset* has ~10,000 observations and includes 9 continuous and categorical variables that represent user's characteristics and online behaviour history such as age, log income, previous purchase, previous online time per week, etc. \n", "\n", "We define the following variables:\n", "\n", "Feature Name|Type|Details \n", ":--- |:---|:--- \n", "**account_age** |W| user's account age\n", "**age** |W|user's age\n", "**avg_hours** |W| the average hours user was online per week in the past\n", "**days_visited** |W| the average number of days user visited the website per week in the past\n", "**friend_count** |W| number of friends user connected in the account \n", "**has_membership** |W| whether the user had membership\n", "**is_US** |W| whether the user accesses the website from the US \n", "**songs_purchased** |W| the average songs user purchased per week in the past\n", "**income** |X| user's income\n", "**price** |T| the price user was exposed during the discount season (baseline price * small discount)\n", "**demand** |Y| songs user purchased during the discount season\n", "\n", "**To protect the privacy of the company, we use the simulated data as an example here. The data is synthetically generated and the feature distributions don't correspond to real distributions. However, the feature names have preserved their names and meaning.*\n", "\n", "\n", "The treatment and outcome are generated using the following functions:\n", "$$\n", "T = \n", "\\begin{cases}\n", "  1 & \\text{with } p=0.2,  \\\\\n", "  0.9 & \\text{with }p=0.3, & \\text{if income}<1 \\\\\n", "  0.8 & \\text{with }p=0.5, \\\\\n", "  \\\\\n", "    1 & \\text{with }p=0.7, \\\\\n", "  0.9 & \\text{with }p=0.2, & \\text{if income}\\ge1 \\\\\n", "  0.8 & \\text{with }p=0.1, \\\\\n", "\\end{cases}\n", "$$\n", "\n", "\n", "\\begin{align}\n", "\\gamma(X) & = -3 - 14 \\cdot \\{\\text{income}<1\\} \\\\\n", "\\beta(X,W) & = 20 + 0.5 \\cdot \\text{avg_hours} + 5 \\cdot \\{\\text{days_visited}>4\\} \\\\\n", "Y &= \\gamma(X) \\cdot T + \\beta(X,W)\n", "\\end{align}\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Import the sample pricing data\n", "file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/Pricing/pricing_sample.csv\"\n", "train_data = pd.read_csv(file_url)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>account_age</th>\n", "      <th>age</th>\n", "      <th>avg_hours</th>\n", "      <th>days_visited</th>\n", "      <th>friends_count</th>\n", "      <th>has_membership</th>\n", "      <th>is_US</th>\n", "      <th>songs_purchased</th>\n", "      <th>income</th>\n", "      <th>price</th>\n", "      <th>demand</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>53</td>\n", "      <td>1.834234</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4.903237</td>\n", "      <td>0.960863</td>\n", "      <td>1.0</td>\n", "      <td>3.917117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>54</td>\n", "      <td>7.171411</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3.330161</td>\n", "      <td>0.732487</td>\n", "      <td>1.0</td>\n", "      <td>11.585706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>33</td>\n", "      <td>5.351920</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3.036203</td>\n", "      <td>1.130937</td>\n", "      <td>1.0</td>\n", "      <td>24.675960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>6.723551</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>7.911926</td>\n", "      <td>0.929197</td>\n", "      <td>1.0</td>\n", "      <td>6.361776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>30</td>\n", "      <td>2.448247</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>7.148967</td>\n", "      <td>0.533527</td>\n", "      <td>0.8</td>\n", "      <td>12.624123</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   account_age  age  avg_hours  days_visited  friends_count  has_membership  \\\n", "0            3   53   1.834234             2              8               1   \n", "1            5   54   7.171411             7              9               0   \n", "2            3   33   5.351920             6              9               0   \n", "3            2   34   6.723551             0              8               0   \n", "4            4   30   2.448247             5              8               1   \n", "\n", "   is_US  songs_purchased    income  price     demand  \n", "0      1         4.903237  0.960863    1.0   3.917117  \n", "1      1         3.330161  0.732487    1.0  11.585706  \n", "2      1         3.036203  1.130937    1.0  24.675960  \n", "3      1         7.911926  0.929197    1.0   6.361776  \n", "4      0         7.148967  0.533527    0.8  12.624123  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data sample\n", "train_data.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Define estimator inputs\n", "train_data[\"log_demand\"] = np.log(train_data[\"demand\"])\n", "train_data[\"log_price\"] = np.log(train_data[\"price\"])\n", "\n", "Y = train_data[\"log_demand\"].values\n", "T = train_data[\"log_price\"].values\n", "X = train_data[[\"income\"]].values  # features\n", "confounder_names = [\"account_age\", \"age\", \"avg_hours\", \"days_visited\", \"friends_count\", \"has_membership\", \"is_US\", \"songs_purchased\"]\n", "W = train_data[confounder_names].values"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Get test data\n", "X_test = np.linspace(0, 5, 100).reshape(-1, 1)\n", "X_test_data = pd.DataFrame(X_test, columns=[\"income\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Causal Model and Identify Causal Effect with DoWhy <a id=\"identify\"></a>\n", "\n", "We define the causal assumptions with <PERSON><PERSON><PERSON>. For example, we can include features we believe as confounders and features we think will influence the heterogeneity of the effect. With these assumptions defined, <PERSON><PERSON><PERSON> can generate a causal graph for us, and use that graph to first identify the causal effect.\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# initiate an EconML cate estimator\n", "est = LinearDML(model_y=GradientBoostingRegressor(), model_t=GradientBoostingRegressor(),\n", "              featurizer=PolynomialFeatures(degree=2, include_bias=False))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:dowhy.causal_model:Causal Graph not provided. <PERSON><PERSON><PERSON> will construct a graph based on data inputs.\n", "INFO:dowhy.causal_graph:If this is observed data (not from a randomized experiment), there might always be missing confounders. Adding a node named \"Unobserved Confounders\" to reflect this.\n", "INFO:dowhy.causal_model:Model to find the causal effect of treatment ['log_price'] on outcome ['log_demand']\n", "WARNING:dowhy.causal_identifier:If this is observed data (not from a randomized experiment), there might always be missing confounders. Causal effect cannot be identified perfectly.\n", "INFO:dowhy.causal_identifier:Continuing by ignoring these unobserved confounders because proceed_when_unidentifiable flag is True.\n", "INFO:dowhy.causal_identifier:Instrumental variables for treatment and outcome:[]\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n"]}], "source": ["# fit through dowhy\n", "est_dw = est.dowhy.fit(Y, T, X=X, W=W, outcome_names=[\"log_demand\"], treatment_names=[\"log_price\"], feature_names=[\"income\"],\n", "               confounder_names=confounder_names, inference=\"statsmodels\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize causal graph\n", "try:\n", "    # Try pretty printing the graph. Requires pydot and pygraphviz\n", "    display(\n", "        Image(to_pydot(est_dw._graph._graph).create_png())\n", "    )\n", "except:\n", "    # Fall back on default graph view\n", "    est_dw.view_model() "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimand type: nonparametric-ate\n", "\n", "### Estimand : 1\n", "Estimand name: backdoor1 (<PERSON><PERSON><PERSON>)\n", "Estimand expression:\n", "     d                                                                        \n", "────────────(Expectation(log_demand|is_US,has_membership,days_visited,age,inco\n", "d[log_price]                                                                  \n", "\n", "                                                        \n", "me,account_age,avg_hours,songs_purchased,friends_count))\n", "                                                        \n", "Estimand assumption 1, Unconfoundedness: If U→{log_price} and U→log_demand then P(log_demand|log_price,is_US,has_membership,days_visited,age,income,account_age,avg_hours,songs_purchased,friends_count,U) = P(log_demand|log_price,is_US,has_membership,days_visited,age,income,account_age,avg_hours,songs_purchased,friends_count)\n", "\n", "### Estimand : 2\n", "Estimand name: iv\n", "No such variable found!\n", "\n"]}], "source": ["identified_estimand = est_dw.identified_estimand_\n", "print(identified_estimand)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Causal Effects with EconML <a id=\"estimate\"></a>\n", "\n", "Based on the identified causal effect above, we fit the model as follows using EconML:\n", "\n", "\n", "\\begin{align}\n", "log(Y) & = \\theta(X) \\cdot log(T) + f(X,W) + \\epsilon \\\\\n", "log(T) & = g(X,W) + \\eta\n", "\\end{align}\n", "\n", "\n", "where $\\epsilon, \\eta$ are uncorrelated error terms. \n", "\n", "\n", "The models we fit here aren't an exact match for the data generation function above, but if they are a good approximation, they will allow us to create a good discount policy.  Although the model is misspecified, we hope to see that our `DML` based estimators can still capture the right trend of $\\theta(X)$ and that the recommended policy beats other baseline policies (such as always giving a discount) on revenue.  Because of the mismatch between the data generating process and the model we're fitting, there isn't a single true $\\theta(X)$ (the true elasticity varies with not only X but also T and W), but given how we generate the data above, we can still calculate the range of true $\\theta(X)$ to compare against."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Define underlying treatment effect function given DGP\n", "def gamma_fn(X):\n", "    return -3 - 14 * (X[\"income\"] < 1)\n", "\n", "def beta_fn(X):\n", "    return 20 + 0.5 * (X[\"avg_hours\"]) + 5 * (X[\"days_visited\"] > 4)\n", "\n", "def demand_fn(data, T):\n", "    Y = gamma_fn(data) * T + beta_fn(data)\n", "    return Y\n", "\n", "def true_te(x, n, stats):\n", "    if x < 1:\n", "        subdata = train_data[train_data[\"income\"] < 1].sample(n=n, replace=True)\n", "    else:\n", "        subdata = train_data[train_data[\"income\"] >= 1].sample(n=n, replace=True)\n", "    te_array = subdata[\"price\"] * gamma_fn(subdata) / (subdata[\"demand\"])\n", "    if stats == \"mean\":\n", "        return np.mean(te_array)\n", "    elif stats == \"median\":\n", "        return np.median(te_array)\n", "    elif isinstance(stats, int):\n", "        return np.percentile(te_array, stats)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Get the estimate and range of true treatment effect\n", "truth_te_estimate = np.apply_along_axis(true_te, 1, X_test, 1000, \"mean\")  # estimate\n", "truth_te_upper = np.apply_along_axis(true_te, 1, X_test, 1000, 95)  # upper level\n", "truth_te_lower = np.apply_along_axis(true_te, 1, X_test, 1000, 5)  # lower level"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parametric heterogeneity\n", "First of all, we can try to learn a **linear projection of the treatment effect** assuming a polynomial form of $\\theta(X)$. We use the `LinearDML` estimator. Since we don't have any priors on these models, we use a generic gradient boosting tree estimators to learn the expected price and demand from the data."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Causal Estimate ***\n", "\n", "## Identified estimand\n", "Estimand type: nonparametric-ate\n", "\n", "## Realized estimand\n", "b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "Target units: ate\n", "\n", "## Estimate\n", "Mean value: -0.****************\n", "\n"]}], "source": ["lineardml_estimate = est_dw.estimate_\n", "print(lineardml_estimate)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Get treatment effect and its confidence interval\n", "te_pred = est_dw.effect(X_test).flatten()\n", "te_pred_interval = est_dw.effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1f0a223bb00>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compare the estimate and the truth\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(X_test.flatten(), te_pred, label=\"Sales Elasticity Prediction\")\n", "plt.plot(X_test.flatten(), truth_te_estimate, \"--\", label=\"True Elasticity\")\n", "plt.fill_between(\n", "    X_test.flatten(),\n", "    te_pred_interval[0].flatten(),\n", "    te_pred_interval[1].flatten(),\n", "    alpha=0.2,\n", "    label=\"95% Confidence Interval\",\n", ")\n", "plt.fill_between(\n", "    X_test.flatten(),\n", "    truth_te_lower,\n", "    truth_te_upper,\n", "    alpha=0.2,\n", "    label=\"True Elasticity Range\",\n", ")\n", "plt.xlabel(\"Income\")\n", "plt.ylabel(\"Songs Sales Elasticity\")\n", "plt.title(\"Songs Sales Elasticity vs Income\")\n", "plt.legend(loc=\"lower right\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the plot above, it's clear to see that the true treatment effect is a **nonlinear** function of income, with elasticity around -1.75 when income is smaller than 1 and a small negative value when income is larger than 1. The model fits a quadratic treatment effect, which is not a great fit. But it still captures the overall trend: the elasticity is negative and people are less sensitive to the price change if they have higher income."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "      <td></td>     <th>point_estimate</th> <th>stderr</th>  <th>zstat</th>  <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>income</th>        <td>2.386</td>      <td>0.081</td> <td>29.485</td>    <td>0.0</td>    <td>2.227</td>    <td>2.545</td> \n", "</tr>\n", "<tr>\n", "  <th>income^2</th>      <td>-0.42</td>      <td>0.028</td> <td>-15.185</td>   <td>0.0</td>   <td>-0.474</td>   <td>-0.366</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th>  <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>     <td>-3.003</td>      <td>0.049</td> <td>-60.738</td>   <td>0.0</td>    <td>-3.1</td>    <td>-2.906</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                      Coefficient Results                      \n", "===============================================================\n", "         point_estimate stderr  zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------------\n", "income            2.386  0.081  29.485    0.0    2.227    2.545\n", "income^2          -0.42  0.028 -15.185    0.0   -0.474   -0.366\n", "                        CATE Intercept Results                       \n", "=====================================================================\n", "               point_estimate stderr  zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------------------\n", "cate_intercept         -3.003  0.049 -60.738    0.0     -3.1   -2.906\n", "---------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the final coefficient and intercept summary\n", "est_dw.summary(feature_names=[\"income\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`LinearDML` estimator can also return the summary of the coefficients and intercept for the final model, including point estimates, p-values and confidence intervals. From the table above, we notice that $income$ has positive effect and ${income}^2$ has negative effect, and both of them are statistically significant."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Nonparametric Heterogeneity\n", "Since we already know the true treatment effect function is nonlinear, let us fit another model using `CausalForestDML`, which assumes a fully **nonparametric estimation of the treatment effect**."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:dowhy.causal_model:Causal Graph not provided. <PERSON><PERSON><PERSON> will construct a graph based on data inputs.\n", "INFO:dowhy.causal_graph:If this is observed data (not from a randomized experiment), there might always be missing confounders. Adding a node named \"Unobserved Confounders\" to reflect this.\n", "INFO:dowhy.causal_model:Model to find the causal effect of treatment ['log_price'] on outcome ['log_demand']\n", "WARNING:dowhy.causal_identifier:If this is observed data (not from a randomized experiment), there might always be missing confounders. Causal effect cannot be identified perfectly.\n", "INFO:dowhy.causal_identifier:Continuing by ignoring these unobserved confounders because proceed_when_unidentifiable flag is True.\n", "INFO:dowhy.causal_identifier:Instrumental variables for treatment and outcome:[]\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n"]}], "source": ["# initiate an EconML cate estimator\n", "est_nonparam = CausalForestDML(model_y=GradientBoostingRegressor(), model_t=GradientBoostingRegressor())\n", "# fit through dowhy\n", "est_nonparam_dw = est_nonparam.dowhy.fit(Y, T, X=X, W=W, outcome_names=[\"log_demand\"], treatment_names=[\"log_price\"],\n", "                                         feature_names=[\"income\"], confounder_names=confounder_names, inference=\"blb\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Get treatment effect and its confidence interval\n", "te_pred = est_nonparam_dw.effect(X_test).flatten()\n", "te_pred_interval = est_nonparam_dw.effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1f0a22e34e0>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Compare the estimate and the truth\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(X_test.flatten(), te_pred, label=\"Sales Elasticity Prediction\")\n", "plt.plot(X_test.flatten(), truth_te_estimate, \"--\", label=\"True Elasticity\")\n", "plt.fill_between(\n", "    X_test.flatten(),\n", "    te_pred_interval[0].flatten(),\n", "    te_pred_interval[1].flatten(),\n", "    alpha=0.2,\n", "    label=\"95% Confidence Interval\",\n", ")\n", "plt.fill_between(\n", "    X_test.flatten(),\n", "    truth_te_lower,\n", "    truth_te_upper,\n", "    alpha=0.2,\n", "    label=\"True Elasticity Range\",\n", ")\n", "plt.xlabel(\"Income\")\n", "plt.ylabel(\"Songs Sales Elasticity\")\n", "plt.title(\"Songs Sales Elasticity vs Income\")\n", "plt.legend(loc=\"lower right\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We notice that this model fits much better than the `LinearDML`, the 95% confidence interval correctly covers the true treatment effect estimate and captures the variation when income is around 1. Overall, the model shows that people with low income are much more sensitive to the price changes than higher income people."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Estimate Robustness with <PERSON><PERSON><PERSON> <a id=\"robustness\"></a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Random Common Cause <a id=\"random-common-cause\"></a>\n", "\n", "How robust are our estimates to adding another confounder? We use DoWhy to test this!"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count+w_random | income\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refute: Add a Random Common Cause\n", "Estimated effect:-0.****************\n", "New effect:-0.****************\n", "\n"]}], "source": ["res_random = est_nonparam_dw.refute_estimate(method_name=\"random_common_cause\", num_simulations=5)\n", "print(res_random)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add Unobserved Common Cause <a id=\"unobserved-common-cause\"></a>\n", "\n", "How robust are our estimates to unobserved confounders? Since we assume the model is under unconfoundedness, adding an unobserved confounder might bias the estimates. We use <PERSON><PERSON><PERSON> to test this!"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refute: Add an Unobserved Common Cause\n", "Estimated effect:-0.****************\n", "New effect:0.*****************\n", "\n"]}], "source": ["res_unobserved = est_nonparam_dw.refute_estimate(\n", "    method_name=\"add_unobserved_common_cause\",\n", "    confounders_effect_on_treatment=\"linear\",\n", "    confounders_effect_on_outcome=\"linear\",\n", "    effect_strength_on_treatment=0.1,\n", "    effect_strength_on_outcome=0.1,\n", ")\n", "print(res_unobserved)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Replace Treatment with a Random (Placebo) Variable <a id=\"placebo-variable\"></a>\n", "\n", "What happens our estimates if we replace the treatment variable with noise? Ideally, the average effect would be wildly different than our original estimate. We use DoWhy to investigate!"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_refuters.placebo_treatment_refuter:Refutation over 3 simulated datasets of permute treatment\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~placebo+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~placebo+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~placebo+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "WARNING:dowhy.causal_refuters.placebo_treatment_refuter:We assume a Normal Distribution as the sample has less than 100 examples.\n", "                 Note: The underlying distribution may not be Normal. We assume that it approaches normal with the increase in sample size.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refute: Use a Placebo Treatment\n", "Estimated effect:-0.****************\n", "New effect:-0.0009044538846515711\n", "p value:0.****************\n", "\n"]}], "source": ["res_placebo = est_nonparam_dw.refute_estimate(\n", "    method_name=\"placebo_treatment_refuter\", placebo_type=\"permute\", \n", "    num_simulations=3\n", ")\n", "print(res_placebo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remove a Random Subset of the Data <a id=\"subset\"></a>\n", "\n", "Do we recover similar estimates on subsets of the data? This speaks to the ability of our chosen estimator to generalize well. We use <PERSON><PERSON><PERSON> to investigate this!"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:dowhy.causal_refuters.data_subset_refuter:Refutation over 0.8 simulated datasets of size 8000.0 each\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "INFO:dowhy.causal_estimator:INFO: Using EconML Estimator\n", "INFO:dowhy.causal_estimator:b: log_demand~log_price+is_US+has_membership+days_visited+age+income+account_age+avg_hours+songs_purchased+friends_count | income\n", "WARNING:dowhy.causal_refuters.data_subset_refuter:We assume a Normal Distribution as the sample has less than 100 examples.\n", "                 Note: The underlying distribution may not be Normal. We assume that it approaches normal with the increase in sample size.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Refute: Use a subset of data\n", "Estimated effect:-0.****************\n", "New effect:-0.****************\n", "p value:0.*****************\n", "\n"]}], "source": ["res_subset = est_nonparam_dw.refute_estimate(\n", "    method_name=\"data_subset_refuter\", subset_fraction=0.8, \n", "    num_simulations=3)\n", "print(res_subset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Understand Treatment Effects with EconML <a id=\"interpret\"></a>\n", "EconML includes interpretability tools to better understand treatment effects. Treatment effects can be complex, but oftentimes we are interested in simple rules that can differentiate between users who respond positively, users who remain neutral and users who respond negatively to the proposed changes.\n", "\n", "The EconML `SingleTreeCateInterpreter` provides interperetability by training a single decision tree on the treatment effects outputted by the any of the EconML estimators. In the figure below we can see in dark red users respond strongly to the discount and the in white users respond lightly to the discount."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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*********************************************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", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\n", "intrp.interpret(est_nonparam_dw, X_test)\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=[\"income\"], fontsize=12)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Make Policy Decision with EconML <a id=\"policy\"></a>\n", "We want to make policy decisions to maximum the **revenue** instead of the demand. In this scenario,\n", "\n", "\n", "\\begin{align}\n", "Rev & = Y \\cdot T \\\\\n", "    & = \\exp^{log(Y)} \\cdot T\\\\\n", "    & = \\exp^{(\\theta(X) \\cdot log(T) + f(X,W) + \\epsilon)} \\cdot T \\\\\n", "    & = \\exp^{(f(X,W) + \\epsilon)} \\cdot T^{(\\theta(X)+1)}\n", "\\end{align}\n", "\n", "\n", "With the decrease of price, revenue will increase only if $\\theta(X)+1<0$. Thus, we set `sample_treatment_cast=-1` here to learn **what kinds of customers we should give a small discount to maximum the revenue**.\n", "\n", "The EconML library includes policy interpretability tools such as `SingleTreePolicyInterpreter` that take in a treatment cost and the treatment effects to learn simple rules about which customers to target profitably. In the figure below we can see the model recommends to give discount for people with income less than $0.985$ and give original price for the others."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["intrp = SingleTreePolicyInterpreter(risk_level=0.05, max_depth=2, min_samples_leaf=1, min_impurity_decrease=0.001)\n", "intrp.interpret(est_nonparam_dw, X_test, sample_treatment_costs=-1)\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=[\"income\"], treatment_names=[\"Discount\", \"No-Discount\"], fontsize=12)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let us compare our policy with other baseline policies! Our model says which customers to give a small discount to, and for this experiment, we will set a discount level of 10% for those users. Because the model is misspecified we would not expect good results with large discounts. Here, because we know the ground truth, we can evaluate the value of this policy."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# define function to compute revenue\n", "def revenue_fn(data, discount_level1, discount_level2, baseline_T, policy):\n", "    policy_price = baseline_T * (1 - discount_level1) * policy + baseline_T * (1 - discount_level2) * (1 - policy)\n", "    demand = demand_fn(data, policy_price)\n", "    rev = demand * policy_price\n", "    return rev"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["policy_dic = {}\n", "# our policy above\n", "policy = intrp.treat(X)\n", "policy_dic[\"Our Policy\"] = np.mean(revenue_fn(train_data, 0, 0.1, 1, policy))\n", "\n", "## previous strategy\n", "policy_dic[\"Previous Strategy\"] = np.mean(train_data[\"price\"] * train_data[\"demand\"])\n", "\n", "## give everyone discount\n", "policy_dic[\"Give Everyone Discount\"] = np.mean(revenue_fn(train_data, 0.1, 0, 1, np.ones(len(X))))\n", "\n", "## don't give discount\n", "policy_dic[\"Give No One Discount\"] = np.mean(revenue_fn(train_data, 0, 0.1, 1, np.ones(len(X))))\n", "\n", "## follow our policy, but give -10% discount for the group doesn't recommend to give discount\n", "policy_dic[\"Our Policy + Give Negative Discount for No-Discount Group\"] = np.mean(revenue_fn(train_data, -0.1, 0.1, 1, policy))\n", "\n", "## give everyone -10% discount\n", "policy_dic[\"Give Everyone Negative Discount\"] = np.mean(revenue_fn(train_data, -0.1, 0, 1, np.ones(len(X))))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue</th>\n", "      <th>Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Our Policy</th>\n", "      <td>14.686241</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Previous Strategy</th>\n", "      <td>14.349342</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Give Everyone Discount</th>\n", "      <td>13.774469</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Give No One Discount</th>\n", "      <td>14.294606</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Our Policy + Give Negative Discount for No-Discount Group</th>\n", "      <td>15.564411</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Give Everyone Negative Discount</th>\n", "      <td>14.612670</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                      Revenue  Rank\n", "Our Policy                                          14.686241   2.0\n", "Previous Strategy                                   14.349342   4.0\n", "Give Everyone Discount                              13.774469   6.0\n", "Give No One Discount                                14.294606   5.0\n", "Our Policy + Give Negative Discount for No-Disc...  15.564411   1.0\n", "Give Everyone Negative Discount                     14.612670   3.0"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# get policy summary table\n", "res = pd.DataFrame.from_dict(policy_dic, orient=\"index\", columns=[\"Revenue\"])\n", "res[\"Rank\"] = res[\"Revenue\"].rank(ascending=False)\n", "res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**We beat the baseline policies!** Our policy gets the highest revenue except for the one raising the price for the No-Discount group. That means our currently baseline price is low, but the way we segment the user does help increase the revenue!"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Conclusions <a id=\"conclusion\"></a>\n", "\n", "In this notebook, we have demonstrated the power of using EconML and DoWhy to:\n", "\n", "* Estimate the treatment effect correctly even the model is misspecified\n", "* Test causal assumptions and investigate the robustness of the resulting estimates\n", "* Interpret the resulting individual-level treatment effects\n", "* Make the policy decision beats the previous and baseline policies\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). \n", "\n", "To learn more about what <PERSON><PERSON><PERSON> can do for you, visit the [GitHub page](https://github.com/py-why/dowhy) or [documentation](https://www.pywhy.org/dowhy/).\n"]}], "metadata": {"interpreter": {"hash": "2e5c6628eef985e7fd2fa2aad22c988c5b8aa1d2648cf9c51c543a2a2637c546"}, "kernelspec": {"display_name": "Python 3.6.6 64-bit", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}}, "nbformat": 4, "nbformat_minor": 2}