#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
展示DoWhy中创建和操作因果图的不同方法
"""

import networkx as nx
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from dowhy import CausalModel
from dowhy.causal_identifier import CausalIdentifier


def create_graph_from_networkx():
    """使用NetworkX创建因果图"""
    # 创建一个有向图
    G = nx.DiGraph()
    
    # 添加节点和边
    G.add_nodes_from(['X', 'Z', 'Y', 'W'])
    G.add_edges_from([('X', 'Y'), ('Z', 'X'), ('Z', 'Y'), ('W', 'Z')])
    
    # 返回图
    return G


def create_graph_from_gml_string():
    """从GML字符串创建因果图"""
    gml_string = """graph [
        directed 1
        node [
            id 0
            label "X"
        ]
        node [
            id 1
            label "Z" 
        ]
        node [
            id 2
            label "Y"
        ]
        node [
            id 3
            label "W"
        ]
        edge [
            source 0
            target 2
        ]
        edge [
            source 1
            target 0
        ]
        edge [
            source 1
            target 2
        ]
        edge [
            source 3
            target 1
        ]
    ]"""
    return gml_string


def create_graph_from_dot_string():
    """从DOT字符串创建因果图"""
    dot_string = """digraph {
        X -> Y;
        Z -> X;
        Z -> Y;
        W -> Z;
    }"""
    return dot_string


def create_graph_from_variable_roles(data):
    """根据变量角色创建因果图"""
    # 在DoWhy中，可以通过指定变量的角色来创建图
    model = CausalModel(
        data=data,
        treatment='X',
        outcome='Y',
        common_causes=['Z'],
        instruments=['W']
    )
    return model.graph


def generate_sample_data(size=1000):
    """生成示例数据"""
    np.random.seed(42)
    
    # 生成变量
    W = np.random.normal(0, 1, size)
    Z = 0.7 * W + np.random.normal(0, 0.5, size)
    X = 0.8 * Z + np.random.normal(0, 0.5, size)
    Y = 0.6 * X + 0.5 * Z + np.random.normal(0, 0.5, size)
    
    # 创建数据框
    data = pd.DataFrame({'W': W, 'Z': Z, 'X': X, 'Y': Y})
    return data


def test_d_separation(G):
    """测试d-分离"""
    # W 和 Y 在给定 Z 的条件下是 d-分离的
    print("测试 W 和 Y 在给定 Z 的条件下是否 d-分离:")
    result = CausalIdentifier.check_dseparation(G, 'W', 'Y', ['Z'])
    print(f"W _||_ Y | Z: {result}")
    
    # X 和 W 在给定 Z 的条件下是 d-分离的
    print("测试 X 和 W 在给定 Z 的条件下是否 d-分离:")
    result = CausalIdentifier.check_dseparation(G, 'X', 'W', ['Z'])
    print(f"X _||_ W | Z: {result}")
    
    # X 和 Y 在不给定 Z 的条件下是 d-连接的
    print("测试 X 和 Y 在不给定任何变量条件下是否 d-分离:")
    result = CausalIdentifier.check_dseparation(G, 'X', 'Y', [])
    print(f"X _||_ Y: {result} (应该为False，因为它们是d-连接的)")


def check_backdoor_validity(G):
    """检验后门集的有效性"""
    # 检查 Z 是否是一个有效的后门调整集
    print("检查 Z 是否是 X->Y 的有效后门调整集:")
    result = CausalIdentifier.check_valid_backdoor_set(G, 'X', 'Y', ['Z'])
    print(f"Z 是有效的后门调整集: {result}")
    
    # 空集不是有效的后门调整集
    print("检查空集是否是 X->Y 的有效后门调整集:")
    result = CausalIdentifier.check_valid_backdoor_set(G, 'X', 'Y', [])
    print(f"空集是有效的后门调整集: {result}")
    
    # W 不是一个有效的后门调整集
    print("检查 W 是否是 X->Y 的有效后门调整集:")
    result = CausalIdentifier.check_valid_backdoor_set(G, 'X', 'Y', ['W'])
    print(f"W 是有效的后门调整集: {result}")


def perform_do_surgery(G):
    """执行do-运算"""
    print("执行 do(X)，即删除所有指向X的边:")
    
    # 复制原图
    G_clone = G.copy()
    print("原始图的边:", list(G_clone.edges()))
    
    # 执行 do(X)
    edges_to_remove = []
    for u, v in G_clone.edges():
        if v == 'X':
            edges_to_remove.append((u, v))
    
    for edge in edges_to_remove:
        G_clone.remove_edge(*edge)
        
    print("执行 do(X) 后的图的边:", list(G_clone.edges()))
    return G_clone


def visualize_graphs(original_graph, do_graph):
    """可视化原图和do后的图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Plot original graph
    pos = nx.spring_layout(original_graph, seed=42)
    nx.draw(original_graph, pos, with_labels=True, node_color='lightblue', 
            node_size=1500, font_size=12, font_weight='bold', arrowsize=20, ax=ax1)
    ax1.set_title("Original Graph")
    
    # Plot do(X) graph
    nx.draw(do_graph, pos, with_labels=True, node_color='lightgreen', 
            node_size=1500, font_size=12, font_weight='bold', arrowsize=20, ax=ax2)
    ax2.set_title("Graph after do(X)")
    
    plt.tight_layout()
    plt.savefig("graph_before_after_do_X.png")
    plt.close()


if __name__ == "__main__":
    # 生成示例数据
    data = generate_sample_data()
    
    # 创建因果图 - 使用NetworkX
    G = create_graph_from_networkx()
    print("使用NetworkX创建的图:", list(G.edges()))
    
    # 测试d-分离
    test_d_separation(G)
    
    # 检验后门集的有效性
    check_backdoor_validity(G)
    
    # 执行do-运算
    G_do_X = perform_do_surgery(G)
    
    # 可视化图
    visualize_graphs(G, G_do_X)
    
    print("\n完整的演示完成。") 