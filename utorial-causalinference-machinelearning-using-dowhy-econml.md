# 教程：因果推断及其与机器学习的联系（使用 DoWhy+EconML）

本教程将引导您逐步了解如何使用 DoWhy 和 EconML库进行因果推断。在此过程中，我们将重点介绍因果推断与机器学习之间的联系——机器学习如何帮助构建因果效应估计器，以及因果推理如何帮助构建更稳健的机器学习模型。

许多数据科学问题本质上是因果推断问题，例如：
*   **A/B 测试实验**：如果我更改算法，是否会提高成功率？
*   **政策决策**：如果我们采用这种治疗方法/政策，是否会带来更健康的患者/更多的收入等？
*   **政策评估**：根据我现在所知，我的政策是有益还是有害？
*   **信用归因**：人们是因为推荐算法而购买的吗？他们无论如何都会购买吗？

在本教程中，您将：
*   了解为什么因果推理对于决策制定至关重要，以及预测任务和决策任务之间的区别。
*   亲自动手，通过因果推断的四个步骤（**建模、识别、估计和反驳**）来估计因果效应。
*   了解 DoWhy+EconML 如何通过**4行代码**帮助您估计因果效应，并使用最新的统计学和机器学习方法来估计因果效应并评估其对模型假设的稳健性。
*   通过 Jupyter notebooks 学习**真实案例研究**，在不同场景下应用因果推理，包括估计客户忠诚度计划对未来交易的影响、预测哪些用户会从干预（如广告）中受益、产品定价以及归因哪些因素对结果贡献最大。
*   了解因果推断与现代机器学习模型挑战之间的联系。

## 目录

*   1.  为什么需要因果推断？
    *   1.1 定义因果效应
    *   1.2 预测与因果推断的区别
    *   1.3 因果推断的两个基本挑战
*   2.  因果推断的四个步骤
    *   2.1 DoWhy+EconML 解决方案
    *   2.2 一个神秘的数据集：你能否找出是否存在因果效应？
        *   2.2.1 使用因果图对数据生成过程进行建模假设
        *   2.2.2 基于因果模型识别目标量的正确估计量
        *   2.2.3 估计目标估计量
        *   2.2.4 使用反驳检验检查估计的稳健性
*   3.  使用 DoWhy+EconML 的案例研究
    *   3.1 估计客户忠诚度计划的影响
    *   3.2 在线公司的推荐 A/B 测试
    *   3.3 针对干预措施的用户细分
    *   3.4 软件公司的多重投资归因
*   4.  与机器学习基本挑战的联系
*   5.  更多资源
    *   5.1 DoWhy+EconML 库
    *   5.2 关于因果推断及其与机器学习联系的视频讲座
    *   5.3 KDD 因果推断详细教程
    *   5.4 关于因果关系和机器学习的书籍章节
    *   5.5 微软的因果关系与机器学习研究组

## 1. 为什么需要因果推断？

许多关键的数据科学任务都与决策制定相关。数据科学家经常被要求支持各级决策者，帮助他们最好地利用数据以实现预期结果。例如，高管制定投资和资源决策，营销人员确定折扣政策，产品团队优先考虑要发布哪些功能，或者医生决定对患者采用哪种治疗方法。

这些决策者中的每一个都在问一个“如果……会怎样？”的问题。要用数据驱动的方式回答这类问题，就需要理解一个事件的*原因*，以及如何采取行动来改善未来的结果。

### 1.1 定义因果效应

假设我们想找出采取行动 A 对结果 Y 的因果效应。为了定义因果效应，考虑两个世界：
1.  世界1（真实世界）：采取了行动 A 并观察到 Y。
2.  世界2（*反事实*世界）：没有采取行动 A（但其他一切都相同）。

因果效应是在真实世界中获得的 Y 值与在反事实世界中获得的 Y 值之间的差异。

\[E[Y_{\text{真实, A=1}}] - E[Y_{\text{反事实, A=0}}]\]

<img src="https://www.pywhy.org/dowhy/v0.12/_images/real_vs_counterfactual_world.png" alt="真实与反事实世界" style="display:block; margin:0 auto; max-width:100%;">

换句话说，当且仅当改变 A 会导致 Y 的改变，*同时保持其他一切不变*时，A 导致 Y。在保持其他一切不变的情况下改变 A 称为**干预**，用特殊符号 \(do(A)\) 表示。

形式上，因果效应是 Y 因 A 的单位*干预性*变化而改变的幅度：

\[E[Y|do(A=1)] - E[Y|do(A=0)]\]

### 1.2 预测与因果推断的区别

**预测**： 关注的是“如果我观察到 X，Y 会是什么？”（即 \(E[Y|X]\)）。它是在给定一组特征的情况下，对一个结果进行最佳猜测。预测模型在无需干预的情况下表现良好。

**因果推断**：关注的是“如果我将 X 设置为某个值，Y 会是什么？”（即 \(E[Y|do(X)]\)）。它涉及到主动改变一个变量（治疗或干预）并估计其对结果的影响。这对于决策至关重要。

例如，在冰淇淋销量和犯罪率同时上升的夏天，一个预测模型可能会发现它们是相关的。然而，一个因果模型会试图理解是否是冰淇淋销量导致犯罪率上升，或者两者是否都由第三个因素（如炎热天气）驱动。禁止销售冰淇淋（干预）不太可能降低犯罪率。

### 1.3 因果推断的两个基本挑战

1.  **我们永远无法同时观察到同一个体的两个潜在结果**：对于一个个体，我们要么进行了干预，要么没有。我们无法同时观察到“如果干预了会怎样”和“如果没干预会怎样”。这是因果推断的“基本问题”。
2.  **混杂因素**：混杂因素是同时影响治疗（干预）和结果的变量。如果不加以控制，它们会使治疗和结果之间产生虚假的相关性，从而导致对因果效应的估计产生偏差。

## 2. 因果推断的四个步骤

为了解决这些挑战，结构化因果推断通常遵循以下四个步骤：

1.  **建模 (Model)**：将关于数据生成过程的假设明确化，通常使用因果图（有向无环图，DAG）。图中的节点代表变量，边代表因果关系。
2.  **识别 (Identify)**：利用因果图，确定我们是否能够以及如何从观察数据中估计目标因果效应。这一步会产生一个称为“估计量 (estimand)”的数学表达式。例如，如果存在混杂因素 W，则识别出的估计量可能是 \(E_W[E[Y|A, W]]\)。
3.  **估计 (Estimate)**：使用统计方法（如回归、匹配、工具变量等）根据观察数据计算识别出的估计量的值。
4.  **反驳 (Refute)**：通过检验模型假设的稳健性来评估估计的有效性。例如，添加一个已知的随机共同原因或使用安慰剂治疗，看看估计的效应是否会发生显著变化。

### 2.1 DoWhy+EconML 解决方案

DoWhy 库实现了因果推断的前两个和最后一个步骤（建模、识别、反驳）。EconML 库专注于第三步（估计），提供了许多先进的机器学习方法来估计异质性因果效应（即不同个体或子群体的因果效应可能不同）。

DoWhy 和 EconML 的结合提供了一个端到端的因果推断框架。

### 2.2 一个神秘的数据集：你能否找出是否存在因果效应？

教程中提供了一个合成数据集的例子，用来说明这四个步骤。

假设我们有一个数据集，其中包含治疗变量 `Treatment`，结果变量 `Outcome`，以及两个观察到的混杂因素 `w0` 和 `w1`。此外，还有一个未观察到的混杂因素 `u`。

**目标**：估计 `Treatment` 对 `Outcome` 的因果效应。

#### 2.2.1 使用因果图对数据生成过程进行建模假设

我们首先定义一个因果图来表示我们对变量之间关系的假设。

```python
# 教程中的示例代码片段（概念性）
# import dowhy
# data_dict = {"df": df, # pandas dataframe
#              "treatment_name": 'Treatment',
#              "outcome_name": 'Outcome',
#              "common_causes_names": ['w0', 'w1'], # 观察到的混杂因素
#              "instrument_names": None}

# model = dowhy.CausalModel(
#     data=data_dict["df"],
#     treatment=data_dict["treatment_name"],
#     outcome=data_dict["outcome_name"],
#     graph="digraph { u[label=\"Unobserved Confounder\"]; Treatment -> Outcome; u -> Treatment; u -> Outcome; w0 -> Treatment; w1 -> Treatment; w0 -> Outcome; w1 -> Outcome;}")
# model.view_model()
```
**解释**：
*   `Treatment -> Outcome`：治疗影响结果。
*   `w0 -> Treatment`, `w1 -> Treatment`：观察到的变量 `w0`, `w1` 影响治疗。
*   `w0 -> Outcome`, `w1 -> Outcome`：观察到的变量 `w0`, `w1` 影响结果（因此它们是混杂因素）。
*   `u -> Treatment`, `u -> Outcome`：存在一个未观察到的混杂因素 `u`。

#### 2.2.2 基于因果模型识别目标量的正确估计量

接下来，我们使用模型来识别因果效应的估计量。

```python
# identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
# print(identified_estimand)
```

**输出示例 (来自教程)**:
```
Estimand type: EstimandType.NONPARAMETRIC_ATE

### Estimand : 1
Estimand name: backdoor
Estimand expression:
     d
────────────(E[Outcome|w0,w1])
d[Treatment]
Estimand assumption 1, Unconfoundedness: If U→{Treatment} and U→Outcome then P(Outcome|Treatment,w0,w1,U) = P(Outcome|Treatment,w0,w1)

### Estimand : 2
Estimand name: iv
No such variable(s) found!

### Estimand : 3
Estimand name: frontdoor
No such variable(s) found!
```
**解释**：
DoWhy 找到了一个基于“后门准则 (backdoor criterion)”的估计量。它建议为了估计 `Treatment` 对 `Outcome` 的效应，我们需要调整或控制观察到的混杂因素 `w0` 和 `w1`。表达式 \( \frac{d}{d[\text{Treatment}]} (E[\text{Outcome}|w0,w1]) \) 表示在给定 `w0` 和 `w1` 的情况下，`Outcome` 的期望值随 `Treatment` 的变化率。

“无混淆性 (Unconfoundedness)”假设意味着，在控制了 `w0` 和 `w1` 之后，就不再有未观察到的混杂因素 `U` 同时影响治疗和结果。然而，我们的图中明确包含了这样一个 `U`，所以这个假设在这里实际上是不成立的，这就是为什么教程中使用了 `proceed_when_unidentifiable=True` 并提到了警告。在实际应用中，处理未观察到的混杂是因果推断的主要挑战。

#### 2.2.3 估计目标估计量

一旦我们有了估计量，就可以使用不同的方法来估计其值。

**使用线性回归**：

```python
# estimate = model.estimate_effect(identified_estimand,
#         method_name="backdoor.linear_regression")
# print(estimate)
# print("Causal Estimate is " + str(estimate.value))
# dowhy.plotter.plot_causal_effect(estimate, df[data_dict["treatment_name"]], df[data_dict["outcome_name"]])
```
**输出示例 (来自教程)**:
```
*** Causal Estimate ***
## Identified estimand
Estimand type: EstimandType.NONPARAMETRIC_ATE
### Estimand : 1
Estimand name: backdoor
Estimand expression:
     d
────────────(E[Outcome|w0,w1])
d[Treatment]
Estimand assumption 1, Unconfoundedness: If U→{Treatment} and U→Outcome then P(Outcome|Treatment,w0,w1,U) = P(Outcome|Treatment,w0,w1)

## Realized estimand
b: Outcome~Treatment+w0+w1
Target units: ate

## Estimate
Mean value: 1.9984660510163166

Causal Estimate is 1.9984660510163166
```
教程指出，对于非线性数据生成过程，线性回归模型无法将因果效应与观察到的相关性区分开。如果数据生成过程是线性的，简单的线性回归就能奏效。

**使用双重机器学习 (DML) from EconML**：
为了处理非线性和高维混杂因素，教程展示了如何使用 EconML 的 DML 估计器。这种估计器使用基于机器学习的方法（如梯度提升树）来学习结果与混杂因素之间、以及治疗与混杂因素之间的关系，然后比较结果和治疗之间的残差变化。

```python
# from sklearn.preprocessing import PolynomialFeatures
# from sklearn.linear_model import LassoCV
# from sklearn.ensemble import GradientBoostingRegressor
# dml_estimate = model.estimate_effect(identified_estimand, method_name="backdoor.econml.dml.DML",
#                                      control_value = 0,
#                                      treatment_value = 1,
#                                  confidence_intervals=False,
#                                 method_params={"init_params":{'model_y':GradientBoostingRegressor(),
#                                                               'model_t': GradientBoostingRegressor(),
#                                                               "model_final":LassoCV(fit_intercept=False),
#                                                               'featurizer':PolynomialFeatures(degree=2, include_bias=True)},
#                                                "fit_params":{}})
# print(dml_estimate)
```
**输出示例 (来自教程)**:
```
*** Causal Estimate ***
## Identified estimand
Estimand type: EstimandType.NONPARAMETRIC_ATE
### Estimand : 1
Estimand name: backdoor
Estimand expression:
     d
────────────(E[Outcome|w0,w1])
d[Treatment]
Estimand assumption 1, Unconfoundedness: If U→{Treatment} and U→Outcome then P(Outcome|Treatment,w0,w1,U) = P(Outcome|Treatment,w0,w1)

## Realized estimand
b: Outcome~Treatment+w0+w1 |
Target units: ate

## Estimate
Mean value: 1.0899098944494576
Effect estimates: [[1.08990989]]
```
教程指出，DML 方法获得了更接近真实因果效应（假设为1）的更好估计。

#### 2.2.4 使用反驳检验检查估计的稳健性

最后一步是反驳估计，以检查其稳健性。

**添加随机共同原因**：
此方法向数据中添加一个随机生成的变量作为治疗和结果的共同原因，然后重新估计效应。如果原始估计对这种（不存在的）混杂因素敏感，则表明它可能不可靠。

```python
# res_random=model.refute_estimate(identified_estimand, dml_estimate, method_name="random_common_cause")
# print(res_random)
```
**输出示例 (来自教程)**:
```
Refute: Add a random common cause
Estimated effect:1.0899098944494576
New effect:1.085246993578022
p value:0.96
```
**解释**：新效应与原效应相似，p 值较大，表明估计对于这种类型的反驳是稳健的。

**安慰剂治疗反驳**：
此方法将治疗变量替换为一个随机排列的（安慰剂）版本，该版本不应与结果有因果关系。理想情况下，估计的安慰剂效应应接近于零。

```python
# res_placebo=model.refute_estimate(identified_estimand, dml_estimate,
#         method_name="placebo_treatment_refuter", placebo_type="permute",
#         num_simulations=20)
# print(res_placebo)
```
**输出示例 (来自教程)**:
```
Refute: Use a Placebo Treatment
Estimated effect:1.0899098944494576
New effect:-0.00016849220899518638
p value:0.39172166125577124
```
**解释**：新效应接近于零，p 值较大，再次表明原始估计是稳健的。

## 3. 使用 DoWhy+EconML 的案例研究

教程提到，在实践中，当数据维度较高时，简单的估计器无法估计正确的因果效应。先进的监督机器学习模型通常也不起作用，甚至可能比简单回归更差，因为它们包含额外的正则化技术以最小化预测误差，但这可能对估计因果效应产生不良影响。因此，需要针对估计因果效应的方法，并辅以合适的反驳方法来检查估计的稳健性。

教程提供了以下案例研究的链接（均为 Jupyter Notebooks）：

*   **估计客户忠诚度计划的影响 (Estimating the impact of a customer loyalty program)**:
    *   链接: `https://www.pywhy.org/dowhy/v0.10.1/example_notebooks/dowhy_example_estimating_effect_of_memberrewards_program.html` (注意: 教程中的链接可能指向旧版本，这里用 v0.10.1 版本的链接作为示例，实际链接应从教程页面获取)
*   **在线公司的推荐 A/B 测试 (Recommendation A/B testing at an online company)**:
    *   链接: `https://www.pywhy.org/dowhy/v0.10.1/example_notebooks/dowhy_ab_testing_example_recommendation_system.html`
*   **针对干预措施的用户细分 (User segmentation for targeting interventions)**:
    *   链接: `https://www.pywhy.org/dowhy/v0.10.1/example_notebooks/dowhy_user_segmentation_example.html`
*   **软件公司的多重投资归因 (Multi-investment attribution at a software company)**:
    *   链接: `https://www.pywhy.org/dowhy/v0.10.1/example_notebooks/dowhy_example_multi_investment_attribution.html`

**总结**：这些案例研究展示了如何在实际场景中应用 DoWhy 和 EconML 来解决因果推断问题，例如评估营销活动的效果、优化推荐系统以及理解不同因素对业务结果的贡献。

## 4. 与机器学习基本挑战的联系

因果关系与构建机器学习模型的许多基本挑战相关联，包括：

*   **分布外泛化 (Out-of-distribution generalization)**：标准机器学习模型通常在训练数据和测试数据来自同一分布时表现良好。然而，在现实世界中，分布经常会发生变化。因果模型通过关注不变的因果机制而不是可能改变的相关性，旨在提高分布外泛化能力。
*   **公平性 (Fairness)**：机器学习模型可能会从数据中学习到并放大现有的偏见。因果推断可以帮助识别和减轻由受保护属性（如种族或性别）引起的歧视性影响。
*   **可解释性 (Explainability)**：理解模型为何做出特定预测至关重要。因果模型提供了对其预测背后机制的洞察，超越了仅仅的特征重要性。
*   **隐私 (Privacy)**：虽然教程中没有详细展开，但理解数据中的因果关系有时可能与隐私考虑因素相关，例如在推断敏感属性时。

<img src="https://www.pywhy.org/dowhy/v0.12/_images/ml_challenges_causality.png" alt="ML挑战与因果关系" width="600"/>

教程指出，如何利用因果关系来解决上述许多挑战是一个活跃的研究领域。

## 5. 更多资源

### 5.1 DoWhy+EconML 库

*   **DoWhy 代码**: [microsoft/dowhy](https://github.com/microsoft/dowhy)
*   **DoWhy 笔记本**: [microsoft/dowhy/tree/main/docs/source/example_notebooks](https://github.com/microsoft/dowhy/tree/main/docs/source/example_notebooks) (链接基于当前GitHub结构，可能与教程页面略有不同)
*   **EconML 代码**: [microsoft/econml](https://github.com/microsoft/econml)
*   **EconML 笔记本**: [microsoft/EconML/tree/main/notebooks](https://github.com/microsoft/EconML/tree/main/notebooks) (链接基于当前GitHub结构)

### 5.2 关于因果推断及其与机器学习联系的视频讲座

*   **微软研究院网络研讨会**: [https://note.microsoft.com/MSR-Webinar-DoWhy-Library-Registration-On-Demand.html](https://note.microsoft.com/MSR-Webinar-DoWhy-Library-Registration-On-Demand.html)

### 5.3 KDD 因果推断详细教程

*   [https://causalinference.gitlab.io/kdd-tutorial/](https://causalinference.gitlab.io/kdd-tutorial/)

### 5.4 关于因果关系和机器学习的书籍章节

*   [http://causalinference.gitlab.io/](http://causalinference.gitlab.io/) (这似乎是更广泛的因果推断资源链接，可能包含书籍章节或相关材料)

### 5.5 微软的因果关系与机器学习研究组

*   [https://www.microsoft.com/en-us/research/group/causal-inference/](https://www.microsoft.com/en-us/research/group/causal-inference/)

---

**文档总结与解释**:

该教程《因果推断及其与机器学习的联系（使用 DoWhy+EconML）》旨在向读者介绍因果推断的基本概念、其在决策中的重要性，以及如何使用 Python 库 DoWhy 和 EconML 来执行因果分析。

**核心要点**：

1.  **因果推断的重要性**：许多数据科学问题不仅仅是预测问题，而是需要理解行动（干预）对结果的因果效应，以便做出明智的决策。教程区分了预测（\(E[Y|X]\)）和因果推断（\(E[Y|do(X)]\)）。
2.  **因果推断的四个步骤**：
    *   **建模**：使用因果图（DAG）来表示关于数据生成过程的假设。
    *   **识别**：基于因果图，确定是否以及如何从观察数据中估计因果效应，得到估计量。
    *   **估计**：使用统计或机器学习方法（如 DoWhy 和 EconML 提供的）来计算估计量的值。
    *   **反驳**：通过各种测试（如添加随机共同原因、安慰剂治疗）来检验估计的稳健性和模型假设的有效性。
3.  **DoWhy 和 EconML 的作用**：
    *   **DoWhy**：提供了一个端到端的框架，侧重于建模、识别和反驳步骤。它帮助用户明确其因果假设，并系统地进行因果分析。
    *   **EconML**：专注于估计步骤，特别是提供了许多基于机器学习的先进方法来估计（异质性）因果效应，能够处理高维数据和复杂的非线性关系。
4.  **实践应用**：教程通过一个合成数据集的例子详细演示了这四个步骤，并展示了如何使用线性回归和更高级的 DML 方法进行估计。它还强调了在非线性场景下简单模型的局限性。
5.  **案例研究**：提供了多个真实世界场景的案例研究链接，展示了这些工具在客户忠诚度计划评估、A/B 测试、用户细分和投资归因等方面的应用。
6.  **与机器学习的联系**：强调了因果推断对于解决机器学习中的一些根本性挑战（如分布外泛化、公平性、可解释性）的潜力。因果视角有助于构建更稳健、更可靠、更值得信赖的 AI 系统。
7.  **资源**：提供了指向 DoWhy 和 EconML 库、相关教程、视频讲座和研究小组的链接，供读者进一步学习。

总的来说，这篇教程为希望在其工作中应用因果推断的数据科学家和研究人员提供了一个很好的起点。它不仅解释了理论概念，还通过实际代码示例和工具使其易于理解和应用。关键信息是，虽然机器学习在预测方面非常强大，但要进行有效的决策，理解和量化因果关系是必不可少的，而 DoWhy 和 EconML 等工具正为此提供了支持。
