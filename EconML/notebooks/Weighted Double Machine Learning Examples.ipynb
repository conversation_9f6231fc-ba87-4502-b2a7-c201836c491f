{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Double Machine Learning: Summarized Data and Interpretability\n", "\n", "Double Machine Learning (DML) is an algorithm that applies arbitrary machine learning methods\n", "to fit the treatment and response, then uses a linear model to predict the response residuals\n", "from the treatment residuals."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Helper imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "%matplotlib inline\n", "\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generating Raw Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import scipy.special\n", "\n", "np.random.seed(123)\n", "n=10000 # number of raw samples\n", "d=10 # number of binary features + 1\n", "\n", "# Generating random segments aka binary features. We will use features 1,...,4 for heterogeneity.\n", "# The rest for controls. Just as an example.\n", "X = np.random.binomial(1, .5, size=(n, d))\n", "# The first column of X is the treatment. Generating an imbalanced A/B test\n", "X[:, 0] = np.random.binomial(1, scipy.special.expit(X[:, 1]))\n", "# Generating an outcome with treatment effect heterogeneity. The first binary feature creates heterogeneity\n", "# We also have confounding on the first variable. We also have heteroskedastic errors.\n", "y = (-1 + 2 * X[:, 1]) * X[:, 0] + X[:, 1] + (1*X[:, 1] + 1)*np.random.normal(0, 1, size=(n,))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Creating Summarized Data\n", "\n", "For each segment, we split the data in two and create one summarized copy for each split. The summarized copy contains the number of samples that were summarized and the variance of the observations for the summarized copies. Optimally we would want two copies per segment, as I'm creating here, but with many segments, the approach would work ok even with a single copy per segment."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from econml.tests.test_statsmodels import _summarize\n", "\n", "X_sum = np.unique(X, axis=0)\n", "n_sum = np.zeros(X_sum.shape[0])\n", "# The _summarize function performs the summary operation and returns the summarized data\n", "# For each segment we have two copies.\n", "X1, X2, y1, y2, X1_sum, X2_sum, y1_sum, y2_sum, n1_sum, n2_sum, var1_sum, var2_sum = _summarize(X, y)\n", "\n", "# We concatenate the two copies data\n", "X_sum = np.vstack([X1_sum, X2_sum]) # first coordinate is treatment, the rest are features\n", "y_sum = np.concatenate((y1_sum, y2_sum)) # outcome\n", "n_sum = np.concatenate((n1_sum, n2_sum)) # number of summarized points\n", "var_sum = np.concatenate((var1_sum, var2_sum)) # variance of the summarized points\n", "splits = (np.arange(len(y1_sum)), np.arange(len(y1_sum), len(y_sum))) # indices of the two summarized copies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Applying the LinearDML"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.dml.LinearDML at 0x1dcfd784408>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "from econml.dml import LinearDML\n", "from sklearn.linear_model import LogisticRegressionCV\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = LinearDML(model_y=WeightedLassoCV(cv=3),\n", "                model_t=LogisticRegressionCV(cv=3),\n", "                discrete_treatment=True)\n", "est.fit(y_sum, X_sum[:, 0], X=X_sum[:, 1:5], W=X_sum[:, 5:],\n", "        freq_weight=n_sum, sample_var=var_sum)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.07157889])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Treatment Effect of particular segments\n", "est.effect(np.array([[1, 0, 0, 0]])) # effect of segment with features [1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([0.87415187]), array([1.26900591]))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Confidence interval for effect\n", "est.effect_interval(np.array([[1, 0, 0, 0]]), alpha=.05) # effect of segment with features [1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[['A' '2.0109841149829912']\n", " ['B' '0.07212612018559361']\n", " ['C' '-0.01525518572828466']\n", " ['D' '-0.18928230056541373']]\n"]}], "source": ["# Getting the coefficients of the linear CATE model together with the corresponding feature names\n", "print(np.array(list(zip(est.cate_feature_names(['A', 'B', 'C', 'D']), est.coef_))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Non-Linear CATE Models with Polynomial Features"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.dml.LinearDML at 0x1dcfd7a4448>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "from econml.dml import LinearDML\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from sklearn.preprocessing import PolynomialFeatures\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = LinearDML(model_y=WeightedLassoCV(cv=3),\n", "                model_t=LogisticRegressionCV(cv=3),\n", "                featurizer=PolynomialFeatures(degree=2, interaction_only=True, include_bias=False),\n", "                discrete_treatment=True)\n", "est.fit(y_sum, X_sum[:, 0], X=X_sum[:, 1:5], W=X_sum[:, 5:],\n", "        freq_weight=n_sum, sample_var=var_sum)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Getting the confidence intervals of the coefficients and the intercept of the CATE model\n", "# together with the corresponding feature names.\n", "feat_names = est.cate_feature_names(['A', 'B', 'C', 'D'])\n", "point_int = est.intercept_\n", "point = est.coef_\n", "lower_int, upper_int = est.intercept__interval(alpha=0.01)\n", "lower, upper = est.coef__interval(alpha=0.01)\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style('darkgrid'):\n", "    fig, ax = plt.subplots(1,1)\n", "    x = np.arange(1, 1 + len(point))\n", "    plt.errorbar(np.concatenate(([0], x)), np.concatenate(([point_int], point)),\n", "                 np.hstack([np.array([[point_int-lower_int], [upper_int - point_int]]), yerr]), fmt='o')\n", "    ax.set_xticks(np.concatenate(([0], x)))\n", "    ax.set_xticklabels([1] + list(feat_names), rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import itertools\n", "# Getting the confidence intervals of the CATE(x) for different x vectors\n", "fnames = np.array(['A', 'B', 'C', 'D'])\n", "\n", "lst = list(itertools.product([0, 1], repeat=4))\n", "point = []\n", "lower = []\n", "upper = []\n", "feat_names = []\n", "for x in lst:\n", "    feat_names.append(\" \".join(fnames[np.array(x)>0]))\n", "    x = np.array(x).reshape((1, -1))\n", "    point.append(est.effect(x)[0])\n", "    lb, ub = est.effect_interval(x, alpha=.01)\n", "    lower.append(lb[0])\n", "    upper.append(ub[0])\n", "\n", "feat_names = np.array(feat_names)\n", "point = np.array(point)\n", "lower = np.array(lower)\n", "upper = np.array(upper)\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style('darkgrid'):\n", "    fig, ax = plt.subplots(1,1, figsize=(20, 5)) \n", "    x = np.arange(len(point))\n", "    stat_sig = (lower>0) | (upper<0)\n", "    plt.errorbar(x[stat_sig], point[stat_sig], yerr[:, stat_sig], fmt='o', label='stat_sig')\n", "    plt.errorbar(x[~stat_sig], point[~stat_sig], yerr[:, ~stat_sig], fmt='o', color='red', label='insig')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(feat_names, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Non-Linear CATE Models with Forests"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.causal_forest.CausalForestDML at 0x1dcfe8f9848>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.dml import CausalForestDML\n", "from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = CausalForestDML(model_y=GradientBoostingRegressor(n_estimators=30, min_samples_leaf=30),\n", "                      model_t=GradientBoostingClassifier(n_estimators=30, min_samples_leaf=30),\n", "                      discrete_treatment=True,\n", "                      n_estimators=1000,\n", "                      min_samples_leaf=2,\n", "                      min_impurity_decrease=0.001,\n", "                      verbose=0, min_weight_fraction_leaf=.03)\n", "est.fit(y_sum, X_sum[:, 0], X=X_sum[:, 1:5], W=X_sum[:, 5:],\n", "        sample_weight=n_sum)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import itertools\n", "# Getting the confidence intervals of the CATE(x) for different x vectors\n", "fnames = np.array(['A', 'B', 'C', 'D'])\n", "\n", "lst = list(itertools.product([0, 1], repeat=4))\n", "point = []\n", "lower = []\n", "upper = []\n", "feat_names = []\n", "for x in lst:\n", "    feat_names.append(\" \".join(fnames[np.array(x)>0]))\n", "    x = np.array(x).reshape((1, -1))\n", "    point.append(est.effect(x)[0])\n", "    lb, ub = est.effect_interval(x, alpha=.01)\n", "    lower.append(lb[0])\n", "    upper.append(ub[0])\n", "\n", "feat_names = np.array(feat_names)\n", "point = np.array(point)\n", "lower = np.array(lower)\n", "upper = np.array(upper)\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style('darkgrid'):\n", "    fig, ax = plt.subplots(1,1, figsize=(20, 5)) \n", "    x = np.arange(len(point))\n", "    stat_sig = (lower>0) | (upper<0)\n", "    plt.errorbar(x[stat_sig], point[stat_sig], yerr[:, stat_sig], fmt='o', label='stat_sig')\n", "    plt.errorbar(x[~stat_sig], point[~stat_sig], yerr[:, ~stat_sig], fmt='o', color='red', label='insig')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(feat_names, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tree Interpretation of the CATE Model"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from econml.cate_interpreter import SingleTreeCateInterpreter"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.cate_interpreter._interpreters.SingleTreeCateInterpreter at 0x1dcfef36348>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=1)\n", "# We interpret the CATE models behavior on the distribution of heterogeneity features\n", "intrp.interpret(est, X_sum[:, 1:5])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# exporting to a dot file\n", "intrp.export_graphviz(out_file='cate_tree.dot', feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# or we can directly render. Requires the graphviz python library\n", "intrp.render(out_file='cate_tree', format='pdf', view=True, feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Matplotlib is currently using agg, which is a non-GUI backend, so cannot show the figure.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# or we can also plot inline with matplotlib. a bit uglier\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=['A', 'B', 'C', 'D'], fontsize=12)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tree Based Treatment Policy Based on CATE Model"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["from econml.cate_interpreter import SingleTreePolicyInterpreter"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.cate_interpreter._interpreters.SingleTreePolicyInterpreter at 0x1dcff1f9748>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["intrp = SingleTreePolicyInterpreter(risk_level=0.05, max_depth=3, min_samples_leaf=1, min_impurity_decrease=.001)\n", "# We find a tree based treatment policy based on the CATE model\n", "# sample_treatment_costs is the cost of treatment. Policy will treat if effect is above this cost.\n", "# It can also be an array that has a different cost for each sample. In case treating different segments\n", "# has different cost.\n", "intrp.interpret(est, X_sum[:, 1:5],\n", "                sample_treatment_costs=0)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# exporting to a dot file\n", "intrp.export_graphviz(out_file='cate_tree.dot', feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# or we can directly render. Requires the graphviz python library\n", "intrp.render(out_file='policy_tree', format='pdf', view=True, feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Matplotlib is currently using agg, which is a non-GUI backend, so cannot show the figure.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# or we can also plot inline with matplotlib. a bit uglier\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=['A', 'B', 'C', 'D'], fontsize=14)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Appendix: Amendment\n", "\n", "To make estimation even more precise one should simply choose the two splits used during the crossfit part of Double Machine Learning so that each summaried copy of a segment ends up in a separate split. We can do this as follows: "]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.dml.LinearDML at 0x1dc81fe0988>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "from econml.dml import LinearDML\n", "from sklearn.linear_model import LogisticRegressionCV\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = LinearDML(model_y=WeightedLassoCV(cv=3),\n", "                model_t=LogisticRegressionCV(cv=3),\n", "                discrete_treatment=True,\n", "                cv=[(splits[0], splits[1]), (splits[1], splits[0])]) # we input custom fold structure\n", "est.fit(y_sum, X_sum[:, 0], X=X_sum[:, 1:5], W=X_sum[:, 5:],\n", "        freq_weight=n_sum, sample_var=var_sum)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.07249425])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# Treatment Effect of particular segments\n", "est.effect(np.array([[1, 0, 0, 0]])) # effect of segment with features [1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([0.90614918]), array([1.23883932]))"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Confidence interval for effect\n", "est.effect_interval(np.array([[1, 0, 0, 0]])) # effect of segment with features [1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}