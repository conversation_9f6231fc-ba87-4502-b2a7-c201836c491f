# Governance Policy

This document provides the governance policy for the Project. Maintainers agree to this policy and to abide by all Project polices, including the [code of conduct](https://github.com/py-why/governance/blob/main/CODE-OF-CONDUCT.md), [trademark policy](https://github.com/py-why/governance/blob/main/TRADEMARKS.md), and [antitrust policy](https://github.com/py-why/governance/blob/main/ANTITRUST.md) by adding their name to the [MAINTAINERS.md file](./MAINTAINERS.md).

## 1. Roles.

This project may include the following roles. Additional roles may be adopted and documented by the Project.

**1.1. Maintainers**. Maintainers are responsible for organizing activities around developing, maintaining, and updating the Project. Maintainers are also responsible for determining consensus. This Project may add or remove Maintainers with the approval of the current Maintainers.

**1.2. Contributors**. Contributors are those that have made contributions to the Project.

## 2. Decisions.

**2.1. Consensus-Based Decision Making**. Projects make decisions through consensus of the Maintainers. While explicit agreement of all Maintainers is preferred, it is not required for consensus. Rather, the Maintainers will determine consensus based on their good faith consideration of a number of factors, including the dominant view of the Contributors and nature of support and objections. The Maintainers will document evidence of consensus in accordance with these requirements.

**2.2. Appeal Process**. Decisions may be appealed by opening an issue and that appeal will be considered by the Maintainers in good faith, who will respond in writing within a reasonable time. If the Maintainers deny the appeal, the appeal may be brought before the PyWhy Steering Committee, who will also respond in writing in a reasonable time.

## 3. How We Work.

**3.1. Openness**. Participation is open to anyone who is directly and materially affected by the activity in question. There shall be no undue financial barriers to participation.

**3.2. Balance**. The development process should balance the interests of Contributors and other stakeholders. Contributors from diverse interest categories shall be sought with the objective of achieving balance.

**3.3. Coordination and Harmonization**. Good faith efforts shall be made to resolve potential conflicts or incompatibility between releases in this Project.

**3.4. Consideration of Views and Objections**. Prompt consideration shall be given to the written views and objections of all Contributors.

**3.5. Written procedures**. This governance document and other materials documenting this project's development process shall be available to any interested person.

## 4. No Confidentiality.

Information disclosed in connection with any Project activity, including but not limited to meetings, contributions, and submissions, is not confidential, regardless of any markings or statements to the contrary.

## 5. Trademarks.

Any names, trademarks, logos, or goodwill developed by and associated with the Project (the "Marks") are controlled by the PyWhy. Maintainers may only use these Marks in accordance with the PyWhy's trademark policy. If a Maintainer resigns or is removed, any rights the Maintainer may have in the Marks revert to the PyWhy.

## 6. Amendments.

Amendments to this governance policy may be made by affirmative vote of 2/3 of all Maintainers, with approval by the PyWhy's Steering Committee.

---
Part of MVG-0.1-beta.
Made with love by GitHub. Licensed under the [CC-BY 4.0 License](https://creativecommons.org/licenses/by-sa/4.0/).
