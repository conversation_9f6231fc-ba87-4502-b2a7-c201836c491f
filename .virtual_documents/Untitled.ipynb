


import numpy as np

from dowhy import CausalModel
import dowhy.datasets
# 引入 dowhy 数据集生成函数，用于生成一个线性的因果模型的数据
data = dowhy.datasets.linear_dataset(
    beta=10,  # 设置treatments（treatment）对结果（outcome）的影响大小为 10
    num_common_causes=1,  # 设置有 5 个common cause（也叫做混杂变量或者控制变量）
    num_instruments=1,  # 设置有 2 个工具变量（instrumental variables）
    num_samples=5000,  # 设置样本数量为 5000
    treatment_is_binary=True,  # 设置treatments（treatment）是一个二进制变量（0 或 1）
)

# 将生成的数据存储在 DataFrame 中
df = data["df"]

df.rename(columns={'v0': 'Education', 'y': 'Income', 'W0': 'Ability', 'Z0': 'Distance_to_college'}, inplace=True)



model = CausalModel(
   data=df, # some pandas dataframe
   treatment="Education",
   outcome="Income",
   common_causes=["Ability"],
   instruments=['Distance_to_college']
)
model.view_model()














# model is an instance of CausalModel
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)





# model is an instance of CausalModel
identified_estimand = model.identify_effect(method_name="id-algorithm")
print(identified_estimand)











estimate = model.estimate_effect(identified_estimand,
       method_name="backdoor.linear_regression",
       test_significance=True
)
print(estimate)





# 使用 `estimate_effect` 方法来估算 `identified_estimand` 定义的因果效应。
# 这里使用 "backdoor.propensity_score_stratification" 作为估计方法，它是一种倾向得分分层方法。
estimate = model.estimate_effect(identified_estimand, method_name="backdoor.propensity_score_stratification")

# 打印出因果效应的估计结果，这将包括用于估计的表达式和相关假设。
print(estimate)






# Propensity-Based Matching
causal_estimate_match = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.propensity_score_matching",
                                             target_units="atc")
print(causal_estimate_match)
print("Causal Estimate is " + str(causal_estimate_match.value))





# Inverse Propensity Weighting
causal_estimate_ipw = model.estimate_effect(identified_estimand,
                                           method_name="backdoor.propensity_score_weighting",
                                           target_units = "ate",
                                           method_params={"weighting_scheme":"ips_weight"})
print(causal_estimate_ipw)
print("Causal Estimate is " + str(causal_estimate_ipw.value))





causal_estimate_dmatch = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.distance_matching")
print(causal_estimate_dmatch)





causal_estimate_iv = model.estimate_effect(identified_estimand,
       method_name="iv.instrumental_variable", method_params = {'iv_instrument_name': 'Distance_to_college'})
print(causal_estimate_iv)
print("Causal Estimate is " + str(causal_estimate_iv.value))


### 评估
