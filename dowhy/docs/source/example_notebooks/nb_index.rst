Example notebooks
=================

These examples are also available on `GitHub <https://github
.com/py-why/dowhy/tree/main/docs/source/example_notebooks>`_. You can `run them locally <https://docs.jupyter
.org/en/latest/running.html>`_ after cloning `DoWhy <https://github.com/py-why/dowhy>`_ and `installing <PERSON><PERSON><PERSON>
<https://jupyter.org/install>`_. Or you can run them directly in a web browser using the
`Binder environment <https://mybinder.org/v2/gh/microsoft/dowhy/main?filepath=docs%2Fsource%2F>`_.

Introductory examples
---------------------

.. grid:: 2

    .. grid-item-card:: :doc:`dowhy_simple_example`

        .. image:: ../_static/effect-estimation-estimand-expression.png
            :align: center

        +++
        | **Level:** Beginner
        | **Task:** Effect estimation

    .. grid-item-card:: :doc:`gcm_basic_example`

        .. image:: ../_static/graph-xyz.png
            :height: 120px
            :align: center

        +++
        | **Level:** Beginner
        | **Task:** Intervention via GCM

.. grid:: 2

    .. grid-item-card:: :doc:`gcm_draw_samples`

        .. image:: ../_static/draw-samples.png
            :height: 120px
            :align: center

        +++
        | **Level:** Beginner
        | **Task:** Sample generation

    .. grid-item-card:: :doc:`dowhy_confounder_example`

        .. image:: ../_static/confounder-example.png
            :height: 120px
            :align: center

        +++
        | **Level:** Beginner
        | **Task:** Effect estimation

.. grid:: 2

    .. grid-item-card:: :doc:`dowhy-conditional-treatment-effects`

        .. image:: ../_static/conditional-treatment-effect.png
            :height: 120px
            :align: center

        +++
        | **Level:** Beginner
        | **Task:** Conditional effect estimation

    .. grid-item-card:: :doc:`tutorial-causalinference-machinelearning-using-dowhy-econml`

        .. image:: ../_static/dowhy-econml-tutorial.png
            :height: 120px
            :align: center

        +++
        | **Level:** Beginner
        | **Task:** All

.. grid:: 2

    .. grid-item-card:: :doc:`do_sampler_demo`

        +++
        | **Level:** Beginner
        | **Task:** Interventions

Real world-inspired examples
----------------------------

.. grid:: 2

    .. grid-item-card:: :doc:`DoWhy-The Causal Story Behind Hotel Booking Cancellations`

        .. image:: ../_static/hotel-bookings.png
            :height: 120px
            :align: center
        +++
        | **Level:** Beginner
        | **Task:** Effect estimation

    .. grid-item-card:: :doc:`dowhy_example_effect_of_memberrewards_program`

        .. image:: ../_static/membership-program-graph.png
            :height: 120px
            :align: center
        +++
        | **Level:** Beginner
        | **Task:** Effect estimation

.. grid:: 2

    .. grid-item-card:: :doc:`gcm_online_shop`

        .. image:: ../_static/online-shop.png
            :height: 120px
            :align: center
        +++
        | **Level:** Beginner
        | **Task:** Quantify causal effects and root cause analysis via GCM

    .. grid-item-card:: :doc:`gcm_rca_microservice_architecture`

        .. image:: ../_static/microservice-architecture.png
            :height: 120px
            :align: center
        +++
        | **Level:** Beginner
        | **Task:** Root cause analysis, intervention, counterfactual via GCM

.. grid:: 2

    .. grid-item-card:: :doc:`gcm_401k_analysis`

        .. image:: ../_static/401k-example.png
            :height: 120px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Intervention via GCM

    .. grid-item-card:: :doc:`gcm_supply_chain_dist_change`

        .. image:: ../_static/supply-chain.png
            :height: 120px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Root cause analysis via GCM

.. grid:: 2

    .. grid-item-card:: :doc:`gcm_cps2015_dist_change_robust`

        .. image:: ../_static/cps2015-shapley-values.png
            :width: 200px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Causal Change Attribution via GCM

    .. grid-item-card:: :doc:`gcm_icc`

        .. image:: ../_static/icc-example.jpg
            :width: 200px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Intrinsic causal influence via GCM
   

.. grid:: 2

    .. grid-item-card:: :doc:`gcm_counterfactual_medical_dry_eyes`

        .. image:: ../_static/gcm-counterfactual-example.png
            :width: 200px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Counterfactuals via GCM

    .. grid-item-card:: :doc:`gcm_falsify_dag`

        .. image:: ../_static/gcm-falsify-example.png
            :height: 120px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Falsifying Causal Graph

.. grid:: 2

    .. grid-item-card:: :doc:`counterfactual_fairness_dowhy`

        .. image:: ../_static/counterfactual_fairness_dowhy.png
            :height: 120px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Fairness estimation via GCM

    .. grid-item-card:: :doc:`sales_attribution_intervention`

        .. image:: ../_static/sales_attribution.png
            :height: 120px
            :align: center
        +++
        | **Level:** Advanced
        | **Task:** Causal Inference



Examples on benchmark datasets
-------------------------------

.. grid:: 3

    .. grid-item-card:: :doc:`dowhy_ihdp_data_example`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_lalonde_example`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_refutation_testing`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

.. grid:: 3

    .. grid-item-card:: :doc:`gcm_401k_analysis`

        +++
        | **Level:** Advanced
        | **Task:** GCM inference

    .. grid-item-card:: :doc:`prediction/dowhy_causal_prediction_demo`

        +++
        | **Level:** Advanced
        | **Task:** Prediction

    .. grid-item-card:: :doc:`lalonde_pandas_api`

        +++
        | **Level:** Advanced
        | **Task:** Do Sampler

.. grid:: 3

    .. grid-item-card:: :doc:`counterfactual_fairness_dowhy`

        +++
        | **Level:** Advanced
        | **Task:** Fairness estimation

    .. grid-item-card:: :doc:`dowhy_twins_example`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference


Modeling and refuting causal assumptions
----------------------------------------

.. grid:: 3

    .. grid-item-card:: :doc:`load_graph_example`

        +++
        | **Level:** Beginner
        | **Task:** All

    .. grid-item-card:: :doc:`dowhy_causal_discovery_example`

        +++
        | **Level:** Beginner
        | **Task:** All

    .. grid-item-card:: :doc:`gcm_falsify_dag`

        +++
        | **Level:** Advanced
        | **Task:** All

.. grid:: 3

    .. grid-item-card:: :doc:`sensitivity_analysis_testing`

        +++
        | **Level:** Beginner
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`sensitivity_analysis_nonparametric_estimators`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_refuter_notebook`

        +++
        | **Level:** Beginner
        | **Task:** Effect inference

.. grid:: 3

    .. grid-item-card:: :doc:`dowhy_refuter_assess_overlap`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_demo_dummy_outcome_refuter`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_ranking_methods`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

Miscellaneous
-------------

.. grid:: 3

    .. grid-item-card:: :doc:`dowhy_estimation_methods`

        +++
        | **Level:** Beginner
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy-simple-iv-example`

        +++
        | **Level:** Beginner
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_interpreter`

        +++
        | **Level:** Beginner
        | **Task:** Effect inference


.. grid:: 3

    .. grid-item-card:: :doc:`dowhy_mediation_analysis`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference
        
    .. grid-item-card:: :doc:`dowhy_multiple_treatments`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_efficient_backdoor_example`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

.. grid:: 3

    .. grid-item-card:: :doc:`identifying_effects_using_id_algorithm`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

    .. grid-item-card:: :doc:`dowhy_optimize_backdoor_example`

        +++
        | **Level:** Advanced
        | **Task:** Effect inference

.. toctree::
   :maxdepth: 1
   :caption: Introductory examples
   :hidden:

   dowhy_simple_example
   gcm_basic_example
   gcm_draw_samples
   dowhy_confounder_example
   dowhy-conditional-treatment-effects
   tutorial-causalinference-machinelearning-using-dowhy-econml
   dowhy_mediation_analysis
   dowhy_refuter_notebook
   do_sampler_demo

.. toctree::
   :maxdepth: 1
   :caption: Real world-inspired examples
   :hidden:

   DoWhy-The Causal Story Behind Hotel Booking Cancellations
   dowhy_example_effect_of_memberrewards_program
   gcm_online_shop
   gcm_rca_microservice_architecture
   gcm_401k_analysis
   gcm_supply_chain_dist_change
   gcm_icc
   gcm_counterfactual_medical_dry_eyes
   gcm_falsify_dag
   counterfactual_fairness_dowhy
   sales_attribution_intervention

.. toctree::
   :maxdepth: 1
   :caption: Examples on benchmarks datasets
   :hidden:

   dowhy_ihdp_data_example
   dowhy_lalonde_example
   dowhy_refutation_testing
   gcm_401k_analysis
   prediction/dowhy_causal_prediction_demo
   lalonde_pandas_api
   counterfactual_fairness_dowhy
   dowhy_twins_example

.. toctree::
   :maxdepth: 1
   :caption: Modeling and refuting causal assumptions
   :hidden:

   load_graph_example
   dowhy_causal_discovery_example
   gcm_falsify_dag
   sensitivity_analysis_testing
   sensitivity_analysis_nonparametric_estimators
   dowhy_refuter_notebook
   dowhy_refuter_assess_overlap
   dowhy_ranking_methods
   dowhy_demo_dummy_outcome_refuter
   
.. toctree::
   :maxdepth: 1
   :caption: Miscellaneous
   :hidden:

   dowhy_estimation_methods
   dowhy-simple-iv-example
   dowhy_interpreter
   dowhy_mediation_analysis
   dowhy_multiple_treatments
   dowhy_efficient_backdoor_example
   identifying_effects_using_id_algorithm
   dowhy_optimize_backdoor_example
   dowhy_functional_api
   dowhy_causal_api
