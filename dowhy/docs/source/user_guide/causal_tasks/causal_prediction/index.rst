Predicting outcome for out-of-distribution inputs
=================================================

DoWhy supports prediction algorithms too. Unlike standard machine learning algorithms that use all features, the prediction algorithm in DoWhy aims to learn a causal feature representation for prediction. The representation is learned using domain knowledge of auxiliary features provided by the user. 

For details, check out the `CACM prediction notebook <../../../example_notebooks/prediction/dowhy_causal_prediction_demo.html>`_.
