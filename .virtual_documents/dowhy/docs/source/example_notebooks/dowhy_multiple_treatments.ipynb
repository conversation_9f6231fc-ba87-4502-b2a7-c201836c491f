


from dowhy import CausalModel
import dowhy.datasets

import warnings
warnings.filterwarnings('ignore')


data = dowhy.datasets.linear_dataset(10, 
                                     num_common_causes=4, 
                                     num_samples=10000,
                                     num_instruments=0, 
                                     num_effect_modifiers=2,
                                     num_treatments=2,
                                     treatment_is_binary=False,
                                     num_discrete_common_causes=2,
                                     num_discrete_effect_modifiers=0,
                                     one_hot_encode=False)
df=data['df']
df.head()


model = CausalModel(data=data["df"], 
                    treatment=data["treatment_name"], outcome=data["outcome_name"], 
                    graph=data["gml_graph"])


model.view_model()


identified_estimand= model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)





linear_estimate = model.estimate_effect(identified_estimand, 
                                        method_name="backdoor.linear_regression",
                                        control_value=(0,0),
                                        treatment_value=(1,1),
                                        method_params={'need_conditional_estimates': False})
print(linear_estimate) 





linear_estimate = model.estimate_effect(identified_estimand, 
                                        method_name="backdoor.linear_regression",
                                        control_value=(0,0),
                                        treatment_value=(1,1))
print(linear_estimate) 



