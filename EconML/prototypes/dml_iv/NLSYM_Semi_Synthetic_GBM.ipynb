{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression, Lasso, LogisticRegression\n", "from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.preprocessing import PolynomialFeatures, StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "import scipy.special"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# NLSYM DATA"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\preprocessing\\data.py:645: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.partial_fit(X, y)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\base.py:464: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.fit(X, **fit_params).transform(X)\n"]}], "source": ["# Preprocess data\n", "df = pd.read_csv(\"data/card.csv\")\n", "data_filter = df['educ'].values >= 6\n", "T = df['educ'].values[data_filter]\n", "Z = df['nearc4'].values[data_filter]\n", "y = df['lwage'].values[data_filter]\n", "\n", "# Impute missing values with mean, add dummy columns\n", "# I excluded the columns 'weights' as we don't know what it is\n", "X_df = df[['exper', 'expersq']].copy()\n", "X_df['fatheduc'] = df['fatheduc'].fillna(value=df['fatheduc'].mean())\n", "X_df['fatheduc_nan'] = df['fatheduc'].isnull()*1\n", "X_df['motheduc'] = df['motheduc'].fillna(value=df['motheduc'].mean())\n", "X_df['motheduc_nan'] = df['motheduc'].isnull()*1\n", "X_df[['momdad14', 'sinmom14', 'reg661', 'reg662',\n", "        'reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']] = df[['momdad14', 'sinmom14', \n", "        'reg661', 'reg662','reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']]\n", "X_df[['black', 'smsa', 'south', 'smsa66']] = df[['black', 'smsa', 'south', 'smsa66']]\n", "columns_to_scale = ['fatheduc', 'motheduc', 'exper', 'expersq']\n", "scaler = StandardScaler()\n", "X_raw = X_df.values[data_filter]\n", "X_df[columns_to_scale] = scaler.fit_transform(X_df[columns_to_scale])\n", "X = X_df.values[data_filter]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['exper', 'expersq', 'fatheduc', 'fatheduc_nan', 'motheduc',\n", "       'motheduc_nan', 'momdad14', 'sinmom14', 'reg661', 'reg662', 'reg663',\n", "       'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66',\n", "       'black', 'smsa', 'south', 'smsa66'],\n", "      dtype='object')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X_df.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Semi-Synthetic Data with Binary Instrument and Continuous Treatment\n", "\n", "Data generating process uning real covariates $X$ and instrument $Z$, but synthetic $y$ and $T$ based on the \"intent to treat\" instrument setting with non-compliance. The instrument corresponds to a fully randomized recommendation of treatment. Then each sample complies with the recommendation to some degree. This probability also depends on an unobserved confounder that has a direct effect on the outcome. Moreover, compliance also depends on the observed feature $X$.\n", "\n", "\\begin{align}\n", "X \\tag{ real features}\\\\\n", "Z \\tag{real instrument}\\\\\n", "\\nu \\sim \\; & \\text{U}[0, 1] \\tag{unobserved confounder}\\\\\n", "C = \\; & c\\cdot X[i], \\; c \\;(const)\\sim \\text{U}[.2, .3] \\tag{compliance degree}\\\\\n", "T = \\; & C\\cdot Z + g(X) + \\nu  \\tag{treatment}\\\\\n", "y \\sim \\; & \\text{Normal}(\\mu=\\theta(X) \\cdot (T + \\nu) + f(X),\\; \\sigma=.1) \\tag{outcome}\n", "\\end{align}\n", "\n", "Moreover:\n", "\\begin{align}\n", "\\theta(X) = \\; & \\alpha + \\beta \\cdot X[i] \\tag{CATE}\\\\\n", "f(X) = \\; & X[i] \\tag{Nuissance function}\\\\\n", "g(X) = \\; & X[i] \\tag{Nuissance function}\n", "\\end{align}\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAloAAACcCAYAAACndQVqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nZlYSJ1pmZmZmJXGiZWZmZlaS/wZXJP9Dsl8moAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 720x144 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def dgp_bin_Z_cont_T(X, Z, hetero_col, true_fn, random_seed=None):\n", "    np.random.seed(random_seed)\n", "    n, d = X.shape\n", "    nu = np.random.uniform(-1, 1, size=(n,))\n", "    c = np.random.uniform(0.2, 0.3)\n", "    C = c * X[:, hetero_col] # Compliers when recomended\n", "    T = C * Z + X[:, hetero_col] + nu # Treatment with compliance\n", "    y = true_fn(X) * (T + nu)  + 0.05*X[:, hetero_col] + np.random.normal(0, .1, size=(n,))\n", "    return y, T\n", "\n", "hetero_col = 4 # Mother's education\n", "hetero_col_2 = 7\n", "true_fn = lambda X: 0.1 + 0.05*X[:, hetero_col] - 0.1*X[:, hetero_col_2]\n", "\n", "np.random.seed(1237)\n", "y, T = dgp_bin_Z_cont_T(X_raw, Z, hetero_col, true_fn)\n", "\n", "plt.figure(figsize=(10, 2))\n", "plt.subplot(1, 2, 1)\n", "plt.hist(T[Z==0])\n", "plt.title(\"T[Z=0]: Total: {}, Mean(T): {:.2f}\".format(T[Z==0].shape[0], np.mean(T[Z==0])))\n", "plt.subplot(1, 2, 2)\n", "plt.hist(T[Z==1])\n", "plt.title(\"T[Z=1]: Total: {}, Mean(T): {:.2f}\".format(T[Z==1].shape[0], np.mean(T[Z==1])))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSIS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some hyperparameters"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.006686726847208292\n"]}], "source": ["random_seed = 12345 # random seed for each experiment\n", "N_SPLITS = 10 # number of splits for cross-fitting\n", "COV_CLIP = 20/X.shape[0] # covariance clipping in driv\n", "print(COV_CLIP)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some generic non-parametric regressors and classifiers"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from utilities import RegWrapper\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from xgboost import XGBClassifier, XGBRegressor\n", "from xgb_utilities import XGBWrapper\n", "\n", "# XGB forest models for Regression and Classification\n", "model = lambda: XGBWrapper(XGBRegressor(gamma=0.001, n_estimators=100, min_child_weight=20, n_jobs=10),\n", "                           early_stopping_rounds=5, eval_metric='rmse', binary=False)\n", "\n", "model_clf = lambda: RegWrapper(XGBWrapper(XGBClassifier(gamma=0.001, n_estimators=100, min_child_weight=20, n_jobs=10),\n", "                                          early_stopping_rounds=5, eval_metric='logloss', binary=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Some utility functions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["def nuisance_diagnostic(cate, nuisance_model, property_name, property_fn, \n", "                        index_names=None, statistic=np.std, threshold=None):\n", "    std = statistic([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]], axis=0)\n", "    if hasattr(std, '__len__'):\n", "        if threshold is None:\n", "            coefs = np.argmax(std).flatten()\n", "        else:\n", "            coefs = np.argwhere(std >= threshold).flatten()\n", "        if index_names is None:\n", "            index_names = np.arange(std.shape[0])\n", "        for high_var in coefs:\n", "            plt.title(\"{}: {}[{}] Across Folds\".format(nuisance_model, property_name, index_names[high_var]))\n", "            plt.plot([property_fn(ns)[high_var] for ns in cate.fitted_nuisances[nuisance_model]])\n", "            plt.xlabel('fold')\n", "            plt.ylabel('property')\n", "            plt.show()\n", "    else:\n", "        plt.title(\"{}: {} Across Folds\".format(nuisance_model, property_name))    \n", "        plt.plot([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]])\n", "        plt.xlabel('fold')\n", "        plt.ylabel('property')\n", "        plt.show()\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE via DMLATEIV"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\numba\\errors.py:105: UserWarning: Insufficiently recent colorama version found. Numba requires colorama >= 0.3.9\n", "  warnings.warn(msg)\n"]}], "source": ["from dml_ate_iv import DMLATEIV\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We need to specify models to be used for each of these residualizations\n", "model_Y_X = lambda: model() # model for E[Y | X]\n", "model_T_X = lambda: model() # model for E[T | X]. We use a regressor since T is continuous\n", "model_Z_X = lambda: model_clf() # model for E[Z | X]. We use a classifier since Z is binary\n", "\n", "dmlate = DMLATEIV(model_Y_X(), model_T_X(), model_Z_X(),\n", "                  n_splits=N_SPLITS, # n_splits determines the number of splits to be used for cross-fitting.\n", "                  binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                  binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "                 )"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<dml_ate_iv.DMLATEIV at 0x18d162284a8>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# We fit DMLATEIV with these models\n", "dmlate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["# We call effect() to get the ATE\n", "ta_effect = dmlate.effect()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.648\n", "True ATE: 0.609\n", "CATE MSE: 0.03\n"]}], "source": ["# Comparison with true ATE\n", "print(\"ATE Estimate: {:.3f}\".format(ta_effect))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))\n", "# CATE MSE\n", "print(\"CATE MSE: {:.2f}\".format(np.mean((true_fn(X_raw) - ta_effect)**2)))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate Interval: (0.601, 0.696)\n", "True ATE: 0.609\n"]}], "source": ["# We can call normal_effect_interval to get confidence intervals based\n", "# based on the asympotic normal approximation\n", "ta_effect = dmlate.normal_effect_interval(lower=2.5, upper=97.5)\n", "# Comparison with true ATE\n", "print(\"ATE Estimate Interval: ({:.3f}, {:.3f})\".format(ta_effect[0], ta_effect[1]))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 12 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done   8 tasks      | elapsed:   30.7s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Coverage: 0.72\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:  2.0min finished\n"]}], "source": ["def get_dmlateiv_coverage(true_effect, iteration):\n", "    y, T = dgp_bin_Z_cont_T(X_raw, Z, hetero_col, true_fn, random_seed=iteration)\n", "    dmlate = DMLATEIV(model_Y_X(), model_T_X(), model_Z_X(),\n", "                  n_splits=N_SPLITS, # n_splits determines the number of splits to be used for cross-fitting.\n", "                  binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                  binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "                 )\n", "    dmlate.fit(y, T, X, Z)\n", "    left, right = dmlate.normal_effect_interval(lower=2.5, upper=97.5)\n", "    if true_effect >= left and true_effect <= right:\n", "        return 1\n", "    return 0\n", "\n", "from joblib import Parallel, delayed\n", "n_experiments=100\n", "true_ate = np.mean(true_fn(X_raw))\n", "if True:\n", "    contains_truth = np.array(Parallel(n_jobs=-1, verbose=3)(\n", "            delayed(get_dmlateiv_coverage)(true_ate, it) for it in range(n_experiments)))\n", "    print(\"Coverage: {}\".format(contains_truth.mean()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE and CATE via DMLIV"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from utilities import SelectiveLasso, SeparateModel\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from econml.utilities import hstack\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DMLIV we also need a model for E[T | X, Z]. To allow for heterogeneity in the compliance, i.e.\n", "# T = beta(X)*Z + gamma(X)\n", "# we train a separate model for Z=1 and Z=0. The model for Z=1 learns the\n", "# quantity beta(X) + gamma(X) and the model for Z=0 learns gamma(X).\n", "model_T_XZ = lambda: SeparateModel(model(), model())\n", "\n", "# We now specify the features to be used for heterogeneity. We will fit a CATE model of the form\n", "#      theta(X) = <theta, phi(X)>\n", "# for some set of features phi(X). The featurizer needs to support fit_transform, that takes\n", "# X and returns phi(X). We need to include a bias if we also want a constant term.\n", "dmliv_featurizer = lambda: PolynomialFeatures(degree=1, include_bias=True)\n", "\n", "# Then we need to specify a model to be used for fitting the parameters theta in the linear form.\n", "# This model will minimize the square loss:\n", "#        (Y - E[Y|X] - <theta, phi(X)> * (E[T|X,Z] - E[T|X]))**2\n", "#dmliv_model_effect = lambda: LinearRegression(fit_intercept=False)\n", "\n", "\n", "# Potentially with some regularization on theta. Here we use an ell_1 penalty on theta\n", "# If we also have a prior that there is no effect heterogeneity we can use a selective lasso\n", "# that does not penalize the constant term in the CATE model\n", "dmliv_model_effect = lambda: SelectiveLasso(np.arange(1, X.shape[1]+1), LassoCV(cv=5, fit_intercept=False))\n", "\n", "\n", "# We initialize DMLIV with all these models and call fit\n", "cate = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(), \n", "             dmliv_model_effect(), dmliv_featurizer(),\n", "             n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "             binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "             binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "            )"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<dml_iv.DMLIV at 0x18d17d0e1d0>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["cate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYEAAAD8CAYAAACRkhiPAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nf2Ld9bhxv8LaAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# To get the CATE at every X we call effect(X)\n", "dml_effect = cate.effect(X)\n", "plt.hist(dml_effect, label='est')\n", "plt.hist(true_fn(X_raw), alpha=.2, label='true')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.60084499  0.          0.          0.00162356 -0.          0.11428089\n", "  0.         -0.         -0.          0.         -0.          0.\n", "  0.          0.         -0.         -0.          0.         -0.\n", " -0.         -0.         -0.00789543 -0.         -0.        ]\n"]}], "source": ["# To get the parameter theta we call coef_. The first entry is the intercept of the CATE model\n", "print(cate.coef_)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.596\n", "True ATE: 0.609\n"]}], "source": ["# We can average the CATE to get an ATE\n", "print(\"ATE Estimate: {:.3f}\".format(np.mean(dml_effect)))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXcAAAEICAYAAACktLTqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also see how it compares to the true CATE at each target point and calculate MSE\n", "plt.title(\"DMLIV CATE as Function of {}. MSE={:.3f}\".format(X_df.columns[4], np.mean((dml_effect-true_fn(X_raw))**2)))\n", "plt.scatter(X[:, 4], dml_effect, label='est')\n", "plt.scatter(X[:, 4], true_fn(X_raw), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE and Projected CATE via DRIV"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper, StatsModelLinearRegression, ConstantModel\n", "from sklearn.dummy import DummyRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DRIV we need a model for predicting E[T*Z | X]. We use a classifier\n", "model_TZ_X = lambda: model()\n", "\n", "# We also need a model for the final regression that will fit the function theta(X)\n", "# If we want to fit an ATE, we simply fit a constant functin theta(X) = theta\n", "# We can do this with a pipeline where the preprocessing step only creates a bias column\n", "# and the regression step fits a linear regression with no intercept.\n", "# To get normal confidence intervals easily we can use a statsmodels linear regression\n", "# wrapped in an sklearn interface\n", "const_driv_model_effect = lambda: ConstantModel()\n", "\n", "# As in OrthoDMLIV we need a perliminary estimator of the CATE.\n", "# We use a DMLIV estimator with no cross-fitting (n_splits=1)\n", "dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=False)\n", "\n", "const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                        dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                        model_TZ_X(), # model for E[T * Z | X]\n", "                        const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                        cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                        n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                        binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                        binary_treatment=False, # a flag whether to stratify cross-fitting by treatment\n", "                        opt_reweighted=False # whether to optimally re-weight samples. Valid only for flexible final model\n", "                       )"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["<dr_iv.DRIV at 0x18d1da9b6d8>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["const_dr_cate.fit(y, T, X, Z, store_final=True)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\statsmodels\\regression\\linear_model.py:1554: RuntimeWarning: invalid value encountered in double_scalars\n", "  return self.ess/self.df_model\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py:877: RuntimeWarning: invalid value encountered in greater\n", "  return (self.a < x) & (x < self.b)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py:877: RuntimeWarning: invalid value encountered in less\n", "  return (self.a < x) & (x < self.b)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py:1831: RuntimeWarning: invalid value encountered in less_equal\n", "  cond2 = cond0 & (x <= self.a)\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>     nan</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>   nan</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>16:39:54</td>     <th>  Log-Likelihood:    </th> <td> -6509.5</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.302e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2990</td>      <th>  BIC:               </th> <td>1.303e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     0</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "    <td></td>       <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th> <td>    0.6501</td> <td>    0.039</td> <td>   16.668</td> <td> 0.000</td> <td>    0.574</td> <td>    0.727</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>2934.155</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   1.968</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>1860178.825</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td> 3.818</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>124.934</td> <th>  Cond. No.          </th>  <td>    1.00</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.000\n", "Model:                            OLS   Adj. R-squared:                  0.000\n", "Method:                 Least Squares   F-statistic:                       nan\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):                nan\n", "Time:                        16:39:54   Log-Likelihood:                -6509.5\n", "No. Observations:                2991   AIC:                         1.302e+04\n", "Df Residuals:                    2990   BIC:                         1.303e+04\n", "Df Model:                           0                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.6501      0.039     16.668      0.000       0.574       0.727\n", "==============================================================================\n", "Omnibus:                     2934.155   <PERSON><PERSON><PERSON>-<PERSON>:                   1.968\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          1860178.825\n", "Skew:                           3.818   Prob(JB):                         0.00\n", "Kurtosis:                     124.934   Cond. No.                         1.00\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is the pipeline, we then look\n", "# at the reg step of the pipeline which is the statsmodel wrapper and then we look\n", "# at the model attribute of the statsmodel wrapper and print the summary()\n", "const_dr_cate.effect_model.summary()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_driv_coverage(true_effect, iteration):\n", "    y, T = dgp_bin_Z_cont_T(X_raw, Z, hetero_col, true_fn, random_seed=iteration)\n", "    dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=True)\n", "    const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                            dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                            model_TZ_X(), # model for E[T * Z | X]\n", "                            const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                            cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                            n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                            binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                            binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "                           )\n", "    const_dr_cate.fit(y, T, X, Z, store_final=True)\n", "    left, right = const_dr_cate.effect_model.est.model.conf_int(alpha=0.05)[0]\n", "    if true_effect >= left and true_effect <= right:\n", "        return 1\n", "    return 0"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 12 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done   8 tasks      | elapsed:   31.2s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Coverage: 0.93\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:  4.3min finished\n"]}], "source": ["from joblib import Parallel, delayed\n", "n_experiments=100\n", "true_ate = np.mean(true_fn(X_raw))\n", "if True:\n", "    contains_truth = np.array(Parallel(n_jobs=-1, verbose=3)(\n", "            delayed(get_driv_coverage)(true_ate, it) for it in range(n_experiments)))\n", "    print(\"Coverage: {}\".format(contains_truth.mean()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Projecting CATE to a pre-chosen subset of variables in final model"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4 7]\n"]}], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We could also fit a projection on a subset of the features by using the\n", "# subset wrapper from our utilities.\n", "\n", "# Example: including everything for expository purposes, but any array_like of indices would work\n", "subset_names = set(['motheduc', 'sinmom14'])\n", "# list of indices of features X to use in the final model\n", "feature_inds = np.argwhere([(x in subset_names) for x in X_df.columns.values]).flatten() #[0] #np.arange(X.shape[1]) \n", "print(feature_inds)\n", "# Because we are projecting to a low dimensional model space, we can\n", "# do valid inference and we can use statsmodel linear regression to get all\n", "# the hypothesis testing capability\n", "proj_driv_model_effect = lambda: SubsetWrapper(StatsModelLinearRegression(),\n", "                                          feature_inds # list of indices of features X to use in the final model\n", "                                         )"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['exper', 'expersq', 'fatheduc', 'fatheduc_nan', 'motheduc',\n", "       'motheduc_nan', 'momdad14', 'sinmom14', 'reg661', 'reg662', 'reg663',\n", "       'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66',\n", "       'black', 'smsa', 'south', 'smsa66'],\n", "      dtype='object')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["X_df.columns"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"collapsed": true}, "outputs": [], "source": ["proj_dr_cate = const_dr_cate.refit_final(proj_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"collapsed": true}, "outputs": [], "source": ["# To get the CATE at every X we call effect(X[:, feature_inds])\n", "proj_dr_effect = proj_dr_cate.effect(X[:, feature_inds])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.002</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.001</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>   2.934</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>0.0533</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>16:44:16</td>     <th>  Log-Likelihood:    </th> <td> -6506.5</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.302e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2988</td>      <th>  BIC:               </th> <td>1.304e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     2</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>        <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>    <td>    0.6352</td> <td>    0.041</td> <td>   15.433</td> <td> 0.000</td> <td>    0.554</td> <td>    0.716</td>\n", "</tr>\n", "<tr>\n", "  <th>motheduc</th> <td>    0.0915</td> <td>    0.040</td> <td>    2.306</td> <td> 0.021</td> <td>    0.014</td> <td>    0.169</td>\n", "</tr>\n", "<tr>\n", "  <th>sinmom14</th> <td>    0.1403</td> <td>    0.131</td> <td>    1.070</td> <td> 0.285</td> <td>   -0.117</td> <td>    0.397</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>2951.753</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   1.972</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>1886680.784</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td> 3.859</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>125.798</td> <th>  Cond. No.          </th>  <td>    3.41</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.002\n", "Model:                            OLS   Adj. R-squared:                  0.001\n", "Method:                 Least Squares   F-statistic:                     2.934\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):             0.0533\n", "Time:                        16:44:16   Log-Likelihood:                -6506.5\n", "No. Observations:                2991   AIC:                         1.302e+04\n", "Df Residuals:                    2988   BIC:                         1.304e+04\n", "Df Model:                           2                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.6352      0.041     15.433      0.000       0.554       0.716\n", "motheduc       0.0915      0.040      2.306      0.021       0.014       0.169\n", "sinmom14       0.1403      0.131      1.070      0.285      -0.117       0.397\n", "==============================================================================\n", "Omnibus:                     2951.753   <PERSON><PERSON><PERSON>-Watson:                   1.972\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          1886680.784\n", "Skew:                           3.859   Prob(JB):                         0.00\n", "Kurtosis:                     125.798   Cond. No.                         3.41\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is\n", "# an instance of SubsetWrapper, we look at the model of the SubsetWrapper which is \n", "# and instance of the pipeline, we then look at the reg step of the pipeline which is the statsmodel wrapper and\n", "# call summary() of the wrapper (most prob there is a better API for this, but we can go with this for now :)\n", "proj_dr_cate.effect_model.summary(alpha=.05, xname=['const']+list(X_df.columns[feature_inds]))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimated Params: 0.6351902202433224, [0.091538   0.14032943]\n", "True Params: [ 0.61740685  0.14934234 -0.1       ]\n"]}], "source": ["# We can access the coefficient by looking at the coefficient attribute of the final step of the pipeline\n", "print(\"Estimated Params: {}, {}\".format(proj_dr_cate.intercept_, proj_dr_cate.coef_))\n", "# True coefficients of projection\n", "print(\"True Params: {}\".format(\n", "        LinearRegression(fit_intercept=False).fit(PolynomialFeatures(degree=1,\n", "                                                                     include_bias=True).fit_transform(X[:, feature_inds]),\n", "                                                  true_fn(X_raw)).coef_))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coverage of True Projection: 0.93\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAecAAAFpCAYAAACmt+D8AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nGdXHLxQAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also evaluate coverage and create prediction intervals using statsmodels attributes\n", "from statsmodels.sandbox.regression.predstd import wls_prediction_std\n", "res = proj_dr_cate.effect_model.model\n", "predictions = res.get_prediction(PolynomialFeatures(degree=1, include_bias=True).fit_transform(X[:, feature_inds]))\n", "frame = predictions.summary_frame(alpha=0.05)\n", "pred = frame['mean']\n", "iv_l = frame['mean_ci_lower']\n", "iv_u = frame['mean_ci_upper']\n", "\n", "# This is the true CATE functions\n", "theta_true = true_fn(X_raw)\n", "# This is the true projection of the CATE function on the subspace of linear functions of the\n", "# subset of the features used in the projection\n", "true_proj = LinearRegression().fit(X[:, feature_inds], theta_true).predict(X[:, feature_inds])\n", "\n", "# Are we covering the true projection\n", "covered = (true_proj <= iv_u) & (true_proj >= iv_l)\n", "print(\"Coverage of True Projection: {:.2f}\".format(np.mean(covered)))\n", "\n", "fig, ax = plt.subplots(figsize=(8,6))\n", "\n", "order = np.argsort(X[:, feature_inds[0]])\n", "ax.plot(X[order, feature_inds[0]], iv_u[order], 'r--')\n", "ax.plot(X[order, feature_inds[0]], iv_l[order], 'r--')\n", "ax.plot(X[order, feature_inds[0]], pred[order], 'g--.', label=\"pred\")\n", "ax.plot(X[order, feature_inds[0]], theta_true[order], 'b-', label=\"True\", alpha=.3)\n", "ax.plot(X[order, feature_inds[0]], true_proj[order], 'b-', label=\"TrueProj\", alpha=.3)\n", "ax.legend(loc='best')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# CATE via Re-Weighted DRIV: DRIV-RW"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lasso CATE"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper, StatsModelLinearRegression, ConstantModel, WeightWrapper\n", "from sklearn.dummy import DummyRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DRIV we need a model for predicting E[T*Z | X]. We use a classifier\n", "model_TZ_X = lambda: model()\n", "\n", "# We also need a model for the final regression that will fit the function theta(X)\n", "# This model now needs to accept sample weights at fit time.\n", "const_driv_model_effect = lambda: WeightWrapper(Pipeline([('bias', PolynomialFeatures(degree=1, include_bias=True)),\n", "                                                          ('reg', Selective<PERSON><PERSON>o(np.arange(1, X.shape[1]+1),\n", "                                                                                  LassoCV(cv=5, fit_intercept=False)))]))\n", "\n", "# As in OrthoDMLIV we need a perliminary estimator of the CATE.\n", "# We use a DMLIV estimator with no cross-fitting (n_splits=1)\n", "dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=False)\n", "\n", "const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                        dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                        model_TZ_X(), # model for E[T * Z | X]\n", "                        const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                        cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                        n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                        binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                        binary_treatment=False, # a flag whether to stratify cross-fitting by treatment\n", "                        opt_reweighted=True # whether to optimally re-weight samples. Valid only for flexible final model\n", "                       )"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["<dr_iv.DRIV at 0x18d22b12da0>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["const_dr_cate.fit(y, T, X, Z, store_final=True)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.599\n", "True ATE: 0.609\n"]}], "source": ["# We can average the CATE to get an ATE\n", "dr_effect = const_dr_cate.effect(X)\n", "print(\"ATE Estimate: {:.3f}\".format(np.mean(dr_effect)))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXcAAAEICAYAAACktLTqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also see how it compares to the true CATE at each target point and calculate MSE\n", "plt.title(\"DMLIV CATE as Function of {}: MSE={:.3f}\".format(X_df.columns[4], np.mean((dr_effect-true_fn(X_raw))**2)))\n", "plt.scatter(X[:, 4], dr_effect, label='est')\n", "plt.scatter(X[:, 4], true_fn(X_raw), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random Forest CATE"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "    \n", "np.random.seed(random_seed)\n", "\n", "rf_driv_model_effect = lambda: RandomForestRegressor(n_estimators=5000, max_depth=3, min_impurity_decrease=0.00001,\n", "                                                     min_samples_leaf=100, bootstrap=True)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true}, "outputs": [], "source": ["rf_dr_cate = const_dr_cate.refit_final(rf_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"collapsed": true}, "outputs": [], "source": ["rf_dr_effect = rf_dr_cate.effect(X)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.60\n", "True ATE: 0.61\n"]}], "source": ["print(\"ATE Estimate: {:.2f}\".format(np.mean(rf_dr_effect)))\n", "print(\"True ATE: {:.2f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXcAAAEICAYAAACktLTqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.title(\"DRIV CATE: MSE {:.2}\".format(np.mean((true_fn(X_raw) - rf_dr_effect)**2)))\n", "plt.scatter(X[:, 4], rf_dr_effect, label='est')\n", "plt.scatter(X[:, 4], true_fn(X_raw), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIIAAAEDCAYAAABJQa+2AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\npyqf7nslAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import shap\n", "import pandas as pd\n", "\n", "Xdf = pd.DataFrame(X, columns=X_df.columns)\n", "# explain the model's predictions using SHAP values\n", "explainer = shap.TreeExplainer(rf_dr_cate.effect_model)\n", "shap_values = explainer.shap_values(Xdf)\n", "\n", "# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(explainer.expected_value, shap_values[0,:], Xdf.iloc[0,:], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjAAAAJICAYAAAB/gN7DAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n5IapAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x684 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values, Xdf)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjgAAAI4CAYAAABndZP2AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ncv4HWVTMW2zSBQsAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x684 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values, Xdf, plot_type='bar')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}