pygraphviz 1.13
===============

We're happy to announce the release of pygraphviz 1.13!

Bug Fixes
---------

- Remove outdated pystrings.swg (`#508 <https://github.com/pygraphviz/pygraphviz/pull/508>`_).
- Fix segfault from repr attempting to access attrs on uninitialized instance (`#520 <https://github.com/pygraphviz/pygraphviz/pull/520>`_).

Maintenance
-----------

- Remove outdated pystrings.swg (`#508 <https://github.com/pygraphviz/pygraphviz/pull/508>`_).
- Update CI tests (`#509 <https://github.com/pygraphviz/pygraphviz/pull/509>`_).
- Update pre-commit repos (`#510 <https://github.com/pygraphviz/pygraphviz/pull/510>`_).
- Bump webfactory/ssh-agent from 0.8.0 to 0.9.0 (`#517 <https://github.com/pygraphviz/pygraphviz/pull/517>`_).
- Update tests for macOS Sonoma v14 (`#525 <https://github.com/pygraphviz/pygraphviz/pull/525>`_).
- Update doc requirements (`#526 <https://github.com/pygraphviz/pygraphviz/pull/526>`_).
- Update pre-commit repos (`#527 <https://github.com/pygraphviz/pygraphviz/pull/527>`_).
- Remove unnecessary pip flags (`#528 <https://github.com/pygraphviz/pygraphviz/pull/528>`_).
- Test on fedora 40 (`#529 <https://github.com/pygraphviz/pygraphviz/pull/529>`_).

Contributors
------------

5 <USER> <GROUP> to this release (alphabetically):

- `@dependabot[bot] <https://github.com/apps/dependabot>`_
- Aaron Z. (`@aaronzo <https://github.com/aaronzo>`_)
- Jarrod Millman (`@jarrodmillman <https://github.com/jarrodmillman>`_)
- Ross Barnowski (`@rossbar <https://github.com/rossbar>`_)
- William S Fulton (`@wsfulton <https://github.com/wsfulton>`_)

4 reviewers added to this release (alphabetically):

- Aaron Z. (`@aaronzo <https://github.com/aaronzo>`_)
- Dan Schult (`@dschult <https://github.com/dschult>`_)
- Jarrod Millman (`@jarrodmillman <https://github.com/jarrodmillman>`_)
- Mridul Seth (`@MridulS <https://github.com/MridulS>`_)

_These lists are automatically generated, and may not be complete or may contain duplicates._
