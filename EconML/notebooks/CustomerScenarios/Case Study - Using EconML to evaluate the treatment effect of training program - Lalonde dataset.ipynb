{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>\n", "\n", "This notebook applies EconML's `LinearDML` and `LinearDRLearner` methodologies to the data from National Supported Work (NSW) Demonstration that have been previously analyzed by <PERSON><PERSON><PERSON><PERSON>(1986) and others. We first replicate the results from <PERSON><PERSON><PERSON><PERSON>'s paper comparing estimates of the Average Treatment Effect (ATE) of a worker training program estimated using an experimental data sample and using alternative observational data sets as controls. Then we generate new estimated ATEs using **Double Machine Learning** and **Doubly Robust** estimation on these same datasets. These new causal machine learning techniques have a moderately better performance than the traditional nonexperimental evalutaion strategies from the original paper in some samples. We further improve the point estimate by estimating heterogeneous treatment effects and using reweighting techniques to make the observational control sets more closely resemble the experimental control set. Finally, we prove that our package provides reasonable effect heterogeneity estimates from observational data that are comparable with what we can learn from the experiment. \n", "\n", "These results reinforce the value of Causal ML techniques like DML and DR Learners when attempting to estimate causal effects from non-experimental data. The reweighting techniques demonstrated here are also generally useful when trying to use treatment effects estimated from one, potentially non-representative, experimental sample to forecast average treatment effects in a new population.\n", "\n", "### Summary \n", "\n", "1. [Background](#background)\n", "2. [Data](#data)\n", "3. [Compare EconML Solution with LaLonde OLS Results](#comparison)\n", "4. [Further Improve EconML Result with Reweighting Trick](#improvement)\n", "7. [Heterogeneous Treatment Effect with EconML](#hte)\n", "8. [Conclusions](#conclusion)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Background <a id=\"background\"></a>\n", "<img src=\"https://image.freepik.com/free-vector/factory-workers-robotic-arm-removing-packages-from-conveyor-line-engineer-using-computer-operating-process-vector-illustration-business-production-machine-technology-concepts_74855-9859.jpg\" width=\"400\" />\n", "\n", "The National Supported Work Demonstration (NSW) was a temporary employment program designed to help a population of disadvantaged workers in the U.S. move into long-term jobs by offering job training. The training program was targeted at populations of male and female workers who met certain conditions of disadvantage (for example, were receiving federal welfare payments or had previously been incarcerated). Participation in the program was also voluntary. Participation in this program is therefore not random, and likely to correlate with other confounding worker characteristics in difficult-to-identify ways.\n", "\n", "However, once workers opted into the program, the NSW randomly assigned them to actually receive the training or remain as a control sample to facilitate later program evaluation. This set-up creates a good environment for testing estimation techniques that are designed to capture causal effects from non-experimental data. Following <PERSON><PERSON><PERSON><PERSON>, we first estimate the effect of the training by comparing the treated and control workers within the NSW program. Because these workers were randomly assigned across groups, we're not worried about any confounders, so these estimates serve as a benchmark for the true causal effect. We then re-estimate the effects of training by comparing the trained workers in the NSW sample to workers in other U.S. datasets, the Panel Study of Income Dynamics (PSID) and the Current Population Survey (CPS). These alternative control samples should differ from the workers who were targeted for the NSW program in complex ways that mirror the usual difficulties of estimating causal effects from observational data."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# imports\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import seaborn as sns\n", "\n", "from econml.dml import LinearDML\n", "from econml.dr import LinearDRLearner\n", "from econml.cate_interpreter import SingleTreeCateInterpreter\n", "from econml.dml import CausalForestDML,NonParamDML\n", "\n", "from sklearn.linear_model import LogisticRegressionCV, LinearRegression,LogisticRegression,Lasso,LassoCV\n", "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor,GradientBoostingClassifier\n", "import lightgbm as lgb\n", "from sklearn.preprocessing import StandardScaler\n", "from tqdm import tqdm\n", "from econml.sklearn_extensions.model_selection import GridSearchCVList\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "from matplotlib.transforms import ScaledTranslation\n", "import seaborn as sns\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# helper functions\n", "# preprocess data\n", "def preprocessing(df,outcome_name,column_names):\n", "    # add indicator of zero earnings\n", "    df[\"re74_dummy\"]=(df[\"re74\"]>0)*1\n", "    df[\"re75_dummy\"]=(df[\"re75\"]>0)*1\n", "    # get growth of pre training earning\n", "    df[\"re_diff_pre\"]=df[\"re75\"]-df[\"re74\"]\n", "    # add age square\n", "    df[\"age_2\"]=df[\"age\"]**2\n", "    # select columns\n", "    df=df[column_names+[outcome_name]]\n", "    return df\n", "\n", "# linear regression wrapper\n", "def ols_reg_wrapper(reg_data, x_columns, y_column, print_summary=False):\n", "    X = reg_data[x_columns].values\n", "    X = pd.DataFrame(sm.add_constant(X,has_constant='add'),columns=[\"intercept\"]+x_columns)\n", "    Y = reg_data[y_column]\n", "    model = sm.OLS(Y, X, hasconst=True)\n", "    results = model.fit()\n", "    # save results for summary table\n", "    effect = int(round(results.params['treated']))\n", "    se = int(round(results.bse['treated']))\n", "    lb=int(round(results.conf_int().loc[\"treated\"][0]))\n", "    ub=int(round(results.conf_int().loc[\"treated\"][1]))\n", "    if print_summary:\n", "        print(results.summary())\n", "    return effect, se, lb, ub\n", "\n", "# nuisance regression model auto tunning wrapper\n", "def first_stage_reg(X, y, *, automl=True):\n", "    if automl:\n", "        model = GridSearchCVList([LassoCV(),\n", "                                  RandomForestRegressor(n_estimators=100),\n", "                                  lgb.LGBMRegressor()],\n", "                                 param_grid_list=[{},\n", "                                                  {'max_depth': [5,10,20],'min_samples_leaf': [5, 10]},\n", "                                                  {'learning_rate': [0.02,0.05,0.08], 'max_depth': [3, 5]}],\n", "                                 cv=3,\n", "                                 scoring='neg_mean_squared_error')\n", "        best_est = model.fit(X, y).best_estimator_\n", "        if isinstance(best_est, LassoCV):\n", "            return Lasso(alpha=best_est.alpha_)\n", "        return best_est\n", "    else:\n", "        model = LassoCV(cv=5).fit(X, y)\n", "        return Lasso(alpha=model.alpha_)\n", "\n", "# nuisance classification model auto tunning wrapper\n", "def first_stage_clf(X, y, *, make_regressor=False, automl=True):\n", "    if automl:\n", "        model = GridSearchCVList([LogisticRegressionCV(max_iter=1000),\n", "                                  RandomForestClassifier(n_estimators=100),\n", "                                  lgb.LGBMClassifier()],\n", "                                 param_grid_list=[{},\n", "                                                  {'max_depth': [5,10,20],\n", "                                                   'min_samples_leaf': [5, 10]},\n", "                                                  {'learning_rate':[0.01,0.05,0.1],\n", "                                                   'max_depth': [3,5]}],\n", "                                 cv=3,\n", "                                 scoring='neg_log_loss')\n", "        est = model.fit(X, y).best_estimator_\n", "        if isinstance(est,LogisticRegressionCV):\n", "            return LogisticRegression(C=est.C_[0])\n", "    else:\n", "        model = LogisticRegressionCV(cv=5, max_iter=1000).fit(X, y)\n", "        est = LogisticRegression(C=model.C_[0])\n", "    if make_regressor:\n", "        return _RegressionWrapper(est)\n", "    else:\n", "        return est\n", "\n", "# econml dml/dr wrapper\n", "def econml_homo_model_wrapper(reg_data, control_names,outcome_name,model_type,*,cols_to_scale, print_summary=False):\n", "    # get variables\n", "    X = None # no heterogeneous treatment\n", "    W = reg_data[control_names].values\n", "    # scale W\n", "    scaler = StandardScaler()\n", "    W = np.hstack([scaler.fit_transform(W[:, :cols_to_scale]).astype(np.float32), W[:, cols_to_scale:]]) \n", "    T = reg_data[\"treated\"]\n", "    y = reg_data[outcome_name]\n", "    \n", "    # select the best nuisances model out of econml estimator\n", "    model_y=first_stage_reg(W, y)\n", "    model_t=first_stage_clf(W, T)\n", "    \n", "    if model_type=='dml':\n", "        est = LinearDML(model_y=model_y,\n", "                        model_t=model_t, \n", "                        discrete_treatment=True, mc_iters=5,cv=5)\n", "    elif model_type=='dr':\n", "        est = LinearDRLearner(model_regression=model_y,\n", "                              model_propensity=model_t,\n", "                              mc_iters=5,cv=5)\n", "    else:\n", "        raise ValueError('invalid model type %s' % model_type)\n", "    try:\n", "        est.fit(y, T, X=X, W=W, inference=\"statsmodels\")\n", "    except np.linalg.LinAlgError as e:\n", "        est.fit(y, T, X=X, W=W, inference=\"statsmodels\")\n", "\n", "    # Get the final coefficient and intercept summary\n", "    if model_type==\"dml\":\n", "        inf=est.intercept__inference()\n", "    else:\n", "        inf=est.intercept__inference(T=1)\n", "    effect=int(round(inf.point_estimate))\n", "    se=int(round(inf.stderr))\n", "    lb,ub=inf.conf_int(alpha=0.05)\n", "    if print_summary:\n", "        if model_type=='dml':\n", "            print(est.summary(alpha=0.05))\n", "        else:\n", "            print(est.summary(T=1,alpha=0.05))\n", "    return effect, se, int(round(lb)), int(round(ub))\n", "\n", "# summary table helper function\n", "def get_summ_table(dfs,treat_df,*,df_names,basic_ols_controls,complete_ols_controls,econml_controls,outcome_name,cols_to_scale):\n", "    summ_dic={\"control_name\":[],\"# of obs\":[],\"earning_growth\":[],\"OLS\":[],\"OLS full controls\":[],\"DML full controls\":[],\"DR full controls\":[]}\n", "    summ_dic1={\"control_name\":[],\"method\":[],\"point_estimate\":[],\"stderr\":[],\"lower_bound\":[],\"upper_bound\":[]}\n", "    for df, name in tqdm(zip(dfs, df_names)):\n", "        summ_dic[\"control_name\"].append(name)\n", "        summ_dic[\"# of obs\"].append(df.shape[0])\n", "        summ_dic1[\"control_name\"]+=[name]*4\n", "        # get table 5 col 1\n", "        growth=int(np.round((df[outcome_name]-df[\"re75\"]).mean(),0))\n", "        summ_dic[\"earning_growth\"].append(growth)\n", "        # get table 5 col 5\n", "        summ_dic1[\"method\"].append(\"OLS\")\n", "        df_all=pd.concat([treat_df,df],axis=0,ignore_index=True)\n", "        effect,se,lb,ub=ols_reg_wrapper(df_all,basic_ols_controls,outcome_name,print_summary=False)\n", "        summ_dic[\"OLS\"].append([effect,se])\n", "        summ_dic1[\"point_estimate\"].append(effect)\n", "        summ_dic1[\"stderr\"].append(se)\n", "        summ_dic1[\"lower_bound\"].append(lb)\n", "        summ_dic1[\"upper_bound\"].append(ub)\n", "        # get table 5 col 10\n", "        summ_dic1[\"method\"].append(\"OLS full controls\")\n", "        effect,se,lb,ub=ols_reg_wrapper(df_all,complete_ols_controls,outcome_name,print_summary=False)\n", "        summ_dic[\"OLS full controls\"].append([effect,se])\n", "        summ_dic1[\"point_estimate\"].append(effect)\n", "        summ_dic1[\"stderr\"].append(se)\n", "        summ_dic1[\"lower_bound\"].append(lb)\n", "        summ_dic1[\"upper_bound\"].append(ub)\n", "        # dml\n", "        summ_dic1[\"method\"].append(\"DML full controls\")\n", "        effect,se,lb,ub=econml_homo_model_wrapper(df_all, econml_controls,outcome_name,\"dml\",cols_to_scale=cols_to_scale, print_summary=False)        \n", "        summ_dic[\"DML full controls\"].append([effect,se])\n", "        summ_dic1[\"point_estimate\"].append(effect)\n", "        summ_dic1[\"stderr\"].append(se)\n", "        summ_dic1[\"lower_bound\"].append(lb)\n", "        summ_dic1[\"upper_bound\"].append(ub)\n", "        # dr\n", "        summ_dic1[\"method\"].append(\"DR full controls\")\n", "        effect,se,lb,ub=econml_homo_model_wrapper(df_all, econml_controls,outcome_name,\"dr\",cols_to_scale=cols_to_scale, print_summary=False)        \n", "        summ_dic[\"DR full controls\"].append([effect,se])\n", "        summ_dic1[\"point_estimate\"].append(effect)\n", "        summ_dic1[\"stderr\"].append(se)\n", "        summ_dic1[\"lower_bound\"].append(lb)\n", "        summ_dic1[\"upper_bound\"].append(ub)\n", "        \n", "    return summ_dic,summ_dic1\n", "\n", "# error bar helper function\n", "def plot_errorbar(df,df_names):\n", "    fig, ax = plt.subplots(figsize=(10,6))\n", "    for ind,control_name in enumerate(df_names):\n", "        sub_df=df[df[\"control_name\"]==control_name]\n", "        method_name=sub_df[\"method\"].values\n", "        point=sub_df[\"point_estimate\"].values\n", "        yerr=np.zeros((2,point.shape[0]))\n", "        yerr[0,:]=point-sub_df[\"lower_bound\"].values\n", "        yerr[1,:]=sub_df[\"upper_bound\"].values-point\n", "        trans = ax.transData + ScaledTranslation((-10+ind*5)/72, 0, fig.dpi_scale_trans)\n", "        plt.errorbar(method_name,point,yerr,fmt=\"o\",capsize=5,elinewidth=2,label=control_name,alpha=0.7,transform=trans)\n", "    plt.axhline(y=0, color='black', linestyle='--',alpha=0.5)\n", "    plt.legend()\n", "    plt.xlabel(\"Methodology\")\n", "    plt.ylabel(\"ATE with CI\")\n", "    plt.title(\"Error bar of each method for each dataset\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data <a id=\"data\"></a>\n", "We use an experimental dataset from the NSW (which includes both a control sample of workers and treated sample). We also  consider several sets of control workers from two large cross-sectional survey sof U.S. workers, the PSID and CPS. For the PSID and CPS, we consider somewhat broader samples of workers (CPS1 and PSID1 for men, PSID1 for women) and smaller samples chosen to better match the characteristics of the NSW population (CSP3 and PSID3 for men, PSID2 for women). The data for men are provided by [<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>](https://users.nber.org/~rdehejia/data/.nswdata2.html) from their 1999 follow-up to <PERSON><PERSON><PERSON><PERSON>'s paper. The data for women are provided by [<PERSON><PERSON><PERSON> and Smith](https://www.journals.uchicago.edu/doi/10.1086/692397) from their 2017 follow-up paper. More details on both sources are available through the linked references. \n", "\n", "All data sets have the same set of variables:\n", "\n", "Feature Name|Type|Data Type|Details \n", ":--- |:---|:--- |:--- \n", "**treated** |T| Boolean| whether this person had undergone NSW job training treatment\n", "**age** |W/X| Integer| user's age\n", "**educ** |W/X| Integer| years of education this person has completed\n", "**nodegree** |W/X| <PERSON><PERSON><PERSON>| whether this person has NO high school degree\n", "**black** |W/X| Boolean| whether this person is Black\n", "**hisp** |W/X| Boolean| whether this person is Hispanic\n", "**married** |W/<PERSON>| <PERSON><PERSON><PERSON>| whether this person is married\n", "**haschild**|W|<PERSON><PERSON><PERSON>|whether this person has child for female\n", "**nchildren75**|W|Integer|number of child in 1975 for female\n", "**afdc75**|W|Boolean|AFDC status for female in 1975\n", "**re74** |W| Float| user's real earnings in 1974 (See <PERSON> & <PERSON> for a discussion)\n", "**re75** |W| Float| user's real earnings in 1975 (before treatment)\n", "**re78** |Y| Float| user's real earnings in 1978 (after treatment for males)\n", "**re79** |Y| Float| user's real earnings in 1979 (after treatment for females)\n", "\n", "Below we also show the mean and standard error of each feature from different experimental and control sets for men and women:\n", "\n", "- Summary Statistics for Men:\n", "    \n", "Feature Name|exp treatment|exp control|cps1|cps3|psid1|psid3\n", ":---         |:---         |:---       |:---|:---|:--- |:--- \n", "**age**      |24.62 (6.69) |24.45 (6.59) |33.23 (11.05)|28.03 (10.79)|34.85 (10.44)|38.26 (12.89)      \n", "**educ**     |10.38 (1.82) |10.19 (1.62) |12.03 (2.87) |10.24 (2.86)|12.12 (3.08)  |10.30 (3.18)\n", "**nodegree** |0.73 (0.44)  |0.81 (0.39)  |0.30 (0.46)  |0.60 (0.49) |0.31 (0.46)   |0.51 (0.50)\n", "**black**    |0.80 (0.40)  |0.80 (0.40)  |0.07 (0.26)  |0.20 (0.40) |0.25 (0.43)   |0.45 (0.50)\n", "**hisp**     |0.09 (0.29)  |0.11 (0.32)  |0.07 (0.26)  |0.14 (0.35) |0.03 (0.18)   |0.12 (0.32)\n", "**married**  |0.17 (0.37)  |0.16 (0.36)  |0.71 (0.45)  |0.51 (0.50) |0.87 (0.34)   |0.70 (0.46)\n", "**re74**     |3571 (5773)  |3672 (6522)  |14017 (9570) |5619 (6789) |19429 (13407) |5567 (7255)\n", "**re75**     |3066 (4875)  |3026 (5201)  |13651 (9270) |2466 (3292) |19063 (13597) |2611 (5572)\n", "**re78**     |5976 (6924)  |5090 (5718)  |14847 (9647) |6984 (7294) |21553 (15555) |5279 (7762)\n", "**# of obs** |297          |425          |15992        |429         |2490          |128\n", "\n", "- Summary Statistics for Women:\n", "\n", "Feature Name|exp treatment|exp control|psid1|psid2\n", ":---        |:---         |:---       |:---|:---\n", "**age**         |33.76 (7.39) |33.74 (7.15) |37.07 (10.57)|34.54 (9.34)\n", "**educ**        |10.29 (1.93) |10.26 (2.03) |11.30 (2.77) |10.49 (2.13)\n", "**nodegree**    |0.70 (0.46)  |0.68 (0.47)  |0.45 (0.50)  |0.59 (0.49)\n", "**black**       |0.84 (0.37)  |0.82 (0.39)  |0.65 (0.48)  |0.86 (0.35)\n", "**hisp**        |0.11 (0.32)  |0.13 (0.33)  |0.02 (0.12)  |0.02 (0.15)\n", "**married**     |0.02 (0.15)  |0.04 (0.19)  |0.02 (0.14)  |0.01 (0.10)\n", "**haschild**    |0.97 (0.16)  |0.98 (0.14)  |0.67 (0.47)  |0.97 (0.16)\n", "**nchildren75** |2.18 (1.29)  |2.23 (1.34)  |1.71 (1.78)  |2.97 (1.79) \n", "**afdc75**      |1.00 (0.00)  |1.00 (0.00)  |0.28 (0.45)  |1.00 (0.00)\n", "**re74**        |913 (2149)   |962 (2376)   |7509 (7296)  |2726 (4414)\n", "**re75**        |861 (2004)   |879 (2195)   |7510 (7541)  |2211 (3568)\n", "**re79**        |4665 (5554)  |3833 (5039)  |8827 (8762)  |4623 (6921)\n", "**# of obs**    |601          |585          |648          |182\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Data\n", "## female\n", "### read in and slice data\n", "female_data = pd.read_csv('https://msalicedatapublic.z5.web.core.windows.net/datasets/Lalonde/calonico_smith_all.csv')\n", "female_data[\"haschild\"]=(female_data[\"nchildren75\"]>0)*1\n", "female_data = female_data[pd.notnull(female_data.re75) & pd.notnull(female_data.re79)]\n", "female_treatment = female_data[female_data.treated==1.].copy()\n", "female_control = female_data[female_data.treated==0.].copy()\n", "female_psid1 = female_data[female_data['psid1']==1].copy()\n", "female_psid2 = female_data[female_data['psid2']==1].copy()\n", "### some preprocessing\n", "female_psid1.loc[:, 'treated'] = 0\n", "female_psid2.loc[:, 'treated'] = 0\n", "\n", "## male\n", "### read in and slice data\n", "male_data = pd.read_csv('https://msalicedatapublic.z5.web.core.windows.net/datasets/Lalonde/smith_todd.csv')\n", "male_treatment = male_data[male_data.treated==1.].copy()\n", "male_control = male_data[male_data.treated==0.].copy()\n", "male_cps1 = pd.read_csv('https://msalicedatapublic.z5.web.core.windows.net/datasets/Lalonde/cps_controls.csv')\n", "male_psid1 = pd.read_csv('https://msalicedatapublic.z5.web.core.windows.net/datasets/Lalonde/psid_controls.csv')\n", "male_cps3 = pd.read_csv('https://msalicedatapublic.z5.web.core.windows.net/datasets/Lalonde/cps_controls3.csv')\n", "male_psid3 = pd.read_csv('https://msalicedatapublic.z5.web.core.windows.net/datasets/Lalonde/psid_controls3.csv')\n", "### some preprocessing\n", "for df in [male_psid1,male_psid3,male_cps1,male_cps3]:\n", "    df.rename(columns={'treat':'treated', 'education':'educ', 'hispanic':'hisp'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# outcome\n", "outcome_name_male=\"re78\"\n", "outcome_name_female=\"re79\"\n", "# ols controls\n", "basic_ols_columns = ['treated', 'age', 'age_2', 'educ', 'nodegree', 'black', 'hisp']\n", "complete_ols_columns_male = basic_ols_columns+[\"married\",\"re75\",\"re75_dummy\",\"re74\",\"re74_dummy\",\"re_diff_pre\"]\n", "complete_ols_columns_female=basic_ols_columns+[\"married\",\"re75\",\"re75_dummy\",\"re74\",\"re74_dummy\",\"re_diff_pre\",\"afdc75\",\"nchildren75\",\"haschild\"]\n", "# econml controls (exclude treatment)\n", "econml_controls_male= ['age', 'age_2', 'educ', 're75','re74','re_diff_pre','nodegree', 'black', 'hisp', 'married','re75_dummy','re74_dummy']\n", "econml_controls_female= ['age', 'age_2', 'educ','nchildren75', 're75','re74','re_diff_pre','nodegree', 'black', 'hisp', 'married','re75_dummy','re74_dummy','afdc75','haschild']"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# preprocessing data\n", "male_control, male_treatment, male_psid1, male_psid3, male_cps1, male_cps3 = [preprocessing(df,outcome_name_male,complete_ols_columns_male) for df in (male_control, male_treatment, male_psid1, male_psid3, male_cps1, male_cps3)]\n", "female_control, female_treatment, female_psid1, female_psid2 =[preprocessing(df,outcome_name_female,complete_ols_columns_female) for df in (female_control, female_treatment, female_psid1, female_psid2)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compare EconML Solution with LaLonde OLS Results <a id=\"comparison\"></a>\n", "We first repilcate key results from Table 5 (for men) and Table 4 (for women) from [<PERSON><PERSON><PERSON> (1986)](http://public.econ.duke.edu/~hf14/teaching/povertydisc/readings/lalonde1986.pdf). These tables calculate the benchmark ATE using only the experimental sample and attempt to match that benchmark by estimating treatment effects using OLS and various alternative observational untreated samples, first with a basic set of control features and then with an expanded set. We then train both `LinearDML` and `LinearDRLearner` models on each dataset, using the expanded control set."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Treatment Effect Comparison for Men"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["5it [04:27, 53.44s/it]\n"]}], "source": ["#male\n", "control_dfs_male=[male_control,male_psid1,male_psid3,male_cps1,male_cps3]\n", "df_names_male=[\"exp controls\",\"psid1\",\"psid3\",\"cps1\",\"cps3\"]\n", "summ_male,summplot_male=get_summ_table(control_dfs_male,male_treatment,df_names=df_names_male,\n", "                         basic_ols_controls=basic_ols_columns,\n", "                         complete_ols_controls=complete_ols_columns_male,\n", "                         econml_controls=econml_controls_male,\n", "                         outcome_name=outcome_name_male,cols_to_scale=6\n", "                         )"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numbers under each method represent [point estimate, standard error]. \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>control_name</th>\n", "      <th># of obs</th>\n", "      <th>earning_growth</th>\n", "      <th>OLS</th>\n", "      <th>OLS full controls</th>\n", "      <th>DML full controls</th>\n", "      <th>DR full controls</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>exp controls</td>\n", "      <td>425</td>\n", "      <td>2063</td>\n", "      <td>[798, 472]</td>\n", "      <td>[817, 469]</td>\n", "      <td>[851, 487]</td>\n", "      <td>[868, 486]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>psid1</td>\n", "      <td>2490</td>\n", "      <td>2491</td>\n", "      <td>[-8067, 990]</td>\n", "      <td>[-1827, 825]</td>\n", "      <td>[-1966, 697]</td>\n", "      <td>[-1576, 401]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>psid3</td>\n", "      <td>128</td>\n", "      <td>2669</td>\n", "      <td>[-509, 967]</td>\n", "      <td>[-239, 1029]</td>\n", "      <td>[133, 976]</td>\n", "      <td>[92, 669]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>cps1</td>\n", "      <td>15992</td>\n", "      <td>1196</td>\n", "      <td>[-4416, 577]</td>\n", "      <td>[-867, 445]</td>\n", "      <td>[-784, 542]</td>\n", "      <td>[-2540, 1022]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>cps3</td>\n", "      <td>429</td>\n", "      <td>4518</td>\n", "      <td>[-1, 681]</td>\n", "      <td>[210, 683]</td>\n", "      <td>[480, 609]</td>\n", "      <td>[310, 448]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   control_name  # of obs  earning_growth           OLS OLS full controls  \\\n", "0  exp controls       425            2063    [798, 472]        [817, 469]   \n", "1         psid1      2490            2491  [-8067, 990]      [-1827, 825]   \n", "2         psid3       128            2669   [-509, 967]      [-239, 1029]   \n", "3          cps1     15992            1196  [-4416, 577]       [-867, 445]   \n", "4          cps3       429            4518     [-1, 681]        [210, 683]   \n", "\n", "  DML full controls DR full controls  \n", "0        [851, 487]       [868, 486]  \n", "1      [-1966, 697]     [-1576, 401]  \n", "2        [133, 976]        [92, 669]  \n", "3       [-784, 542]    [-2540, 1022]  \n", "4        [480, 609]       [310, 448]  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["summ_male_df=pd.DataFrame(summ_male)\n", "print(\"Numbers under each method represent [point estimate, standard error]. \")\n", "summ_male_df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["summplot_male_df=pd.DataFrame(summplot_male)\n", "plot_errorbar(summplot_male_df,df_names_male)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Treatment Effect Comparison for Women"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["3it [01:25, 28.42s/it]\n"]}], "source": ["# female\n", "control_dfs_female=[female_control,female_psid1,female_psid2]\n", "df_names_female=[\"exp controls\",\"psid1\",\"psid2\"]\n", "summ_female,summplot_female=get_summ_table(control_dfs_female,female_treatment,df_names=df_names_female,\n", "                         basic_ols_controls=basic_ols_columns,\n", "                         complete_ols_controls=complete_ols_columns_female,\n", "                         econml_controls=econml_controls_female,\n", "                         outcome_name=outcome_name_female,cols_to_scale=7\n", "                         )"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numbers under each method represent [point estimate, standard error]. \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>control_name</th>\n", "      <th># of obs</th>\n", "      <th>earning_growth</th>\n", "      <th>OLS</th>\n", "      <th>OLS full controls</th>\n", "      <th>DML full controls</th>\n", "      <th>DR full controls</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>exp controls</td>\n", "      <td>585</td>\n", "      <td>2954</td>\n", "      <td>[858, 307]</td>\n", "      <td>[880, 307]</td>\n", "      <td>[830, 307]</td>\n", "      <td>[834, 307]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>psid1</td>\n", "      <td>648</td>\n", "      <td>1316</td>\n", "      <td>[-2730, 441]</td>\n", "      <td>[1068, 529]</td>\n", "      <td>[1012, 682]</td>\n", "      <td>[762, 610]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>psid2</td>\n", "      <td>182</td>\n", "      <td>2412</td>\n", "      <td>[-90, 514]</td>\n", "      <td>[510, 560]</td>\n", "      <td>[790, 711]</td>\n", "      <td>[777, 848]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   control_name  # of obs  earning_growth           OLS OLS full controls  \\\n", "0  exp controls       585            2954    [858, 307]        [880, 307]   \n", "1         psid1       648            1316  [-2730, 441]       [1068, 529]   \n", "2         psid2       182            2412    [-90, 514]        [510, 560]   \n", "\n", "  DML full controls DR full controls  \n", "0        [830, 307]       [834, 307]  \n", "1       [1012, 682]       [762, 610]  \n", "2        [790, 711]       [777, 848]  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["summ_female_df=pd.DataFrame(summ_female)\n", "print(\"Numbers under each method represent [point estimate, standard error]. \")\n", "summ_female_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["summplot_female_df=pd.DataFrame(summplot_female)\n", "plot_errorbar(summplot_female_df,df_names_female)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Improved Performance with Sample Reweighting <a id=\"improvement\"></a>\n", "\n", "An important difficulty in the above exercise is that all of the alternative control samples are somewhat different than the experimental NSW sample (for example, workers in the experimental sample are younger on average and far more likely to be Black or Hispanic). Even with a perfect tool for estimating causal effects from observational data, estimating the *average* treatment effect across these separate samples is unlikely to yield identical results. \n", "\n", "We now address this limitation by reestimating our `LinearDML` model after reweighting the observational samples to better align with the experimental sample. For this exercise we focus on the CPS3 sample for men, which was most similar to the experimental sample to begin with. We estimate a classification model between the full NSW sample and observational sample (which combines treated workers from NSW with the CPS3 control sample) to estimate the likelihood that a worker with a given set of features originated from each sample. We then reweight the CPS3 sample to give higher weight to workers who are more likely to have come from the experimental set: $$weight=P(obs\\_from\\_experimental\\_distribution)/(1-P(obs\\_from\\_experimental\\_distribution)) $$\n", "Because the data samples in this exercise are so small, the estimated treatment effects from both the weighted and unweighted models are unstable. We therefore run this analysis 100 times and learn the distribution of ATE from each approach and their significance level (p value). "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# experimental data male\n", "df=pd.concat([male_treatment,male_control])\n", "# cps3 data male\n", "df_cps=pd.concat([male_treatment,male_cps3])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df[\"label\"]=1\n", "df_cps[\"label\"]=0\n", "male_cls=pd.concat([df,df_cps]).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["X=male_cls[econml_controls_male].values\n", "y=male_cls[\"label\"].values\n", "# scale numeric features\n", "cols_to_scale=6\n", "scaler = StandardScaler()\n", "X=np.hstack([scaler.fit_transform(X[:, :cols_to_scale]).astype(np.float32), X[:, cols_to_scale:]]) "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# train a classification model to learn the weight\n", "cls=first_stage_clf(X,y)\n", "cls.fit(X,y)\n", "male_cls[\"prob\"]=cls.predict_proba(X)[:,1]/cls.predict_proba(X)[:,0]\n", "weight=male_cls[male_cls[\"label\"]==0][\"prob\"].values"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["X = None\n", "W = df_cps[econml_controls_male].values\n", "# scale W\n", "W = np.hstack([scaler.fit_transform(W[:, :cols_to_scale]).astype(np.float32), W[:, cols_to_scale:]]) \n", "T = df_cps[\"treated\"]\n", "y = df_cps[outcome_name_male]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["model_y=first_stage_reg(W, y)\n", "model_t=first_stage_clf(W, T)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# train dml with sample weight 100 times\n", "p_value_with_weight=[]\n", "point_estimate_with_weight=[]\n", "for _ in range(100):    \n", "    est=LinearDML(model_t=model_t,model_y=model_y,discrete_treatment=True,mc_iters=10,cv=3)\n", "    est.fit(y, T, X=None, W=W, sample_weight=weight,inference=\"statsmodels\")\n", "    point_estimate_with_weight.append(est.intercept_)\n", "    p_value_with_weight.append(est.effect_inference().pvalue()[0])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# train dml without sample weight 100 times\n", "p_value_without_weight=[]\n", "point_estimate_without_weight=[]\n", "for _ in range(100):    \n", "    est1=LinearDML(model_t=model_t,model_y=model_y,discrete_treatment=True,mc_iters=10,cv=3)\n", "    est1.fit(y, T, X=None,W=W,inference=\"statsmodels\")\n", "    point_estimate_without_weight.append(est1.intercept_)\n", "    p_value_without_weight.append(est1.effect_inference().pvalue()[0])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'frequency')"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ranges=(min(point_estimate_with_weight+point_estimate_without_weight),max(point_estimate_with_weight+point_estimate_without_weight))\n", "plt.hist(point_estimate_with_weight,label=\"with sample weight\",bins=20,range=ranges,alpha=0.8)\n", "plt.hist(point_estimate_without_weight,label=\"without sample weight\",bins=20,range=ranges,alpha=0.8)\n", "plt.legend()\n", "plt.title(\"Comparison of point estimates between LinearDML with or without reweighting trick (n=100)\")\n", "plt.xlabel(\"point estimate\")\n", "plt.ylabel(\"frequency\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'frequency')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ranges=(min(p_value_with_weight+p_value_without_weight),max(p_value_with_weight+p_value_without_weight))\n", "plt.hist(p_value_with_weight,label=\"with sample weight\",range=ranges,alpha=0.8)\n", "plt.hist(p_value_without_weight,label=\"without sample weight\",range=ranges,alpha=0.8)\n", "plt.legend()\n", "plt.title(\"Comparison of p-value between LinearDML with or without reweighting trick (n=100)\")\n", "plt.xlabel(\"p value\")\n", "plt.ylabel(\"frequency\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Overall, reweighting the sample substantially improves the ability of the DML model to approximate the experimental average treatment effect using observational data. Recall the benchmark ATE for men is around 817. The average ATE estimated by the unweighted DML model is 471 , while the average ATE from the weighted model is 584. However, adding sample weights increases the variance of the estimate. As shown in the second plot, the estimated treatment effects from the weighted model are somewhat less likely to be significantly different from zero."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Heterogeneous Treatment Effect with EconML <a id=\"hte\"></a>\n", "Finally, we want to learn whether we could get heterogeneous treatment effect insight using EconML, which could better tell us what kind of people are more or less responsive to this training program. We will start with using the unbiased experimental dataset, and then we are also interested to learn whether the observational dataset could recover the same findings from the \n", "experiment.\n", "\n", "We train a `CausalForestDML` to learn non-parametric heterogeneous treatment effect by fitting a Casual Forest as the final stage model. EconML also supports interpretability tools such as `SingleTreeCateInterpreter` to further segment the users with different responsiveness to the treatment.\n", "\n", "## Experimental Data"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# experimental data male\n", "X = None\n", "W = df[econml_controls_male].values\n", "# scale W\n", "cols_to_scale=6\n", "scaler = StandardScaler()\n", "W = np.hstack([scaler.fit_transform(W[:, :cols_to_scale]).astype(np.float32), W[:, cols_to_scale:]]) \n", "T = df[\"treated\"]\n", "y = df[outcome_name_male]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["model_y=first_stage_reg(W, y)\n", "model_t=first_stage_clf(W, T)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.causal_forest.CausalForestDML at 0x26eebafb0c8>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["est=CausalForestDML(model_y=model_y,model_t=model_t,discrete_treatment=True, mc_iters=5,cv=5)\n", "est.fit(y,T,X=W,W=None)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\n", "# We interpret the CATE models behavior on the distribution of heterogeneity features\n", "intrp.interpret(est, W)\n", "# plot\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=econml_controls_male, fontsize=12)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Observational Control - CPS3"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["X = None\n", "W = df_cps[econml_controls_male].values\n", "# scale W\n", "cols_to_scale=6\n", "scaler = StandardScaler()\n", "W = np.hstack([scaler.fit_transform(W[:, :cols_to_scale]).astype(np.float32), W[:, cols_to_scale:]]) \n", "T = df_cps[\"treated\"]\n", "y = df_cps[outcome_name_male]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["model_y=first_stage_reg(W, y)\n", "model_t=first_stage_clf(W, T)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dml.causal_forest.CausalForestDML at 0x26eecb6fd08>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["est=CausalForestDML(model_y=model_y,model_t=model_t,discrete_treatment=True, mc_iters=5,cv=5)\n", "est.fit(y,T,X=W,W=None)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\n", "# We interpret the CATE models behavior on the distribution of heterogeneity features\n", "intrp.interpret(est, W)\n", "# plot\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=econml_controls_male, fontsize=12)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the experimental tree plot we see this training program has higher positive effects for older workers with lower income before the training. Workers with relatively higher income in the pre-period experience a small and imprecise effect of training. While the exact splits are somewhat different in the observational sample, the main message is the same: the training is most effective for workers with lower pre-period earnings and, within that set, for older workers. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Conclusion <a id=\"conclusion\"></a>\n", "\n", "In this notebook, we have demonstrated the power of using EconML to:\n", "\n", "* Outperform the traditional OLS method on estimating ATE under some settings.\n", "* Substantially improve performance when reweighting samples with analytical confidence intervals.\n", "* Learn treatment effect heterogeneity and recover the same insight from using observational dataset.\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 4}