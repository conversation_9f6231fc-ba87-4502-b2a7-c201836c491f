Metadata-Version: 2.1
Name: pygraphviz
Version: 1.14
Summary: Python interface to Graphviz
Author: <PERSON><PERSON>
Author-email: <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
License: BSD-3-Clause
Project-URL: Homepage, https://pygraphviz.github.io
Project-URL: Bug Tracker, https://github.com/pygraphviz/pygraphviz/issues/
Project-URL: Documentation, https://pygraphviz.github.io/documentation/stable/
Project-URL: Source Code, https://github.com/pygraphviz/pygraphviz/
Project-URL: Download, https://pypi.python.org/pypi/pygraphviz
Keywords: Networks,Graph Visualization,network,graph,graph drawing
Platform: Linux
Platform: Mac OSX
Platform: Windows
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: C
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Scientific/Engineering :: Visualization
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE

PyGraphviz
==========

.. image:: https://github.com/pygraphviz/pygraphviz/workflows/test/badge.svg?branch=main
  :target: https://github.com/pygraphviz/pygraphviz/actions?query=workflow%3Atest+branch%3Amain

.. image:: https://codecov.io/gh/pygraphviz/pygraphviz/branch/main/graph/badge.svg
   :target: https://app.codecov.io/gh/pygraphviz/pygraphviz/branch/main


PyGraphviz is a Python interface to the Graphviz graph layout and
visualization package.
With PyGraphviz you can create, edit, read, write, and draw graphs using
Python to access the Graphviz graph data structure and layout algorithms.
PyGraphviz provides a similar programming interface to NetworkX
(https://networkx.org).

- **Website (including documentation):** https://pygraphviz.github.io
- **Mailing list:** https://groups.google.com/forum/#!forum/pygraphviz-discuss
- **Source:** https://github.com/pygraphviz/pygraphviz
- **Bug reports:** https://github.com/pygraphviz/pygraphviz/issues

Simple example
--------------

.. code:: pycon

    >>> import pygraphviz as pgv
    >>> G = pgv.AGraph()
    >>> G.add_node("a")
    >>> G.add_edge("b", "c")
    >>> print(G)
    strict graph "" {
            a;
            b -- c;
    }

Install
-------

PyGraphviz requires Graphviz.
Please see `INSTALL.txt` for details.

License
-------

Released under the 3-Clause BSD license (see ``LICENSE``)::

  Copyright (C) 2006-2024 PyGraphviz Developers
  Aric Hagberg <<EMAIL>>
  Dan Schult <<EMAIL>>
  Manos Renieris
