# DoWhy 因果推断环境

这个项目提供了一个适用于DoWhy和相关因果推断库的Python环境，用于执行因果分析和推断任务。

## 环境配置

本环境使用Python 3.12创建，包含以下主要库：

- **DoWhy**: 因果推断的端到端库
- **causal-learn**: 因果发现和学习工具
- **EconML**: 经济学和因果推断的机器学习工具
- **NetworkX**: 网络和图形处理库
- **Pandas, NumPy, SciPy**: 数据处理和科学计算库
- **Matplotlib**: 数据可视化库

## 目录结构

```
causality_env/
├── venv/                     # Python 3.12虚拟环境
├── causal_graph_examples.py  # 因果图创建和分析示例
├── dowhy_complete_workflow.py # 完整的DoWhy工作流示例
├── run_examples.sh           # 运行示例的脚本
└── README.md                 # 本文件
```

## 示例文件说明

1. **causal_graph_examples.py**: 演示了如何创建和操作因果图
   - 使用NetworkX创建因果图
   - 从GML字符串创建图
   - 从DOT字符串创建图
   - 通过变量角色创建图
   - 测试d-分离
   - 验证后门调整集
   - 执行do-运算

2. **dowhy_complete_workflow.py**: 展示了DoWhy的完整工作流
   - 模型(Model): 使用数据和图定义因果模型
   - 识别(Identify): 识别可估计的因果效应
   - 估计(Estimate): 使用多种方法估计因果效应
   - 验证(Refute): 应用不同的鲁棒性检验验证估计结果

## 使用方法

1. 激活虚拟环境：
   ```bash
   source venv/bin/activate
   ```

2. 运行示例脚本：
   ```bash
   # 运行因果图示例
   python causal_graph_examples.py
   
   # 运行完整因果推断工作流
   python dowhy_complete_workflow.py
   ```

   或者使用提供的脚本一次运行所有示例：
   ```bash
   bash run_examples.sh
   ```

3. 查看生成的可视化结果：
   - `graph_before_after_do_X.png`: 原始图和应用do-运算后的图
   - `obs_data.png`: 观测数据的可视化图表
   - `effect_comparison.png`: 不同估计方法的比较图

## DoWhy的四步流程

DoWhy采用了一种结构化的四步流程进行因果推断：

1. **模型(Model)**: 明确声明因果关系假设，通常通过因果图表示
2. **识别(Identify)**: 确定从可用数据中是否可以估计所需的因果量
3. **估计(Estimate)**: 使用统计方法从数据中估计因果效应
4. **验证(Refute)**: 通过各种鲁棒性测试检验估计结果

这种流程的优势在于它将因果假设与统计估计明确分开，使分析过程更加透明和可靠。

## 注意事项

- 本环境与Python 3.13不兼容，因为DoWhy和EconML目前最高仅支持Python 3.12
- 如果您希望使用更高级的图形可视化功能，建议安装pygraphviz（需要单独配置） 