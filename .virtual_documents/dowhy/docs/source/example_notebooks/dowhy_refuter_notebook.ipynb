





from dowhy.datasets import linear_dataset
from dowhy import CausalModel
import econml

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'WARN',
        },
    }
}

logging.config.dictConfig(DEFAULT_LOGGING)
# Disabling warnings output
import warnings
from sklearn.exceptions import DataConversionWarning
warnings.filterwarnings(action='ignore', category=DataConversionWarning)





inspect_datasets = True
inspect_models = True
inspect_identified_estimands = True
inspect_estimates = True
inspect_refutations = True





from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

estimator_list = ["backdoor.propensity_score_matching", "backdoor.propensity_score_weighting", "backdoor.econml.dml.DML"]
method_params= [ None, None, {"init_params":{'model_y':GradientBoostingRegressor(),
                                                              'model_t': GradientBoostingRegressor(),
                                                              "model_final":LassoCV(fit_intercept=False), 
                                                              'featurizer':PolynomialFeatures(degree=1, include_bias=False)},
                                               "fit_params":{}} ]





refuter_list = ["bootstrap_refuter", "data_subset_refuter"]





# Parameters for creating the Dataset
TREATMENT_IS_BINARY = True
BETA = 10
NUM_SAMPLES = 5000
NUM_CONFOUNDERS = 5
NUM_INSTRUMENTS = 3
NUM_EFFECT_MODIFIERS = 2

# Creating a Linear Dataset with the given parameters
linear_data = linear_dataset(
            beta = BETA,
            num_common_causes = NUM_CONFOUNDERS,
            num_instruments = NUM_INSTRUMENTS,
            num_effect_modifiers = NUM_EFFECT_MODIFIERS,
            num_samples = NUM_SAMPLES,
            treatment_is_binary = True
        )
# Other datasets come here 


# Append them together in an array
datasets = [linear_data]






dataset_num = 1
if inspect_datasets is True:
    for data in datasets:
        print("####### Dataset {}###########################################################################################".format(dataset_num))
        print(data['df'].head())
        print("#############################################################################################################")
        dataset_num += 1





models = []
for data in datasets:
    model = CausalModel(
                data = data['df'],
                treatment = data['treatment_name'],
                outcome = data['outcome_name'],
                graph = data['gml_graph']
            )
    models.append(model)





model.view_model()





identified_estimands = []
for model in models:
    identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
    identified_estimands.append(identified_estimand)





estimand_count = 1
for estimand in identified_estimands:
    print("####### Identified Estimand {}#####################################################################################".format(estimand_count))
    print(estimand)
    print("###################################################################################################################")
    estimand_count += 1





estimate_list = []
for i in range(len(identified_estimands)):
    for j in range(len(estimator_list)):
        estimate = model.estimate_effect(
                        identified_estimands[i],
                        method_name=estimator_list[j],
                        method_params=method_params[j]
                  )
        estimate_list.append(estimate)





estimand_count = 1
if inspect_estimates is True:
    for estimand in estimate_list:
        print("####### Estimand {}#######################################################################################".format(estimand_count))
        print("*** Class Name ***")
        print()
        print(estimand.params['estimator_class'])
        print()
        print(estimand)
        print("########################################################################################################")
        print()
        estimand_count += 1
    





refutation_list = []
for estimand in identified_estimands:
    for estimate in estimate_list: 
        for refuter in refuter_list:
            ref = model.refute_estimate(estimand, estimate,method_name=refuter)
            refutation_list.append(ref)





refuter_count = 1
if inspect_refutations is True:
    for refutation in refutation_list:
        print("####### Refutation {}#######################################################################################".format(refuter_count))
        print("*** Class Name ***")
        print()
        print(refutation.refutation_type)
        print()
        print(refutation)
        print("########################################################################################################")
        print()
        refuter_count += 1



