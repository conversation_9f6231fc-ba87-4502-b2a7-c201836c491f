[project]
name = "econml"
requires-python = ">=3.7"
authors = [{ name = "PyWhy contributors" }]
description = "This package contains several methods for calculating Conditional Average Treatment Effects"
readme = "README.md"
keywords = ["treatment-effect"]
license = { text = "MIT" }
dynamic = ["version"]

classifiers = [
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "License :: OSI Approved :: MIT License",
    "Operating System :: MacOS",
    "Operating System :: Microsoft :: Windows",
    "Operating System :: POSIX :: Linux"
]
dependencies = [
    "numpy",
    "scipy > 1.4.0",
    "scikit-learn > 0.22.0, < 1.3",
    "sparse",
    "joblib >= 0.13.0; python_version > '3.7' or platform_system != 'Windows'",
    # upper limit due to https://github.com/joblib/loky/issues/411
    "joblib >= 0.13.0, < 1.3.0; python_version <= '3.7' and platform_system == 'Windows'",
    "statsmodels >= 0.10",
    "pandas",
    "shap >= 0.38.1, < 0.42.0",
    "lightgbm"
]

[project.urls]
"Homepage" = "https://github.com/py-why/EconML"
"Bug Tracker" = "https://github.com/py-why/EconML/Issues"
"Source Code" = "https://github.com/py-why/EconML" 
"Documentation" = "https://econml.azurewebsites.net/"

[project.optional-dependencies]
automl = [
    # Disabled due to incompatibility with scikit-learn
    # azureml-sdk[explain,automl] == 1.0.83
    "azure-cli"
]
tf = [
    # This extra is not currently compatible with python 3.9 or above because of tensorflow breaking changes
    "keras < 2.4; python_version < '3.9'",
    "tensorflow > 1.10, < 2.3; python_version < '3.9'",
    # Version capped due to tensorflow incompatibility
    "protobuf < 4",
    # Version capped due to tensorflow incompatibility
    "numpy < 1.24"
]
plt = [
    "graphviz",
    # Version capped due to shap incompatibility
    "matplotlib < 3.6.0"
]
dowhy = [
    "dowhy < 0.11"
]
all = [
    # Disabled due to incompatibility with scikit-learn
    # azureml-sdk[explain,automl] == 1.0.83
    "azure-cli",
    # This extra is not currently compatible with python 3.9 or above because of tensorflow breaking changes
    "keras < 2.4; python_version < '3.9'",
    "tensorflow > 1.10, < 2.3; python_version < '3.9'",
    # Version capped due to tensorflow incompatibility
    "protobuf < 4",
    # Version capped due to tensorflow incompatibility
    "numpy < 1.24",
    "graphviz",
    # Version capped due to shap incompatibility
    "matplotlib < 3.6.0",
    "dowhy < 0.11"
]

[build-system]
requires = [
    "setuptools",
    "wheel",
    "oldest-supported-numpy",
    "scipy",
    "cython<3" # Our native code has several incompatibilities with Cython 3
]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
addopts = "--junitxml=junit/test-results.xml -n auto --strict-markers --cov-config=setup.cfg --cov=econml"
markers = [    
    "slow",
    "notebook",
    "automl",
    "dml",
    "serial",
    "cate_api",
    "treatment_featurization"
]