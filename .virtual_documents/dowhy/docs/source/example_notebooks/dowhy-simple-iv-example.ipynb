


%load_ext autoreload
%autoreload 2


# 导入 NumPy 库，用于数值计算
# 导入 Pandas 库，用于数据处理和分析
import numpy as np
import pandas as pd  
# 导入 Patsy 库，用于描述统计模型（特别是线性模型）和生成设计矩阵
import patsy as ps  
# 从 Statsmodels 库中导入 IV2SLS（工具变量两阶段最小二乘法）模型
from statsmodels.sandbox.regression.gmm import IV2SLS  
# 导入 os 和 sys 库，用于操作系统交互和 Python 运行时环境
import os, sys  
# 从 DoWhy 库中导入 CausalModel 类，用于因果模型建立和分析
from dowhy import CausalModel 





n_points = 1000
education_abilty = 1
education_voucher = 2
income_abilty = 2
income_education = 4


# confounder
ability = np.random.normal(0, 3, size=n_points)

# instrument
voucher = np.random.normal(2, 1, size=n_points) 

# treatment
education = np.random.normal(5, 1, size=n_points) + education_abilty * ability +\
            education_voucher * voucher

# outcome
income = np.random.normal(10, 3, size=n_points) +\
         income_abilty * ability + income_education * education

# build dataset (exclude confounder `ability` which we assume to be unobserved)
data = np.stack([education, income, voucher]).T
df = pd.DataFrame(data, columns = ['education', 'income', 'voucher'])





#Step 1: Model
model=CausalModel(
        data = df,
        treatment='education',
        outcome='income',
        common_causes=['U'],
        instruments=['voucher']
        )
model.view_model()



# Step 2: Identify
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)





# Step 3: Estimate
#Choose the second estimand: using IV
estimate = model.estimate_effect(identified_estimand,
        method_name="iv.instrumental_variable", test_significance=True)

print(estimate)





# Step 4: Refute
ref = model.refute_estimate(identified_estimand, estimate, method_name="placebo_treatment_refuter", placebo_type="permute") # only permute placebo_type works with IV estimate
print(ref)








income_vec, endog = ps.dmatrices("income ~ education", data=df)
exog = ps.dmatrix("voucher", data=df)

m = IV2SLS(income_vec, endog, exog).fit()
m.summary()



