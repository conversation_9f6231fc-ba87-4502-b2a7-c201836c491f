Credits
=======

Thanks to <PERSON> and the AT&T Graphviz team for creating
and maintaining the Graphviz graph layout and drawing packages

Thanks to <PERSON><PERSON> for the original idea.

Thanks to the following people who have made contributions:

 - <PERSON> helped clean up the packaging for Debian and find bugs.

 - <PERSON> developed the threads code to provide nonblocking,
   multiplatform IO.

 - <PERSON> suggested fixes and tested the attribute handling.

 - <PERSON> debugged the setup and installation for OSX.

 - <PERSON> reported attribute bugs and contributed the
   code to run Graphviz "tred" and friends.

 - <PERSON> contributed unicode handling design and code.
