name: Build Docs (CI)

on:
  pull_request:
    branches: [main]
    paths:
      - ".github/workflows/docs-ci.yml"
      - "dowhy/**"
      - "docs/**"
      - "pyproject.toml"
      - "poetry.lock"

jobs:
  docs:
    runs-on: ubuntu-latest
    container:
      image: "ghcr.io/${{ github.repository_owner }}/dowhy-docs-generation:latest"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Python Dependencies
        run: poetry install -E plotting -E pydot -E pygraphviz -E econml --with docs

      - run: git config --global --add safe.directory /__w/dowhy/dowhy

      - name: Build
        run: ./docs/generate_docs.sh
