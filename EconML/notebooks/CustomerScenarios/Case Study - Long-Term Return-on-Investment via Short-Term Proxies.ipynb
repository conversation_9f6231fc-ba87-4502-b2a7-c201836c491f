{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Long-Term Return-on-Investment via Short-Term Proxies\n", "\n", "\n", "Policy makers typically face the problem of wanting to estimate the treatment effect of some new incentives on long-run downstream interests. However, we only have historical data of older, **different** treatment options, and we haven't seen the long-run effects **of these new incentives** play out yet. We assume access to a long-term dataset where only past treatments were administered and a short-term dataset where novel treatments have been administered. We propose a surrogate based approach where we assume that the long-term effect is channeled through a multitude of available short-term proxies. Our work combines three major recent techniques in the causal machine learning literature: **surrogate indices**, **dynamic treatment effect estimation** and **double machine learning**, in a unified\n", "pipeline. For more details, see this paper [here](https://arxiv.org/pdf/2103.08390.pdf).\n", "\n", "In this case study, we will show you how to apply this unified pipeline to a ROI estimation problem. These methodologies have already been implemented into our [EconML](https://aka.ms/econml) library and you could do it with only a few lines of code."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary\n", "\n", "1. [Background](#Background)\n", "2. [Data](#Data)\n", "3. [Do Dynamic Adjustment with EconML](#Do-Dynamic-Adjustment-with-EconML)\n", "4. [Train Surrogate Index](#Train-Surrogate-Index)\n", "5. [Run DML to Learn ROI with EconML](#Run-DML-to-Learn-ROI-with-EconML)\n", "6. [Model Evaluation](#Model-Evaluation)\n", "7. [Extensions -- Including Heterogeneity in Effect](#Extensions----Including-Heterogeneity-in-Effect)\n", "8. [Conclusions](#Conclusions)"]}, {"attachments": {"b86f5631-2e12-46ce-8072-a6a8a59ceda5.png": {"image/png": "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"}, "ebc29274-f089-47ae-94ef-c6edfd8a589b.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# Background\n", "\n", "A sales firm provides multiple monetary and resource investments to enterprice customers in support of products adoption, the sales manager would like to know which of these programs (\"investments\") are more successful than others? Specifically, we are interested in identifying the average treatment effect of each investment at some period $t$, on the cumulative outcome in the subsequent $m$ months. \n", "\n", "There are a few challenges to answer this question. First of all, we haven't fully observed the long-term revenue yet and we don't want to wait that long to evaluate a program. In addition, a careful causal modeling is required to correctly attribute the long-term ROI of multiple programs in a holistic manner, avoiding the biased estimate coming from confounding effect or double counting issues. \n", "\n", "The causal graph below shows how to frame this problem:\n", "\n", "![causal_graph.png](attachment:b86f5631-2e12-46ce-8072-a6a8a59ceda5.png)\n", "\n", "**Methodology:** Our proposed adjusted surrogate index approach could address all the chanllenges above by assuming the long-term effect is channeled through some short-term observed surrogates and employing a dynamic adjustment step (`DynamicDML`) to the surrogate model in order to get rid of the effect from future investment, finally applying double machine learning (`DML`) techniques to estimate the ROI. \n", "\n", "The pipeline below tells you how to solve this problem step by step:\n", "\n", "![pipeline.png](attachment:ebc29274-f089-47ae-94ef-c6edfd8a589b.png)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# imports\n", "from econml.data.dynamic_panel_dgp import SemiSynthetic\n", "from sklearn.linear_model import LassoCV, MultiTaskLassoCV\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data\n", "\n", "The **semi-synthetic data*** is comprised of 4 components:\n", " * **Surrogates:** short-term metrics that could represent long-term revenue\n", " * **Treatments:** different types of monetary investments to the end customers\n", " * **Outcomes:** cumulative long-term revenue\n", " * **Controls:** lagged surrogates and treatments, other time-invariant controls (e.g. demographics)\n", "\n", "To build the semi-synthetic data we estimate a series of moments from a real-world dataset: a full covariance matrix of\n", "all surrogates, treatments, and controls in one period and a series of linear prediction models (lassoCV) of each surrogate and\n", "treatment on a set of 6 lags of each treatment, 6 lags of each surrogate, and time-invariant controls. Using these values, we draw new parameters from distributions matching the key characteristics of each family of parameters. Finally, we use these new\n", "parameters to simulate surrogates, treatments, and controls by drawing a set of initial values from the covariance matrix and\n", "forward simulating to match intertemporal relationships from the transformed prediction models. We use one surrogate to be the outcome of interests. Then we consider the effect of each treatment in period $t$ on the cumulative sum of outcome from following 4 periods. We can calculate the true treatment effects in the semi-synthetic data as a function of parameters from the linear prediction models.\n", "\n", "The input data is in a **panel format**. Each panel corresponds to one company and the different rows in a panel correspond to different time period. \n", "\n", "Example:\n", "\n", "||Company|Year|Features|Controls/Surrogates|T1|T2|T3|AdjRev|\n", "|---|---|---|---|---|---|---|---|---|\n", "|1|A|2018|...|...|\\$1,000|...|...|\\$10,000|\n", "|2|A|2019|...|...|\\$2,000|...|...|\\$12,000|\n", "|3|A|2020|...|...|\\$3,000|...|...|\\$15,000|\n", "|4|A|2021|...|...|\\$3,000|...|...|\\$18,000|\n", "|5|B|2018|...|...|\\$0|...|...|\\$5,000|\n", "|6|B|2019|...|...|\\$1,000|...|...|\\$10,000|\n", "|7|B|2020|...|...|\\$0|...|...|\\$7,000|\n", "|8|B|2021|...|...|\\$1,200|...|...|\\$12,000|\n", "|9|C|2018|...|...|\\$1,000|...|...|\\$20,000|\n", "|10|C|2019|...|...|\\$1,500|...|...|\\$25,000|\n", "|11|C|2020|...|...|\\$500|...|...|\\$18,000|\n", "|12|C|2021|...|...|\\$500|...|...|\\$20,000|\n", " \n", " **For confidentiality reason, the data used in this case study is synthetically generated and the feature distributions don't exactly correspond to real distributions.*"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# generate historical dataset (training purpose)\n", "np.random.seed(43)\n", "dgp = SemiSynthetic()\n", "dgp.create_instance()\n", "n_periods = 4\n", "n_units = 5000\n", "n_treatments = dgp.n_treatments\n", "random_seed = 43\n", "thetas = np.random.uniform(0, 2, size=(dgp.n_proxies, n_treatments))\n", "\n", "panelX, panelT, panelY, panelGroups, true_effect = dgp.gen_data(\n", "    n_units, n_periods, thetas, random_seed\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Outcome shape:  (5000, 4)\n", "Treatment shape:  (5000, 4, 3)\n", "Controls shape:  (5000, 4, 71)\n"]}], "source": ["# print panel data shape\n", "print(\"Outcome shape: \", panelY.shape)\n", "print(\"Treatment shape: \", panelT.shape)\n", "print(\"Controls shape: \", panelX.shape)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# generate new dataset (testing purpose)\n", "thetas_new = np.random.uniform(0, 2, size=(dgp.n_proxies, n_treatments))\n", "panelXnew, panelTnew, panelYnew, panelGroupsnew, true_effect_new = dgp.gen_data(\n", "    n_units, n_periods, thetas_new, random_seed\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Long-term Effect for each investment:  [0.90994672 0.709811   2.45310877]\n"]}], "source": ["# print true long term effect\n", "true_longterm_effect = np.sum(true_effect_new, axis=0)\n", "print(\"True Long-term Effect for each investment: \", true_longterm_effect)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Do Dynamic Adjustment with EconML\n", "From the causal graph above, we could see we want to first remove the effects of future incentives from the historical outcomes to create an **adjusted long-term revenue** as if those future incentives never happened.\n", "\n", "EconML's `DynamicDML` estimator is an extension of Double Machine Learning approach to **dynamically estimate the period effect of treatments assigned sequentially over time period**. In this scenario, it could help us to adjust the cumulative revenue by subtracting the period effect of all of the investments after the target investment.\n", "\n", "For more details about `DynamicDML`, please read this [paper](https://arxiv.org/pdf/2002.07285.pdf). "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00],\n", "       [1.000e+00, 1.000e+00, 1.000e+00, 1.000e+00],\n", "       [2.000e+00, 2.000e+00, 2.000e+00, 2.000e+00],\n", "       ...,\n", "       [4.997e+03, 4.997e+03, 4.997e+03, 4.997e+03],\n", "       [4.998e+03, 4.998e+03, 4.998e+03, 4.998e+03],\n", "       [4.999e+03, 4.999e+03, 4.999e+03, 4.999e+03]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["panelGroups"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.35952949 1.92451605 0.34684417]\n", "[0.74662029 1.13138969 0.25069193 1.30585143 1.79051531 0.34597602]\n", "[0.46734394 0.74952179 0.16292026 0.67056612 0.92299133 0.23686006\n", " 1.36311063 1.91659314 0.34728767]\n"]}], "source": ["# on historical data construct adjusted outcomes\n", "from econml.panel.dml import DynamicDML\n", "from econml.panel.utilities import long, wide\n", "\n", "panelYadj = panelY.copy()\n", "\n", "est = DynamicDML(\n", "    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=2\n", ")\n", "for t in range(1, n_periods):  # for each target period 1...m\n", "    # learn period effect for each period treatment on target period t\n", "    est.fit(\n", "        long(panelY[:, 1 : t + 1]),\n", "        long(panelT[:, 1 : t + 1, :]),  # reshape data to long format\n", "        X=None,\n", "        W=long(panelX[:, 1 : t + 1, :]),\n", "        groups=long(panelGroups[:, 1 : t + 1]),\n", "    )\n", "    print(est.intercept_)\n", "    # remove effect of observed treatments\n", "    T1 = wide(panelT[:, 1 : t + 1, :])\n", "    panelYadj[:, t] = panelY[:, t] - est.effect(\n", "        T0=np.zeros_like(T1), T1=T1\n", "    )  # reshape data to wide format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Train Surrogate Index\n", "Once we have the adjusted outcome, we'd like to train any ML model to learn the relationship between short-term surrogates and long-term revenue from the historical dataset, assuming the treatment effect of investments on long-term revenue could **only** go through short-term surrogates, and the **relationship keeps the same** between the historical dataset and the new dataset."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 9;\n                var nbb_formatted_code = \"# train surrogate index on historical dataset\\nXS = np.hstack(\\n    [panelX[:, 1], panelYadj[:, :1]]\\n)  # concatenate controls and surrogates from historical dataset\\nTotalYadj = np.sum(panelYadj, axis=1)  # total revenue from historical dataset\\nadjusted_proxy_model = LassoCV().fit(\\n    XS, TotalYadj\\n)  # train proxy model from historical dataset\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# train surrogate index on historical dataset\n", "XS = np.hstack(\n", "    [panelX[:, 1], panelYadj[:, :1]]\n", ")  # concatenate controls and surrogates from historical dataset\n", "TotalYadj = np.sum(panelYadj, axis=1)  # total revenue from historical dataset\n", "adjusted_proxy_model = LassoCV().fit(\n", "    XS, TotalYadj\n", ")  # train proxy model from historical dataset"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 10;\n                var nbb_formatted_code = \"# predict new long term revenue\\nXSnew = np.hstack(\\n    [panelXnew[:, 1], panelYnew[:, :1]]\\n)  # concatenate controls and surrogates from new dataset\\nsindex_adj = adjusted_proxy_model.predict(XSnew)\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# predict new long term revenue\n", "XSnew = np.hstack(\n", "    [panelXnew[:, 1], panelYnew[:, :1]]\n", ")  # concatenate controls and surrogates from new dataset\n", "sindex_adj = adjusted_proxy_model.predict(XSnew)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run DML to Learn ROI with EconML\n", "Finally we will call `LinearDML` estimator from EconML to learn the treatment effect of multiple investments on the adjusted surrogate index in new dataset. `LinearDML` is a two stage machine learning models for estimating **(heterogeneous) treatment effects** when all potential confounders are observed, it leverages the machine learning power to deal with **high dimensional dataset** and still be able to construct **confidence intervals**. \n", "\n", "For more details, please read this [paper](https://arxiv.org/pdf/1608.00060.pdf). "]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Long-term Effect for each investment:  [0.90994672 0.709811   2.45310877]\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "          <td></td>          <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0</th>      <td>0.83</td>       <td>0.015</td> <td>57.214</td>   <td>0.0</td>    <td>0.802</td>    <td>0.858</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T1</th>      <td>0.677</td>      <td>0.028</td> <td>23.767</td>   <td>0.0</td>    <td>0.621</td>    <td>0.733</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T2</th>      <td>2.438</td>      <td>0.035</td> <td>69.711</td>   <td>0.0</td>    <td>2.369</td>    <td>2.507</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                         CATE Intercept Results                        \n", "=======================================================================\n", "                  point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-----------------------------------------------------------------------\n", "cate_intercept|T0           0.83  0.015 57.214    0.0    0.802    0.858\n", "cate_intercept|T1          0.677  0.028 23.767    0.0    0.621    0.733\n", "cate_intercept|T2          2.438  0.035 69.711    0.0    2.369    2.507\n", "-----------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 11;\n                var nbb_formatted_code = \"# learn treatment effect on surrogate index on new dataset\\nfrom econml.dml import LinearDML\\n\\nadjsurr_est = LinearDML(\\n    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=3\\n)\\n# fit treatment_0 on total revenue from new dataset\\nadjsurr_est.fit(sindex_adj, panelTnew[:, 0], X=None, W=panelXnew[:, 0])\\n# print treatment effect summary\\nprint(\\\"True Long-term Effect for each investment: \\\", true_longterm_effect)\\nadjsurr_est.summary(alpha=0.05)\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# learn treatment effect on surrogate index on new dataset\n", "from econml.dml import LinearDML\n", "\n", "adjsurr_est = LinearDML(\n", "    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=3\n", ")\n", "# fit treatment_0 on total revenue from new dataset\n", "adjsurr_est.fit(sindex_adj, panelTnew[:, 0], X=None, W=panelXnew[:, 0])\n", "# print treatment effect summary\n", "print(\"True Long-term Effect for each investment: \", true_longterm_effect)\n", "adjsurr_est.summary(alpha=0.05)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 12;\n                var nbb_formatted_code = \"# save the treatment effect and confidence interval\\nadjsurr_point_est = adjsurr_est.intercept_\\nadjsurr_conf_int_lb, adjsurr_conf_int_ub = adjsurr_est.intercept__interval(alpha=0.05)\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# save the treatment effect and confidence interval\n", "adjsurr_point_est = adjsurr_est.intercept_\n", "adjsurr_conf_int_lb, adjsurr_conf_int_ub = adjsurr_est.intercept__interval(alpha=0.05)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Evaluation\n", "Now we want to compare the proposed **adjusted surrogate index** approach with estimation from realized long-term outcome. Below we train another `LinearDML` model on the realized cumulative revenue directly, without any adjustment. And then we visualize the two models output, comparing with the ground truth."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Long-term Effect for each investment:  [0.90994672 0.709811   2.45310877]\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "          <td></td>          <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0</th>      <td>2.227</td>      <td>0.039</td> <td>56.865</td>   <td>0.0</td>    <td>2.15</td>     <td>2.304</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T1</th>      <td>1.561</td>      <td>0.226</td>  <td>6.911</td>   <td>0.0</td>    <td>1.118</td>    <td>2.004</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T2</th>      <td>4.335</td>      <td>0.209</td> <td>20.748</td>   <td>0.0</td>    <td>3.926</td>    <td>4.745</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                         CATE Intercept Results                        \n", "=======================================================================\n", "                  point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-----------------------------------------------------------------------\n", "cate_intercept|T0          2.227  0.039 56.865    0.0     2.15    2.304\n", "cate_intercept|T1          1.561  0.226  6.911    0.0    1.118    2.004\n", "cate_intercept|T2          4.335  0.209 20.748    0.0    3.926    4.745\n", "-----------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 13;\n                var nbb_formatted_code = \"# learn treatment effect on direct outcome\\nfrom econml.dml import LinearDML\\n\\ndirect_est = LinearDML(\\n    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=3\\n)\\n# fit treatment_0 on total revenue from new dataset\\ndirect_est.fit(np.sum(panelYnew, axis=1), panelTnew[:, 0], X=None, W=panelXnew[:, 0])\\n# print treatment effect summary\\nprint(\\\"True Long-term Effect for each investment: \\\", true_longterm_effect)\\ndirect_est.summary(alpha=0.05)\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# learn treatment effect on direct outcome\n", "from econml.dml import LinearDML\n", "\n", "direct_est = LinearDML(\n", "    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=3\n", ")\n", "# fit treatment_0 on total revenue from new dataset\n", "direct_est.fit(np.sum(panelYnew, axis=1), panelTnew[:, 0], X=None, W=panelXnew[:, 0])\n", "# print treatment effect summary\n", "print(\"True Long-term Effect for each investment: \", true_longterm_effect)\n", "direct_est.summary(alpha=0.05)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 14;\n                var nbb_formatted_code = \"# save the treatment effect and confidence interval\\ndirect_point_est = direct_est.intercept_\\ndirect_conf_int_lb, direct_conf_int_ub = direct_est.intercept__interval(alpha=0.05)\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# save the treatment effect and confidence interval\n", "direct_point_est = direct_est.intercept_\n", "direct_conf_int_lb, direct_conf_int_ub = direct_est.intercept__interval(alpha=0.05)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0.98, 'Error bar plot of treatment effect from different models')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1296x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 15;\n                var nbb_formatted_code = \"# plot the error bar plot of different models\\nplt.figure(figsize=(18, 6))\\nplt.subplot(1, 2, 1)\\n\\nplt.errorbar(\\n    np.arange(n_treatments) - 0.04,\\n    true_longterm_effect,\\n    fmt=\\\"o\\\",\\n    alpha=0.6,\\n    label=\\\"Ground truth\\\",\\n)\\nplt.errorbar(\\n    np.arange(n_treatments),\\n    adjsurr_point_est,\\n    yerr=(\\n        adjsurr_conf_int_ub - adjsurr_point_est,\\n        adjsurr_point_est - adjsurr_conf_int_lb,\\n    ),\\n    fmt=\\\"o\\\",\\n    label=\\\"Adjusted Surrogate Index\\\",\\n)\\nplt.xticks(np.arange(n_treatments), [\\\"T0\\\", \\\"T1\\\", \\\"T2\\\"])\\nplt.ylabel(\\\"Effect\\\")\\nplt.legend()\\n\\nplt.subplot(1, 2, 2)\\nplt.errorbar(\\n    np.arange(n_treatments) - 0.04,\\n    true_longterm_effect,\\n    fmt=\\\"o\\\",\\n    alpha=0.6,\\n    label=\\\"Ground truth\\\",\\n)\\nplt.errorbar(\\n    np.arange(n_treatments),\\n    adjsurr_point_est,\\n    yerr=(\\n        adjsurr_conf_int_ub - adjsurr_point_est,\\n        adjsurr_point_est - adjsurr_conf_int_lb,\\n    ),\\n    fmt=\\\"o\\\",\\n    label=\\\"Adjusted Surrogate Index\\\",\\n)\\nplt.errorbar(\\n    np.arange(n_treatments) + 0.04,\\n    direct_point_est,\\n    yerr=(direct_conf_int_ub - direct_point_est, direct_point_est - direct_conf_int_lb),\\n    fmt=\\\"o\\\",\\n    label=\\\"Direct Model\\\",\\n)\\nplt.xticks(np.arange(n_treatments), [\\\"T0\\\", \\\"T1\\\", \\\"T2\\\"])\\nplt.ylabel(\\\"Effect\\\")\\nplt.legend()\\nplt.suptitle(\\\"Error bar plot of treatment effect from different models\\\")\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot the error bar plot of different models\n", "plt.figure(figsize=(18, 6))\n", "plt.subplot(1, 2, 1)\n", "\n", "plt.errorbar(\n", "    np.arange(n_treatments) - 0.04,\n", "    true_longterm_effect,\n", "    fmt=\"o\",\n", "    alpha=0.6,\n", "    label=\"Ground truth\",\n", ")\n", "plt.errorbar(\n", "    np.arange(n_treatments),\n", "    adjsurr_point_est,\n", "    yerr=(\n", "        adjsurr_conf_int_ub - adjsurr_point_est,\n", "        adjsurr_point_est - adjsurr_conf_int_lb,\n", "    ),\n", "    fmt=\"o\",\n", "    label=\"Adjusted Surrogate Index\",\n", ")\n", "plt.xticks(np.arange(n_treatments), [\"T0\", \"T1\", \"T2\"])\n", "plt.ylabel(\"Effect\")\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.errorbar(\n", "    np.arange(n_treatments) - 0.04,\n", "    true_longterm_effect,\n", "    fmt=\"o\",\n", "    alpha=0.6,\n", "    label=\"Ground truth\",\n", ")\n", "plt.errorbar(\n", "    np.arange(n_treatments),\n", "    adjsurr_point_est,\n", "    yerr=(\n", "        adjsurr_conf_int_ub - adjsurr_point_est,\n", "        adjsurr_point_est - adjsurr_conf_int_lb,\n", "    ),\n", "    fmt=\"o\",\n", "    label=\"Adjusted Surrogate Index\",\n", ")\n", "plt.errorbar(\n", "    np.arange(n_treatments) + 0.04,\n", "    direct_point_est,\n", "    yerr=(direct_conf_int_ub - direct_point_est, direct_point_est - direct_conf_int_lb),\n", "    fmt=\"o\",\n", "    label=\"Direct Model\",\n", ")\n", "plt.xticks(np.arange(n_treatments), [\"T0\", \"T1\", \"T2\"])\n", "plt.ylabel(\"Effect\")\n", "plt.legend()\n", "plt.suptitle(\"Error bar plot of treatment effect from different models\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We could see the **adjusted surrogate index** approach does a good job overcomes a common data limitation when considering long-term effects of novel treatments and expands the surrogate approach to consider a common, and previously\n", "problematic, pattern of serially correlated treatments."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Extensions -- Including Heterogeneity in Effect\n", "\n", "Finally, I will show that our EconML's `DynamicDML` and `LinearDML` estimators could not only learn Average Treatment Effect (ATE), but also **Heterogeneous Treatment Effect (CATE)**, which will return the treatment effect as a function of interested characteristics. In the example below, I will use first control variable as feature to learn effect heterogeneity, and retrain the final `LinearDML` model. Similarly, you could train `DynamicDML` with feature $X$ as well."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Long-term Effect for each investment:  [0.90994672 0.709811   2.45310877]\n", "Average treatment effect for each investment:  [0.82738185 0.71610965 2.56087599]\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>    <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>X0|T0</th>      <td>0.009</td>      <td>0.011</td>  <td>0.76</td>   <td>0.447</td>  <td>-0.014</td>    <td>0.031</td> \n", "</tr>\n", "<tr>\n", "  <th>X0|T1</th>      <td>0.037</td>      <td>0.031</td>  <td>1.218</td>  <td>0.223</td>  <td>-0.023</td>    <td>0.098</td> \n", "</tr>\n", "<tr>\n", "  <th>X0|T2</th>     <td>-0.072</td>      <td>0.151</td> <td>-0.478</td>  <td>0.633</td>  <td>-0.369</td>    <td>0.224</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "          <td></td>          <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T0</th>      <td>0.827</td>      <td>0.015</td> <td>56.625</td>   <td>0.0</td>    <td>0.799</td>    <td>0.856</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T1</th>      <td>0.716</td>      <td>0.032</td> <td>22.466</td>   <td>0.0</td>    <td>0.654</td>    <td>0.779</td> \n", "</tr>\n", "<tr>\n", "  <th>cate_intercept|T2</th>      <td>2.56</td>       <td>0.237</td>  <td>10.82</td>   <td>0.0</td>    <td>2.096</td>    <td>3.024</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                    Coefficient Results                    \n", "===========================================================\n", "      point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-----------------------------------------------------------\n", "X0|T0          0.009  0.011   0.76  0.447   -0.014    0.031\n", "X0|T1          0.037  0.031  1.218  0.223   -0.023    0.098\n", "X0|T2         -0.072  0.151 -0.478  0.633   -0.369    0.224\n", "                         CATE Intercept Results                        \n", "=======================================================================\n", "                  point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-----------------------------------------------------------------------\n", "cate_intercept|T0          0.827  0.015 56.625    0.0    0.799    0.856\n", "cate_intercept|T1          0.716  0.032 22.466    0.0    0.654    0.779\n", "cate_intercept|T2           2.56  0.237  10.82    0.0    2.096    3.024\n", "-----------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": "\n            setTimeout(function() {\n                var nbb_cell_id = 16;\n                var nbb_formatted_code = \"# learn treatment effect on surrogate index on new dataset\\nfrom econml.dml import LinearDML\\n\\nadjsurr_est = LinearDML(\\n    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=3\\n)\\n# fit treatment_0 on total revenue from new dataset\\nadjsurr_est.fit(\\n    sindex_adj, panelTnew[:, 0], X=panelXnew[:, 0, :1], W=panelXnew[:, 0, 1:]\\n)\\n# print treatment effect summary\\nprint(\\\"True Long-term Effect for each investment: \\\", true_longterm_effect)\\nprint(\\n    \\\"Average treatment effect for each investment: \\\",\\n    adjsurr_est.const_marginal_ate(panelXnew[:, 0, :1]),\\n)\\nadjsurr_est.summary(alpha=0.05)\";\n                var nbb_cells = Jupyter.notebook.get_cells();\n                for (var i = 0; i < nbb_cells.length; ++i) {\n                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n                        nbb_cells[i].set_text(nbb_formatted_code);\n                        break;\n                    }\n                }\n            }, 500);\n            ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# learn treatment effect on surrogate index on new dataset\n", "from econml.dml import LinearDML\n", "\n", "adjsurr_est = LinearDML(\n", "    model_y=LassoCV(max_iter=2000), model_t=MultiTaskLassoCV(max_iter=2000), cv=3\n", ")\n", "# fit treatment_0 on total revenue from new dataset\n", "adjsurr_est.fit(\n", "    sindex_adj, panelTnew[:, 0], X=panelXnew[:, 0, :1], W=panelXnew[:, 0, 1:]\n", ")\n", "# print treatment effect summary\n", "print(\"True Long-term Effect for each investment: \", true_longterm_effect)\n", "print(\n", "    \"Average treatment effect for each investment: \",\n", "    adjsurr_est.const_marginal_ate(panelXnew[:, 0, :1]),\n", ")\n", "adjsurr_est.summary(alpha=0.05)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the summary table above, none of the coefficient for feature $X0$ is significant, that means there is no effect heterogeneity identified, which is consistent with the data generation process."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Conclusions\n", "\n", "In this notebook, we have demonstrated the power of using EconML to:\n", "\n", "* estimate treatment effects in settings when multiple treatments are assigned over time and treatments can have a causal effect on future outcomes\n", "* correct the bias coming from auto-correlation of the historical treatment policy\n", "* use Machine Learning to enable estimation with high-dimensional surrogates and controls\n", "* solve a complex problem using an unified pipeline with only a few lines of code\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}