"""
Matplotlib 后端配置模块

本代码段用于配置 Matplotlib 的显示后端为 inline 模式，这是在 Jupyter Notebook 
环境中进行数据可视化的标准配置。inline 后端允许图表直接嵌入到 notebook 单元格中，
无需弹出独立窗口，提供了更好的交互体验。

主要功能：
- 导入 matplotlib 核心库
- 设置显示后端为 inline 模式  
- 验证并输出当前使用的后端类型

适用场景：
- Jupyter Notebook 数据分析环境
- 需要内嵌图表显示的场景
- 交互式数据可视化开发
"""
import matplotlib
%matplotlib inline
import dowhy
print(f"DoWhy 版本: {dowhy.__version__}")

current_backend = matplotlib.get_backend()
print("=" * 50)
print("📊 Matplotlib 后端配置信息")
print("=" * 50)
print(f"✅ 当前激活的后端类型: {current_backend}")
print(f"🎯 配置状态: {'成功' if 'inline' in current_backend.lower() else '需要检查'}")
print("=" * 50)

# 警告抑制配置
import warnings

# 抑制 statsmodels 相关警告
warnings.filterwarnings('ignore', category=UserWarning, module='statsmodels')
warnings.filterwarnings('ignore', message='omni_normtest is not valid with less than 8 observations')

# 抑制 pandas/dowhy FutureWarning
warnings.filterwarnings('ignore', category=FutureWarning, module='dowhy')
warnings.filterwarnings('ignore', message='Series.__getitem__ treating keys as positions is deprecated')

print("🔇 警告抑制配置已启用")
print("   - statsmodels ValueWarning 已抑制")
print("   - dowhy FutureWarning 已抑制")
print("   - 输出将更加清洁")



from langchain_deepseek import ChatDeepSeek
import os
os.environ["http_proxy"] = "http://127.0.0.1:7897"
os.environ["https_proxy"] = "http://127.0.0.1:7897"
os.environ["all_proxy"]= "socks5://127.0.0.1:7897"
os.environ["no_proxy"]= "http://127.0.0.1,localhost"
os.environ['LANGCHAIN_TRACING_V2'] = 'true'
os.environ['LANGCHAIN_ENDPOINT'] = 'https://api.smith.langchain.com'
os.environ['LANGCHAIN_API_KEY'] = '***************************************************'
LANGCHAIN_PROJECT= "dowhy"
from langchain_openai import OpenAIEmbeddings, ChatOpenAI


deepseek_url= "https://api.deepseek.com/v1"
deepseek_api= "***********************************"
ni_url= "https://api.365api.shop/v1"
claude_api="sk-QBBtTxLIJ4FUK62i6d2c6fAb4c9f4c3aBbF6A7073022099f"

opus4=ChatOpenAI(model_name="claude-opus-4-20250514",temperature=0,api_key=claude_api,base_url=ni_url)
deepseek=ChatDeepSeek(model_name="deepseek-chat",base_url=deepseek_url,api_key=deepseek_api,temperature=0)

from langchain.prompts.prompt import PromptTemplate
from langchain_core.runnables import (
    RunnableLambda,
    RunnableParallel,
    RunnablePassthrough,
)

# Template
template = """
你是一个因果推理领域专家，请使用用中文解释客户问题，需要对重要概念和名词进行解释，并对指标的现实或业务翻译进行详尽解释和分析。
Human: {input}
AI Assistant:"""
PROMPT = PromptTemplate(input_variables=["history", "input"], template=template)

explain= { 'input': RunnablePassthrough()} | PROMPT | opus4



import dowhy
from dowhy import CausalModel
import pandas as pd

# 创建一个示例数据集
data = pd.DataFrame({
    'health_status': [1, 2, 1, 3, 2, 3, 1], 
    'treatment': [1, 0, 1, 0, 1, 0, 1], 
    'outcome': [1, 1, 0, 1, 0, 1, 0]
})

# 1. 建模：使用GML图语言定义因果图
# 'health_status' -> 'treatment' (健康状况影响是否接受治疗)
# 'health_status' -> 'outcome' (健康状况影响结果)
# 'treatment' -> 'outcome' (治疗影响结果)
model = CausalModel(
    data=data,
    treatment='treatment',
    outcome='outcome',
    graph="""
    digraph {
        health_status -> treatment;
        health_status -> outcome;
        treatment -> outcome;
    }
    """
)

model.view_model()

# 2. 识别：找到一个可用于估计的数学公式（估计目标）
identified_estimand = model.identify_effect()
print(identified_estimand)

# 3. 估计：使用统计方法计算效应值
estimate = model.estimate_effect(
    identified_estimand,
    method_name="backdoor.linear_regression" # 指定使用线性回归
)
print(estimate)

# 4. 反驳：检验结果的稳健性
# 例如，添加一个随机的共同原因，看效应值是否发生巨大变化
refute_results = model.refute_estimate(
    identified_estimand, 
    estimate,
    method_name="random_common_cause"
)
print(refute_results)

import pandas as pd
df = pd.read_csv("./dowhy/docs/source/example_notebooks/rca_microservice_architecture_latencies.csv")
df.head()

import networkx as nx
from dowhy import gcm
from dowhy.utils import plot, bar_plot

causal_graph = nx.DiGraph([('www', 'Website'),
                           ('Auth Service', 'www'),
                           ('API', 'www'),
                           ('Customer DB', 'Auth Service'),
                           ('Customer DB', 'API'),
                           ('Product Service', 'API'),
                           ('Auth Service', 'API'),
                           ('Order Service', 'API'),
                           ('Shipping Cost Service', 'Product Service'),
                           ('Caching Service', 'Product Service'),
                           ('Product DB', 'Caching Service'),
                           ('Customer DB', 'Product Service'),
                           ('Order DB', 'Order Service')])

plot(causal_graph, figure_size=[13, 13])


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

import dowhy

dataset = pd.read_csv('./data/hotel_bookings.csv')
dataset.head()

# Total stay in nights
dataset['total_stay'] = dataset['stays_in_week_nights']+dataset['stays_in_weekend_nights']
# Total number of guests
dataset['guests'] = dataset['adults']+dataset['children'] +dataset['babies']
# Creating the different_room_assigned feature
dataset['different_room_assigned']=0
slice_indices =dataset['reserved_room_type']!=dataset['assigned_room_type']
dataset.loc[slice_indices,'different_room_assigned']=1
# Deleting older features
dataset = dataset.drop(['stays_in_week_nights','stays_in_weekend_nights','adults','children','babies'
                        ,'reserved_room_type','assigned_room_type'],axis=1)
dataset.columns

dataset.isnull().sum() # Country,Agent,Company contain 488,16340,112593 missing entries
# First check if columns exist before dropping
cols_to_drop = [col for col in ['agent','company'] if col in dataset.columns]
if cols_to_drop:
    dataset = dataset.drop(cols_to_drop, axis=1)
# Replacing missing countries with most freqently occuring countries
dataset['country']= dataset['country'].fillna(dataset['country'].mode()[0])
# Drop other columns safely
cols_to_drop = ['reservation_status','reservation_status_date','arrival_date_day_of_month',
               'arrival_date_year','distribution_channel']
cols_to_drop = [col for col in cols_to_drop if col in dataset.columns]
dataset = dataset.drop(cols_to_drop, axis=1)
# Replacing 1 by True and 0 by False for the experiment and outcome variables
pd.set_option('future.no_silent_downcasting', True)
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(1,True).infer_objects(copy=False)
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(0,False).infer_objects(copy=False)
dataset['is_canceled']= dataset['is_canceled'].replace(1,True).infer_objects(copy=False)
dataset['is_canceled']= dataset['is_canceled'].replace(0,False).infer_objects(copy=False)
dataset.dropna(inplace=True)
print(dataset.columns)
dataset.iloc[:, 5:20].head(100)

import pygraphviz
causal_graph = """digraph {
different_room_assigned[label="Different Room Assigned"];
is_canceled[label="Booking Cancelled"];
booking_changes[label="Booking Changes"];
previous_bookings_not_canceled[label="Previous Booking Retentions"];
days_in_waiting_list[label="Days in Waitlist"];
lead_time[label="Lead Time"];
market_segment[label="Market Segment"];
country[label="Country"];
U[label="Unobserved Confounders",observed="no"];
is_repeated_guest;
total_stay;
guests;
meal;
hotel;
U->{different_room_assigned,required_car_parking_spaces,guests,total_stay,total_of_special_requests};
market_segment -> lead_time;
lead_time->is_canceled; country -> lead_time;
different_room_assigned -> is_canceled;
country->meal;
lead_time -> days_in_waiting_list;
days_in_waiting_list ->{is_canceled,different_room_assigned};
previous_bookings_not_canceled -> is_canceled;
previous_bookings_not_canceled -> is_repeated_guest;
is_repeated_guest -> {different_room_assigned,is_canceled};
total_stay -> is_canceled;
guests -> is_canceled;
booking_changes -> different_room_assigned; booking_changes -> is_canceled;
hotel -> {different_room_assigned,is_canceled};
required_car_parking_spaces -> is_canceled;
total_of_special_requests -> {booking_changes,is_canceled};
country->{hotel, required_car_parking_spaces,total_of_special_requests};
market_segment->{hotel, required_car_parking_spaces,total_of_special_requests};
}"""

model= dowhy.CausalModel(
        data = dataset,
        graph=causal_graph.replace("\n", " "),
        treatment="different_room_assigned",
        outcome='is_canceled')
model.view_model()

import dowhy
from dowhy import CausalModel

import numpy as np
import pandas as pd
import graphviz
import networkx as nx

np.set_printoptions(precision=3, suppress=True)
np.random.seed(0)

def make_graph(adjacency_matrix, labels=None):
    idx = np.abs(adjacency_matrix) > 0.01
    dirs = np.where(idx)
    d = graphviz.Digraph(engine='dot')
    names = labels if labels else [f'x{i}' for i in range(len(adjacency_matrix))]
    for name in names:
        d.node(name)
    for to, from_, coef in zip(dirs[0], dirs[1], adjacency_matrix[idx]):
        d.edge(names[from_], names[to], label=str(coef))
    return d

def str_to_dot(string):
    '''
    Converts input string from graphviz library to valid DOT graph format.
    '''
    graph = string.strip().replace('\n', ';').replace('\t','')
    graph = graph[:9] + graph[10:-2] + graph[-1] # Removing unnecessary characters from string
    return graph

data_mpg = pd.read_csv('http://archive.ics.uci.edu/ml/machine-learning-databases/auto-mpg/auto-mpg.data-original',
                   delim_whitespace=True, header=None,
                   names = ['mpg', 'cylinders', 'displacement',
                            'horsepower', 'weight', 'acceleration',
                            'model year', 'origin', 'car name'])
data_mpg.dropna(inplace=True)
data_mpg.drop(['model year', 'origin', 'car name'], axis=1, inplace=True)
print(data_mpg.shape)
data_mpg.head()

from causallearn.search.ConstraintBased.PC import pc

labels = [f'{col}' for i, col in enumerate(data_mpg.columns)]
data = data_mpg.to_numpy()

cg = pc(data)

# Visualization using pydot
from causallearn.utils.GraphUtils import GraphUtils
import matplotlib.image as mpimg
import matplotlib.pyplot as plt
import io

pyd = GraphUtils.to_pydot(cg.G, labels=labels)
tmp_png = pyd.create_png(f="png")
fp = io.BytesIO(tmp_png)
img = mpimg.imread(fp, format='png')
plt.axis('off')
plt.imshow(img)
plt.show()

from causallearn.search.ScoreBased.GES import ges

# default parameters
Record = ges(data)

# Visualization using pydot
from causallearn.utils.GraphUtils import GraphUtils
import matplotlib.image as mpimg
import matplotlib.pyplot as plt
import io

pyd = GraphUtils.to_pydot(Record['G'], labels=labels)
tmp_png = pyd.create_png(f="png")
fp = io.BytesIO(tmp_png)
img = mpimg.imread(fp, format='png')
plt.axis('off')
plt.imshow(img)
plt.show()

from causallearn.search.FCMBased import lingam
model = lingam.ICALiNGAM()
model.fit(data)

from causallearn.search.FCMBased.lingam.utils import make_dot
make_dot(model.adjacency_matrix_, labels=labels)

from causallearn.utils.Dataset import load_dataset

data_sachs, labels = load_dataset("sachs")

print(data.shape)
print(labels)

data_sachs

graphs = {}
graphs_nx = {}
labels = [f'{col}' for i, col in enumerate(labels)]
data = data_sachs

from causallearn.search.ConstraintBased.PC import pc

cg = pc(data)

# Visualization using pydot
from causallearn.utils.GraphUtils import GraphUtils
import matplotlib.image as mpimg
import matplotlib.pyplot as plt
import io

pyd = GraphUtils.to_pydot(cg.G, labels=labels)
tmp_png = pyd.create_png(f="png")
fp = io.BytesIO(tmp_png)
img = mpimg.imread(fp, format='png')
plt.axis('off')
plt.imshow(img)
plt.show()

from causallearn.search.ScoreBased.GES import ges

# default parameters
Record = ges(data)

# Visualization using pydot
from causallearn.utils.GraphUtils import GraphUtils
import matplotlib.image as mpimg
import matplotlib.pyplot as plt
import io

pyd = GraphUtils.to_pydot(Record['G'], labels=labels)
tmp_png = pyd.create_png(f="png")
fp = io.BytesIO(tmp_png)
img = mpimg.imread(fp, format='png')
plt.axis('off')
plt.imshow(img)
plt.show()

from causallearn.search.FCMBased import lingam
model = lingam.ICALiNGAM()
model.fit(data)

from causallearn.search.FCMBased.lingam.utils import make_dot
make_dot(model.adjacency_matrix_, labels=labels)

import os, sys
import random
sys.path.append(os.path.abspath("../../../"))
import numpy as np
import pandas as pd

import dowhy
from dowhy import CausalModel
from IPython.display import Image, display

z=[i for i in range(10)]
random.shuffle(z)
df = pd.DataFrame(data = {'Z': z, 'X': range(0,10), 'Y': range(0,100,10)})
df

# With GML string
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="""graph[directed 1 node[id "Z" label "Z"]
                    node[id "X" label "X"]
                    node[id "Y" label "Y"]
                    edge[source "Z" target "X"]
                    edge[source "Z" target "Y"]
                    edge[source "X" target "Y"]]"""

        )
model.view_model()

# With GML file
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="./dowhy/docs/source/example_graphs/simple_graph_example.gml"
        )
model.view_model()

# With DOT string
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="digraph {Z -> X;Z -> Y;X -> Y;}"
        )
model.view_model()


# With DOT file
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="./dowhy/docs/source/example_graphs/simple_graph_example.dot"
        )
model.view_model()

import numpy as np, pandas as pd

# 生成因果链数据 X → Y → Z
X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

import dowhy.gcm as gcm
# 零假设：X 与 Z 独立
p_value = gcm.independence_test(X, Z, method='kernel')

alpha = 0.05
print(f"零假设：X 与 Z 独立")
print(f"p-value: {p_value:.4f}")
if p_value < alpha:
    print(f"p值小于显著性水平 {alpha}，拒绝零假设，X 与 Z 不是独立的。")
else:
    print(f"p值大于显著性水平 {alpha}，无法拒绝零假设，X 与 Z 独立。")

import dowhy.gcm as gcm
# 零假设：X 与 Z 在给定 Y 的条件下独立
p_value = gcm.independence_test(X, Z, conditioned_on=Y, method='kernel')
alpha = 0.05
print(f"零假设：X 与 Z 在给定 Y 的条件下独立")
print(f"p-value: {p_value:.4f}")
if p_value < alpha:
    print(f"p值小于显著性水平 {alpha}，拒绝零假设，X 与 Z 在给定 Y 的条件下不是独立的。")
else:
    print(f"p值大于显著性水平 {alpha}，无法拒绝零假设，X 与 Z 在给定 Y 的条件下独立。")

# 生成分叉结构数据：Z ← X → Y
import numpy as np
import pandas as pd

np.random.seed(42)
n_samples = 1000

# X 是根节点
X = np.random.normal(0, 1, n_samples)

# Y 依赖于 X
Y = 2 * X + np.random.normal(0, 0.5, n_samples)

# Z 也依赖于 X
Z = -1.5 * X + np.random.normal(0, 0.8, n_samples)

# 创建数据框
fork_data = pd.DataFrame({
    'X': X,
    'Y': Y,
    'Z': Z
})

print("分叉结构数据（前5行）：")
print(fork_data.head())

# 测试分叉结构的独立性关系
print("\n=== 分叉结构独立性测试 ===")

# 1. Y 与 Z 的无条件独立性（应该不独立）
p_value_yz = gcm.independence_test(Y, Z, method='kernel')
print(f"\n1. Y 与 Z 无条件独立性测试:")
print(f"   p-value: {p_value_yz:.4f}")
if p_value_yz < 0.05:
    print("   结果：Y 与 Z 不独立 ✓（符合分叉结构预期）")
else:
    print("   结果：Y 与 Z 独立 ✗（不符合分叉结构预期）")

# 2. Y 与 Z 在给定 X 条件下的独立性（应该独立）
p_value_yz_given_x = gcm.independence_test(Y, Z, conditioned_on=X, method='kernel')
print(f"\n2. Y 与 Z 在给定 X 条件下的独立性测试:")
print(f"   p-value: {p_value_yz_given_x:.4f}")
if p_value_yz_given_x >= 0.05:
    print("   结果：Y ⫫ Z | X（条件独立）✓（符合分叉结构预期）")
else:
    print("   结果：Y 与 Z 在给定 X 条件下不独立 ✗（不符合分叉结构预期）")


np.random.seed(123)  # 使用不同的随机种子

# 确保 X 和 Y 完全独立
X_new = np.random.normal(0, 1, n_samples)
Y_new = np.random.normal(0, 1, n_samples)

# Z 依赖于 X 和 Y，但噪声更大以减少X和Y之间的间接关联
Z_new = 0.8 * X_new + 0.8 * Y_new + np.random.normal(0, 1.5, n_samples)

# 创建新的数据框
collider_data_new = pd.DataFrame({
    'X': X_new,
    'Y': Y_new,
    'Z': Z_new
})

print("生成的对撞结构数据（前5行）：")
print(collider_data_new.head())

# 重新测试对撞结构的独立性关系
print("\n=== 测试对撞结构独立性 ===")

# 1. X 与 Y 的无条件独立性（应该独立）
p_value_xy_new = gcm.independence_test(X_new, Y_new, method='kernel')
print(f"\n1. X 与 Y 无条件独立性测试:")
print(f"   p-value: {p_value_xy_new:.4f}")
if p_value_xy_new >= 0.05:
    print("   结果：X 与 Y 独立 ✓（符合对撞结构预期）")
else:
    print("   结果：X 与 Y 不独立 ✗（不符合对撞结构预期）")

# 2. X 与 Y 在给定 Z 条件下的独立性（应该不独立）
p_value_xy_given_z_new = gcm.independence_test(X_new, Y_new, conditioned_on=Z_new, method='kernel')
print(f"\n2. X 与 Y 在给定 Z 条件下的独立性测试:")
print(f"   p-value: {p_value_xy_given_z_new:.4f}")
if p_value_xy_given_z_new < 0.05:
    print("   结果：X 与 Y 在给定 Z 条件下不独立 ✓（符合对撞结构预期）")
else:
    print("   结果：X ⫫ Y | Z（条件独立）✗（不符合对撞结构预期）")


import pandas as pd
import numpy as np
import networkx as nx
from dowhy.gcm.falsify import falsify_graph

# --- 1. 创建示例数据 (data) ---
# 我们来创建一个符合简单因果链 X -> Y -> Z 的数据集。
# X 是原因，Y 是中介，Z 是结果。
np.random.seed(42) # 为了结果可复现
num_samples = 1000

# X 是一个独立的随机变量
X = np.random.normal(0, 1, num_samples)

# Y 的值依赖于 X，并加上一些随机噪声
Y = 2 * X + np.random.normal(0, 1, num_samples)

# Z 的值依赖于 Y，并加上一些随机噪声
Z = 3 * Y + np.random.normal(0, 1, num_samples)

# 将数据整合成一个 pandas DataFrame
data = pd.DataFrame({'X': X, 'Y': Y, 'Z': Z})

# 打印数据的前几行，看看它长什么样
print("--- 示例数据 (前5行) ---")
print(data.head())
print("\n")


# --- 2. 定义因果图 (causal_graph) ---
# 我们使用 networkx 库来创建一个有向图。
# 我们假设的因果图是 X -> Y -> Z，这与我们生成数据的方式是一致的。
causal_graph = nx.DiGraph([('X', 'Y'), ('Y', 'Z')])

# 你也可以这样创建图：
# causal_graph = nx.DiGraph()
# causal_graph.add_nodes_from(['X', 'Y', 'Z'])
# causal_graph.add_edges_from([('X', 'Y'), ('Y', 'Z')])


# --- 3. 运行证伪检验 ---
# 现在我们有了 data 和 causal_graph，可以调用 falsify_graph 了
print("--- 开始运行因果图证伪检验 ---")
result = falsify_graph(causal_graph, data,plot_histogram=True)

# --- 4. 打印并解读结果 ---
print("\n--- 检验结果 ---")
print(result)

print(f"\n图是否可证伪 (Falsifiable): {result.falsifiable}")
print(f"图是否已被证伪 (Falsified): {result.falsified}")

# --- 结果解读 ---
# 在这个例子中，因为我们用来生成数据的真实模型就是 X -> Y -> Z，
# 所以我们期望的结果是：
# 1. Falsifiable: True。 这个图的结构 (X->Y->Z) 隐含了一个清晰的条件独立性假设：X 和 Z 在给定 Y 的条件下是独立的 (X ⟂ Z | Y)。
#    这个约束足够强，可以和其它结构（如 X->Y, X->Z, Y->Z 的全连接图）区分开，因此是可证伪的。
# 2. Falsified: False。 因为数据本身就是根据这个图的结构生成的，所以数据应该与图的假设相符，图不会被证伪。

# Import the necessary libraries and functions for this demo
import numpy as np
import pandas as pd
import networkx as nx
from sklearn.ensemble import GradientBoostingRegressor
from dowhy.gcm.falsify import FalsifyConst, falsify_graph, plot_local_insights, run_validations, apply_suggestions
from dowhy.gcm.independence_test.generalised_cov_measure import generalised_cov_based
from dowhy.gcm.util import plot
from dowhy.gcm.util.general import set_random_seed
from dowhy.gcm.ml import SklearnRegressionModel

# Set random seed
set_random_seed(1332)

# Load example graph and data
g_true = nx.read_gml(f"./dowhy/docs/source/example_notebooks/falsify_g_true.gml")
data = pd.read_csv(f"./dowhy/docs/source/example_notebooks/falsify_data_nonlinear.csv")

# Plot true DAG
print("True DAG")
plot(g_true)

result = falsify_graph(g_true, data, plot_histogram=True)
# Summarize the result
print(result)

print(f"Graph is falsifiable: {result.falsifiable}")
print(f"Graph is falsified: {result.falsified}")

explain.invoke(result).pretty_print()

# Simulate a domain expert with knowledge over some of the edges in the system
g_given = g_true.copy()
g_given.add_edges_from(([('X4', 'X1')]))  # Add wrong edge from X4 -> X1
g_given.remove_edge('X2', 'X0')  # Remove true edge from X2 -> X0
plot(g_given)

# Run evaluation and plot the result using `plot=True`
result = falsify_graph(g_given, data, plot_histogram=True)
print(f"Graph is falsifiable: {result.falsifiable}, Graph is falsified: {result.falsified}")
# explain.invoke(result).pretty_print()

# Plot nodes for which violations of LMCs occured
print('Violations of LMCs')
plot_local_insights(g_given, result, method=FalsifyConst.VALIDATE_LMC)

# Load the data and consensus DAG
data_url = "https://raw.githubusercontent.com/FenTechSolutions/CausalDiscoveryToolbox/master/cdt/data/resources/cyto_full_data.csv"
data_sachs = pd.read_csv(data_url)
g_sachs = nx.read_gml('./dowhy/docs/source/example_notebooks/falsify_sachs.gml')

plot(g_sachs)

"""
基于广义协方差测度的独立性检验定义

本模块定义了使用梯度提升决策树作为预测模型的独立性检验方法。
广义协方差测度（Generalised Covariance Measure, GCM）是一种非参数的独立性检验方法，
它通过比较变量之间的预测误差来评估独立性。

主要功能：
1. create_gradient_boost_regressor: 创建基于梯度提升的回归模型
2. gcm: 执行基于广义协方差测度的独立性检验

使用场景：
- 当数据样本量较大时，相比核方法具有更好的计算效率
- 适用于非线性关系的检测
- 可以处理条件独立性和无条件独立性检验
"""

def create_gradient_boost_regressor(**kwargs) -> SklearnRegressionModel:
    """
    创建梯度提升回归器的工厂函数
    
    该函数封装了sklearn的GradientBoostingRegressor，使其符合DoWhy框架的接口要求。
    梯度提升是一种集成学习方法，通过逐步添加弱学习器来改进预测性能。
    
    参数:
        **kwargs: 传递给GradientBoostingRegressor的参数
                 常用参数包括n_estimators（树的数量）、learning_rate（学习率）等
    
    返回:
        SklearnRegressionModel: 包装后的回归模型对象
    
    示例:
        model = create_gradient_boost_regressor(n_estimators=100, learning_rate=0.1)
    """
    return SklearnRegressionModel(GradientBoostingRegressor(**kwargs))

def gcm(X, Y, Z=None):
    """
    基于广义协方差测度的独立性检验函数
    
    该函数实现了广义协方差测度方法来检验变量间的独立性。
    GCM通过训练预测模型来评估变量间的依赖关系：
    - 如果X和Y独立，那么用X预测Y的误差应该与随机预测相当
    - 如果存在条件变量Z，则检验在给定Z的条件下X和Y的条件独立性
    
    参数:
        X: 第一个变量的数据，可以是单变量或多变量
        Y: 第二个变量的数据，可以是单变量或多变量  
        Z: 条件变量的数据，默认为None表示无条件独立性检验
           当Z不为None时，执行条件独立性检验
    
    返回:
        检验结果对象，包含p值和检验统计量等信息
    
    原理说明:
        广义协方差测度基于以下思想：
        1. 训练模型用X预测Y，计算预测误差
        2. 训练模型用Y预测X，计算预测误差  
        3. 比较这些误差与基准误差，评估独立性
        4. 如果变量独立，预测误差应该接近随机预测的误差
    
    示例:
        # 无条件独立性检验
        result = gcm(data['X'], data['Y'])
        
        # 条件独立性检验
        result = gcm(data['X'], data['Y'], Z=data['Z'])
    """
    return generalised_cov_based(X, Y, Z=Z, 
                                prediction_model_X=create_gradient_boost_regressor,
                                prediction_model_Y=create_gradient_boost_regressor)

"""
对一致性图和数据运行评估

该代码段使用falsify_graph函数对Sachs数据集的一致性有向无环图进行统计验证。
通过100个随机排列来评估图结构的有效性，使用广义协方差测度(GCM)作为独立性
和条件独立性检验方法。

参数说明:
- g_sachs: Sachs数据集的一致性有向无环图
- data_sachs: Sachs蛋白质信号数据
- n_permutations=100: 使用100个随机排列进行评估，平衡计算效率和统计可靠性
- independence_test=gcm: 使用GCM进行无条件独立性检验
- conditional_independence_test=gcm: 使用GCM进行条件独立性检验  
- plot_histogram=True: 绘制评估结果的直方图可视化

该评估将验证给定图结构是否与观测数据一致，并提供统计显著性检验结果。
"""
result_sachs = falsify_graph(g_sachs, data_sachs, n_permutations=100,
                              independence_test=gcm,
                              conditional_independence_test=gcm,
                              plot_histogram=True)
print(result_sachs)
# explain.invoke(result_sachs).pretty_print()

result = falsify_graph(g_sachs, data_sachs, n_permutations=100,independence_test=gcm, conditional_independence_test=gcm, plot_histogram=True, suggestions=True)

print(result)
explain.invoke(result).pretty_print()

# Plot suggestions
plot_local_insights(g_sachs, result, method=FalsifyConst.VALIDATE_CM)

# Apply all suggestions (we could exclude suggestions via `edges_to_keep=[('X3', 'X4')])`)
g_given_pruned = apply_suggestions(g_sachs, result)
# Plot pruned DAG
plot(g_given_pruned)

# 应用后门调整规则分析辛普森悖论案例

# 1. 导入所需库
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import networkx as nx

# 2. 创建数据集
# 根据表1.1的数据创建数据框
data = {
    '性别': ['男', '男', '女', '女'],
    '用药': ['是', '否', '是', '否'],
    '康复人数': [81, 234, 192, 55],
    '总人数': [87, 270, 263, 80]
}
df = pd.DataFrame(data)
df['康复率'] = df['康复人数'] / df['总人数'] * 100

print("原始数据:")
display(df)

# 3. 计算总体效应（未调整）
总体数据 = {
    '用药': ['是', '否'],
    '康复人数': [81 + 192, 234 + 55],
    '总人数': [87 + 263, 270 + 80]
}
总体df = pd.DataFrame(总体数据)
总体df['康复率'] = 总体df['康复人数'] / 总体df['总人数'] * 100

print("未调整的总体效应:")
display(总体df)
print(f"用药组康复率: {总体df.loc[0, '康复率']:.2f}%")
print(f"未用药组康复率: {总体df.loc[1, '康复率']:.2f}%")
print(f"粗效应差异: {总体df.loc[0, '康复率'] - 总体df.loc[1, '康复率']:.2f}%")

# 4. 应用后门调整规则 - 执行步骤详解
print("\n======== 后门调整执行过程 ========")
print("步骤1: 识别后门路径 - 在这个例子中，性别是影响用药分配和康复结果的混杂变量")

print("\n步骤2: 按调整变量(性别)分层")
print("男性子群体:")
display(df[df['性别'] == '男'])
print("\n女性子群体:")
display(df[df['性别'] == '女'])

print("\n步骤3: 在每个分层中计算因果效应")
男性用药组 = df[(df['性别'] == '男') & (df['用药'] == '是')]['康复率'].values[0]
男性对照组 = df[(df['性别'] == '男') & (df['用药'] == '否')]['康复率'].values[0]
男性效应 = 男性用药组 - 男性对照组
print(f"男性中: 用药组康复率 {男性用药组:.2f}% - 对照组康复率 {男性对照组:.2f}% = 效应 {男性效应:.2f}%")

女性用药组 = df[(df['性别'] == '女') & (df['用药'] == '是')]['康复率'].values[0]
女性对照组 = df[(df['性别'] == '女') & (df['用药'] == '否')]['康复率'].values[0]
女性效应 = 女性用药组 - 女性对照组
print(f"女性中: 用药组康复率 {女性用药组:.2f}% - 对照组康复率 {女性对照组:.2f}% = 效应 {女性效应:.2f}%")

print("\n步骤4: 计算各层的权重")
男性总人数 = df[df['性别'] == '男']['总人数'].sum()
女性总人数 = df[df['性别'] == '女']['总人数'].sum()
总人数 = 男性总人数 + 女性总人数
男性权重 = 男性总人数 / 总人数
女性权重 = 女性总人数 / 总人数
print(f"男性总人数: {男性总人数}, 占比: {男性权重:.4f}")
print(f"女性总人数: {女性总人数}, 占比: {女性权重:.4f}")

print("\n步骤5: 计算加权平均效应")
调整后效应 = 男性权重 * 男性效应 + 女性权重 * 女性效应
print(f"调整后效应 = {男性权重:.4f} × {男性效应:.2f}% + {女性权重:.4f} × {女性效应:.2f}% = {调整后效应:.2f}%")

# 结论解释：
print("\n======== 结果比较与分析 ========")
print(f"未调整效应: {总体df.loc[0, '康复率'] - 总体df.loc[1, '康复率']:.2f}%")
print(f"后门调整后效应: {调整后效应:.2f}%")
print("\n结论分析:")
print("1. 未调整时观察到的是负效应(-5.14%)，表明药物似乎降低了康复率")
print("2. 使用性别作为后门调整变量后，发现药物实际上对男性和女性都有正面效应")
print("3. 调整后的加权平均效应为正值(+3.78%)，表明药物确实有治疗效果")
print("4. 这个案例完美展示了后门调整如何解决混杂偏差问题")
print("5. 辛普森悖论在这里得到了清晰的解释：混杂变量(性别)的不平衡分布导致了总体效应的反转")


from dowhy import CausalModel

# 使用已有的药物试验数据df
# 创建一个更适合DoWhy处理的数据集
# 我们需要生成每个个体的记录，而不是汇总数据
individual_records = []

for _, row in df.iterrows():
    # 对于康复的人
    for i in range(int(row['康复人数'])):
        individual_records.append({
            '性别': row['性别'],
            '用药': 1 if row['用药'] == '是' else 0,
            '康复': 1
        })
    
    # 对于未康复的人
    for i in range(int(row['总人数'] - row['康复人数'])):
        individual_records.append({
            '性别': row['性别'],
            '用药': 1 if row['用药'] == '是' else 0,
            '康复': 0
        })

# 创建个体级别数据集
individual_df = pd.DataFrame(individual_records)

# 将性别转换为数值变量
individual_df['性别_数值'] = individual_df['性别'].map({'男': 1, '女': 0})

# 使用DoWhy创建因果模型
# 首先定义因果图
causal_graph = """
digraph {
    性别_数值 -> 用药;
    性别_数值 -> 康复;
    用药 -> 康复;
}
"""

model = CausalModel(
    data=individual_df,
    treatment='用药',
    outcome='康复',
    graph=causal_graph
)

model.view_model()
# 识别因果效应
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)


# 抑制警告信息
import warnings
warnings.filterwarnings("ignore", message="Series.__getitem__ treating keys as positions is deprecated")

# 或者，如果想更具体地抑制特定的FutureWarning
# from pandas.errors import FutureWarning
# warnings.filterwarnings("ignore", category=FutureWarning, 
#                        message="Series.__getitem__ treating keys as positions is deprecated")


# 估计因果效应
estimate = model.estimate_effect(identified_estimand,
                                method_name="backdoor.linear_regression")
print("\n使用线性回归的后门调整估计结果:")
print(f"{estimate.value*100:.2f}%")

%load_ext autoreload
%autoreload 2
%reload_ext autoreload
import numpy as np
import pandas as pd
import logging

import dowhy
from dowhy import CausalModel
import dowhy.datasets

data = dowhy.datasets.linear_dataset(beta=10,
        num_common_causes=5,
        num_instruments = 2,
        num_treatments=1,
        num_samples=10000,
        treatment_is_binary=True,
        outcome_is_binary=False,
        stddev_treatment_noise=10)
df = data["df"]
df

# With graph
model=CausalModel(
        data = df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        graph=data["gml_graph"],
        instruments=data["instrument_names"]
        )


model.view_model()
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand.backdoor_variables)

model.view_model()
identified_estimand = model.identify_effect(method_name="maximal-adjustment")
identified_estimand.backdoor_variables

model.view_model()
identified_estimand = model.identify_effect(method_name="minimal-adjustment")
identified_estimand.backdoor_variables

model.view_model()
identified_estimand = model.identify_effect(method_name="exhaustive-search")
identified_estimand.backdoor_variables

model.view_model()
identified_estimand = model.identify_effect()
identified_estimand.backdoor_variables

# 绘制前门调整的因果图示例
import matplotlib.pyplot as plt
import networkx as nx

# 创建有向图
G = nx.DiGraph()

# 添加节点
nodes = ['U', 'X', 'M', 'Y']
G.add_nodes_from(nodes)

# 添加边
edges = [
    ('U', 'X'),  # 未观测混杂影响处理
    ('U', 'Y'),  # 未观测混杂影响结果
    ('X', 'M'),  # 处理影响中介
    ('M', 'Y')   # 中介影响结果
]
G.add_edges_from(edges)

# 设置图形大小
plt.figure(figsize=(10, 6))

# 定义节点位置
pos = {
    'U': (1, 2),    # 未观测混杂在上方
    'X': (0, 1),    # 处理在左下
    'M': (1, 1),    # 中介在中间
    'Y': (2, 1)     # 结果在右下
}

# 绘制节点
nx.draw_networkx_nodes(G, pos, 
                      node_color=['red', 'lightblue', 'lightgreen', 'orange'],
                      node_size=2000,
                      alpha=0.8)

# 绘制边
# 实线边（可观测路径）
observable_edges = [('X', 'M'), ('M', 'Y')]
nx.draw_networkx_edges(G, pos, 
                      edgelist=observable_edges,
                      edge_color='blue',
                      width=2,
                      alpha=0.8,
                      arrowsize=20)

# 虚线边（未观测混杂路径）
unobserved_edges = [('U', 'X'), ('U', 'Y')]
nx.draw_networkx_edges(G, pos, 
                      edgelist=unobserved_edges,
                      edge_color='red',
                      width=2,
                      alpha=0.6,
                      style='dashed',
                      arrowsize=20)

# 添加节点标签
labels = {
    'U': 'U\n(Unobserved\nConfounder)',
    'X': 'X\n(Treatment)',
    'M': 'M\n(Mediator)',
    'Y': 'Y\n(Outcome)'
}
nx.draw_networkx_labels(G, pos, labels, font_size=10, font_weight='bold')

# 添加路径说明
plt.text(0.5, 0.5, 'Front-door Path: X → M → Y', 
         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
         fontsize=12, ha='center')

plt.text(1.5, 1.7, 'Back-door Path: X ← U → Y', 
         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7),
         fontsize=12, ha='center')

plt.title('Front-door Adjustment Causal Graph', fontsize=16, fontweight='bold', pad=20)
plt.axis('off')
plt.tight_layout()
plt.show()

# 打印前门准则的三个条件
print("前门准则的三个条件:")
print("1. 完全中介条件: M完全截获从X到Y的所有因果路径")
print("2. M无后门路径: 没有从X到M的未被阻断的后门路径") 
print("3. Y无新后门路径: 所有从M到Y的后门路径都被X阻断")


"""
创建一个前门调整示例

本代码演示了如何使用前门调整方法估计因果效应。
前门调整适用于存在未观测混杂因素但有完全中介变量的情况。

在这个例子中，我们模拟了以下因果图：
U (未观测) → X → M → Y
         ↘     ↗
其中：
- U 是未观测的混杂变量（如遗传因素）
- X 是处理变量（如药物剂量）
- M 是中介变量（如血药浓度）
- Y 是结果变量（如治疗效果）

前门调整允许我们在无法直接控制 U 的情况下，
通过分析 X→M 和 M→Y 的关系来正确估计 X→Y 的因果效应。
"""

import numpy as np
import pandas as pd
from dowhy import CausalModel
import networkx as nx
import matplotlib.pyplot as plt
from scipy import stats
import statsmodels.api as sm
import warnings

# 抑制警告信息
warnings.filterwarnings('ignore')

# 生成模拟数据
# 设置随机种子确保可重复性
np.random.seed(42)

# 样本量
n = 1000

# 1. 生成未观测的混杂变量 U
U = np.random.normal(0, 1, n)

# 2. 生成处理变量 X，它受 U 影响
X = 0.8 * U + np.random.normal(0, 1, n)

# 3. 生成中介变量 M，它只受 X 影响（满足前门标准的关键）
M = 0.7 * X + np.random.normal(0, 0.5, n)

# 4. 生成结果变量 Y，它受 M 和 U 影响
Y = 0.6 * M + 1.2 * U + np.random.normal(0, 0.5, n)

# 创建数据框
df_frontdoor = pd.DataFrame({
    'X': X,
    'M': M,
    'Y': Y
})

print("模拟数据集的前5行:")
print(df_frontdoor.head())

# 创建前门调整的因果图
gml_frontdoor = """graph [
  directed 1
  node [
    id 0
    label "X"
  ]
  node [
    id 1
    label "M" 
  ]
  node [
    id 2
    label "Y"
  ]
  node [
    id 3
    label "U"
    observed 0
  ]
  edge [
    source 0
    target 1
  ]
  edge [
    source 1
    target 2
  ]
  edge [
    source 3
    target 0
  ]
  edge [
    source 3
    target 2
  ]
]"""

# 创建因果模型
model_frontdoor = CausalModel(
    data=df_frontdoor,
    treatment='X',
    outcome='Y',
    graph=gml_frontdoor
)

# 查看模型
model_frontdoor.view_model()

"""
前门调整（Front-door Adjustment）手动实现
前门调整是处理无法观测的混杂因素的因果推断方法，分三步实施：
1. 估计 X→M 的因果效应：X 对中介变量 M 的直接影响
2. 估计 M→Y 的因果效应：控制 X 后，M 对结果 Y 的影响
3. 计算总效应：将上述两个效应相乘得到 X→Y 的总因果效应

数学表示：
- 总效应 = E[Y|do(X=x)] = ∑_m P(M=m|X=x) × E[Y|X=x,M=m]
- 简化后可表示为两个回归系数之积
"""
# 步骤1：估计 X 对 M 的因果效应
model_x_m = sm.OLS(df_frontdoor['M'], sm.add_constant(df_frontdoor['X'])).fit()
beta_xm_corrected = model_x_m.params['X']  # 获取X对M的影响系数
print("\n步骤1: X 对 M 的效应 (系数):", beta_xm_corrected)


# 步骤2：估计 M 对 Y 的因果效应，同时控制 X
model_m_y_controlled = sm.OLS(df_frontdoor['Y'], sm.add_constant(df_frontdoor[['M', 'X']])).fit()
alpha_my_corrected = model_m_y_controlled.params['M']  # 获取控制X后，M对Y的影响系数
print("\n步骤2: M 对 Y 的效应 (控制 X 后, M 的系数):", alpha_my_corrected)


# 步骤3：计算 X 对 Y 的前门效应（总因果效应）
frontdoor_effect_corrected_manual = beta_xm_corrected * alpha_my_corrected
print(f"\n手动计算的前门效应 (修正后): {frontdoor_effect_corrected_manual:.4f}")


"""
使用前门准则（Front-door Criterion）识别因果效应

前门准则是一种因果推断方法，允许我们在存在未观测混杂因素的情况下识别因果效应。
它通过中介变量M来识别X对Y的因果效应，要求：
1. M完全调节了X对Y的影响（X→M→Y是唯一路径）
2. 不存在X和M之间的未观测混杂
3. 所有M到Y的路径都已被阻断（通过控制X）

数学表示：
P(Y|do(X=x)) = ∑_m P(m|x) ∑_x' P(Y|m,x')P(x')

其中：
- P(m|x)：X对M的影响（第一阶段）
- P(Y|m,x')：控制X后M对Y的影响（第二阶段）
- P(x')：X的边际分布
"""

# 使用前门调整方法识别因果效应
identified_estimand_frontdoor = model_frontdoor.identify_effect(proceed_when_unidentifiable=True)
print("识别的估计量：", identified_estimand_frontdoor)


"""
前门调整因果效应估计
-------------------
本部分使用DoWhy库实现前门调整，并将结果与手动计算和真实效应进行比较。

前门调整方法要点：
1. 需要指定正确的方法名称格式：'frontdoor.estimator_name'
2. 在DoWhy中，'two_stage_regression'是实现前门调整的常用估计器
3. 前门效应计算逻辑：X→M的效应 × M→Y的效应(控制X)

数学表示：
前门效应 = P(M|do(X)) × P(Y|do(M),X)
       = β_{X→M} × α_{M→Y|X}
"""

# 使用DoWhy库实现前门调整
causal_estimate = model_frontdoor.estimate_effect(
        identified_estimand_frontdoor,
        method_name="frontdoor.two_stage_regression"  # 前门方法需要使用格式：frontdoor.estimator_name
    )
print(f"\nDoWhy估计的前门效应: {causal_estimate.value:.4f}")


# 手动计算的前门效应
frontdoor_effect_corrected_manual = beta_xm_corrected * alpha_my_corrected
print(f"\n手动计算的前门效应 (修正后): {frontdoor_effect_corrected_manual:.4f}")

# 计算真实的因果效应
true_effect = 0.7 * 0.6
print(f"\n真实的因果效应: {true_effect:.4f}")

%load_ext autoreload
%autoreload 2
import numpy as np
import pandas as pd
import patsy as ps

from statsmodels.sandbox.regression.gmm import IV2SLS
import os, sys
from dowhy import CausalModel

n_points = 1000
education_abilty = 1
education_voucher = 2
income_abilty = 2
income_education = 4


# confounder
ability = np.random.normal(0, 3, size=n_points)

# instrument
voucher = np.random.normal(2, 1, size=n_points)

# treatment
education = np.random.normal(5, 1, size=n_points) + education_abilty * ability +\
            education_voucher * voucher

# outcome
income = np.random.normal(10, 3, size=n_points) +\
         income_abilty * ability + income_education * education

# build dataset (exclude confounder `ability` which we assume to be unobserved)
data = np.stack([education, income, voucher]).T
df = pd.DataFrame(data, columns = ['education', 'income', 'voucher'])


#Step 1: Model
model=CausalModel(
        data = df,
        treatment='education',
        outcome='income',
        common_causes=['U'],
        instruments=['voucher']
        )
model.view_model()


# Step 2: Identify
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

"""
基础因果图分析案例

本案例展示如何使用DoWhy库分析最简单的因果关系图，其中：
- 处理变量T直接影响结果变量Y
- 无其他中介或混杂变量

主要步骤：
1. 创建空数据集和因果图定义
2. 初始化因果模型
3. 可视化因果图
4. 使用ID算法进行因果效应识别

数学关系表示为：$Y = f(T)$
"""

from dowhy import CausalModel
import pandas as pd
import numpy as np
from IPython.display import Image, display

# 创建基础数据结构
treatment = "T"  # 处理变量名称
outcome = "Y"    # 结果变量名称
causal_graph = "digraph{T->Y;}"  # 定义因果图：T→Y的单向关系

# 初始化空数据框（仅包含T和Y两列）
df = pd.DataFrame(columns=[treatment, outcome])

# 直接初始化因果模型
causal_model = CausalModel(
    data=df,
    treatment=treatment,
    outcome=outcome,
    graph=causal_graph
)

# 可视化因果图（输出图形见notebook_cell_output_0）
causal_model.view_model()

# 使用ID算法识别因果效应
identified_effect = causal_model.identify_effect(method_name="id-algorithm")

print("\n因果识别结果(ID算法):")
print(identified_effect)


"""
案例2：循环因果图验证

本案例演示当因果图中存在循环路径时（T→Y且Y→T），ID算法无法识别因果效应的情况。
关键点说明：
1. 循环图违反了DAG（有向无环图）的基本假设
2. 数学上表示为：$Y = f(T)$ 且 $T = g(Y)$
3. 实际应用场景：如价格与需求相互影响的经济模型
"""

treatment = "T"  # 处理变量名称
outcome = "Y"    # 结果变量名称
causal_graph = "digraph{T->Y; Y->T;}"  # 定义双向循环因果图

# 创建空数据框（仅包含T和Y两列）
df = pd.DataFrame(columns=[treatment, outcome])

"""
初始化因果模型
参数说明：
- data: 包含变量的数据框
- treatment: 处理变量名
- outcome: 结果变量名  
- graph: 因果图DOT语言描述
"""
causal_model = CausalModel(
    data=df,
    treatment=treatment,
    outcome=outcome,
    graph=causal_graph
)

# 可视化因果图（将显示T和Y的双向箭头）
causal_model.view_model()

try:
    """
    尝试使用ID算法识别因果效应
    预期会失败，因为:
    1. ID算法要求输入图为DAG
    2. 循环图会导致无限递归问题
    """
    identified_estimand = causal_model.identify_effect(
        method_name="id-algorithm"
    )
    print("\n因果识别结果(ID算法):")
    print(identified_estimand)
except Exception as e:
    print("识别失败：因果图必须是有向无环图(DAG)")
    print(f"技术细节：{str(e)}")


"""
因果推断案例：中介变量模型

本案例展示如何使用DoWhy库处理包含中介变量X1的因果图结构：
T → X1 → Y

关键组件说明：
- treatment: 处理变量T
- outcome: 结果变量Y  
- variables: 中介变量X1
- causal_graph: 使用DOT语言定义的因果图
- df: 空数据框（仅包含变量结构）
"""

treatment = "T"  # 处理变量名称
outcome = "Y"    # 结果变量名称
variables = ["X1"]  # 中介变量列表

"""
定义因果图结构：
T → X1 → Y
使用DOT语法描述变量间的因果关系
"""
causal_graph = "digraph{T->X1;X1->Y;}"

# 创建包含所有变量的空数据框
columns = [treatment] + [outcome] + variables
df = pd.DataFrame(columns=columns)

"""
初始化因果模型：
- data: 包含变量的数据框
- treatment: 处理变量名
- outcome: 结果变量名
- graph: 因果图DOT描述
"""
causal_model = CausalModel(
    data=df,
    treatment=treatment,
    outcome=outcome,
    graph=causal_graph
)

# 可视化因果图结构
causal_model.view_model()

"""
使用ID算法进行因果识别：
该方法可以自动识别中介变量结构中的因果效应
"""
identified_estimand = causal_model.identify_effect(
    method_name="id-algorithm"
)

print("\nID算法识别结果：")
print(identified_estimand)


"""
案例4：存在直接和间接影响的因果图分析

本案例展示处理变量T通过两条路径影响结果变量Y：
1. 直接路径：T → Y
2. 间接路径：T → X1 → Y

使用DOT语法定义因果图结构：
digraph{
    T->Y;  # 直接影响路径
    T->X1; # 中介路径第一部分 
    X1->Y; # 中介路径第二部分
}

变量说明：
- T: 处理变量（treatment）
- Y: 结果变量（outcome） 
- X1: 中介变量（mediator）
"""

# 定义核心变量
treatment = "T"
outcome = "Y"
variables = ["X1"]

# 构建因果图DOT描述
causal_graph = "digraph{T->Y;T->X1;X1->Y;}"

# 创建包含所有变量的空数据框
columns = list(treatment) + list(outcome) + list(variables)
df = pd.DataFrame(columns=columns)

"""
初始化因果模型：
参数说明：
- data: 包含变量的数据框
- treatment: 处理变量名称
- outcome: 结果变量名称  
- graph: 因果图的DOT描述
"""
causal_model = CausalModel(
    data=df,
    treatment=treatment,
    outcome=outcome,
    graph=causal_graph
)

# 可视化因果图结构
causal_model.view_model()

"""
使用ID算法进行因果效应识别：
该方法可以自动识别包含中介变量的因果效应
输出将包含：
- 可识别性结果
- 需要调整的变量集
- 识别方法详情
"""
identified_estimand = causal_model.identify_effect(
    method_name="id-algorithm"
)

print("\nID算法识别结果：")
print(identified_estimand)


"""
构建因果分析案例5：混杂变量与工具变量共存场景

该案例展示当存在：
- 混杂变量X1：同时影响处理变量T和结果变量Y
- 工具变量X2：仅影响处理变量T时的因果效应识别

变量说明：
$treatment$: 处理变量名称，本例为"T"
$outcome$: 结果变量名称，本例为"Y" 
$variables$: 协变量列表，包含["X1","X2"]

因果图使用DOT语法描述：
T → Y 表示处理直接影响结果
X1 → T 和 X1 → Y 表示混杂效应
X2 → T 表示工具变量效应
"""

treatment = "T"
outcome = "Y"
variables = ["X1", "X2"]
causal_graph = "digraph{T->Y;X1->T;X1->Y;X2->T;}"

"""
初始化空数据框用于模型构建
列包含所有变量：处理变量、结果变量及协变量
"""
columns = list(treatment) + list(outcome) + list(variables)
df = pd.DataFrame(columns=columns)

"""
因果模型初始化：
参数说明：
data: 包含分析变量的数据框
treatment: 处理变量名称
outcome: 结果变量名称
graph: 因果图的结构化描述
"""
causal_model = CausalModel(
    data=df,
    treatment=treatment,
    outcome=outcome,
    graph=causal_graph
)

"""可视化因果图结构"""
causal_model.view_model()

"""
使用ID算法进行因果效应识别：
该方法可自动处理：
- 混杂变量调整
- 工具变量利用
- 中介效应分解
"""
identified_estimand = causal_model.identify_effect(
    method_name="id-algorithm"
)

print("\nID算法识别结果：")
print(identified_estimand)


"""
因果效应分析示例代码

该代码演示如何使用dowhy库进行以下操作：
1. 创建包含处理变量、结果变量和协变量的空数据框
2. 初始化因果模型并指定因果图结构
3. 可视化因果图
4. 使用ID算法识别因果效应

变量说明：
- treatment: 处理变量名称，本例中为"T"
- outcome: 结果变量名称，本例中为"Y" 
- variables: 协变量列表，本例中仅包含"X1"
- causal_graph: 因果图结构，使用DOT语法描述
"""

# 定义分析变量
treatment = "T"
outcome = "Y"
variables = ["X1"]

# 指定因果图结构
"""
因果图说明：
- T: 处理变量
- X1: 协变量
- Y: 结果变量
箭头表示因果关系：
- X1 -> Y 表示X1直接影响Y
- T; 表示T是独立节点
"""
causal_graph = "digraph{T;X1->Y;}"

# 创建空数据框
"""
数据框将包含三列：
- T列: 存储处理变量值
- Y列: 存储结果变量值  
- X1列: 存储协变量值
"""
columns = list(treatment) + list(outcome) + list(variables)
df = pd.DataFrame(columns=columns)

# 初始化因果模型
"""
参数说明：
- data: 包含分析变量的数据框
- treatment: 处理变量名称
- outcome: 结果变量名称  
- graph: 因果图的结构化描述
"""
causal_model = CausalModel(
    data=df,
    treatment=treatment,
    outcome=outcome,
    graph=causal_graph
)

# 可视化因果图
"""
调用view_model()方法生成因果图可视化
图形将显示:
- 节点: T, X1, Y
- 边: X1指向Y的箭头
"""
causal_model.view_model()

# 使用ID算法识别因果效应
"""
ID算法说明：
- 自动识别可估计的因果效应
- 处理混杂变量调整
- 返回可估计的因果量
"""
identified_estimand = causal_model.identify_effect(
    method_name="id-algorithm"
)

# 输出识别结果
print("\n使用ID算法识别的因果效应结果：")
print(identified_estimand)


import numpy as np

from dowhy import CausalModel
import dowhy.datasets

data = dowhy.datasets.linear_dataset(beta=10,
        num_common_causes=5,
        num_instruments = 2,
        num_effect_modifiers=1,
        num_samples=5000,
        treatment_is_binary=True,
        stddev_treatment_noise=10,
        num_discrete_common_causes=1)
df = data["df"]

# With graph
model=CausalModel(
        data = df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        graph=data["gml_graph"]
        )

model.view_model()

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

import warnings
import pandas as pd

# 暂时抑制 FutureWarning
with warnings.catch_warnings():
    warnings.filterwarnings("ignore", category=FutureWarning)
    # 解决 pd.groupby 的 observed 参数警告
    pd.options.future.infer_string = True
    
    causal_estimate_reg = model.estimate_effect(identified_estimand,
            method_name="backdoor.linear_regression",
            test_significance=True)
print(causal_estimate_reg)
print("Causal Estimate is " + str(causal_estimate_reg.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_reg.interpret(method_name="textual_effect_interpreter")

causal_estimate_dmatch = model.estimate_effect(identified_estimand,
                                              method_name="backdoor.distance_matching",
                                              target_units="att",
                                              method_params={'distance_metric':"minkowski", 'p':2})
print(causal_estimate_dmatch)
print("Causal Estimate is " + str(causal_estimate_dmatch.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_dmatch.interpret(method_name="textual_effect_interpreter")

causal_estimate_strat = model.estimate_effect(identified_estimand,
                                              method_name="backdoor.propensity_score_stratification",
                                              target_units="att")
print(causal_estimate_strat)
print("Causal Estimate is " + str(causal_estimate_strat.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_strat.interpret(method_name="textual_effect_interpreter")

causal_estimate_match = model.estimate_effect(identified_estimand,
                                              method_name="backdoor.propensity_score_matching",
                                              target_units="atc")
print(causal_estimate_match)
print("Causal Estimate is " + str(causal_estimate_match.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_match.interpret(method_name="textual_effect_interpreter")

causal_estimate_ipw = model.estimate_effect(identified_estimand,
                                            method_name="backdoor.propensity_score_weighting",
                                            target_units = "ate",
                                            method_params={"weighting_scheme":"ips_weight"})
print(causal_estimate_ipw)
print("Causal Estimate is " + str(causal_estimate_ipw.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_ipw.interpret(method_name="textual_effect_interpreter")

causal_estimate_iv = model.estimate_effect(identified_estimand,
        method_name="iv.instrumental_variable", method_params = {'iv_instrument_name': 'Z0'})
print(causal_estimate_iv)
print("Causal Estimate is " + str(causal_estimate_iv.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_iv.interpret(method_name="textual_effect_interpreter")

causal_estimate_regdist = model.estimate_effect(identified_estimand,
        method_name="iv.regression_discontinuity",
        method_params={'rd_variable_name':'Z1',
                       'rd_threshold_value':0.5,
                       'rd_bandwidth': 0.15})
print(causal_estimate_regdist)
print("Causal Estimate is " + str(causal_estimate_regdist.value))
print("Real effect: " + str(data["ate"]))

# Textual Interpreter
interpretation = causal_estimate_regdist.interpret(method_name="textual_effect_interpreter")

import pandas as pd
from IPython.display import display, Markdown

# 构建因果效应估计方法总结表
methods_summary = [
    {
        "方法": "回归调整（Regression Adjustment）",
        "核心思想": "通过回归模型控制混杂变量，直接估计处理效应",
        "适用场景": "混杂变量可观测，线性/非线性关系均可",
        "关键假设": "无未观测混杂，模型正确指定"
    },
    {
        "方法": "倾向得分匹配（Propensity Score Matching）",
        "核心思想": "根据处理概率将样本配对，比较处理组与对照组",
        "适用场景": "高维混杂，样本量较大",
        "关键假设": "无未观测混杂，匹配充分"
    },
    {
        "方法": "工具变量法（Instrumental Variable）",
        "核心思想": "利用与处理相关但与结果无关的工具变量估计因果效应",
        "适用场景": "存在未观测混杂，工具变量可用",
        "关键假设": "工具变量相关性、排除性、独立性"
    },
    {
        "方法": "回归断点（Regression Discontinuity）",
        "核心思想": "利用断点附近的局部随机性估计因果效应",
        "适用场景": "存在明确断点，断点附近样本充足",
        "关键假设": "局部随机性，连续性，单调性"
    },
    {
        "方法": "前门准则（Front-door Criterion）",
        "核心思想": "通过中介变量阻断未观测混杂路径",
        "适用场景": "存在未观测混杂，满足前门条件",
        "关键假设": "中介变量可观测，满足前门准则"
    },
    {
        "方法": "差分法（Difference-in-Differences）",
        "核心思想": "利用处理前后变化与对照组对比消除混杂",
        "适用场景": "有时间序列数据，处理分组明确",
        "关键假设": "平行趋势假设"
    }
]

df_methods = pd.DataFrame(methods_summary)
display(Markdown("**因果效应估计方法总结表**"))
display(df_methods)


# importing required libraries
import dowhy
from dowhy import CausalModel
import pandas as pd
import numpy as np

data= pd.read_csv("ihdp_npci_1.csv", header = None)
col =  ["treatment", "y_factual", "y_cfactual", "mu0", "mu1" ,]
for i in range(1,26):
    col.append("x"+str(i))
data.columns = col
data = data.astype({"treatment":'bool'}, copy=False)
data.head()

# Create a causal model from the data and given common causes.
model=CausalModel(
        data = data,
        treatment='treatment',
        outcome='y_factual',
        common_causes=["x"+str(i) for  i in range(1,26)]
        )
model.view_model()


# model is an instance of CausalModel
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True, method_name="maximal-adjustment")
print(identified_estimand)
# explain.invoke(identified_estimand).pretty_print()

estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.linear_regression", test_significance=True
)
print(estimate)
# explain.invoke(estimate).pretty_print()

print("Causal Estimate is " + str(estimate.value))
data_1 = data[data["treatment"]==1]
data_0 = data[data["treatment"]==0]

print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))


estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_matching"
)

print("Causal Estimate is " + str(estimate.value))

print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))


estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_stratification", method_params={'num_strata':50, 'clipping_threshold':5}
)

print("Causal Estimate is " + str(estimate.value))
print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))



estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_weighting"
)

print("Causal Estimate is " + str(estimate.value))

print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))


causal_estimate_dmatch = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.distance_matching",
                                             target_units="att",
                                             method_params={'distance_metric':"minkowski", 'p':2})

print(causal_estimate_dmatch)
# explain.invoke(causal_estimate_dmatch).pretty_print()

%load_ext autoreload
%autoreload 2
import numpy as np
import pandas as pd
import patsy as ps

from statsmodels.sandbox.regression.gmm import IV2SLS
import os, sys
from dowhy import CausalModel

n_points = 1000
education_abilty = 1
education_voucher = 2
income_abilty = 2
income_education = 4


# confounder
ability = np.random.normal(0, 3, size=n_points)

# instrument
voucher = np.random.normal(2, 1, size=n_points)

# treatment
education = np.random.normal(5, 1, size=n_points) + education_abilty * ability +\
            education_voucher * voucher

# outcome
income = np.random.normal(10, 3, size=n_points) +\
         income_abilty * ability + income_education * education

# build dataset (exclude confounder `ability` which we assume to be unobserved)
data = np.stack([education, income, voucher]).T
df = pd.DataFrame(data, columns = ['education', 'income', 'voucher'])

#Step 1: Model
model=CausalModel(
        data = df,
        treatment='education',
        outcome='income',
        common_causes=['Ability'],
        instruments=['voucher']
        )
model.view_model()


# Step 2: Identify
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)
# explain.invoke(identified_estimand).pretty_print()

# Step 3: Estimate
#Choose the second estimand: using IV
estimate = model.estimate_effect(identified_estimand,
        method_name="iv.instrumental_variable", test_significance=True)

print(estimate)
# explain.invoke(estimate).pretty_print()

"""
本代码块用于生成用于因果推断实验的模拟数据集，并输出数据的基本信息。

主要步骤说明：

1. 自动重载模块功能的激活，便于在交互式环境下实时更新代码。
2. 导入常用科学计算与因果推断相关的库，包括numpy、pandas、dowhy、econml等。
3. 关闭警告信息，保证输出界面整洁。
4. 通过dowhy.datasets.linear_dataset函数生成线性结构方程模型数据集。该函数支持自定义因果效应强度、混杂变量、工具变量、效应修饰变量等参数，适用于多种因果推断方法的实验。
5. 输出生成的数据集前五行，便于快速了解数据结构。
6. 输出真实的因果效应（平均处理效应，ATE），用于后续模型估计结果的对比和验证。

参数说明：

- BETA：设定因果效应的真实强度
- num_common_causes：混杂变量的数量
- num_samples：样本数量
- num_instruments：工具变量的数量
- num_effect_modifiers：效应修饰变量的数量
- num_treatments：处理变量的数量
- treatment_is_binary：处理变量是否为二值型
- num_discrete_common_causes：离散型混杂变量数量
- num_discrete_effect_modifiers：离散型效应修饰变量数量
- one_hot_encode：是否对分类变量进行独热编码

返回值说明：

- data['df']：生成的数据集，类型为pandas.DataFrame
- data['ate']：真实的平均处理效应（Average Treatment Effect）

"""

# 激活自动重载扩展，便于交互式开发环境下自动更新已修改的模块
%load_ext autoreload
%autoreload 2

import numpy as np
import pandas as pd
import logging

import dowhy
from dowhy import CausalModel
import dowhy.datasets

import econml
import warnings
warnings.filterwarnings('ignore')

# 设置真实因果效应强度
BETA = 10

# 生成线性结构方程模型数据集
data = dowhy.datasets.linear_dataset(
    beta=BETA,
    num_common_causes=4,
    num_samples=10000,
    num_instruments=2,
    num_effect_modifiers=2,
    num_treatments=1,
    treatment_is_binary=False,
    num_discrete_common_causes=2,
    num_discrete_effect_modifiers=0,
    one_hot_encode=False
)

df = data['df']

# 输出数据集的前五行，便于观察变量结构
print("数据集前五行：")
print(df.head())

# 输出真实的平均因果效应（ATE），用于后续模型估计结果的对比
print("真实的平均因果效应（ATE）为：", data["ate"])

"""
本段代码用于构建因果推断模型，并可视化因果图结构。

- CausalModel类是DoWhy库的核心对象，用于封装因果建模的四大步骤：建模、识别、估计和检验。
- 初始化CausalModel时需传入观测数据、处理变量名、结果变量名以及因果图结构（GML格式）。
- view_model方法可将因果图以图形方式展示，便于理解变量间的因果关系。

参数说明：
    data: 观测数据集，类型为pandas.DataFrame，包含所有变量
    treatment: 处理变量的名称，字符串或字符串列表
    outcome: 结果变量的名称，字符串
    graph: 因果图的GML字符串，描述变量间的因果结构

示例：
    设$X$为处理变量，$Y$为结果变量，$Z$为混杂变量，GML图描述了$X$、$Y$、$Z$之间的因果路径。
"""

model = CausalModel(
    data=data["df"],
    treatment=data["treatment_name"],
    outcome=data["outcome_name"],
    graph=data["gml_graph"]
)

print("因果模型已成功构建。下方将展示因果图结构，便于直观理解变量间的因果路径。")
model.view_model()

identified_estimand= model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

"""
本段代码通过线性回归方法估计条件平均处理效应（CATE）。

- estimate_effect方法用于根据已识别的因果效应表达式，结合指定的估计方法，计算处理变量对结果变量的因果影响。
- 这里采用"backdoor.linear_regression"方法，即利用线性回归模型进行后门调整，估计处理变量从0变为1时对结果变量的平均因果效应。
- control_value参数指定对照组（处理变量取值为0），treatment_value参数指定处理组（处理变量取值为1）。
- 返回结果为线性回归估计的因果效应量。

参数说明：
    identified_estimand: 已识别的因果效应表达式
    method_name: 估计方法名称，此处为线性回归
    control_value: 处理变量的对照组取值
    treatment_value: 处理变量的处理组取值

输出说明：
    linear_estimate: 线性回归方法估计得到的平均因果效应（ATE）

数学表达：
    估计的ATE = E[Y | treatment=1] - E[Y | treatment=0]
"""

linear_estimate = model.estimate_effect(
    identified_estimand,
    method_name="backdoor.linear_regression",
    control_value=0,
    treatment_value=1
)

print("使用线性回归方法估计的平均因果效应（ATE）为：", linear_estimate)

"""
本段代码演示如何利用EconML库中的DML（Double Machine Learning，双重机器学习）方法，结合梯度提升回归器和Lasso回归，估计条件平均处理效应（CATE）。

主要步骤与参数说明如下：

- PolynomialFeatures: 用于对特征进行多项式扩展，degree=1表示仅包含原始特征，不引入高阶项，include_bias=False表示不添加常数项。
- LassoCV: 交叉验证的Lasso回归，用于最终的因果效应回归，fit_intercept=False表示不拟合截距项。
- GradientBoostingRegressor: 梯度提升回归器，分别用于预测结果变量（model_y）和处理变量（model_t）。
- method_name: 指定因果效应估计方法为"backdoor.econml.dml.DML"，即EconML的DML实现。
- control_value与treatment_value: 分别指定处理变量的对照组（0）和处理组（1）取值。
- target_units: 通过lambda函数筛选目标单元，这里仅对X0大于1的样本估计CATE。
- confidence_intervals: 是否计算置信区间，此处设为False。
- method_params: 传递给DML估计器的初始化参数（init_params）和拟合参数（fit_params）。

输出结果为dml_estimate，表示在指定条件下估计得到的条件平均处理效应。

数学表达式：
CATE = E[Y | treatment=1, X0>1] - E[Y | treatment=0, X0>1]
"""

from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

dml_estimate = model.estimate_effect(
    identified_estimand,
    method_name="backdoor.econml.dml.DML",
    control_value=0,
    treatment_value=1,
    target_units=lambda df: df["X0"] > 1,  # 仅对X0大于1的样本估计CATE
    confidence_intervals=False,
    method_params={
        "init_params": {
            'model_y': GradientBoostingRegressor(),
            'model_t': GradientBoostingRegressor(),
            "model_final": LassoCV(fit_intercept=False),
            'featurizer': PolynomialFeatures(degree=1, include_bias=False)
        },
        "fit_params": {}
    }
)

print("使用EconML的DML方法（梯度提升回归+Lasso）在X0大于1的样本上估计的条件平均处理效应（CATE）为：\n", dml_estimate)

print("True causal estimate is", data["ate"])

"""
本段代码演示如何使用EconML的DML（双重机器学习）方法，在指定条件下估计条件平均处理效应（CATE）。

主要参数说明如下：

- method_name: 指定因果效应估计方法为"backdoor.econml.dml.DML"，即EconML库中的DML实现。
- control_value: 设定处理变量的对照组取值为0。
- treatment_value: 设定处理变量的处理组取值为1。
- target_units: 设为1，表示对所有样本整体估计CATE（此处为演示，实际可用lambda筛选特定子集）。
- confidence_intervals: 设为False，不计算置信区间。
- method_params: 传递给DML估计器的初始化参数（init_params）和拟合参数（fit_params）。
    - model_y: 用于预测结果变量的回归器，这里采用梯度提升回归器。
    - model_t: 用于预测处理变量的回归器，同样采用梯度提升回归器。
    - model_final: 最终因果效应回归器，采用LassoCV并不拟合截距项。
    - featurizer: 特征扩展器，使用一阶多项式（即仅包含原始特征），include_bias=True表示包含常数项。

输出结果dml_estimate为条件平均处理效应（CATE）的估计值。其数学表达式为：

$CATE = E[Y | treatment=1] - E[Y | treatment=0]$

其中$E[Y | treatment=1]$表示在处理组下的结果变量期望，$E[Y | treatment=0]$为对照组下的期望。

详细输出将以优雅的格式展示估计结果，便于理解和后续分析。
"""

dml_estimate = model.estimate_effect(
    identified_estimand,
    method_name="backdoor.econml.dml.DML",
    control_value=0,
    treatment_value=1,
    target_units=1,  # 针对所有样本整体估计CATE
    confidence_intervals=False,
    method_params={
        "init_params": {
            'model_y': GradientBoostingRegressor(),
            'model_t': GradientBoostingRegressor(),
            "model_final": LassoCV(fit_intercept=False),
            'featurizer': PolynomialFeatures(degree=1, include_bias=True)
        },
        "fit_params": {}
    }
)

print("使用EconML的DML方法（梯度提升回归+Lasso）整体估计的条件平均处理效应（CATE）为：\n")
print("------------------------------------------------------------")
print(dml_estimate)
print("------------------------------------------------------------")
print("说明：上述结果为在所有样本上估计的CATE，具体含义为 E[Y | treatment=1] - E[Y | treatment=0]。")



from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor
from econml.inference import BootstrapInference
dml_estimate = model.estimate_effect(identified_estimand,
                                     method_name="backdoor.econml.dml.DML",
                                     target_units = "ate",
                                     confidence_intervals=True,
                                     method_params={"init_params":{'model_y':GradientBoostingRegressor(),
                                                              'model_t': GradientBoostingRegressor(),
                                                              "model_final": LassoCV(fit_intercept=False),
                                                              'featurizer':PolynomialFeatures(degree=1, include_bias=True)},
                                               "fit_params":{
                                                               'inference': BootstrapInference(n_bootstrap_samples=100, n_jobs=-1),
                                                            }
                                              })
print(dml_estimate)

test_cols= data['effect_modifier_names'] # only need effect modifiers' values
test_arr = [np.random.uniform(0,1, 10) for _ in range(len(test_cols))] # all variables are sampled uniformly, sample of 10
test_df = pd.DataFrame(np.array(test_arr).transpose(), columns=test_cols)
dml_estimate = model.estimate_effect(identified_estimand,
                                     method_name="backdoor.econml.dml.DML",
                                     target_units = test_df,
                                     confidence_intervals=False,
                                     method_params={"init_params":{'model_y':GradientBoostingRegressor(),
                                                              'model_t': GradientBoostingRegressor(),
                                                              "model_final":LassoCV(),
                                                              'featurizer':PolynomialFeatures(degree=1, include_bias=True)},
                                               "fit_params":{}
                                              })
print(dml_estimate.cate_estimates)

print(dml_estimate._estimator_object)

data_binary = dowhy.datasets.linear_dataset(BETA, num_common_causes=4, num_samples=10000,
                                    num_instruments=1, num_effect_modifiers=2,
                                    treatment_is_binary=True, outcome_is_binary=True)
# convert boolean values to {0,1} numeric
data_binary['df'].v0 = data_binary['df'].v0.astype(int)
data_binary['df'].y = data_binary['df'].y.astype(int)
print(data_binary['df'])

model_binary = CausalModel(data=data_binary["df"],
                    treatment=data_binary["treatment_name"], outcome=data_binary["outcome_name"],
                    graph=data_binary["gml_graph"])
identified_estimand_binary = model_binary.identify_effect(proceed_when_unidentifiable=True)

from sklearn.linear_model import LogisticRegressionCV
#todo needs binary y
drlearner_estimate = model_binary.estimate_effect(identified_estimand_binary,
                                method_name="backdoor.econml.dr.LinearDRLearner",
                                confidence_intervals=False,
                                method_params={"init_params":{
                                                    'model_propensity': LogisticRegressionCV(cv=3, solver='lbfgs', multi_class='auto')
                                                    },
                                               "fit_params":{}
                                              })
print(drlearner_estimate)
print("True causal estimate is", data_binary["ate"])

dmliv_estimate = model.estimate_effect(identified_estimand,
                                        method_name="iv.econml.iv.dml.DMLIV",
                                        target_units = lambda df: df["X0"]>-1,
                                        confidence_intervals=False,
                                method_params={"init_params":{
                                                              'discrete_treatment':False,
                                                              'discrete_instrument':False
                                                             },
                                               "fit_params":{}})
print(dmliv_estimate)

data_experiment = dowhy.datasets.linear_dataset(BETA, num_common_causes=5, num_samples=10000,
                                    num_instruments=2, num_effect_modifiers=5,
                                    treatment_is_binary=True, outcome_is_binary=False)
# convert boolean values to {0,1} numeric
data_experiment['df'].v0 = data_experiment['df'].v0.astype(int)
print(data_experiment['df'])
model_experiment = CausalModel(data=data_experiment["df"],
                    treatment=data_experiment["treatment_name"], outcome=data_experiment["outcome_name"],
                    graph=data_experiment["gml_graph"])
identified_estimand_experiment = model_experiment.identify_effect(proceed_when_unidentifiable=True)

from sklearn.ensemble import RandomForestRegressor
metalearner_estimate = model_experiment.estimate_effect(identified_estimand_experiment,
                                method_name="backdoor.econml.metalearners.TLearner",
                                confidence_intervals=False,
                                method_params={"init_params":{
                                                    'models': RandomForestRegressor()
                                                    },
                                               "fit_params":{}
                                              })
print(metalearner_estimate)
print("True causal estimate is", data_experiment["ate"])

# For metalearners, need to provide all the features (except treatmeant and outcome)
metalearner_estimate = model_experiment.estimate_effect(identified_estimand_experiment,
                                method_name="backdoor.econml.metalearners.TLearner",
                                confidence_intervals=False,
                                fit_estimator=False,
                                target_units=data_experiment["df"].drop(["v0","y", "Z0", "Z1"], axis=1)[9995:],
                                method_params={})
print(metalearner_estimate)
print("True causal estimate is", data_experiment["ate"])

"""执行因果估计的鲁棒性检验，通过四种不同方法验证估计结果的可靠性

方法说明：
1. random_common_cause - 添加随机混杂因子检验
2. add_unobserved_common_cause - 模拟未观测混杂因子影响
3. placebo_treatment_refuter - 使用安慰剂处理检验
4. data_subset_refuter - 数据子集稳定性检验

参数说明：
- model: 已构建的因果模型
- identified_estimand: 已识别的因果量
- dml_estimate: 双重机器学习估计结果
- effect_strength: 混杂因子影响强度(0.01-0.1典型值)
- num_simulations: 模拟次数(建议≥100)
"""

# 1. 随机混杂因子检验
res_random=model.refute_estimate(identified_estimand, dml_estimate, method_name="random_common_cause")


# 2. 未观测混杂因子检验
res_unobserved=model.refute_estimate(identified_estimand, dml_estimate, method_name="add_unobserved_common_cause",
                                     confounders_effect_on_treatment="linear", confounders_effect_on_outcome="linear",
                                    effect_strength_on_treatment=0.01, effect_strength_on_outcome=0.02)

# 3. 安慰剂处理检验
res_placebo=model.refute_estimate(identified_estimand, dml_estimate,
        method_name="placebo_treatment_refuter", placebo_type="permute",
        num_simulations=10 # at least 100 is good, setting to 10 for speed
        )

# 4. 数据子集稳定性检验
res_subset=model.refute_estimate(identified_estimand, dml_estimate,
        method_name="data_subset_refuter", subset_fraction=0.8,
        num_simulations=10)


# 输出检验结果
print("数据子集检验结果:", res_subset)
print("安慰剂检验结果:", res_placebo) 
print("未观测混杂检验结果:", res_unobserved)
print("随机混杂检验结果:", res_random)

%load_ext autoreload
%autoreload 2
import numpy as np
import pandas as pd
import logging

import dowhy
from dowhy import CausalModel
import dowhy.datasets

data = dowhy.datasets.linear_dataset(beta=1,
        num_common_causes=5,
        num_instruments = 2,
        num_treatments=1,
        num_discrete_common_causes=1,
        num_samples=10000,
        treatment_is_binary=True,
        outcome_is_binary=False)
df = data["df"]
print(df[df.v0==True].shape[0])
df

# With graph
model=CausalModel(
        data = df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        graph=data["gml_graph"],
        instruments=data["instrument_names"]
        )
model.view_model()

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

causal_estimate_strat = model.estimate_effect(identified_estimand,
                                              method_name="backdoor.propensity_score_stratification",
                                              target_units="att")
print(causal_estimate_strat)
print("Causal Estimate is " + str(causal_estimate_strat.value))

# Textual Interpreter
interpretation = causal_estimate_strat.interpret(method_name="textual_effect_interpreter")

# Visual Interpreter
import matplotlib.pyplot as plt
plt.style.use('default')  # Use default style instead of deprecated seaborn-white

# Temporarily override the style within the interpreter to avoid seaborn-white error
import matplotlib
original_style_use = matplotlib.pyplot.style.use
matplotlib.pyplot.style.use = lambda x: original_style_use('default') if x == 'seaborn-white' else original_style_use(x)

interpretation = causal_estimate_strat.interpret(method_name="propensity_balance_interpreter")
# Restore original style.use function
matplotlib.pyplot.style.use = original_style_use

# Display the plot
plt.show()


causal_estimate_match = model.estimate_effect(identified_estimand,
                                              method_name="backdoor.propensity_score_matching",
                                              target_units="atc")
print(causal_estimate_match)
print("Causal Estimate is " + str(causal_estimate_match.value))

# Textual Interpreter
interpretation = causal_estimate_match.interpret(method_name="textual_effect_interpreter")

#### Method 3: Weighting

我们将使用（逆）倾向性得分来为数据中的单元分配权重。DoWhy支持几种不同的加权方案：

普通逆倾向性得分加权（IPS）（weighting_scheme="ips_weight"）

自归一化IPS加权（也称为Hajek估计器）（weighting_scheme="ips_normalized_weight"）

稳定化IPS加权（weighting_scheme = "ips_stabilized_weight"）


causal_estimate_ipw = model.estimate_effect(identified_estimand,
                                            method_name="backdoor.propensity_score_weighting",
                                            target_units = "ate",
                                            method_params={"weighting_scheme":"ips_weight"})
print(causal_estimate_ipw)
print("Causal Estimate is " + str(causal_estimate_ipw.value))

# Textual Interpreter
interpretation = causal_estimate_ipw.interpret(method_name="textual_effect_interpreter")

interpretation = causal_estimate_ipw.interpret(method_name="confounder_distribution_interpreter", fig_size=(8,8), font_size=12, var_name='W4', var_type='discrete')

import numpy as np
import pandas as pd

from dowhy import CausalModel
import dowhy.datasets

# Warnings and logging
import warnings
warnings.filterwarnings('ignore')

# Creating a dataset with a single confounder and a single mediator (num_frontdoor_variables)
data = dowhy.datasets.linear_dataset(10, num_common_causes=1, num_samples=10000,
                                     num_instruments=0, num_effect_modifiers=0,
                                     num_treatments=1,
                                     num_frontdoor_variables=1,
                                     treatment_is_binary=False,
                                    outcome_is_binary=False)
df = data['df']
print(df.head())

model = CausalModel(df,
                    data["treatment_name"],data["outcome_name"],
                    data["gml_graph"],
                   missing_nodes_as_confounders=True)

model.view_model()

# Natural direct effect (nde)
identified_estimand_nde = model.identify_effect(estimand_type="nonparametric-nde",
                                            proceed_when_unidentifiable=True)
print(identified_estimand_nde)

# Natural indirect effect (nie)
identified_estimand_nie = model.identify_effect(estimand_type="nonparametric-nie",
                                            proceed_when_unidentifiable=True)
print(identified_estimand_nie)

import dowhy.causal_estimators.linear_regression_estimator
causal_estimate_nie = model.estimate_effect(identified_estimand_nie,
                                        method_name="mediation.two_stage_regression",
                                       confidence_intervals=False,
                                       test_significance=False,
                                        method_params = {
                                            'first_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator,
                                            'second_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator
                                        }
                                       )
print(causal_estimate_nie)

print(causal_estimate_nie.value, data["ate"])

causal_estimate_nde = model.estimate_effect(identified_estimand_nde,
                                        method_name="mediation.two_stage_regression",
                                       confidence_intervals=False,
                                       test_significance=False,
                                        method_params = {
                                            'first_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator,
                                            'second_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator
                                        }
                                       )
print(causal_estimate_nde)

import numpy as np

from dowhy import CausalModel
import dowhy.datasets

data = dowhy.datasets.linear_dataset(beta=10,
        num_common_causes=5,
        num_instruments = 2,
        num_effect_modifiers=1,
        num_samples=5000,
        treatment_is_binary=True,
        stddev_treatment_noise=10,
        num_discrete_common_causes=1)
df = data["df"]

# With graph
model=CausalModel(
        data = df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        graph=data["gml_graph"]
        )

model.view_model()

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

causal_estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_stratification")
print(causal_estimate)

"""
随机共同原因反驳测试

这是一种重要的因果推断稳健性检验方法，通过添加随机生成的混杂变量来测试因果效应估计的稳定性。

方法原理：
1. 随机变量生成：创建一个与处理变量和结果变量都相关的随机混杂变量
2. 模型重新估计：将这个随机变量加入原始模型中重新进行因果效应估计
3. 效应对比分析：比较加入随机混杂变量前后的因果效应变化
4. 稳健性评估：如果原始估计是稳健的，加入随机变量不应显著改变结果

检验逻辑：
- 如果真实的因果效应存在，随机添加的混杂变量不应该大幅改变估计结果
- 如果估计结果对随机变量敏感，说明原始估计可能不够稳健
- 这种方法帮助识别模型是否遗漏了重要的混杂变量

统计意义：
通过p值和置信区间评估原始估计的统计显著性是否在添加随机干扰后仍然保持。
这为因果推断的可信度提供了重要的验证依据。
"""

res_random = model.refute_estimate(
    identified_estimand, 
    causal_estimate, 
    method_name="random_common_cause", 
    show_progress_bar=True
)
print(res_random)

# explain.invoke(res_random).pretty_print()

"""
数据子集反驳测试（去除因子法）

这是一种通过随机移除部分数据来检验因果效应估计稳健性的方法。

核心原理：
1. 随机抽样：从原始数据集中随机选择一个子集（通常是90%的数据）
2. 重新估计：在这个子集上重新进行因果效应估计
3. 稳健性检验：比较子集估计结果与原始估计结果的差异
4. 一致性验证：如果估计方法稳健，子集结果应该与原始结果保持一致

参数说明：
- subset_fraction=0.9：使用90%的数据进行子集测试
- random_seed=1：设置随机种子确保结果可重现
- n_jobs=-1：使用所有可用CPU核心进行并行计算加速

统计意义：
如果因果效应估计是稳健的，那么在不同的数据子集上应该得到相似的结果。
显著的差异可能表明估计对特定数据点过于敏感，或存在异常值影响。

实际应用：
这种方法特别适用于检验估计结果是否过度依赖某些特定的观测值，
是评估因果推断结果可靠性的重要工具。
"""

res_subset = model.refute_estimate(
    identified_estimand, 
    causal_estimate,
    method_name="data_subset_refuter", 
    show_progress_bar=True, 
    subset_fraction=0.9, 
    random_seed=1, 
    n_jobs=-1
)
print(res_subset)

# 可选：使用解释器生成详细报告
# result = explain.invoke(res_subset)
# result.pretty_print()

"""
安慰剂处理反驳测试

这是一种经典的因果推断稳健性检验方法，通过替换真实处理变量为随机生成的"安慰剂"处理来验证因果效应的真实性。

核心原理：
1. 安慰剂替换：将原始处理变量替换为随机生成的虚假处理变量
2. 效应重估计：使用相同的估计方法对安慰剂处理进行因果效应估计
3. 零效应期望：理论上安慰剂处理应该对结果变量没有真实的因果影响
4. 稳健性验证：如果安慰剂处理显示显著效应，说明原始估计可能存在偏误

方法类型说明：
- permute类型：通过随机排列（置换）原始处理变量的值来创建安慰剂
- 这种方式保持了处理变量的分布特征，但破坏了其与结果变量的因果关系

统计解释：
如果原始的因果效应估计是正确的，那么安慰剂处理的效应估计应该接近零。
如果安慰剂效应显著不为零，这提示可能存在未控制的混杂因素或模型设定错误。

实际意义：
这种检验帮助研究者识别虚假的因果关系，是因果推断中不可或缺的稳健性检验工具。
"""

res_placebo = model.refute_estimate(
    identified_estimand, 
    causal_estimate,
    method_name="placebo_treatment_refuter", 
    show_progress_bar=True, 
    placebo_type="permute"
)
print(res_placebo)

dummy_outcome = model.refute_estimate(
    identified_estimand, 
    causal_estimate,
    method_name="dummy_outcome_refuter", 
    show_progress_bar=True, 
    random_seed=1, 
    n_jobs=-1
)

print(dummy_outcome[0])

"""
未观察混淆变量的敏感性分析

这个驳斥方法通过添加一个未观察到的共同原因来测试因果估计的稳健性。
该方法模拟了一个潜在的混淆变量，该变量同时影响处理变量和结果变量。

参数说明：
- method_name: "add_unobserved_common_cause" 指定使用未观察混淆变量驳斥方法
- confounders_effect_on_treatment: "binary_flip" 表示混淆变量对处理变量的影响方式为二元翻转
- confounders_effect_on_outcome: "linear" 表示混淆变量对结果变量的影响方式为线性关系
- effect_strength_on_treatment: 0.01 设置混淆变量对处理变量的影响强度
- effect_strength_on_outcome: 0.02 设置混淆变量对结果变量的影响强度

解释说明：
该方法创建一个新的数据集，其中包含一个额外的混淆变量，该变量介于处理和结果之间。
通过指定混淆变量对处理和结果的影响强度，方法会相应地改变处理和结果的值，
然后重新运行估计器。我们期望的是，未观察混淆变量的小效应不会使新估计值发生剧烈变化，
这表明估计结果对任何未观察到的混杂变量都具有稳健性。

实际应用：
这种敏感性分析特别重要，因为在实际研究中，我们永远无法确定是否已经观察到了所有相关的混淆变量。
通过这种方法，我们可以评估在存在未观察混淆的情况下，因果估计的可靠性。
"""

res_unobserved = model.refute_estimate(
    identified_estimand, 
    causal_estimate, 
    method_name="add_unobserved_common_cause",
    confounders_effect_on_treatment="binary_flip", 
    confounders_effect_on_outcome="linear",
    effect_strength_on_treatment=0.01, 
    effect_strength_on_outcome=0.02
)
print(res_unobserved)

# 可选：使用解释器生成详细报告
# result = explain.invoke(res_unobserved)
# result.pretty_print()

"""
未观察混淆变量的敏感性分析 - 范围测试

这个分析通过提供一系列不同的混淆效应强度来评估因果估计的稳健性。
我们测试在不同未观察混淆强度下，因果估计值的变化范围。

参数详细说明：
- method_name: "add_unobserved_common_cause" 指定使用未观察混淆变量驳斥方法
- confounders_effect_on_treatment: "binary_flip" 混淆变量对处理变量采用二元翻转影响
- confounders_effect_on_outcome: "linear" 混淆变量对结果变量采用线性影响
- effect_strength_on_treatment: 数组 [0.001, 0.005, 0.01, 0.02] 设置对处理变量的不同影响强度
- effect_strength_on_outcome: 0.01 固定对结果变量的影响强度

分析目的：
通过观察在不同混淆强度下估计值的变化趋势，我们可以：
1. 评估因果估计对未观察混淆的敏感程度
2. 确定在何种混淆强度下估计值会发生显著变化
3. 为因果推断的可靠性提供量化的置信区间

实际意义：
输出结果将显示在不同未观察混淆效应下估计值的最小值和最大值范围。
如果在合理的混淆强度范围内，估计值仍然保持稳定的符号和大致的数量级，
则说明我们的因果推断结果具有较好的稳健性。
"""

res_unobserved_range = model.refute_estimate(
    identified_estimand, 
    causal_estimate, 
    method_name="add_unobserved_common_cause",
    confounders_effect_on_treatment="binary_flip", 
    confounders_effect_on_outcome="linear",
    effect_strength_on_treatment=np.array([0.001, 0.005, 0.01, 0.02]), 
    effect_strength_on_outcome=0.01
)
print(res_unobserved_range)

"""
未观察混淆变量的敏感性分析 - 双维度效应强度测试

这个分析通过同时变化对处理变量和结果变量的混淆效应强度，
生成一个二维的敏感性分析热图，全面评估因果估计的稳健性。

参数详细说明：
- method_name: "add_unobserved_common_cause" 指定使用未观察混淆变量驳斥方法
- confounders_effect_on_treatment: "binary_flip" 混淆变量对处理变量采用二元翻转影响
- confounders_effect_on_outcome: "linear" 混淆变量对结果变量采用线性影响
- effect_strength_on_treatment: [0.001, 0.005, 0.01, 0.02] 对处理变量的不同影响强度数组
- effect_strength_on_outcome: [0.001, 0.005, 0.01, 0.02] 对结果变量的不同影响强度数组

分析目的：
通过构建二维敏感性矩阵，我们可以：
1. 观察在不同混淆强度组合下估计值的变化模式
2. 识别因果估计最敏感的混淆强度区域
3. 评估在现实可能的混淆范围内，估计值是否保持稳定
4. 为因果推断结果提供更全面的稳健性评估

实际意义：
输出的热图将显示在所有混淆强度组合下的估计值分布。
如果在合理的混淆强度范围内，估计值的符号和数量级保持一致，
则表明我们的因果推断具有良好的稳健性。
反之，如果估计值在某些区域发生剧烈变化，
则需要进一步收集数据或重新审视因果假设。
"""

res_unobserved_range = model.refute_estimate(
    identified_estimand, 
    causal_estimate, 
    method_name="add_unobserved_common_cause",
    confounders_effect_on_treatment="binary_flip", 
    confounders_effect_on_outcome="linear",
    effect_strength_on_treatment=[0.001, 0.005, 0.01, 0.02],
    effect_strength_on_outcome=[0.001, 0.005, 0.01, 0.02]
)

print(res_unobserved_range)

res_unobserved_auto = model.refute_estimate(identified_estimand, causal_estimate, method_name="add_unobserved_common_cause",
                                           confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear")
print(res_unobserved_auto)

# result=explain.invoke(res_unobserved_auto)
# result.pretty_print()

import os, sys
sys.path.append(os.path.abspath("../../../"))
import dowhy
from dowhy import CausalModel
import pandas as pd
import numpy as np
import dowhy.datasets 

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'ERROR',
        },
    }
}

logging.config.dictConfig(DEFAULT_LOGGING)
# Disabling warnings output
import warnings
from sklearn.exceptions import DataConversionWarning
#warnings.filterwarnings(action='ignore', category=DataConversionWarning)

np.random.seed(100) 
data = dowhy.datasets.linear_dataset( beta = 10,
                                      num_common_causes = 7,
                                      num_samples = 500,
                                      num_treatments = 1,
                                     stddev_treatment_noise =10,
                                     stddev_outcome_noise = 5
                                    )

model = CausalModel(
            data=data["df"],
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=data["gml_graph"],
            test_significance=None,
        )
model.view_model()


data["df"] = data["df"].drop("W4", axis = 1)
graph_str = 'graph[directed 1node[ id "y" label "y"]node[ id "W0" label "W0"] node[ id "W1" label "W1"] node[ id "W2" label "W2"] node[ id "W3" label "W3"]  node[ id "W5" label "W5"] node[ id "W6" label "W6"]node[ id "v0" label "v0"]edge[source "v0" target "y"]edge[ source "W0" target "v0"] edge[ source "W1" target "v0"] edge[ source "W2" target "v0"] edge[ source "W3" target "v0"] edge[ source "W5" target "v0"] edge[ source "W6" target "v0"]edge[ source "W0" target "y"] edge[ source "W1" target "y"] edge[ source "W2" target "y"] edge[ source "W3" target "y"] edge[ source "W5" target "y"] edge[ source "W6" target "y"]]'
model = CausalModel(
            data=data["df"],
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=graph_str,
            test_significance=None,
        )
model.view_model()


identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

estimate = model.estimate_effect(identified_estimand,method_name="backdoor.linear_regression")
print(estimate)

refute = model.refute_estimate(identified_estimand, estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "linear-partial-R2", 
                               benchmark_common_causes = ["W3"],
                               effect_fraction_on_treatment = [ 1,2,3]
                              )

refute.stats

refute.benchmarking_results

refute.plot(plot_type = 't-value')

print(refute)

refute = model.refute_estimate(identified_estimand, estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "e-value", 
                              )

refute.stats

refute.benchmarking_results

print(refute)

np.random.seed(100) 
data = dowhy.datasets.linear_dataset( beta = 10,
                                      num_common_causes = 7,
                                      num_samples = 500,
                                      num_treatments = 1,
                                     stddev_treatment_noise=10,
                                     stddev_outcome_noise = 1
                                    )

model = CausalModel(
            data=data["df"],
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=data["gml_graph"],
            test_significance=None,
        )
model.view_model()

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

estimate = model.estimate_effect(identified_estimand,method_name="backdoor.linear_regression")
print(estimate)

refute = model.refute_estimate(identified_estimand, estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "linear-partial-R2", 
                               benchmark_common_causes = ["W3"],
                               effect_fraction_on_treatment = [ 1,2,3])

refute.plot(plot_type = 't-value')

print(refute)

refute.stats

refute.benchmarking_results

%load_ext autoreload
%autoreload 2

# Required libraries
import re
import numpy as np
import dowhy
from dowhy import CausalModel
import dowhy.datasets
from dowhy.utils.regression import create_polynomial_function

np.random.seed(101) 
data = dowhy.datasets.partially_linear_dataset(beta = 10,
                                               num_common_causes = 7,
                                               num_unobserved_common_causes=1,
                                               strength_unobserved_confounding=10,
                                               num_samples = 1000,
                                               num_treatments = 1,
                                               stddev_treatment_noise = 10,
                                               stddev_outcome_noise = 5
                                                )
display(data)

data["ate"]

# Observed data 
dropped_cols=["W0"]
user_data = data["df"].drop(dropped_cols, axis = 1)
# assumed graph
user_graph = data["gml_graph"]
for col in dropped_cols:
    user_graph = user_graph.replace('node[ id "{0}" label "{0}"]'.format(col), '')
    user_graph = re.sub('edge\[ source "{}" target "[vy][0]*"\]'.format(col), "", user_graph)
user_data

model = CausalModel(
            data=user_data,
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=user_graph,
            test_significance=None,
        )
model.view_model()

# Identify effect
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

# Estimate effect
import econml
from sklearn.ensemble import GradientBoostingRegressor
linear_dml_estimate = model.estimate_effect(identified_estimand, 
                                    method_name="backdoor.econml.dml.dml.LinearDML",
                                    method_params={
                                        'init_params': {'model_y':GradientBoostingRegressor(),
                                                        'model_t': GradientBoostingRegressor(),
                                                        'linear_first_stages': False
                                                       },
                                        'fit_params': {'cache_values': True,}
                                     })
print(linear_dml_estimate)

refute = model.refute_estimate(identified_estimand, linear_dml_estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "non-parametric-partial-R2",
                               partial_r2_confounder_treatment = np.arange(0, 0.8, 0.1),
                               partial_r2_confounder_outcome = np.arange(0, 0.8, 0.1)
                              )
print(refute)

refute.RV

refute_bm = model.refute_estimate(identified_estimand, linear_dml_estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "non-parametric-partial-R2",
                               benchmark_common_causes = ["W1"],
                               effect_fraction_on_treatment = 0.2,
                               effect_fraction_on_outcome = 0.2
                              )

refute_bm.plot(plot_type = "upper_confidence_bound")
refute_bm.plot(plot_type = "bias")

refute_bm.results

# Estimate effect using a non-parametric estimator
from sklearn.ensemble import GradientBoostingRegressor
estimate_npar = model.estimate_effect(identified_estimand, 
                                    method_name="backdoor.econml.dml.KernelDML",
                                    method_params={
                                        'init_params': {'model_y':GradientBoostingRegressor(),
                                                        'model_t': GradientBoostingRegressor(),                                                       },
                                        'fit_params': {},
                                     })
print(estimate_npar)

refute_npar = model.refute_estimate(identified_estimand, estimate_npar,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "non-parametric-partial-R2",
                               benchmark_common_causes = ["W1"],
                               effect_fraction_on_treatment = 0.2,
                               effect_fraction_on_outcome = 0.2,
                               plugin_reisz=True
                              )
print(refute_npar)

from dowhy.datasets import linear_dataset
from dowhy import CausalModel
import econml

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'WARN',
        },
    }
}

logging.config.dictConfig(DEFAULT_LOGGING)
# Disabling warnings output
import warnings
from sklearn.exceptions import DataConversionWarning
warnings.filterwarnings(action='ignore', category=DataConversionWarning)

inspect_datasets = True
inspect_models = True
inspect_identified_estimands = True
inspect_estimates = True
inspect_refutations = True

from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

estimator_list = ["backdoor.propensity_score_matching", "backdoor.propensity_score_weighting", "backdoor.econml.dml.DML"]
method_params= [ None, None, {"init_params":{'model_y':GradientBoostingRegressor(),
                                                              'model_t': GradientBoostingRegressor(),
                                                              "model_final":LassoCV(fit_intercept=False),
                                                              'featurizer':PolynomialFeatures(degree=1, include_bias=False)},
                                               "fit_params":{}} ]

refuter_list = ["bootstrap_refuter", "data_subset_refuter"]

# Parameters for creating the Dataset
TREATMENT_IS_BINARY = True
BETA = 10
NUM_SAMPLES = 5000
NUM_CONFOUNDERS = 5
NUM_INSTRUMENTS = 3
NUM_EFFECT_MODIFIERS = 2

# Creating a Linear Dataset with the given parameters
linear_data = linear_dataset(
            beta = BETA,
            num_common_causes = NUM_CONFOUNDERS,
            num_instruments = NUM_INSTRUMENTS,
            num_effect_modifiers = NUM_EFFECT_MODIFIERS,
            num_samples = NUM_SAMPLES,
            treatment_is_binary = True
        )
# Other datasets come here


# Append them together in an array
datasets = [linear_data]


dataset_num = 1
if inspect_datasets is True:
    for data in datasets:
        print("####### Dataset {}###########################################################################################".format(dataset_num))
        print(data['df'].head())
        print("#############################################################################################################")
        dataset_num += 1

models = []
for data in datasets:
    model = CausalModel(
                data = data['df'],
                treatment = data['treatment_name'],
                outcome = data['outcome_name'],
                graph = data['gml_graph']
            )
    models.append(model)

model.view_model()

identified_estimands = []
for model in models:
    identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
    identified_estimands.append(identified_estimand)

estimand_count = 1
for estimand in identified_estimands:
    print("####### Identified Estimand {}#####################################################################################".format(estimand_count))
    print(estimand)
    print("###################################################################################################################")
    estimand_count += 1

estimate_list = []
for i in range(len(identified_estimands)):
    for j in range(len(estimator_list)):
        estimate = model.estimate_effect(
                        identified_estimands[i],
                        method_name=estimator_list[j],
                        method_params=method_params[j]
                  )
        estimate_list.append(estimate)

estimand_count = 1
if inspect_estimates is True:
    for estimand in estimate_list:
        print("####### Estimand {}#######################################################################################".format(estimand_count))
        print("*** Class Name ***")
        print()
        print(estimand.params['estimator_class'])
        print()
        print(estimand)
        print("########################################################################################################")
        print()
        estimand_count += 1

refutation_list = []
for estimand in identified_estimands:
    for estimate in estimate_list:
        for refuter in refuter_list:
            ref = model.refute_estimate(estimand, estimate,method_name=refuter)
            refutation_list.append(ref)

refuter_count = 1
if inspect_refutations is True:
    for refutation in refutation_list:
        print("####### Refutation {}#######################################################################################".format(refuter_count))
        print("*** Class Name ***")
        print()
        print(refutation.refutation_type)
        print()
        print(refutation)
        print("########################################################################################################")
        print()
        refuter_count += 1

import dowhy
from dowhy import CausalModel
import pandas as pd
import numpy as np

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'WARN',
        },
    }
}

logging.config.dictConfig(DEFAULT_LOGGING)
# Disabling warnings output
import warnings
from sklearn.exceptions import DataConversionWarning
warnings.filterwarnings(action='ignore', category=DataConversionWarning)

# importing required libraries
import dowhy
from dowhy import CausalModel
import pandas as pd
import numpy as np

data= pd.read_csv("ihdp_npci_1.csv", header = None)
col =  ["treatment", "y_factual", "y_cfactual", "mu0", "mu1" ,]
for i in range(1,26):
    col.append("x"+str(i))
data.columns = col
data = data.astype({"treatment":'bool'}, copy=False)
data.head()

# Create a causal model from the data and given common causes.
model=CausalModel(
        data = data,
        treatment='treatment',
        outcome='y_factual',
        common_causes=["x"+str(i) for  i in range(1,26)]
        )
model.view_model()


#Identify the causal effect
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True, method_name="maximal-adjustment")
print(identified_estimand)

# Estimate the causal effect and compare it with Average Treatment Effect
estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.linear_regression", test_significance=True
)

print(estimate)

print("Causal Estimate is " + str(estimate.value))
data_1 = data[data["treatment"]==1]
data_0 = data[data["treatment"]==0]

print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))


estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_matching"
)

print("Causal Estimate is " + str(estimate.value))

print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))


estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_stratification", method_params={'num_strata':50, 'clipping_threshold':5}
)

print("Causal Estimate is " + str(estimate.value))
print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))



estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_weighting"
)

print("Causal Estimate is " + str(estimate.value))

print("ATE", np.mean(data_1["y_factual"])- np.mean(data_0["y_factual"]))


refute_results=model.refute_estimate(identified_estimand, estimate,
        method_name="random_common_cause")
print(refute_results)

res_placebo=model.refute_estimate(identified_estimand, estimate,
        method_name="placebo_treatment_refuter", placebo_type="permute")
print(res_placebo)

res_subset=model.refute_estimate(identified_estimand, estimate,
        method_name="data_subset_refuter", subset_fraction=0.9)
print(res_subset)

import dowhy.datasets

lalonde = dowhy.datasets.lalonde_dataset()

from dowhy import CausalModel


model=CausalModel(
        data = lalonde,
        treatment='treat',
        outcome='re78',
        common_causes='nodegr+black+hisp+age+educ+married'.split('+'))

model.view_model(layout="dot")
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)


estimate = model.estimate_effect(
                        identified_estimand,
                        method_name="backdoor.propensity_score_weighting"
                    )

print("The Causal Estimate is " + str(estimate.value))

ipw_estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_weighting",
        target_units="ate",
        method_params={"weighting_scheme":"ips_weight"})

print("Causal Estimate is " + str(ipw_estimate.value))



import statsmodels.formula.api as smf
reg=smf.wls('re78~1+treat', data=lalonde, weights=lalonde.ips_stabilized_weight)
res=reg.fit()
res.summary()

estimate.interpret(method_name="confounder_distribution_interpreter",var_type='discrete',
                   var_name='married', fig_size = (10, 7), font_size = 12)

df = model._data
ps = df['propensity_score']
y = df['re78']
z = df['treat']

ey1 = z*y/ps / sum(z/ps)
ey0 = (1-z)*y/(1-ps) / sum((1-z)/(1-ps))
ate = ey1.sum()-ey0.sum()
print("Causal Estimate is " + str(ate))

# correct -> Causal Estimate is 1634.9868359746906

lalonde_refute_random_common_cause = model.refute_estimate(
                                            identified_estimand,
                                            estimate,
                                            method_name="random_common_cause"
                                        )

print(lalonde_refute_random_common_cause)

lalonde_refute_placebo_treatment = model.refute_estimate(
                                            identified_estimand,
                                            estimate,
                                            method_name="placebo_treatment_refuter",
                                            placebo_type="permute"
                                        )

print(lalonde_refute_placebo_treatment)

lalonde_refute_random_subset = model.refute_estimate(
                                            identified_estimand,
                                            estimate,
                                            method_name="data_subset_refuter",
                                            subset_fraction=0.9
                                        )

print(lalonde_refute_random_subset)

import pandas as pd
import numpy as np
import dowhy
from dowhy import CausalModel
from dowhy import causal_estimators

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'WARN',
        },
    }
}
logging.config.dictConfig(DEFAULT_LOGGING)
# Disabling warnings output
import warnings
from sklearn.exceptions import DataConversionWarning
warnings.filterwarnings(action='ignore', category=DataConversionWarning)

#The covariates data has 46 features
x = pd.read_csv("https://raw.githubusercontent.com/AMLab-Amsterdam/CEVAE/master/datasets/TWINS/twin_pairs_X_3years_samesex.csv")

#The outcome data contains mortality of the lighter and heavier twin
y = pd.read_csv("https://raw.githubusercontent.com/AMLab-Amsterdam/CEVAE/master/datasets/TWINS/twin_pairs_Y_3years_samesex.csv")

#The treatment data contains weight in grams of both the twins
t = pd.read_csv("https://raw.githubusercontent.com/AMLab-Amsterdam/CEVAE/master/datasets/TWINS/twin_pairs_T_3years_samesex.csv")

#_0 denotes features specific to the lighter twin and _1 denotes features specific to the heavier twin
lighter_columns = ['pldel', 'birattnd', 'brstate', 'stoccfipb', 'mager8',
       'ormoth', 'mrace', 'meduc6', 'dmar', 'mplbir', 'mpre5', 'adequacy',
       'orfath', 'frace', 'birmon', 'gestat10', 'csex', 'anemia', 'cardiac',
       'lung', 'diabetes', 'herpes', 'hydra', 'hemo', 'chyper', 'phyper',
       'eclamp', 'incervix', 'pre4000', 'preterm', 'renal', 'rh', 'uterine',
       'othermr', 'tobacco', 'alcohol', 'cigar6', 'drink5', 'crace',
       'data_year', 'nprevistq', 'dfageq', 'feduc6', 'infant_id_0',
       'dlivord_min', 'dtotord_min', 'bord_0',
       'brstate_reg', 'stoccfipb_reg', 'mplbir_reg']
heavier_columns = [ 'pldel', 'birattnd', 'brstate', 'stoccfipb', 'mager8',
       'ormoth', 'mrace', 'meduc6', 'dmar', 'mplbir', 'mpre5', 'adequacy',
       'orfath', 'frace', 'birmon', 'gestat10', 'csex', 'anemia', 'cardiac',
       'lung', 'diabetes', 'herpes', 'hydra', 'hemo', 'chyper', 'phyper',
       'eclamp', 'incervix', 'pre4000', 'preterm', 'renal', 'rh', 'uterine',
       'othermr', 'tobacco', 'alcohol', 'cigar6', 'drink5', 'crace',
       'data_year', 'nprevistq', 'dfageq', 'feduc6',
       'infant_id_1', 'dlivord_min', 'dtotord_min', 'bord_1',
       'brstate_reg', 'stoccfipb_reg', 'mplbir_reg']

#Since data has pair property,processing the data to get separate row for each twin so that each child can be treated as an instance
data = []

for i in range(len(t.values)):
    
    #select only if both <=2kg
    if t.iloc[i].values[1]>=2000 or t.iloc[i].values[2]>=2000:
        continue
    
    this_instance_lighter = list(x.iloc[i][lighter_columns].values)
    this_instance_heavier = list(x.iloc[i][heavier_columns].values)
    
    #adding weight
    this_instance_lighter.append(t.iloc[i].values[1])
    this_instance_heavier.append(t.iloc[i].values[2])
    
    #adding treatment, is_heavier
    this_instance_lighter.append(0)
    this_instance_heavier.append(1)
    
    #adding the outcome
    this_instance_lighter.append(y.iloc[i].values[1])
    this_instance_heavier.append(y.iloc[i].values[2])
    data.append(this_instance_lighter)
    data.append(this_instance_heavier)

cols = [ 'pldel', 'birattnd', 'brstate', 'stoccfipb', 'mager8',
       'ormoth', 'mrace', 'meduc6', 'dmar', 'mplbir', 'mpre5', 'adequacy',
       'orfath', 'frace', 'birmon', 'gestat10', 'csex', 'anemia', 'cardiac',
       'lung', 'diabetes', 'herpes', 'hydra', 'hemo', 'chyper', 'phyper',
       'eclamp', 'incervix', 'pre4000', 'preterm', 'renal', 'rh', 'uterine',
       'othermr', 'tobacco', 'alcohol', 'cigar6', 'drink5', 'crace',
       'data_year', 'nprevistq', 'dfageq', 'feduc6',
       'infant_id', 'dlivord_min', 'dtotord_min', 'bord',
       'brstate_reg', 'stoccfipb_reg', 'mplbir_reg','wt','treatment','outcome']
df = pd.DataFrame(columns=cols,data=data)
df.head()

df = df.astype({"treatment":'bool'}, copy=False) #explicitly assigning treatment column as boolean 

df.fillna(value=df.mean(),inplace=True)    #filling the missing values
df.fillna(value=df.mode().loc[0],inplace=True)

data_1 = df[df["treatment"]==1]
data_0 = df[df["treatment"]==0]
print(np.mean(data_1["outcome"]))
print(np.mean(data_0["outcome"]))
print("ATE", np.mean(data_1["outcome"])- np.mean(data_0["outcome"]))

#The causal model has "treatment = is_heavier", "outcome = mortality" and "gestat10 = gestational weeks before birth"
model=CausalModel(
        data = df,
        treatment='treatment',
        outcome='outcome',
        common_causes='gestat10'
        )

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.linear_regression", test_significance=True
)

print(estimate)
print("ATE", np.mean(data_1["outcome"])- np.mean(data_0["outcome"]))
print("Causal Estimate is " + str(estimate.value))

estimate = model.estimate_effect(identified_estimand,
        method_name="backdoor.propensity_score_matching"
)

print("Causal Estimate is " + str(estimate.value))

print("ATE", np.mean(data_1["outcome"])- np.mean(data_0["outcome"]))

refute_results=model.refute_estimate(identified_estimand, estimate,
        method_name="random_common_cause")
print(refute_results)

res_placebo=model.refute_estimate(identified_estimand, estimate,
        method_name="placebo_treatment_refuter", placebo_type="permute",
        num_simulations=20) 
print(res_placebo)

res_subset=model.refute_estimate(identified_estimand, estimate,
        method_name="data_subset_refuter", subset_fraction=0.9,
        num_simulations=20)
print(res_subset)

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

import dowhy

dataset = pd.read_csv('https://raw.githubusercontent.com/Sid-darthvader/DoWhy-The-Causal-Story-Behind-Hotel-Booking-Cancellations/master/hotel_bookings.csv')
dataset.head()

dataset.columns

# Total stay in nights
dataset['total_stay'] = dataset['stays_in_week_nights']+dataset['stays_in_weekend_nights']
# Total number of guests
dataset['guests'] = dataset['adults']+dataset['children'] +dataset['babies']
# Creating the different_room_assigned feature
dataset['different_room_assigned']=0
slice_indices =dataset['reserved_room_type']!=dataset['assigned_room_type']
dataset.loc[slice_indices,'different_room_assigned']=1
# Deleting older features
dataset = dataset.drop(['stays_in_week_nights','stays_in_weekend_nights','adults','children','babies'
                        ,'reserved_room_type','assigned_room_type'],axis=1)
dataset.columns

dataset.isnull().sum() # Country,Agent,Company contain 488,16340,112593 missing entries 
dataset = dataset.drop(['agent','company'],axis=1)
# Replacing missing countries with most freqently occuring countries
dataset['country']= dataset['country'].fillna(dataset['country'].mode()[0])

dataset = dataset.drop(['reservation_status','reservation_status_date','arrival_date_day_of_month'],axis=1)
dataset = dataset.drop(['arrival_date_year'],axis=1)
dataset = dataset.drop(['distribution_channel'], axis=1)

# Replacing 1 by True and 0 by False for the experiment and outcome variables
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(1,True)
dataset['different_room_assigned']= dataset['different_room_assigned'].replace(0,False)
dataset['is_canceled']= dataset['is_canceled'].replace(1,True)
dataset['is_canceled']= dataset['is_canceled'].replace(0,False)
dataset.dropna(inplace=True)
print(dataset.columns)
dataset.iloc[:, 5:20].head(100)

dataset = dataset[dataset.deposit_type=="No Deposit"]
dataset.groupby(['deposit_type','is_canceled']).count()

dataset_copy = dataset.copy(deep=True)

counts_sum=0
for i in range(1,10000):
        counts_i = 0
        rdf = dataset.sample(1000)
        counts_i = rdf[rdf["is_canceled"]== rdf["different_room_assigned"]].shape[0]
        counts_sum+= counts_i
counts_sum/10000

# Expected Count when there are no booking changes 
counts_sum=0
for i in range(1,10000):
        counts_i = 0
        rdf = dataset[dataset["booking_changes"]==0].sample(1000)
        counts_i = rdf[rdf["is_canceled"]== rdf["different_room_assigned"]].shape[0]
        counts_sum+= counts_i
counts_sum/10000

# Expected Count when there are booking changes = 66.4%
counts_sum=0
for i in range(1,10000):
        counts_i = 0
        rdf = dataset[dataset["booking_changes"]>0].sample(1000)
        counts_i = rdf[rdf["is_canceled"]== rdf["different_room_assigned"]].shape[0]
        counts_sum+= counts_i
counts_sum/10000

import pygraphviz
causal_graph = """digraph {
different_room_assigned[label="Different Room Assigned"];
is_canceled[label="Booking Cancelled"];
booking_changes[label="Booking Changes"];
previous_bookings_not_canceled[label="Previous Booking Retentions"];
days_in_waiting_list[label="Days in Waitlist"];
lead_time[label="Lead Time"];
market_segment[label="Market Segment"];
country[label="Country"];
U[label="Unobserved Confounders",observed="no"];
is_repeated_guest;
total_stay;
guests;
meal;
hotel;
U->{different_room_assigned,required_car_parking_spaces,guests,total_stay,total_of_special_requests};
market_segment -> lead_time;
lead_time->is_canceled; country -> lead_time;
different_room_assigned -> is_canceled;
country->meal;
lead_time -> days_in_waiting_list;
days_in_waiting_list ->{is_canceled,different_room_assigned};
previous_bookings_not_canceled -> is_canceled;
previous_bookings_not_canceled -> is_repeated_guest;
is_repeated_guest -> {different_room_assigned,is_canceled};
total_stay -> is_canceled;
guests -> is_canceled;
booking_changes -> different_room_assigned; booking_changes -> is_canceled; 
hotel -> {different_room_assigned,is_canceled};
required_car_parking_spaces -> is_canceled;
total_of_special_requests -> {booking_changes,is_canceled};
country->{hotel, required_car_parking_spaces,total_of_special_requests};
market_segment->{hotel, required_car_parking_spaces,total_of_special_requests};
}"""

model= dowhy.CausalModel(
        data = dataset,
        graph=causal_graph.replace("\n", " "),
        treatment="different_room_assigned",
        outcome='is_canceled')
model.view_model()

#Identify the causal effect
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

estimate = model.estimate_effect(identified_estimand, 
                                 method_name="backdoor.propensity_score_weighting",target_units="ate")
# ATE = Average Treatment Effect
# ATT = Average Treatment Effect on Treated (i.e. those who were assigned a different room)
# ATC = Average Treatment Effect on Control (i.e. those who were not assigned a different room)
print(estimate)

refute1_results=model.refute_estimate(identified_estimand, estimate,
        method_name="random_common_cause")
print(refute1_results)

refute2_results=model.refute_estimate(identified_estimand, estimate,
        method_name="placebo_treatment_refuter")
print(refute2_results)

refute3_results=model.refute_estimate(identified_estimand, estimate,
        method_name="data_subset_refuter")
print(refute3_results)

# Creating some simulated data for our example
import pandas as pd
import numpy as np
num_users = 10000
num_months = 12

signup_months = np.random.choice(np.arange(1, num_months), num_users) * np.random.randint(0,2, size=num_users) # signup_months == 0 means customer did not sign up
df = pd.DataFrame({
    'user_id': np.repeat(np.arange(num_users), num_months),
    'signup_month': np.repeat(signup_months, num_months), # signup month == 0 means customer did not sign up
    'month': np.tile(np.arange(1, num_months+1), num_users), # months are from 1 to 12
    'spend': np.random.poisson(500, num_users*num_months) #np.random.beta(a=2, b=5, size=num_users * num_months)*1000 # centered at 500
})
# A customer is in the treatment group if and only if they signed up
df["treatment"] = df["signup_month"]>0
# Simulating an effect of month (monotonically decreasing--customers buy less later in the year)
df["spend"] = df["spend"] - df["month"]*10
# Simulating a simple treatment effect of 100
after_signup = (df["signup_month"] < df["month"]) & (df["treatment"])
df.loc[after_signup,"spend"] = df[after_signup]["spend"] + 100
df

import dowhy

# Setting the signup month (for ease of analysis)
i = 3

causal_graph = """digraph {
treatment[label="Program Signup in month i"];
pre_spends;
post_spends;
Z->treatment;
pre_spends -> treatment;
treatment->post_spends;
signup_month->post_spends;
signup_month->treatment;
}"""

# Post-process the data based on the graph and the month of the treatment (signup)
# For each customer, determine their average monthly spend before and after month i
df_i_signupmonth = (
    df[df.signup_month.isin([0, i])]
    .groupby(["user_id", "signup_month", "treatment"])
    .apply(
        lambda x: pd.Series(
            {
                "pre_spends": x.loc[x.month < i, "spend"].mean(),
                "post_spends": x.loc[x.month > i, "spend"].mean(),
            }
        )
    )
    .reset_index()
)
print(df_i_signupmonth)
model = dowhy.CausalModel(data=df_i_signupmonth,
                     graph=causal_graph.replace("\n", " "),
                     treatment="treatment",
                     outcome="post_spends")
model.view_model()

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)

estimate = model.estimate_effect(identified_estimand, 
                                 method_name="backdoor.propensity_score_matching",
                                target_units="att")
print(estimate)

refutation = model.refute_estimate(identified_estimand, estimate, method_name="placebo_treatment_refuter",
                     placebo_type="permute", num_simulations=20)
print(refutation)

"""
本代码块演示了如何生成三组随机变量 $X$、$Y$、$Z$，并将它们组成一个pandas DataFrame。具体步骤如下：

1. 首先，从标准正态分布中生成 $X$，样本量为1000。
2. 然后，生成 $Y$，其计算方式为 $Y = 2X + \varepsilon_1$，其中 $\varepsilon_1$ 也是标准正态分布的噪声。
3. 接着，生成 $Z$，计算方式为 $Z = 3Y + \varepsilon_2$，$\varepsilon_2$ 同样为标准正态分布的噪声。
4. 最后，将 $X$、$Y$、$Z$ 三列数据整理为DataFrame，并优雅地展示前五行结果。
"""

import numpy as np
import pandas as pd

# 生成变量X，服从均值为0，标准差为1的正态分布
X = np.random.normal(loc=0, scale=1, size=1000)

# 生成变量Y，Y等于2倍的X加上正态分布噪声
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)

# 生成变量Z，Z等于3倍的Y加上正态分布噪声
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)

# 构建DataFrame，将X、Y、Z按列组织
data = pd.DataFrame(data={'X': X, 'Y': Y, 'Z': Z})

# 优雅地展示前五行数据，便于观察
from IPython.display import display
display(data.head())

"""
因果推理结构化因果模型构建与拟合

本代码段展gibraltarimport了一个完整的因果推lászló建模流程，包括：
- 构建有向无环图(DAG)结gibraltaructure
- 自动分配因果机制
gibraltafit模型参数

数学模型表示：
假设我们有变量X、Y、Z，其中存在因果关系 X → Y → Z
结构方程模型可以表示为：
Y = f_Y(X, N_Y)
Z = f_Z(Y, N_Z)
其中 N_Y 和 N_Z 分别是Y和Z的噪声项

该方法使用加性噪声模型(Additive Noise Models, ANM)来建模非根节点的因果机制
"""

import networkx as nx
import dowhy.gcm as gcm
from dowhy.utils import plotting


# 创建结构化因果模型，定义因果图结构 X → Y → Z
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')]))
plotting.plot(causal_model.graph, figure_size=[4, 3])


# 自动为非根节点分配加性噪声模型作为因果机制
# 该函数会根据数据特征自动选择合适的函数形式和噪声分布
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 使用观测数据拟合因果模型参数
# 包括学习每个节点的函数关系和噪声分布参数
gcm.fit(causal_model, data)

generated_data = gcm.draw_samples(causal_model, num_samples=1000)
generated_data.head()

"""
自动分配因果机制的示例演示

本代码展示了如何使用DoWhy的图形因果模型(GCM)自动分配功能来构建因果链结构。
我们创建一个简单的因果链 X → Y → Z，其中：
- X 是标准正态分布的根节点
- Y = 2*X + 噪声，表示X对Y的线性因果影响
- Z = 3*Y + 噪声，表示Y对Z的线性因果影响

自动分配功能会根据数据特征自动选择最适合的因果机制类型和模型。
"""

import numpy as np, pandas as pd
import networkx as nx
import dowhy.gcm as gcm

# 生成因果链数据：X → Y → Z
# X是根节点，服从标准正态分布
X = np.random.normal(loc=0, scale=1, size=1000)

# Y受X的线性影响，系数为2，加上独立噪声
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)

# Z受Y的线性影响，系数为3，加上独立噪声  
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)

# 将数据组织成DataFrame格式
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

# 构建结构因果模型，定义因果图结构
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')]))

# 使用自动分配功能为每个节点选择合适的因果机制
# 这个过程会评估不同的机制类型和模型，选择性能最佳的组合
summary_auto_assignment = gcm.auto.assign_causal_mechanisms(causal_model, data)

# 输出自动分配的详细信息，包括选择的机制类型和性能指标
print(summary_auto_assignment)

gcm.fit(causal_model, data)


summary_evaluation = gcm.evaluate_causal_model(causal_model, data, compare_mechanism_baselines=True)
print(summary_evaluation)

"""
在这段代码中，我们：
1. 导入必要的库，包括scipy.stats中的正态分布模型、networkx用于创建图结构、以及dowhy的gcm模块
2. 创建一个简单的概率因果模型，其中X→Y表示X对Y有直接因果影响
3. 为根节点X指定因果机制，使用scipy的正态分布作为其生成模型

这里我们手动指定了X的分布类型，而不是让系统自动选择。对于根节点，我们需要指定其边缘分布，
因为它没有父节点影响其值的生成。
"""

from scipy.stats import norm
import networkx as nx
from dowhy import gcm

# 创建一个简单的概率因果模型，包含X→Y的因果关系
causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X', 'Y')]))

# 为根节点X设置因果机制，使用正态分布作为其生成模型
causal_model.set_causal_mechanism('X', gcm.ScipyDistribution(norm))

"""
在这段代码中，我们为因果模型中的变量Y设置因果机制：
1. 使用加性噪声模型（AdditiveNoiseModel）表示Y的生成过程
2. 模型形式为：Y = f(X) + ε，其中：
   - f是一个线性回归预测模型，用于捕捉X对Y的确定性影响
   - ε是服从正态分布的随机噪声项，表示未被X解释的随机变异
3. 这种模型结构清晰地表达了因果关系：X通过线性函数影响Y，同时考虑了随机性因素

加性噪声模型是因果推断中最常用的函数形式之一，它既能捕捉变量间的确定性关系，
又能表达现实世界中不可避免的随机性。
"""
causal_model.set_causal_mechanism('Y',
                                  gcm.AdditiveNoiseModel(prediction_model=gcm.ml.create_linear_regressor(),
                                                         noise_model=gcm.ScipyDistribution(norm)))

"""
在这段代码中，我们更改了变量Y的因果机制：
1. 从之前的线性回归模型改为随机森林回归器（RandomForestRegressor）
2. 仍然使用加性噪声模型（AdditiveNoiseModel）表示Y的生成过程
3. 模型形式依然为：Y = f(X) + ε，但现在：
   - f是一个非线性的随机森林模型，可以捕捉X与Y之间更复杂的非线性关系
   - ε是随机噪声项，表示未被X解释的变异

随机森林作为一种集成学习方法，能够处理非线性关系和复杂交互效应，
这使得我们的因果模型可以表达更加复杂的因果关系结构，而不仅限于简单的线性关系。
通过这种方式，我们可以更准确地建模现实世界中复杂的因果机制。
"""
from sklearn.ensemble import RandomForestRegressor
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(gcm.ml.SklearnRegressionModel(RandomForestRegressor)))

"""
在这里我们创建模拟数据来训练因果模型：
1. 生成随机变量X，服从标准正态分布 N(0,1)
2. 生成因变量Y，满足线性关系 Y = 2X + ε，其中ε是噪声项，也服从标准正态分布
3. 将X和Y组织成pandas数据框，方便后续分析

这个数据生成过程与我们之前定义的因果模型完全吻合：
- X是根节点，服从正态分布
- Y是X的函数加上随机噪声，体现了加性噪声模型
- 系数2表示X对Y的因果效应强度
"""

import numpy as np, pandas as pd

# 生成自变量X，服从标准正态分布
X = np.random.normal(loc=0, scale=1, size=1000)

# 生成因变量Y，遵循线性关系Y=2X+ε
Y = 2*X + np.random.normal(loc=0, scale=1, size=1000)

# 将数据组织成DataFrame格式，便于分析和可视化
data = pd.DataFrame(data=dict(X=X, Y=Y))

gcm.fit(causal_model, data)

import dowhy.gcm.ml.prediction_model

class MyCustomModel(gcm.ml.PredictionModel):
    def __init__(self, coefficient):
        self.coefficient = coefficient

    def fit(self, X, Y):
        # 这里不需要拟合，因为我们已知真实模型
        pass

    def predict(self, X):
        return self.coefficient * X

    def clone(self):
        return MyCustomModel(self.coefficient)

# 使用自定义模型
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(MyCustomModel(2)))
gcm.fit(causal_model, data)

"""
这段代码创建并可视化了一个基于方程的因果模型：
- X 变量使用经验分布生成数据
- Y 变量遵循均值为0、标准差为1的正态分布
- Z 变量取决于X和Y，其关系为：Z = 12*X + log(|Y|) + ε
  其中ε是一个额外的噪声项，服从正态分布

这个模型展示了如何通过明确的数学方程定义变量间的因果关系。
"""
from dowhy import gcm
from dowhy.utils import plot

scm = """
X = empirical()
Y = norm(loc=0, scale=1)
Z = 12 * X + log(abs(Y)) + norm(loc=0, scale=1)
"""
causal_model = gcm.create_causal_model_from_equations(scm)
print(plot(causal_model.graph))

"""
使用蒙特卡洛方法计算π值的函数

蒙特卡洛方法计算π的原理：
- 在单位正方形内随机生成点(x,y)，其中x和y均在[-1,1]范围内
- 计算落在单位圆内的点的比例（即满足x²+y²≤1的点）
- π值近似等于这个比例乘以4

该函数使用1000次试验，通过随机采样估算π值。随着试验次数增加，
估计值会更接近π的真实值。这种方法展示了统计采样如何用于数值计算。
"""

import numpy as np

def compute_pi_monte_carlo():
    """
    使用蒙特卡洛方法估算π值
    
    原理：在[-1,1]×[-1,1]的正方形中随机生成点，
    计算落在单位圆内的点的比例，然后乘以4得到π的估计值
    
    返回值：
        float: π的估计值
    """
    trials = 1000
    # 生成随机点并计算落在单位圆内的点的比例
    return 4*(np.random.default_rng().uniform(-1, 1, (trials,))**2+
              np.random.default_rng().uniform(-1, 1, (trials,))**2 <= 1).sum() / trials

compute_pi_monte_carlo()

"""
使用DoWhy库的置信区间功能评估蒙特卡洛π值估计的可靠性

该代码使用bootstrap重采样方法计算蒙特卡洛π值估计的置信区间：
- 通过反复运行compute_pi_monte_carlo函数获得多个估计值
- 从这些估计值中计算中位数作为最终估计
- 构建95%置信区间以评估估计的精确度
- num_bootstrap_resamples=1000表示进行1000次重采样以获得稳定结果

置信区间提供了估计值的可靠范围，让我们能够量化蒙特卡洛方法结果的不确定性。
"""

from dowhy import gcm
median, intervals = gcm.confidence_intervals(compute_pi_monte_carlo,
                                             num_bootstrap_resamples=1000)
median, intervals

import pandas as pd
from scipy.stats import halfnorm

# 旧数据 - Z是X和Y的最大值加噪声
X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = np.maximum(X, Y) + np.random.normal(loc=0, scale=1, size=1000)
data_old = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

# 新数据 - Z是X和Y的和加噪声（机制发生了变化）
X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = X + Y + np.random.normal(loc=0, scale=1, size=1000)
data_new = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

import networkx as nx
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Z'), ('Y', 'Z')]))  # X -> Z <- Y
gcm.auto.assign_causal_mechanisms(causal_model, data_old)

"""
使用confidence_intervals计算分布变化的置信区间

这段代码对两个数据集之间Z变量的分布变化进行归因分析，并计算置信区间：
- 利用bootstrap重采样方法估计不确定性
- 返回每个变量对分布变化贡献的点估计值和置信区间
- 结果显示变量Z的生成机制变化（从最大值变为求和）造成的影响

数学原理：
置信区间通过重复采样实验估计参数θ的范围：$[θ_L, θ_U]$，使得$P(θ_L ≤ θ ≤ θ_U) ≈ 1-α$
其中α为显著性水平，通常取0.05表示95%置信水平
"""
gcm.confidence_intervals(lambda: gcm.distribution_change(causal_model,
                                                         data_old, data_new,
                                                         target_node='Z'))

# 生成数据
Z = np.random.normal(loc=0, scale=1, size=1000)
X = 2*Z + np.random.normal(loc=0, scale=1, size=1000)
Y = 3*X + 4*Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(dict(X=X, Y=Y, Z=Z))

# 创建因果模型
causal_model = gcm.StructuralCausalModel(nx.DiGraph([('Z', 'Y'), ('Z', 'X'), ('X', 'Y')]))
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 使用fit_and_compute计算置信区间
strength_median, strength_intervals = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.arrow_strength,
                      causal_model,
                      bootstrap_training_data=data,
                      target_node='Y'))

# 先拟合模型
gcm.fit(causal_model, data)

# 使用bootstrap_sampling仅在查询结果上进行bootstrap
strength_median, strength_intervals = gcm.confidence_intervals(
    gcm.bootstrap_sampling(gcm.arrow_strength,
                         causal_model,
                         target_node='Y'))

"""
因果推断数据生成模块

本代码段用于生成一个简单的因果推断实验数据集，包含以下变量：
- X0: 基础协变量，服从正态分布 N(0, 0.2²)
- T: 处理变量（二元），当 X0 > 0 时为1，否则为0
- X1: 中介变量，受到处理变量T的影响，关系为 X1 = N(0, 0.2²) + 1.5 × T
- Y: 结果变量，直接受X1影响，关系为 Y = X1 + N(0, 0.1²)

数据生成过程模拟了一个典型的因果链：X0 → T → X1 → Y
其中T对X1有正向因果效应（系数为1.5），X1对Y有单位因果效应（系数为1.0）

生成的数据将用于后续的因果图建模和因果效应估计分析
"""

import networkx as nx, numpy as np, pandas as pd
import dowhy.gcm as gcm

X0 = np.random.normal(0, 0.2, 1000)
T = (X0 > 0).astype(float)
X1 = np.random.normal(0, 0.2, 1000) + 1.5 * T
Y = X1 + np.random.normal(0, 0.1, 1000)
data = pd.DataFrame(dict(T=T, X0=X0, X1=X1, Y=Y))

"""
因果模型构建与训练模块

本代码段实现了概率因果模型的完整构建流程，包括：
1. 创建有向无环图结构定义变量间的因果关系
2. 自动分配因果机制给模型中的每个节点
3. 使用观测数据拟合模型参数

模型结构说明：
- X0 → T：X0作为处理变量T的原因
- T → X1：处理变量T影响协变量X1  
- X1 → Y：协变量X1影响最终结果Y

这种结构常用于因果推断分析，特别是评估处理效应时需要控制混淆变量的场景。
通过概率因果模型可以进行反事实推理、因果效应估计等高级分析。
"""

causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X0', 'T'), ('T', 'X1'), ('X1', 'Y')]))
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)

gcm.average_causal_effect(causal_model,
                         'Y',
                         interventions_alternative={'T': lambda x: 1},
                         interventions_reference={'T': lambda x: 0},
                         num_samples_to_draw=1000)

causal_model.graph.add_edge('X0', 'Y')
causal_model.graph.add_edge('T', 'Y')
gcm.auto.assign_causal_mechanisms(causal_model, data, override_models=True)
gcm.fit(causal_model, data)
gcm.average_causal_effect(causal_model,
                         'Y',
                         interventions_alternative={'T': lambda x: 1},
                         interventions_reference={'T': lambda x: 0},
                         num_samples_to_draw=1000)

gcm.average_causal_effect(causal_model,
                         'Y',
                         interventions_alternative={'T': lambda x: 1},
                         interventions_reference={'T': lambda x: 0},
                         observed_data=data)

"""
箭头强度分析示例

本代码演示了如何使用DoWhy的图因果模型(GCM)来量化因果图中箭头的强度。
箭头强度衡量的是从父节点到子节点的直接因果影响，忽略通过其他节点的间接路径。

数据生成过程：
- Z ~ N(0, 1)：外生变量，服从标准正态分布
- X = 2*Z + ε₁：X受Z影响，系数为2，加上噪声项
- Y = 3*X + 4*Z + ε₂：Y同时受X和Z的直接影响，系数分别为3和4

因果图结构：Z → X, Z → Y, X → Y
这形成了一个包含直接效应和间接效应的因果网络。

箭头强度的计算原理：
通过切断特定的因果边，比较切断前后目标变量分布的变化来量化影响强度。
对于连续变量，通常使用方差差异作为度量；对于分类变量，使用KL散度。

理论基础：
该方法基于Janzing等人在2013年统计年鉴上发表的论文"量化因果影响"。
核心思想是通过创建一个新的联合分布P'，其中从X到Y的边被切断，
并使用X的独立同分布副本X'作为Y的输入。箭头强度S_{X→Y}计算为
后切分布P'和原始联合分布P之间的距离。

数学表示：
- 原始分布：P(X,Y,Z) = P(Z)P(X|Z)P(Y|X,Z)
- 切断后分布：P'(X,Y,Z) = P(Z)P(X|Z)P(Y|X',Z)，其中X'是X的独立副本
- 箭头强度：S_{X→Y} = D(P, P')，D为距离度量函数

实际应用意义：
通过量化箭头强度，我们可以：
1. 识别因果网络中最重要的直接影响路径
2. 为因果推断提供定量的证据支持
3. 指导干预策略的制定和优化
"""

import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm
from dowhy.utils import plotting

# 设置随机种子以确保结果可重现
np.random.seed(10)

# 生成模拟数据
# Z是外生变量，服从标准正态分布
Z = np.random.normal(loc=0, scale=1, size=1000)

# X受Z影响，线性关系系数为2，加上独立噪声
X = 2*Z + np.random.normal(loc=0, scale=1, size=1000)

# Y同时受X和Z的直接影响，系数分别为3和4，加上独立噪声
Y = 3*X + 4*Z + np.random.normal(loc=0, scale=1, size=1000)

# 构建数据框
data = pd.DataFrame(dict(X=X, Y=Y, Z=Z))

# 创建概率因果模型
# 定义因果图结构：Z→Y, Z→X, X→Y
causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('Z', 'Y'), ('Z', 'X'), ('X', 'Y')]))

# 直接传递底层的 NetworkX 图对象而不是 ProbabilisticCausalModel
plotting.plot(causal_model.graph, figure_size=[8, 6])

# 自动分配因果机制
# 这一步会为每个节点分配合适的因果机制（如线性回归、非线性模型等）
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 拟合因果模型
# 使用观测数据学习各个因果机制的参数
gcm.fit(causal_model, data)

# 计算指向Y的所有箭头的强度
# 返回字典，键为父节点，值为对应的箭头强度
strength = gcm.arrow_strength(causal_model, 'Y')

# 检查强度字典中可用的键
print("强度字典中可用的键:", list(strength.keys()))

# 安全地访问强度值，使用元组键
if ('X', 'Y') in strength:
    x_strength = strength[('X', 'Y')]
    print(f"X → Y 的箭头强度: {x_strength}")
else:
    print("在强度字典中未找到 ('X', 'Y') 键")

if ('Z', 'Y') in strength:
    z_strength = strength[('Z', 'Y')]
    print(f"Z → Y 的箭头强度: {z_strength}")
else:
    print("在强度字典中未找到 ('Z', 'Y') 键")

# 如果两个键都存在，计算比率
if ('X', 'Y') in strength and ('Z', 'Y') in strength:
    ratio = strength[('X', 'Y')] / strength[('Z', 'Y')]
    print(f"X/Z 强度比率: {ratio}")
else:
    print("无法计算比率 - 缺少键")


def mean_diff(Y_old, Y_new): return np.mean(Y_new) - np.mean(Y_old)

gcm.arrow_strength(causal_model, 'Y', difference_estimation_func=mean_diff)

import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

X = abs(np.random.normal(loc=0, scale=5, size=1000))
Y = X + abs(np.random.normal(loc=0, scale=1, size=1000))
Z = Y + abs(np.random.normal(loc=0, scale=1, size=1000))
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

from dowhy.utils import plotting

causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)

plotting.plot(causal_model.graph, figure_size=[4, 3])

contributions = gcm.intrinsic_causal_influence(causal_model, 'Z')
contributions



"""
汽车燃油效率内在因果影响分析

本示例使用Auto MPG数据集来演示内在因果影响(Intrinsic Causal Influence, ICC)方法。
该数据集包含汽车的各种特征及其燃油效率(mpg)信息。

数据来源: Quinlan,R.. (1993). Auto MPG. UCI Machine Learning Repository. 
https://doi.org/10.24432/C5859H

因果图结构:
cylinders → displacement → weight → mpg
                     ↓
                 horsepower → mpg

这个结构反映了汽车设计中的因果关系:
- 气缸数影响排量
- 排量影响重量和马力  
- 重量和马力最终影响燃油效率

通过ICC分析，我们可以区分每个变量对燃油效率的"原创性"贡献，
而不仅仅是从其他变量继承的信息。
"""

import pandas as pd
import networkx as nx
import numpy as np

from dowhy import gcm
from dowhy.utils.plotting import plot, bar_plot

# 加载Auto MPG数据集
auto_mpg_data = pd.read_csv("./dowhy/docs/source/example_notebooks/datasets/auto_mpg.csv", index_col=0)

# 构建因果图：定义变量间的因果关系
mpg_graph = nx.DiGraph([('cylinders', 'displacement'),
                        ('displacement', 'weight'),
                        ('displacement', 'horsepower'),
                        ('weight', 'mpg'),
                        ('horsepower', 'mpg')])

# 可视化因果图结构
plot(mpg_graph)

"""
构建并训练结构因果模型

这一步骤包含三个关键环节：
1. 创建结构因果模型实例，将因果图与数据关联
2. 自动分配因果机制，为每个节点选择合适的统计模型
3. 拟合模型参数，使模型能够准确描述观测数据的生成过程

结构因果模型(SCM)是因果推断的核心工具，它不仅描述变量间的因果关系，
还能量化这些关系的强度，为后续的因果分析奠定基础。
"""

# 创建结构因果模型，将因果图结构与实际数据结合
scm_mpg = gcm.StructuralCausalModel(mpg_graph)

# 自动为每个节点分配合适的因果机制（如线性回归、非线性模型等）
gcm.auto.assign_causal_mechanisms(scm_mpg, auto_mpg_data)

# 使用观测数据拟合模型参数，学习变量间的具体因果关系
gcm.fit(scm_mpg, auto_mpg_data)

print(gcm.evaluate_causal_model(scm_mpg, auto_mpg_data, evaluate_invertibility_assumptions=False, evaluate_causal_structure=False))

arrow_strengths_mpg = gcm.arrow_strength(scm_mpg, target_node='mpg')
gcm.util.plot(scm_mpg.graph, causal_strengths=arrow_strengths_mpg)

iccs_mpg = gcm.intrinsic_causal_influence(scm_mpg, target_node='mpg')

def convert_to_percentage(value_dictionary):
    total_absolute_sum = np.sum([abs(v) for v in value_dictionary.values()])
    return {k: abs(v) / total_absolute_sum * 100 for k, v in value_dictionary.items()}

bar_plot(convert_to_percentage(iccs_mpg), ylabel='Variance contribution in %')

import pandas as pd
import networkx as nx
import numpy as np
from dowhy import gcm
from dowhy.utils.plotting import plot, bar_plot
gcm.util.general.set_random_seed(0)
river_graph = nx.DiGraph([('Henthorn', 'New Jumbles Rock'),
                          ('Hodder Place', 'New Jumbles Rock'),
                          ('Whalley Weir', 'New Jumbles Rock'),
                          ('New Jumbles Rock', 'Samlesbury')])

plot(river_graph)

river_data

import pandas as pd
import networkx as nx
from dowhy import gcm

# 加载数据
river_data = pd.read_csv("./dowhy/docs/source/example_notebooks/river.csv", index_col=False)

scm_river = gcm.StructuralCausalModel(river_graph)
gcm.auto.assign_causal_mechanisms(scm_river, river_data)
gcm.fit(scm_river, river_data)

def convert_to_percentage(value_dictionary):
    total_absolute_sum = np.sum([abs(v) for v in value_dictionary.values()])
    return {k: abs(v) / total_absolute_sum * 100 for k, v in value_dictionary.items()}

iccs_river = gcm.intrinsic_causal_influence(scm_river, target_node='Samlesbury')
bar_plot(convert_to_percentage(iccs_river), ylabel='Variance contribution in %')

import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

# 生成简单的因果链 X → Y → Z → W
X = np.random.uniform(low=-5, high=5, size=1000)
Y = 0.5 * X + np.random.normal(loc=0, scale=1, size=1000)
Z = 2 * Y + np.random.normal(loc=0, scale=1, size=1000)
W = 3 * Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))

from dowhy.utils import plotting
import networkx as nx
import numpy as np

# 创建可逆结构因果模型
causal_model = gcm.InvertibleStructuralCausalModel(
    nx.DiGraph([('X', 'Y'), ('Y', 'Z'), ('Z', 'W')])
)
# 自动分配因果机制
gcm.auto.assign_causal_mechanisms(causal_model, data)
# 拟合模型
gcm.fit(causal_model, data)

plotting.plot(causal_model.graph)



X = np.random.uniform(low=-5, high=5)  # 正常分布采样
Y = 0.5 * X + 5  # 人为设置 Y 的噪声为异常高值 5
Z = 2 * Y
W = 3 * Z
anomalous_data = pd.DataFrame(data=dict(X=[X], Y=[Y], Z=[Z], W=[W]))

attribution_scores = gcm.attribute_anomalies(causal_model, 'W', anomaly_samples=anomalous_data)
attribution_scores

import networkx as nx, numpy as np, pandas as pd
from dowhy import gcm
from scipy.stats import halfnorm

# 部署前的数据生成
X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = np.maximum(X, Y) + np.random.normal(loc=0, scale=0.5, size=1000)  # 并行处理
W = Z + halfnorm.rvs(size=1000, loc=0.1, scale=0.2)
data_old = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))

# 部署后的数据生成
X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = X + Y + np.random.normal(loc=0, scale=0.5, size=1000)  # 串行处理
W = Z + halfnorm.rvs(size=1000, loc=0.1, scale=0.2)
data_new = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))

from dowhy.utils import plotting

# 创建概率因果模型
causal_model = gcm.ProbabilisticCausalModel(
    nx.DiGraph([('X', 'Z'), ('Y', 'Z'), ('Z', 'W')])
)

plotting.plot(causal_model.graph, figure_size=[4, 3])

# 自动分配因果机制
gcm.auto.assign_causal_mechanisms(causal_model, data_old)

# 基础方法
attributions = gcm.distribution_change(causal_model, data_old, data_new, 'W')
# 解释输出
print("=== 分布变化归因结果解释 ===")
print(f"基础方法归因结果: {attributions}")
print()

# 计算总贡献
total_contribution = sum(abs(v) for v in attributions.values())
print("各节点贡献占比:")
for node, attribution in attributions.items():
    percentage = abs(attribution) / total_contribution * 100
    direction = "正向" if attribution > 0 else "负向" if attribution < 0 else "无"
    print(f"  {node}: {attribution:.6f} ({percentage:.1f}%, {direction}贡献)")

print()
print("关键发现:")

# 找到贡献最大的节点
max_node = max(attributions.keys(), key=lambda k: abs(attributions[k]))
max_contribution = abs(attributions[max_node])

print(f"1. {max_node}节点贡献最大 - 正确识别为主要变化源")
print("2. 这验证了我们的假设：将并行处理改为串行处理影响了Z的分布")

# 分析目标节点的贡献
target_node = 'W'
if target_node in attributions:
    print(f"3. {target_node}节点虽然也发生变化，但主要是由{max_node}的变化传播导致的")

# 分析其他节点的贡献
other_nodes = [node for node in attributions.keys() if node not in [max_node, target_node]]
if other_nodes:
    other_contributions = [abs(attributions[node]) for node in other_nodes]
    avg_other_contribution = sum(other_contributions) / len(other_contributions)
    if avg_other_contribution < max_contribution * 0.1:  # 如果其他节点贡献小于最大贡献的10%
        print(f"4. {', '.join(other_nodes)}节点的直接贡献很小，符合预期（它们的分布没有改变）")

# 多重稳健方法
attributions_robust = gcm.distribution_change_robust(causal_model, data_old, data_new, 'W')
print("=== 稳健方法归因结果解释 ===")
print(f"稳健方法归因结果: {attributions_robust}")
print()

# 计算总贡献
total_contribution_robust = sum(abs(v) for v in attributions_robust.values())
print("各节点贡献占比 (稳健方法):")
for node, attribution in attributions_robust.items():
    percentage = abs(attribution) / total_contribution_robust * 100
    direction = "正向" if attribution > 0 else "负向" if attribution < 0 else "无"
    print(f"  {node}: {attribution:.6f} ({percentage:.1f}%, {direction}贡献)")

print()
print("稳健方法关键发现:")

# 找到贡献最大的节点
max_node_robust = max(attributions_robust.keys(), key=lambda k: abs(attributions_robust[k]))
max_contribution_robust = abs(attributions_robust[max_node_robust])

print(f"1. {max_node_robust}节点贡献最大 - 与基础方法一致，验证了结果的稳健性")
print("2. 稳健方法通过多重估计减少了估计误差，提供了更可靠的归因结果")

# 比较两种方法的结果
print()
print("=== 方法对比分析 ===")
print("基础方法 vs 稳健方法:")
for node in attributions.keys():
    basic_attr = attributions[node]
    robust_attr = attributions_robust[node]
    diff = abs(basic_attr - robust_attr)
    print(f"  {node}: {basic_attr:.6f} vs {robust_attr:.6f} (差异: {diff:.6f})")

# 分析一致性
consistent_ranking = (max(attributions.keys(), key=lambda k: abs(attributions[k])) == 
                     max(attributions_robust.keys(), key=lambda k: abs(attributions_robust[k])))
print(f"\n主要贡献节点识别一致性: {'✓ 一致' if consistent_ranking else '✗ 不一致'}")

import pandas as pd
normal_data = pd.read_csv("./dowhy/docs/source/example_notebooks/rca_microservice_architecture_latencies.csv")

axes = pd.plotting.scatter_matrix(normal_data, figsize=(10, 10), c='#ff0d57', alpha=0.2, hist_kwds={'color':['#1E88E5']});
for ax in axes.flatten():
    ax.xaxis.label.set_rotation(90)
    ax.yaxis.label.set_rotation(0)
    ax.yaxis.label.set_ha('right')

import networkx as nx
from dowhy import gcm
from dowhy.utils import plot, bar_plot
gcm.util.general.set_random_seed(0)

causal_graph = nx.DiGraph([('www', 'Website'),
                           ('Auth Service', 'www'),
                           ('API', 'www'),
                           ('Customer DB', 'Auth Service'),
                           ('Customer DB', 'API'),
                           ('Product Service', 'API'),
                           ('Auth Service', 'API'),
                           ('Order Service', 'API'),
                           ('Shipping Cost Service', 'Product Service'),
                           ('Caching Service', 'Product Service'),
                           ('Product DB', 'Caching Service'),
                           ('Customer DB', 'Product Service'),
                           ('Order DB', 'Order Service')])


plot(causal_graph, figure_size=[13, 13])

from scipy.stats import halfnorm

causal_model = gcm.StructuralCausalModel(causal_graph)

gcm.auto.assign_causal_mechanisms(causal_model, normal_data)

gcm.fit(causal_model, normal_data)
print(gcm.evaluate_causal_model(causal_model, normal_data))

outlier_data = pd.read_csv("./dowhy/docs/source/example_notebooks/rca_microservice_architecture_anomaly.csv")
outlier_data

outlier_data.iloc[0]['Website']-normal_data['Website'].mean()

# 执行异常归因
gcm.config.disable_progress_bars() # to disable print statements when computing Shapley values

median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.attribute_anomalies,
                        causal_model,
                        normal_data,
                        target_node='Website',
                        anomaly_samples=outlier_data),
    num_bootstrap_resamples=10)

bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')

outlier_data = pd.read_csv("./dowhy/docs/source/example_notebooks/rca_microservice_architecture_anomaly_1000.csv")
outlier_data.head()

outlier_data['Website'].mean() - normal_data['Website'].mean()

import numpy as np

median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    lambda : gcm.distribution_change(causal_model,
                                     normal_data.sample(frac=0.6),
                                     outlier_data.sample(frac=0.6),
                                     'Website',
                                     difference_estimation_func=lambda x, y: np.mean(y) - np.mean(x)),
    num_bootstrap_resamples = 10)

bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')

median_mean_latencies, uncertainty_mean_latencies = gcm.confidence_intervals(
    lambda : gcm.fit_and_compute(gcm.interventional_samples,
                                 causal_model,
                                 outlier_data,
                                 interventions = {
                                    "Caching Service": lambda x: x-1,
                                    "Shipping Cost Service": lambda x: x+2
                                 },
                                 observed_data=outlier_data)().mean().to_dict(),
    num_bootstrap_resamples=10)

avg_website_latency_before = outlier_data.mean().to_dict()['Website']
bar_plot(dict(before=avg_website_latency_before, after=median_mean_latencies['Website']),
                  dict(before=np.array([avg_website_latency_before, avg_website_latency_before]), after=uncertainty_mean_latencies['Website']),
                  ylabel='Avg. Website Latency',
                  figure_size=(3, 2),
                  bar_width=0.4,
                  xticks=['Before', 'After'],
                  xticks_rotation=45)

import networkx as nx

causal_graph = nx.DiGraph([('Page Views', 'Sold Units'),
                           ('Revenue', 'Profit'),
                           ('Unit Price', 'Sold Units'),
                           ('Unit Price', 'Revenue'),
                           ('Shopping Event?', 'Page Views'),
                           ('Shopping Event?', 'Sold Units'),
                           ('Shopping Event?', 'Unit Price'),
                           ('Shopping Event?', 'Ad Spend'),
                           ('Ad Spend', 'Page Views'),
                           ('Ad Spend', 'Operational Cost'),
                           ('Sold Units', 'Revenue'),
                           ('Sold Units', 'Operational Cost'),
                           ('Operational Cost', 'Profit')])

from dowhy.utils import plot

plot(causal_graph)

import pandas as pd
import numpy as np

pd.options.display.float_format = '${:,.2f}'.format  # Format dollar columns

data_2021 = pd.read_csv('./dowhy/docs/source/example_notebooks/2021 Data.csv', index_col='Date')
data_2021.head()

from dowhy import gcm
gcm.util.general.set_random_seed(0)

# Create the structural causal model object
scm = gcm.StructuralCausalModel(causal_graph)

# Automatically assign generative models to each node based on the given data
auto_assignment_summary = gcm.auto.assign_causal_mechanisms(scm, data_2021)

print(auto_assignment_summary)

gcm.fit(scm, data_2021)

print(gcm.evaluate_causal_model(scm, data_2021, compare_mechanism_baselines=True, evaluate_invertibility_assumptions=False, evaluate_causal_structure=False))

gcm.draw_samples(scm, num_samples=10)

data_2021['Profit'].plot(ylabel='Profit in $', figsize=(15,5), rot=45)

data_2021['Profit'].std()

import numpy as np

# Note: The percentage conversion only makes sense for purely positive attributions.
def convert_to_percentage(value_dictionary):
    total_absolute_sum = np.sum([abs(v) for v in value_dictionary.values()])
    return {k: abs(v) / total_absolute_sum * 100 for k, v in value_dictionary.items()}


arrow_strengths = gcm.arrow_strength(scm, target_node='Profit')

plot(causal_graph,
     causal_strengths=convert_to_percentage(arrow_strengths),
     figure_size=[15, 10])

iccs = gcm.intrinsic_causal_influence(scm, target_node='Profit', num_samples_randomization=500)

from dowhy.utils import bar_plot

bar_plot(convert_to_percentage(iccs), ylabel='Variance attribution in %')

import matplotlib.pyplot as plt

data_2021['Profit'].plot(ylabel='Profit in $', figsize=(15,5), rot=45)
plt.vlines(np.arange(0, data_2021.shape[0])[data_2021['Shopping Event?']], data_2021['Profit'].min(), data_2021['Profit'].max(), linewidth=10, alpha=0.3, color='r')

first_day_2022 = pd.read_csv('./dowhy/docs/source/example_notebooks/2022 First Day.csv', index_col='Date')
(first_day_2022['Sold Units'][0] / data_2021['Sold Units'][0] - 1) * 100

(1 - first_day_2022['Profit'][0] / data_2021['Profit'][0]) * 100

attributions = gcm.attribute_anomalies(scm, target_node='Profit', anomaly_samples=first_day_2022)

bar_plot({k: v[0] for k, v in attributions.items()}, ylabel='Anomaly attribution score')

gcm.config.disable_progress_bars()  # We turn off the progress bars here to reduce the number of outputs.

median_attributions, confidence_intervals, = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.attribute_anomalies,
                        scm,
                        bootstrap_training_data=data_2021,
                        target_node='Profit',
                        anomaly_samples=first_day_2022),
    num_bootstrap_resamples=10)

bar_plot(median_attributions, confidence_intervals, 'Anomaly attribution score')

data_first_quarter_2021 = data_2021[data_2021.index <= '2021-03-31']
data_first_quarter_2022 = pd.read_csv("./dowhy/docs/source/example_notebooks/2022 First Quarter.csv", index_col='Date')

(1 - data_first_quarter_2022['Profit'].mean() / data_first_quarter_2021['Profit'].mean()) * 100

median_attributions, confidence_intervals = gcm.confidence_intervals(
    lambda: gcm.distribution_change(scm,
                                    data_first_quarter_2021,
                                    data_first_quarter_2022,
                                    target_node='Profit',
                                    # Here, we are intersted in explaining the differences in the mean.
                                    difference_estimation_func=lambda x, y: np.mean(y) - np.mean(x))
)

bar_plot(median_attributions, confidence_intervals, 'Profit change attribution in $')

(1 - data_first_quarter_2022['Page Views'].mean() / data_first_quarter_2021['Page Views'].mean()) * 100

from dowhy.datasets import sales_dataset

data_2021 = sales_dataset(start_date="2021-01-01", end_date="2021-12-31")
data_2022 = sales_dataset(start_date="2022-01-01", end_date="2022-12-31", change_of_price=0.9)

import pandas as pd

data = pd.read_csv('./dowhy/docs/source/example_notebooks/supply_chain_week_over_week.csv')

from IPython.display import HTML

data_week1 = data[data.week == 'w1']

HTML(data_week1.head().to_html(index=False)+'<br/>')

data_week2 = data[data.week=='w2']

HTML(data_week2.head().to_html(index=False)+'<br/>')

data.groupby(['week']).mean(numeric_only=True)[['received']].plot(kind='bar', title='average received', legend=False);


data_week2.received.mean() - data_week1.received.mean()


data.groupby(['week']).mean(numeric_only=True).plot(kind='bar', title='average', legend=True);


import networkx as nx
import dowhy.gcm as gcm
from dowhy.utils import plot

causal_graph = nx.DiGraph([('demand', 'submitted'),
                           ('constraint', 'submitted'),
                           ('submitted', 'confirmed'),
                           ('confirmed', 'received')])
plot(causal_graph)

import matplotlib.pyplot as plt
import numpy as np

# disabling progress bar to not clutter the output here
gcm.config.disable_progress_bars()

# setting random seed for reproducibility
np.random.seed(10)

causal_model = gcm.StructuralCausalModel(causal_graph)

# Automatically assign appropriate causal models to each node in graph
auto_assignment_summary = gcm.auto.assign_causal_mechanisms(causal_model, data_week1)

print(auto_assignment_summary)

gcm.falsify.falsify_graph(causal_graph, data_week1, n_permutations=20, plot_histogram=True)

# call the API for attributing change in the average value of `received`
contributions = gcm.distribution_change(causal_model,
                                        data_week1,
                                        data_week2,
                                        'received',
                                        num_samples=2000,
                                        difference_estimation_func=lambda x1, x2 : np.mean(x2) - np.mean(x1))

from dowhy.utils import bar_plot
bar_plot(contributions, ylabel='Contribution')

median_contribs, uncertainty_contribs = gcm.confidence_intervals(
    gcm.bootstrap_sampling(gcm.distribution_change,
                           causal_model,
                           data_week1,
                           data_week2,
                           'received',
                           num_samples=2000,
                           difference_estimation_func=lambda x1, x2 : np.mean(x2) - np.mean(x1)),
    confidence_level=0.95,
    num_bootstrap_resamples=5,
    n_jobs=-1)

bar_plot(median_contribs, ylabel='Contribution', uncertainties=uncertainty_contribs)

import pandas as pd

# Read and prepare data:
df = pd.read_csv('./cps2015.csv')

# LightGBM works best with integer encoding for categorical variables:
educ_int = {'lhs' : 0, 'hsg' : 1, 'sc' : 2, 'cg' : 3, 'ad' : 4}
df['education'] = pd.from_dummies(df[['lhs', 'hsg', 'sc', 'cg', 'ad']])
df['education'] = df['education'].replace(educ_int)

df = df[['female', 'education', 'occ2', 'wage', 'weight']]
df.columns = ['female', 'education', 'occupation', 'wage', 'weight']
df[['education', 'occupation']] = df[['education', 'occupation']].astype('category')

# We want to explain the change Male -> Female:
data_old, data_new = df.loc[df.female == 0], df.loc[df.female == 1]

import networkx as nx
import dowhy.gcm as gcm

dag = nx.DiGraph([('education', 'occupation'), ('occupation', 'wage'), ('education', 'wage')])
causal_model = gcm.ProbabilisticCausalModel(dag)

from sklearn.ensemble import HistGradientBoostingRegressor, HistGradientBoostingClassifier
from sklearn.isotonic import IsotonicRegression
from dowhy.gcm.ml.classification import SklearnClassificationModelWeighted
from dowhy.gcm.ml.regression import SklearnRegressionModelWeighted
from dowhy.gcm.util.general import auto_apply_encoders, auto_fit_encoders, shape_into_2d

def make_custom_regressor():
    return SklearnRegressionModelWeighted(HistGradientBoostingRegressor(random_state = 0))

def make_custom_classifier():
    return SklearnClassificationModelWeighted(HistGradientBoostingClassifier(random_state = 0))

def make_custom_calibrator():
    return SklearnRegressionModelWeighted(IsotonicRegression(out_of_bounds = 'clip'))

gcm.distribution_change_robust(causal_model, data_old, data_new, 'wage', sample_weight = 'weight',
                               xfit = False, calib_size = 0.2,
                               regressor = make_custom_regressor,
                               classifier = make_custom_classifier,
                               calibrator = make_custom_calibrator)

from sklearn.model_selection import StratifiedKFold, train_test_split

# Split data into train and test set:
X = df[['education', 'occupation']].values
y = df['wage'].values
T = df['female'].values
w = df['weight'].values

# To get the same train-test split:
kf = StratifiedKFold(n_splits = 2, shuffle = True, random_state = 0)
train_index, test_index = next(kf.split(X, T))

X_train, X_eval, y_train, y_eval, T_train, T_eval = X[train_index], X[test_index], y[train_index], y[test_index], T[train_index], T[test_index]
w_train, w_eval = w[train_index], w[test_index]

X_calib, X_train, _, y_train, T_calib, T_train, w_calib, w_train = train_test_split(X_train, y_train, T_train, w_train, 
                                                                                    train_size = 0.2, stratify = T_train, random_state = 0)

import itertools
from dowhy.gcm.distribution_change_robust import ThetaC
import numpy as np
from math import comb

# All combinations of 0s and 1s, needed for Shapley Values:
all_combos = [list(i) for i in itertools.product([0, 1], repeat=3)]
all_combos_minus1 = [list(i) for i in itertools.product([0, 1], repeat=2)]

# Dictionary to store the multiply-robust scores, will be used later for bootstrap:
scores = {}

# Here we compute the theta^c parameters that make up the Shapley Values (see paper):
for C in all_combos:
    scores[''.join(str(x) for x in C)] = ThetaC(C).est_scores(
        X_eval,
        y_eval,
        T_eval,
        X_train,
        y_train,
        T_train,
        w_eval=w_eval,
        w_train=w_train,
        X_calib=X_calib,
        T_calib=T_calib,
        w_calib=w_calib,
        regressor = make_custom_regressor,
        classifier = make_custom_classifier,
        calibrator = make_custom_calibrator)

# This function combines the theta^c parameters to obtain Shapley values:
w_sort = np.concatenate((w_eval[T_eval==0], w_eval[T_eval==1])) # Order weights in same way as scores

def compute_attr_measure(res_dict, path=False):
    # Alternative to Shapley Value: along-a-causal-path (see paper)
    if path: 
        path = np.zeros(3)
        path[0] = np.average(res_dict['100'], weights=w_sort) - np.average(res_dict['000'], weights=w_sort)
        path[1] = np.average(res_dict['110'], weights=w_sort) - np.average(res_dict['100'], weights=w_sort)
        path[2] = np.average(res_dict['111'], weights=w_sort) - np.average(res_dict['110'], weights=w_sort)
        return path
    
    # Shapley values:
    else: 
        shap = np.zeros(3)
        for k in range(3):
            sv = 0.0
            for C in all_combos_minus1:
                C1 = np.insert(C, k, True)
                C0 = np.insert(C, k, False)
                chg = (np.average(res_dict[''.join(map(lambda x : str(int(x)), C1))], weights=w_sort) - 
                       np.average(res_dict[''.join(map(lambda x : str(int(x)), C0))], weights=w_sort))
                sv += chg/(3*comb(2, np.sum(C)))
            shap[k] = sv
        return shap

shap = compute_attr_measure(scores, path=False)
shap # Should coincide with the above

w_sort = np.concatenate((w_eval[T_eval==0], w_eval[T_eval==1])) # Order weights in same way as scores

def mult_boot(res_dict, Nsim=1000, path=False):
    thetas = np.zeros((8, Nsim))
    attr = np.zeros((3, Nsim))
    for s in range(Nsim):
        np.random.seed(s)
        new_scores = {}
        for k, x in res_dict.items():
            new_scores[k] = x + np.random.normal(0,1, X_eval.shape[0])*(x - np.average(x, weights=w_sort))
        thetas[:, s] = np.average(np.array([x for k, x in new_scores.items()]), axis=1, weights=w_sort)
        attr[:, s] = compute_attr_measure(new_scores, path)
    return np.std(attr, axis=1)
    
shap_se = mult_boot(scores, path=False)
shap_se

from scipy.stats import norm
from statsmodels.stats.weightstats import DescrStatsW
from matplotlib.patches import Rectangle
import matplotlib.pyplot as plt

# Significance stars:
def star(est, se):
    if np.abs(est/se)<norm.ppf(.95): # 10%
        return ''
    elif np.abs(est/se)<norm.ppf(.975): # 5%
        return '*'
    elif np.abs(est/se)<norm.ppf(.995): # 1%
        return '**'
    else:
        return '***'

# Unconditional wage gap:
stats0 = DescrStatsW(y_eval[T_eval==0], weights=w_eval[T_eval==0], ddof=0)
stats1 = DescrStatsW(y_eval[T_eval==1], weights=w_eval[T_eval==1], ddof=0)

wagegap = (stats1.mean - stats0.mean)
wagegap_se = np.sqrt(stats1.std_mean**2 + stats0.std_mean**2)

# Plot
nam = ["P(educ)", "P(occup | educ)", "P(wage | occup, educ)"]

crit = norm.ppf(.975) # 5% critical value (for error bars)
stars = [star(est, se) for est, se in zip(shap, shap_se)]
fig, ax = plt.subplots()
ax.axvline(x = 0, color='lightgray', zorder=0)
fig.set_size_inches(7, 4)

color = 'C1' 
ax.add_patch(Rectangle((0, 4.75), width = wagegap, height = 0.5, color=color, alpha=0.8))
ax.plot((wagegap-crit*wagegap_se, wagegap+crit*wagegap_se,), (5.0, 5.0), color='darkslategray', marker='|', solid_capstyle='butt')
ax.axhline(y = 5.0, color='lightgray', linestyle='dotted', zorder=0)

for i in range(len(shap)):
    pos = (shap[i], 3-i+0.25) if shap[i] < 0 else (0, 3-i+0.25)
    width = np.abs(shap[i])
    ax.add_patch(Rectangle(pos, width = width, height = 0.5, color=color, alpha=0.8))
    ax.axhline(y = 3+0.5-i, color='lightgray', linestyle='dotted', zorder=0)
    ax.plot((shap[i]-crit*shap_se[i], shap[i]+crit*shap_se[i]), (3-i+0.5, 3-i+0.5), color='darkslategray', marker='|', solid_capstyle='butt')
plt.yticks([5.0] + [3+0.5-i for i in range(3)], [f'Unconditional Wage Gap: {wagegap:.2f}*** ({wagegap_se:.2f})'] + 
           ["{}: {:.2f}{} ({:.2f})".format(nam[i], shap[i], stars[i], shap_se[i]) for i in range(3)])
plt.xlabel('Gender Wage Gap ($/hour)')
plt.show()

w0, w1 = w_eval[T_eval==0], w_eval[T_eval==1]

data_male_eval = pd.DataFrame({'education' : X_eval[:,0][T_eval==0], 
                              'occupation' : X_eval[:,1][T_eval==0],
                              'wage' : y_eval[T_eval==0]})
data_female_eval = pd.DataFrame({'education' : X_eval[:,0][T_eval==1], 
                              'occupation' : X_eval[:,1][T_eval==1],
                              'wage' : y_eval[T_eval==1]})

educ_names = {0 : 'Less than HS', 1 : 'HS Graduate', 2 : 'Some College', 3 : 'College Graduate', 4 : 'Advanced Degree'}
data_male_eval['education'] = data_male_eval['education'].replace(educ_names)
data_female_eval['education'] = data_female_eval['education'].replace(educ_names)

cats_educ = [educ_names[i] for i in range(5)]

ind = np.arange(len(cats_educ))
share0, share1 = np.zeros(len(cats_educ)), np.zeros(len(cats_educ))
for i, c in enumerate(cats_educ):
    share0[i] = np.sum(w0*(data_male_eval['education'] == c))/np.sum(w0)*100
    share1[i] = np.sum(w1*(data_female_eval['education'] == c))/np.sum(w1)*100

fig = plt.figure()
fig.set_size_inches(6, 5)
plt.bar(ind, share0, 0.4, label='Male')
plt.bar(ind+0.4, share1, 0.4, label='Female')
plt.xticks(ind+0.2, cats_educ, rotation=20, ha='right')
plt.ylabel('Relative Frequency (%)')
plt.xlabel('Education')
plt.legend()
plt.show()

# College graduates vs. high school graduates
diff = (np.average(df[df['education'] == 3]['wage'], weights=df[df['education'] == 3]['weight']) - 
        np.average(df[df['education'] == 1]['wage'], weights=df[df['education'] == 1]['weight']))
print(f"College vs. HS: {diff:.2f}")

occup_names= {1 : 'Management', 2 : 'Business/Finance', 3 : 'Computer/Math', 4 : 'Architecture/Engineering', 5 : 'Life/Physical/Social Science', 
              6 : 'Community/Social Sevice', 7 : 'Legal', 8 : 'Education', 9 : 'Arts/Sports/Media', 10 : 'Healthcare Practitioner', 
              11 : 'Healthcare Support', 12 : 'Protective Services', 13 : 'Food Preparation/Serving', 14 : 'Building Cleaning/Maintenance', 
              15 : 'Personal Care', 16 : 'Sales', 17 : 'Administrative', 18: 'Farming/Fishing/Forestry', 19 : 'Construction/Mining', 
              20 : 'Installation/Repairs', 21 : 'Production', 22 : 'Transportation'}
data_male_eval['occupation'] = data_male_eval['occupation'].replace(occup_names)
data_female_eval['occupation'] = data_female_eval['occupation'].replace(occup_names)

cats_occu = ['Management', 'Sales', 'Administrative', 'Education', 'Healthcare Practitioner', 'Other']

ind = np.arange(len(cats_occu))
share0, share1 = np.zeros(len(cats_occu)), np.zeros(len(cats_occu))
for i, c in enumerate(cats_occu[:-1]):
    share0[i] = np.sum(w0*((data_male_eval['occupation'] == c) & (data_male_eval['education'] ==
                                               'College Graduate')))/np.sum(w0 * (data_male_eval['education'] ==
                                               'College Graduate'))*100
    share1[i] = np.sum(w1*((data_female_eval['occupation'] == c) & (data_female_eval['education'] ==
                                               'College Graduate')))/np.sum(w1 * (data_female_eval['education'] ==
                                               'College Graduate'))*100
share0[-1] = np.sum(w0*((~data_male_eval['occupation'].isin(cats_occu[:-1])) & (data_male_eval['education'] ==
                                               'College Graduate')))/np.sum(w0 * (data_male_eval['education'] ==
                                               'College Graduate'))*100
share1[-1] = np.sum(w1*((~data_female_eval['occupation'].isin(cats_occu[:-1])) & (data_female_eval['education'] ==
                                               'College Graduate')))/np.sum(w1 * (data_female_eval['education'] ==
                                               'College Graduate'))*100

fig = plt.figure()
fig.set_size_inches(6, 5)
plt.bar(ind, share0, 0.4, label='Male')
plt.bar(ind+0.4, share1, 0.4, label='Female')
plt.xticks(ind+0.2, cats_occu, rotation=20, ha='right')
plt.ylabel('Relative Frequency (%)')
plt.xlabel('Occupation | College Graduate')
plt.legend()
plt.show()

# Managers vs. Education
diff = (np.average(df[df['occupation'] == 1]['wage'], weights=df[df['occupation'] == 1]['weight']) - 
        np.average(df[df['occupation'] == 8]['wage'], weights=df[df['occupation'] == 8]['weight']))
print(f"Management vs. Education: {diff:.2f}")

# Managers vs. Healthcare
diff = (np.average(df[df['occupation'] == 1]['wage'], weights=df[df['occupation'] == 1]['weight']) - 
        np.average(df[df['occupation'] == 10]['wage'], weights=df[df['occupation'] == 10]['weight']))
print(f"Management vs. Healthcare: {diff:.2f}")

# Female vs. Male College Managers
diff = (np.average(data_female_eval[np.logical_and(data_female_eval['occupation'] == 'Management', data_female_eval['education'] == 'College Graduate')]['wage'], 
                   weights=w1[np.logical_and(data_female_eval['occupation'] == 'Management', data_female_eval['education'] == 'College Graduate')]) - 
        np.average(data_male_eval[np.logical_and(data_male_eval['occupation'] == 'Management', data_male_eval['education'] == 'College Graduate')]['wage'], 
                   weights=w0[np.logical_and(data_male_eval['occupation'] == 'Management', data_male_eval['education'] == 'College Graduate')]))
print(f"Female vs. Male College Managers: {diff:.2f}")



import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

# 生成示例数据
X = np.random.normal(loc=0, scale=1, size=1000)
Z = np.random.normal(loc=0, scale=1, size=1000)
Y = X + 3 * Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

# 创建可逆结构因果模型
causal_model = gcm.InvertibleStructuralCausalModel(
    nx.DiGraph([('X', 'Y'), ('Z', 'Y')])  # X -> Y <- Z
)

# 自动分配因果机制
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 拟合模型
gcm.fit(causal_model, data)

# 评估父节点的全局相关性
parent_relevance, noise_relevance = gcm.parent_relevance(causal_model, target_node="Y")
print(parent_relevance, noise_relevance)
# 输出示例:
# {('X', 'Y'): 1.1211907746332785, ('Z', 'Y'): 8.92516062224172}, [1.0313637]

from sklearn.linear_model import LinearRegression
from dowhy.gcm.util.general import variance_of_deviations

# 训练线性回归模型
mdl = LinearRegression()
mdl.fit(data[['X', 'Z']].to_numpy(), Y)

# 计算特征相关性
relevance = gcm.feature_relevance_distribution(
    mdl.predict, 
    data[['X', 'Z']].to_numpy(), 
    subset_scoring_func=variance_of_deviations
)
print(relevance)
# 输出示例: [0.98705591 8.95981759]

from dowhy.gcm.util.general import means_difference

# 定义单个观察值
single_observation = np.array([[2, 1]])  # X=2, Z=1, 预期 Y=5

# 计算单个样本的特征相关性
relevance = gcm.feature_relevance_sample(
    mdl.predict, 
    data[['X', 'Z']].to_numpy(), 
    baseline_samples=single_observation, 
    subset_scoring_func=means_difference
)
print(relevance)
# 输出示例: [[2.01652995 3.04522823]]





import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

X = np.random.normal(loc=0, scale=1, size=1000)
Z = np.random.normal(loc=0, scale=1, size=1000)
Y = X + 3 * Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Z', 'Y')]))  # X -> Y <- Z
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)

parent_relevance, noise_relevance = gcm.parent_relevance(causal_model, target_node="Y")
parent_relevance, noise_relevance

from sklearn.linear_model import LinearRegression
from dowhy.gcm.util.general import variance_of_deviations

mdl = LinearRegression()
mdl.fit(data[['X', 'Z']].to_numpy(), Y)
relevance = gcm.feature_relevance_distribution(mdl.predict, data[['X', 'Z']].to_numpy(), subset_scoring_func=variance_of_deviations)
relevance

single_observation = np.array([[2, 1]])

from dowhy.gcm.util.general import means_difference
relevance = gcm.feature_relevance_sample(mdl.predict, data[['X', 'Z']].to_numpy(), baseline_samples=single_observation, subset_scoring_func=means_difference)
relevance

import numpy as np, pandas as pd

# 生成因果链数据 X → Y → Z
X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2*X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3*Y + np.random.normal(loc=0, scale=1, size=1000)
training_data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

**数据特征**：
- **X**: 标准正态分布，根节点
- **Y**: Y = 2X + 噪声，受 X 影响
- **Z**: Z = 3Y + 噪声，受 Y 影响
- **因果链**: X → Y → Z

import networkx as nx
from dowhy import gcm

# 创建概率因果模型
causal_model = gcm.ProbabilisticCausalModel(
    nx.DiGraph([('X', 'Y'), ('Y', 'Z')])  # X -> Y -> Z
)

# 自动分配因果机制
gcm.auto.assign_causal_mechanisms(causal_model, training_data)

# 拟合模型
gcm.fit(causal_model, training_data)

# 执行原子干预：do(X := 1)
samples = gcm.interventional_samples(
    causal_model,
    {'X': lambda x: 1},  # 将 X 固定为 1
    num_samples_to_draw=1000
)

print(samples.head())

# 执行移位干预：do(X := X + 0.5)
samples = gcm.interventional_samples(
    causal_model,
    {'X': lambda x: x + 0.5},  # 将 X 增加 0.5
    num_samples_to_draw=1000
)

print(samples.head())

import numpy as np, pandas as pd

X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2*X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3*Y + np.random.normal(loc=0, scale=1, size=1000)
training_data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))
training_data.head()

import networkx as nx
from dowhy import gcm

causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
gcm.auto.assign_causal_mechanisms(causal_model, training_data)

gcm.fit(causal_model, training_data)

samples = gcm.interventional_samples(causal_model,
                                     {'X': lambda x: 1},
                                     num_samples_to_draw=1000)
samples.head()

samples = gcm.interventional_samples(causal_model,
                                     {'X': lambda x: x + 0.5},
                                     num_samples_to_draw=1000)
samples.head()

import networkx as nx
from dowhy import gcm

causal_graph = nx.DiGraph([('X', 'Y'), ('Y', 'Z')])
causal_model = gcm.StructuralCausalModel(causal_graph)

import numpy as np, pandas as pd

X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2 * X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3 * Y + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))
data.head()

auto_assignment_summary = gcm.auto.assign_causal_mechanisms(causal_model, data)
print(auto_assignment_summary)

causal_model.set_causal_mechanism('X', gcm.EmpiricalDistribution())
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
causal_model.set_causal_mechanism('Z', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))

gcm.fit(causal_model, data)

print(gcm.evaluate_causal_model(causal_model, data))

samples = gcm.interventional_samples(causal_model,
                                     {'Y': lambda y: 2.34 },
                                     num_samples_to_draw=1000)
samples.head()

import pandas as pd
df = pd.read_csv("./dowhy/docs/source/example_notebooks/pension.csv")

df.head()

import networkx as nx
import dowhy.gcm as gcm
from dowhy.utils import plot

treatment_var = "e401"
outcome_var = "net_tfa"
covariates = ["age","inc","fsize","educ","male","db",
              "marr","twoearn","pira","hown","hval",
              "hequity","hmort","nohs","hs","smcol"]

edges = [(treatment_var, outcome_var)]
edges.extend([(covariate, treatment_var) for covariate in covariates])
edges.extend([(covariate, outcome_var) for covariate in covariates])

# To ensure that the treatment is considered as a categorical variable, we convert the values explicitly to strings.
df = df.astype({treatment_var: str})

causal_graph = nx.DiGraph(edges)



plot(causal_graph, figure_size=[20, 20])

import matplotlib.pyplot as plt

cols = [treatment_var, outcome_var]
cols.extend(covariates)
plt.figure(figsize=(10,5))
for i, col in enumerate(cols):
    plt.subplot(3,6,i+1)
    plt.grid(False)
    plt.hist(df[col])
    plt.xlabel(col)
plt.tight_layout()
plt.show()

causal_model = gcm.StructuralCausalModel(causal_graph)
causal_model.set_causal_mechanism(treatment_var, gcm.ClassifierFCM(gcm.ml.create_random_forest_classifier()))
causal_model.set_causal_mechanism(outcome_var, gcm.AdditiveNoiseModel(gcm.ml.create_random_forest_regressor()))
for covariate in covariates:
    causal_model.set_causal_mechanism(covariate, gcm.EmpiricalDistribution())

gcm.auto.assign_causal_mechanisms(causal_model, df)

gcm.fit(causal_model, df)

import numpy as np

percentages = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
bin_edges = [0]
bin_edges.extend(np.quantile(df.inc, percentages[1:]).tolist())
bin_edges[-1] += 1 # adding 1 to the last edge as last edge is excluded by np.digitize

groups = [f'{percentages[i]*100:.0f}%-{percentages[i+1]*100:.0f}%' for i in range(len(percentages)-1)]
group_index_to_group_label = dict(zip(range(1, len(bin_edges)+1), groups))

np.random.seed(47)

def estimate_cate():
    samples = gcm.interventional_samples(causal_model,
                                         {treatment_var: lambda x: np.random.choice(['0', '1'])},
                                         observed_data=df)
    eligible = samples[treatment_var] == '1'
    ate = samples[eligible][outcome_var].mean() - samples[~eligible][outcome_var].mean()
    result = dict(ate = ate)

    group_indices = np.digitize(samples['inc'], bin_edges)
    samples['group_index'] = group_indices

    for group_index in group_index_to_group_label:
        group_samples = samples[samples['group_index'] == group_index]
        eligible_in_group = group_samples[treatment_var] == '1'
        cate = group_samples[eligible_in_group][outcome_var].mean() - group_samples[~eligible_in_group][outcome_var].mean()
        result[group_index_to_group_label[group_index]] = cate

    return result

group_to_median, group_to_ci = gcm.confidence_intervals(estimate_cate, num_bootstrap_resamples=100)
print(group_to_median)
print(group_to_ci)

fig = plt.figure(figsize=(8,4))
for x, group in enumerate(groups):
    ci = group_to_ci[group]
    plt.plot((x, x), (ci[0], ci[1]), 'ro-', color='orange')
ax = fig.axes[0]
ax.spines['right'].set_visible(False)
ax.spines['top'].set_visible(False)
plt.xticks(range(len(groups)), groups)
plt.xlabel('Income group')
plt.ylabel('ATE of 401(k) eligibility on net financial assets')
plt.show()

import pandas as pd

normal_data = pd.read_csv("./rca_microservice_architecture_latencies.csv")
normal_data.head()

axes = pd.plotting.scatter_matrix(normal_data, figsize=(10, 10), c='#ff0d57', alpha=0.2, hist_kwds={'color':['#1E88E5']});
for ax in axes.flatten():
    ax.xaxis.label.set_rotation(90)
    ax.yaxis.label.set_rotation(0)
    ax.yaxis.label.set_ha('right')

import networkx as nx
from dowhy import gcm
from dowhy.utils import plot, bar_plot
gcm.util.general.set_random_seed(0)

causal_graph = nx.DiGraph([('www', 'Website'),
                           ('Auth Service', 'www'),
                           ('API', 'www'),
                           ('Customer DB', 'Auth Service'),
                           ('Customer DB', 'API'),
                           ('Product Service', 'API'),
                           ('Auth Service', 'API'),
                           ('Order Service', 'API'),
                           ('Shipping Cost Service', 'Product Service'),
                           ('Caching Service', 'Product Service'),
                           ('Product DB', 'Caching Service'),
                           ('Customer DB', 'Product Service'),
                           ('Order DB', 'Order Service')])

plot(causal_graph, figure_size=[13, 13])

from scipy.stats import halfnorm

causal_model = gcm.StructuralCausalModel(causal_graph)

for node in causal_graph.nodes:
    if len(list(causal_graph.predecessors(node))) > 0:
        causal_model.set_causal_mechanism(node, gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
    else:
        causal_model.set_causal_mechanism(node, gcm.ScipyDistribution(halfnorm))

gcm.fit(causal_model, normal_data)
print(gcm.evaluate_causal_model(causal_model, normal_data))

outlier_data = pd.read_csv("./rca_microservice_architecture_anomaly.csv")
outlier_data

outlier_data.iloc[0]['Website']-normal_data['Website'].mean()

gcm.config.disable_progress_bars() # to disable print statements when computing Shapley values

median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.attribute_anomalies,
                        causal_model,
                        normal_data,
                        target_node='Website',
                        anomaly_samples=outlier_data),
    num_bootstrap_resamples=10)

bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')

outlier_data = pd.read_csv("./rca_microservice_architecture_anomaly_1000.csv")
outlier_data.head()

outlier_data['Website'].mean() - normal_data['Website'].mean()

import numpy as np

median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    lambda : gcm.distribution_change(causal_model,
                                     normal_data.sample(frac=0.6),
                                     outlier_data.sample(frac=0.6),
                                     'Website',
                                     difference_estimation_func=lambda x, y: np.mean(y) - np.mean(x)),
    num_bootstrap_resamples = 10)

bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')

median_mean_latencies, uncertainty_mean_latencies = gcm.confidence_intervals(
    lambda : gcm.fit_and_compute(gcm.interventional_samples,
                                 causal_model,
                                 outlier_data,
                                 interventions = {
                                    "Caching Service": lambda x: x-1,
                                    "Shipping Cost Service": lambda x: x+2
                                 },
                                 observed_data=outlier_data)().mean().to_dict(),
    num_bootstrap_resamples=10)

avg_website_latency_before = outlier_data.mean().to_dict()['Website']
bar_plot(dict(before=avg_website_latency_before, after=median_mean_latencies['Website']),
                  dict(before=np.array([avg_website_latency_before, avg_website_latency_before]), after=uncertainty_mean_latencies['Website']),
                  ylabel='Avg. Website Latency',
                  figure_size=(3, 2),
                  bar_width=0.4,
                  xticks=['Before', 'After'],
                  xticks_rotation=45)























import networkx as nx, numpy as np, pandas as pd
from dowhy import gcm

X = np.random.uniform(low=0, high=10, size=2000)
Y = -2*X + np.random.normal(loc=0, scale=5, size=2000)
Z = 3*Y + 80 + np.random.normal(loc=0, scale=5, size=2000)
training_data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))

causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
gcm.auto.assign_causal_mechanisms(causal_model, training_data)

gcm.fit(causal_model, training_data)

gcm.counterfactual_samples(
    causal_model,
    {'X': lambda x: 5},
    observed_data=pd.DataFrame(data=dict(X=[0], Y=[5], Z=[110])))

gcm.counterfactual_samples(
    causal_model,
    {'X': lambda x: 5},
    noise_data=pd.DataFrame(data=dict(X=[0], Y=[5], Z=[15])))

import pandas as pd

medical_data = pd.read_csv('./dowhy/docs/source/example_notebooks/patients_database.csv')
medical_data.head()

medical_data.iloc[0:100].plot(figsize=(15, 10))

import networkx as nx
import dowhy.gcm as gcm
from dowhy.utils import plot

causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('Treatment', 'Vision'), ('Condition', 'Vision')]))
gcm.auto.assign_causal_mechanisms(causal_model, medical_data)

plot(causal_model.graph)

gcm.fit(causal_model, medical_data)

print(gcm.evaluate_causal_model(causal_model, medical_data))

specific_patient_data = pd.read_csv('./dowhy/docs/source/example_notebooks/newly_come_patients.csv')
specific_patient_data.head()

counterfactual_data1 = gcm.counterfactual_samples(causal_model,
                                                  {'Treatment': lambda x: 1},
                                                  observed_data = specific_patient_data)
                                                   
counterfactual_data2 = gcm.counterfactual_samples(causal_model,
                                                  {'Treatment': lambda x: 0},
                                                  observed_data = specific_patient_data)
    

import matplotlib.pyplot as plt

df_plot2 = pd.DataFrame()
df_plot2['Vision after option 2'] = specific_patient_data['Vision']
df_plot2['Counterfactual vision (option 1)'] = counterfactual_data1['Vision']
df_plot2['Counterfactual vision (No treatment)'] = counterfactual_data2['Vision']

df_plot2.plot.bar(title="Counterfactual outputs")
plt.xlabel('Alice')
plt.ylabel('Eyesight quality')
plt.legend()    





















pip install pytorch-lightning -U


import tensorflow as tf

print("Num GPUs Available: ", len(tf.config.list_physical_devices('GPU')))

# Additional details about the detected GPUs
print("GPU Details: ", tf.config.list_physical_devices('GPU'))

import torch

if torch.cuda.is_available():
    print(torch.cuda.get_device_name(0))
else:
    print("No GPU available")



import torch
import pytorch_lightning as pl

from dowhy.causal_prediction.datasets.mnist import MNISTCausalAttribute

# dataset class initialization requires mandatory param `data_dir`
# `download` is passed to torchvision.datasets.MNIST and downloads data if not present
data_dir = 'data'
dataset = MNISTCausalAttribute(data_dir, download=True)

from dowhy.causal_prediction.dataloaders.get_data_loader import get_loaders
loaders = get_loaders(dataset, train_envs=[0, 1], batch_size=64,
            holdout_fraction=0.2, test_envs=[3])

from dowhy.causal_prediction.models.networks import MNIST_MLP, Classifier
featurizer = MNIST_MLP(dataset.input_shape)
classifier = Classifier(
    featurizer.n_outputs,
    dataset.num_classes)

model = torch.nn.Sequential(featurizer, classifier)

from dowhy.causal_prediction.algorithms.erm import ERM
algorithm = ERM(model, lr=1e-3)

trainer = pl.Trainer(devices=1, max_epochs=5)

# val_loaders is optional param
trainer.fit(algorithm, loaders['train_loaders'], loaders['val_loaders'])

if 'test_loaders' in loaders:
    trainer.test(dataloaders=loaders['test_loaders'], ckpt_path='best')

from dowhy.causal_prediction.algorithms.cacm import CACM

# `attr_types` list contains type of attributes present (supports 'causal', 'conf', ind', and  'sel' currently)
algorithm = CACM(model, lr=1e-3, gamma=1e-2, attr_types=['causal'], lambda_causal=100.)

trainer = pl.Trainer(devices=1, max_epochs=5)

trainer.fit(algorithm, loaders['train_loaders'], loaders['val_loaders'])

if 'test_loaders' in loaders:
    trainer.test(dataloaders=loaders['test_loaders'], ckpt_path='best')