name: Build & Deploy Docs (For Release)

on:
  release:
    types: [created]
  workflow_dispatch:
    inputs:
      release_tag:
        description: 'Release tag'
        required: true
        type: string

env:
  # Pull the tag either from the release event if this workflow was triggered by a release
  # or from the input if this workflow was triggered manually
  release_tag: ${{ github.event_name == 'release' && github.event.release.tag_name || inputs.release_tag }}

jobs:
  docs:
    runs-on: ubuntu-latest
    container:
      image: "ghcr.io/${{ github.repository_owner }}/dowhy-docs-generation:latest"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Python Dependencies  
        run: poetry install -E plotting -E pydot -E pygraphviz -E econml --with docs

      - run: git config --global --add safe.directory /__w/dowhy/dowhy

      - name: Build
        run: ./docs/generate_docs.sh
        env:
          DOCS_VERSION: ${{ env.release_tag }}

      - name: Prune old doc versions
        run: |
            mkdir -p ./tmp
            mv ./dowhy-docs/$VERSION ./tmp/$VERSION
            rm -rf ./dowhy-docs/*
            mv ./tmp/$VERSION ./dowhy-docs/$VERSION
            echo "Assets ready, listing contents..."
            find ./dowhy-docs
        env:
          VERSION: ${{ env.release_tag }}
          
      - name: Commit and push to gh-pages branch
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          exclude_assets: '.github'
          publish_dir: ./dowhy-docs/
          keep_files: true
