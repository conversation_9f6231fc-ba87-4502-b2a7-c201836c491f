pygraphviz 1.14
===============

We're happy to announce the release of pygraphviz 1.14!

Enhancements
------------

- Avoid subprocess window creation with agraph.AGraph._run_prog (`#514 <https://github.com/pygraphviz/pygraphviz/pull/514>`_).

Documentation
-------------

- Update release process (`#530 <https://github.com/pygraphviz/pygraphviz/pull/530>`_).
- Fix typo (`#533 <https://github.com/pygraphviz/pygraphviz/pull/533>`_).
- Support Python 3.13 (`#542 <https://github.com/pygraphviz/pygraphviz/pull/542>`_).

Maintenance
-----------

- AGraph.draw(): close filehandle for pathlib.Path (`#535 <https://github.com/pygraphviz/pygraphviz/pull/535>`_).
- Fix formatting (`#538 <https://github.com/pygraphviz/pygraphviz/pull/538>`_).
- Test on Python 3.13 (`#539 <https://github.com/pygraphviz/pygraphviz/pull/539>`_).
- Use ruff and update pre-commit config (`#540 <https://github.com/pygraphviz/pygraphviz/pull/540>`_).

Contributors
------------

3 <USER> <GROUP> to this release (alphabetically):

- chrizzftd (`@chrizzFTD <https://github.com/chrizzFTD>`_)
- Jarrod Millman (`@jarrodmillman <https://github.com/jarrodmillman>`_)
- Philipp van Kempen (`@PhilippvK <https://github.com/PhilippvK>`_)

4 reviewers added to this release (alphabetically):

- Jarrod Millman (`@jarrodmillman <https://github.com/jarrodmillman>`_)
- Mridul Seth (`@MridulS <https://github.com/MridulS>`_)
- Philipp van Kempen (`@PhilippvK <https://github.com/PhilippvK>`_)
- Ross Barnowski (`@rossbar <https://github.com/rossbar>`_)

_These lists are automatically generated, and may not be complete or may contain
duplicates._
