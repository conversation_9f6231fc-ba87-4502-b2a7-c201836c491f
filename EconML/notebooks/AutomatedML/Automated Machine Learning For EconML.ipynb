{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Copyright (c) PyWhy contributors. All rights reserved.\n", "\n", "Licensed under the MIT License."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Impressions](https://PixelServer20190423114238.azurewebsites.net/api/impressions/MachineLearningNotebooks/how-to-use-azureml/automated-machine-learning/classification-bank-marketing-all-features/auto-ml-classification-bank-marketing.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Automated Machine Learning For EconML\n", "\n", "This is a version of the evaluation for running in the cloud, it has settings that leverage greater access to compute."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Continuum\\miniconda3\\envs\\azure_automl\\lib\\site-packages\\sklearn\\externals\\joblib\\__init__.py:15: DeprecationWarning: sklearn.externals.joblib is deprecated in 0.21 and will be removed in 0.23. Please import this functionality directly from joblib, which can be installed with: pip install joblib. If this warning is raised when loading pickled models, you may need to re-serialize those models with scikit-learn 0.21+.\n", "  warnings.warn(msg, category=DeprecationWarning)\n"]}], "source": ["import logging\n", "import sys\n", "# Main imports\n", "from econml.dml import DML, LinearDML,SparseLinearDML,CausalForestDML, NonParamDML, KernelDML\n", "from matplotlib import pyplot as plt\n", "import pandas as pd\n", "import os\n", "import copy\n", "\n", "import azureml.core\n", "from azureml.core.experiment import Experiment\n", "from azureml.core.workspace import Workspace\n", "from azureml.automl.core.featurization import FeaturizationConfig\n", "from azureml.core.dataset import Dataset\n", "from azureml.explain.model._internal.explanation_client import ExplanationClient\n", "\n", "#Import scikit-learn model AutoML wrapper\n", "from econml.automated_ml import addAutomatedML,setAutomatedMLWorkspace, EconAutoMLConfig\n", "# Imports for linear double machine learning evaluation\n", "from econml.dml import LinearDML\n", "from matplotlib import pyplot as plt\n", "import pandas as pd\n", "import os\n", "# Helper imports\n", "import math\n", "import numpy as np\n", "from itertools import product\n", "from sklearn.linear_model import Lasso, LassoCV, LogisticRegression, LogisticRegressionCV,LinearRegression,MultiTaskElasticNet,MultiTaskElasticNetCV\n", "from sklearn.ensemble import RandomForestRegressor,RandomForestClassifier\n", "from sklearn.preprocessing import PolynomialFeatures\n", "from sklearn.model_selection import train_test_split\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AutoML Setup\n", "\n", "To begin we will configure our AutoML instance and load environment variable necessary to leverage AutoML."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Workspace configuration has succeeded.\n"]}], "source": ["setAutomatedMLWorkspace(workspace_name = \"<workspace_name>\",\n", "                        subscription_id=\"<subscription_id>\",\n", "                        resource_group=\"<resource_group>\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The first step for using AutoML with double machine learning is to specify the configuration object of your AutoML target, this `AutoMLConfig` object specifies the settings of the AutoML session that will be used to develop your model."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "automl_settings_reg = {\n", "    \"experiment_timeout_minutes\" : 1,\n", "    \"enable_early_stopping\" : True,\n", "    \"iteration_timeout_minutes\": 1,\n", "    \"max_cores_per_iteration\": -1,\n", "    \"n_cross_validations\": 2,\n", "    'preprocess': <PERSON><PERSON><PERSON>,\n", "    \"featurization\": 'off',\n", "    \"verbosity\": logging.INFO,\n", "    \"primary_metric\": 'normalized_mean_absolute_error'\n", "}\n", "\n", "automl_settings_clf = {\n", "    \"experiment_timeout_minutes\" : 1,\n", "    \"enable_early_stopping\" : True,\n", "    \"iteration_timeout_minutes\": 1,\n", "    \"max_cores_per_iteration\": -1,\n", "    \"n_cross_validations\": 2,\n", "    'preprocess': <PERSON><PERSON><PERSON>,\n", "    \"featurization\": 'off',\n", "    \"verbosity\": logging.INFO,\n", "    \"primary_metric\": 'AUC_weighted'\n", "}\n", "\n", "automl_config_reg = EconAutoMLConfig(task = 'regression',\n", "                                     debug_log = 'automl_errors.log',\n", "                                     enable_onnx_compatible_models=True,\n", "                                     model_explainability=True,\n", "                                     **automl_settings_reg)\n", "\n", "automl_config_clf = EconAutoMLConfig(task = 'classification',\n", "                                     debug_log = 'automl_errors.log',\n", "                                     enable_onnx_compatible_models=True,\n", "                                     model_explainability=True,\n", "                                     **automl_settings_clf)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Grid Search Baseline\n", "\n", "In order to evaluate our model against a well performing Random Forest Classifier, we need to specify a class that completes grid search CV over the RandomForestRegressor class and chooses the best resulting model. Below is the class definition and as well as two funtions for instantiating our RandomForestRegressor wrapper."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import GridSearchCV\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import PolynomialFeatures\n", "\n", "class GridSearchCVList:\n", "\n", "    def __init__(self, estimator_list, param_grid_list, scoring=None,\n", "                 n_jobs=None, iid='warn', refit=True, cv='warn', verbose=0, pre_dispatch='2*n_jobs',\n", "                 error_score='raise-deprecating', return_train_score=False):\n", "        self._gcv_list = [GridSearchCV(estimator, param_grid, scoring=scoring,\n", "                                       n_jobs=n_jobs, iid=iid, refit=refit, cv=cv, verbose=verbose,\n", "                                       pre_dispatch=pre_dispatch, error_score=error_score,\n", "                                       return_train_score=return_train_score)\n", "                          for estimator, param_grid in zip(estimator_list, param_grid_list)]\n", "        return\n", "\n", "    def fit(self, X, y, sample_weight = None, **fit_params):\n", "        self.best_ind_ = np.argmax([gcv.fit(X, y, sample_weight = sample_weight, **fit_params).best_score_ for gcv in self._gcv_list])\n", "        self.best_estimator_ = self._gcv_list[self.best_ind_].best_estimator_\n", "        self.best_score_ = self._gcv_list[self.best_ind_].best_score_\n", "        self.best_params_ = self._gcv_list[self.best_ind_].best_params_\n", "        return self\n", "\n", "    def predict(self, X):\n", "        return self.best_estimator_.predict(X)\n", "\n", "    def predict_proba(self, X):\n", "        return self.best_estimator_.predict_proba(X)\n", "\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "\n", "def grid_search_reg():\n", "    return GridSearchCVList([RandomForestRegressor(n_estimators=100, random_state=123),\n", "                             GradientBoostingRegressor(random_state=123)],\n", "                             param_grid_list=[{'max_depth': [3, None],\n", "                                               'min_samples_leaf': [10, 50]},\n", "                                              {'n_estimators': [50, 100],\n", "                                               'max_depth': [3],\n", "                                               'min_samples_leaf': [10, 30]}],\n", "                             cv=3,\n", "                             iid=True,\n", "                             scoring='neg_mean_squared_error')\n", "\n", "\n", "def grid_search_clf():\n", "    return GridSearchCVList([RandomForestClassifier(n_estimators=100, random_state=123),\n", "                             GradientBoostingClassifier(random_state=123)],\n", "                             param_grid_list=[{'max_depth': [3, 5],\n", "                                               'min_samples_leaf': [10, 50]},\n", "                                              {'n_estimators': [50, 100],\n", "                                               'max_depth': [3],\n", "                                               'min_samples_leaf': [10, 30]}],\n", "                             cv=3,\n", "                             iid=True,\n", "                             scoring='neg_mean_squared_error')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DGP \n", "The DGP is described by the following equations:\n", "\n", "\\begin{align}\n", "T =& g(W \\cdot \\beta) + \\eta, & \\;\\eta \\sim \\text{Uniform}(-1, 1)\\\\\n", "Y =& T\\cdot \\theta + m(W\\cdot \\beta) + \\epsilon, &\\; \\epsilon \\sim \\text{Uniform}(-1, 1)\\\\\n", "W \\sim& \\text{Normal}(0,\\, I_{n_w})\\\\\n", "X \\sim& \\text{Uniform}(0,1)^{n_x}\n", "\\end{align}\n", "\n", "where $W$ is a matrix of high-dimensional confounders, $m, g$ can be nonlinear, and $\\beta, \\gamma$ have high sparsity.\n", "\n", "For this DGP, \n", "\\begin{align}\n", "\\theta(x) = \\exp(2\\cdot x_1)\\\\\n", "g(x) = sin^2(x)\\\\\n", "m(x;\\eta,\\gamma)=\\frac{1}{2\\pi}\\frac{sinh(\\gamma)}{cosh(\\gamma)-cos(x-\\eta)}\n", "\\end{align}\n", "\n", "Let’s keep it simple and set $\\eta = 0$ and $\\gamma = 1$."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Structural Model for Estimation\n", "\n", "The model we are trying to estimate is as follows\n", "\n", "$$t_i = g(w_i\\cdot b) + \\eta$$\n", "$$y_i = t_i\\cdot \\theta + m(w_i\\cdot b) + \\epsilon$$\n", "\n", "For this example the true value of the causal parameter will be $\\theta = 0.5$"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Treatment effect function\n", "def te(x):\n", "    return np.sin(2*math.pi*x[0])/2+0.5\n", "def g(x):\n", "    return np.power(np.sin(x),2)\n", "def m(x,nu=0.,gamma=1.):\n", "    return 0.5/math.pi*(np.sinh(gamma))/(np.cosh(gamma)-np.cos(x-nu))\n", "#vectorized g and m for applying to dataset\n", "vg = np.vectorize(g)\n", "vm = np.vectorize(m)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# DGP constants\n", "np.random.seed(123)\n", "n = 10000\n", "n_w = 30\n", "support_size = 5\n", "n_x = 1\n", "# Outcome support\n", "support_Y = np.random.choice(np.arange(n_w), size=support_size, replace=False)\n", "coefs_Y = np.random.uniform(0, 1, size=support_size)\n", "epsilon_sample = lambda n: np.random.uniform(-1, 1, size=n)\n", "# Treatment support\n", "support_T = support_Y\n", "coefs_T = np.random.uniform(0, 1, size=support_size)\n", "eta_sample = lambda n: np.random.uniform(-1, 1, size=n)\n", "\n", "# Generate controls, covariates, treatments and outcomes\n", "W = np.random.normal(0, 1, size=(n, n_w))\n", "X = np.random.uniform(0, 1, size=(n, n_x))\n", "# Heterogeneous treatment effects\n", "TE = np.array([te(x_i) for x_i in X])\n", "\n", "T = vg(np.dot(W[:, support_T], coefs_T))+ eta_sample(n)\n", "Y = TE * T + vm(np.dot(W[:, support_Y], coefs_Y))+ epsilon_sample(n)\n", "\n", "Y_train, Y_val, T_train, T_val, X_train, X_val, W_train, W_val = train_test_split(Y, T, X, W, test_size=.2)\n", "# Generate test data\n", "X_test = np.array(list(product(np.arange(0, 1, 0.01), repeat=n_x)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Train Estimators\n", "We train models in three different ways, and compare their performance.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Default Setting"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["est = LinearDML(model_y=RandomForestRegressor(),\n", "                model_t=RandomForestRegressor(),\n", "                random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_rf_regressor = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["est = LinearDML(model_y=grid_search_reg(),\n", "                model_t=grid_search_reg(),\n", "                random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_cv_regressor = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_31-16_34_42 has started.\n", "Experiment model_t_20_01_31-16_34_42 completed.\n", "Experiment model_y_20_01_31-16_36_44 has started.\n"]}], "source": ["AutomatedLinearDML = addAutomatedML(LinearDML)\n", "est = AutomatedLinearDML(model_y=automl_config_reg,\n", "                         model_t=automl_config_reg,\n", "                         random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_aml_regressor = est.effect(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performance Visualization"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAmEAAAFzCAYAAAB2A95GAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjAsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+17YcXAAAgAElEQVR4nOzde1yO9//A8dfVQSFncpzIOZ2plNJBkjFDDDOHmc2ZHTHDzOG72QwzxmwOO2KztZnZRoWcG9UMRSHlfE4H6XT9/rjm/omQVPddvZ+PR4+57+u6r+t9393rft+fz+d6vxVVVRFCCCGEECXLSN8BCCGEEEKUR5KECSGEEELogSRhQgghhBB6IEmYEEIIIYQeSBImhBBCCKEHkoQJIYQQQuiBib4DeFy1a9dWmzRpou8whBBCCCEe6eDBg1dUVa2T37ZSl4Q1adKEAwcO6DsMIYQQQohHUhTl9IO2yXSkEEIIIYQeSBImhBBCCKEHkoQJIYQQQuhBqVsTJoQQ+pSVlcWZM2fIyMjQdyhCCANibm5Oo0aNMDU1LfBjJAkTQojHcObMGapUqUKTJk1QFEXf4QghDICqqly9epUzZ87QtGnTAj9OpiOFEOIxZGRkUKtWLUnAhBA6iqJQq1atxx4hlyRMCCEekyRgQoh7FebvgiRhQghRily9ehVHR0ccHR2pV68eDRs21N3OzMwskRhef/112rZty5QpU7h48SKurq44OTmxZ8+exzpOZGQkf/75ZzFFKYThkzVhQghRitSqVYvo6GgAZs6ciYWFBW+++WaefVRVRVVVjIyK/nu2qqp8+eWXXL16FVNTU7799lvs7OxYuXLlYx8rMjKSw4cPExgYWORxClEaFNtImKIoqxRFuaQoyuEHbFcURVmsKEq8oiiHFEVxLq5YhBCirIuPj8fW1pZRo0bh7OxMUlIS1atX121ft24dI0aMAODixYv06dOH9u3b4+rqyr59++47XnZ2Nq+//jqurq7Y29vz5ZdfAtC9e3fS0tJwcXFh/vz5TJ06lY0bN+pG4v744w/c3d1xdnamf//+pKWlAbB//37c3d1xcHDAzc2NtLQ0Zs2axXfffYejoyMbNmwogVdJCMNSnCNha4AlwNcP2N4NaPHfjxuw7L//CiFEqeHjc/99zz0HY8ZAejo8/fT924cN036uXIG+ffNu27698LEcPXqU1atXs3z5crKzsx+434QJE5g0aRIdOnQgISGBHj16cPhw3u/LK1aswNLSkoiICG7fvk2HDh0ICAhg48aN1K5dWzcaV716dQ4fPsyiRYu4dOkSH3zwAaGhoVSqVIm5c+fyySef8PrrrzNgwAB++uknnJ2dSU5OxtzcnBkzZugeK0R5VGxJmKqq4YqiNHnILs8CX6uqqgL7FEWprihKfVVVzxdXTELkJyUFrl+H5GS4eRNSU6FGDXB11banp4OZGRgb6zdOIR6lWbNmuLi4PHK/kJAQjh07prt9/fp1bt26RcWKFXX3bdmyhZiYGNatWwdAcnIycXFxNGjQ4IHH3bNnD0ePHsXDwwOAzMxMPD09iYmJoXHjxjg7axMe1apVK9TzE6Ks0eeasIZA0l23z/x3331JmKIorwCvADRu3LhEghNlS24uxMbC4cPaz/Xr8Omn2rYBA2Dz5rz7t2gBx49r/+7eHXbsgKZNwdZW+3Fzg549S/Y5CMP0sJGrSpUevr127Scb+bpX5cqVdf82MjJC+46rufvSeVVViYiIoEKFCg88lqqqfPbZZ3Tu3DnP/Q8bYVNVlcDAQL755ps890dGRsoVpULkQ59XR+b3f6Saz32oqrpCVdX2qqq2r1OnTjGHJcqaxYuhbl1o2xb694e5cyEsDHJytO1jxsAXX8APP8Bff8GuXfD1XZPoL78M77wD7dtDfDzMmwdLlvz/9rffhjVr4LyM4QoDYmRkRI0aNYiLiyM3N5fg4GDdNn9/f5YuXaq7fWdq8W5du3bls88+0yVdx44d49atWw89p4eHBzt27ODkyZMApKWlERcXR9u2bTl9+jSRkZEA3Lx5k5ycHKpUqUJKSsoTP1chSit9joSdAZ6663Yj4JyeYhFlxOXL8NVX8OOP2k/jxmBpCYGB0LkzODpC69Zgbv7/j+ne/eHHfP75vLdv39ZG0kCbqvzmGzh7Vrvt6AhBQTB8ODxk1kaIEjFv3jwCAwNp3LgxNjY23L59G4ClS5cyevRoVq9eTXZ2Nr6+vnmSMoCRI0eSmJiIo6MjAJaWlvz6668PbclSt25dVq5cSf/+/XXlMv73v//RokUL1q5dy+jRo8nIyKBixYqEhYXh5+fHRx99hJOTE++88w59710gJ0QZp9w9XF3kB9fWhG1SVdU2n23dgXHA02gL8herqur6qGO2b99ePXDgQBFHKkqz3FzYtg1WrIDgYMjK0tZzLVsGziVwza2qwqFD8Mcf8Pvv2kjakiUwdixkZmpryWQ9WdkRExNDmzZt9B2GEMIA5ff3QVGUg6qqts9v/2IbCVMUZS3gA9RWFOUM8C5gCqCq6nJgM1oCFg+kAy8WVyyibDt/HgICoHp1GDcORowAG5uSO7+igIOD9jNlijZlaWmpbVuzBmbPhtGjtdiqVi25uIQQQhi24rw6cuAjtqvA2OI6vyi7bt3SRr0OHYKVK6FhQwgJAXf3vNOM+tK8+f//u0ULLSF85x34+GN46y0tGbOw0F98QgghDIO0LbrX7RTYtQgy0/QdibjH7duwdKmW5Lz6Kpw6pSVkAL6+hpGA3cvXV1vsHxEBHTpoi/h799Z3VEIIIQyBJGH3OvYnhLwLn7aDqG8hN0ffEQkgOhratNFGkZo1065uDAuDu8oaGTQXF2292L59MHOmdt/Nm/DTT9qaMiGEEOWPJGH3su8HL/4JVRvCr2NhuRfEh+o7qnLrToLSuLH289dfWs0uX1/9xlVYbm7QsaP27+XLtWrp3brBiRP6jUsIIUTJkyQsP1buMCIE+q6GrDT4tg980xsu5NsGUxSD7GxYuFBrCZOdDTVrakUtAwK0hfBlwRtvaDXM9uzRCsDOmaNNuQohhCgfJAl7EEUB2z4wNgK6vg9nI2G5J/wyBpLP6ju6Mi0mRhsxev11bQH7zZv6jqh4GBvD+PFaJf+ePWH6dBg1St9RidLA2NgYR0dH2rZti4ODAwsWLCA3NxeA7du3oygKK1eu1O0fFRWFoijMnz8fgGHDhj2yYfadc9ja2tKvXz/S09Mfur+Fnq42OXDgABMmTHjoPtu3b6dHjx753l+tWjUcHR1xdHTE39+/UDFER0ez+d62G3p29+uyfft29uzZo9tWkN8/5H0PPPPMM9y4cQOAhIQEKlasqHvd7jRvv9ud19bJyYnWrVvz5ptvFuGzKzskCXsUEzNwHwMTo8FjHPz7o7ZeLHQ2ZJTR7EBPVFWrXN+uHSQmwoYNsGmTNgpWljVoAOvXa3XG7qwXS03V6p8JkZ+KFSsSHR3NkSNH2Lp1K5s3b+a9997Tbbezs2P9+vW62+vWrcPBwaFQ5zh8+DAVKlRg+fLlRRZ/UWrfvj2LFy8u9OO9vLyIjo4mOjqakJCQQh2jMEmYqqq6xLk43P263JuEFdTd74GaNWvmKejbrFkz3esWHR2dbwssLy8voqKiiIqKYtOmTezevbvwT+guOTkls1a7JM4jSVhBVawBAXNg3AFo0wN2zofFThDxBeRk6Tu6MiEzExYtAk9PrfxEUFDZmXosiMBAsLLSktH+/bW1Yhcu6DsqYegsLS1ZsWIFS5Ys0fWKbNy4MRkZGVy8eBFVVfnzzz/p1q1boc/h5eVFfHw8AAsWLMDW1hZbW1sWLVp0376DBw/m119/1d0eNGgQGzduZM2aNfTp04fAwEBatGjBpEmTdPusXbsWOzs7bG1tmTx5su5+CwsLJk+eTLt27fD39yciIgIfHx+sra3ZuHEjkHeUKyIiAg8PD5ycnPDw8MjTpPxxXL58maCgIFxcXHBxcdElD/kdPzMzkxkzZrB+/XocHR1Zv349M2fO1I06Atja2pKQkEBCQgJt2rRhzJgxODs7k5SUxJYtW3B3d8fZ2Zl+/fqRmpoKwJQpU7CxscHe3j7fUSQ7Oztu3LiBqqrUqlWLr//rtTZ48GBCQkJ0r0tCQgLLly9n4cKFODo6snPnTgDCw8Px8PDA2tq6QKNi7u7unD1buFmgO6Nmdx6flpbG8OHDcXFxwcnJSfd+SU9P57nnnsPe3p7+/fvj5ubGneLsFhYWzJgxAzc3N/bu3cvBgwfx9vamXbt2dO3alfP/9Y1bvHix7nUbMGAAADt27NCN2Dk5OZGSkoKqqrz11lvY2trm+dKyfft2fH19ef7557GzsyvU830c+mxbVDrVsIKgL6HDGNgyHTa/Cfs/hy7vQauny1fWUER279YKnVpYaFc81qkDRuX860HPnloZDnt7reDr00/rOyKRn/d+O8LRc0U7Im7ToCrvPtP2sR5jbW1Nbm4uly5d0t3Xt29ffvzxR5ycnHB2dsbMzKxQ8WRnZ/PHH38QGBjIwYMHWb16Nfv370dVVdzc3PD29sbJyUm3/4gRI1i4cCHPPvssycnJ7Nmzh6+++opvv/2W6OhooqKiMDMzo1WrVowfPx5jY2MmT57MwYMHqVGjBgEBAfzyyy/06tWLtLQ0fHx8mDdvHr1792batGls3bqVo0ePMnToUHr27Jkn1tatWxMeHo6JiQkhISFMnTqVn3766aHPb+fOnbrWTP369eOdd95h4sSJvPbaa3h6epKYmEjXrl2JiYl54PFnzZrFgQMHWPJfU9mZd4a083Hs2DFWr17NZ599xpUrV5gzZw4hISFUrlyZefPmsWDBAsaNG0dwcDCxsbEoiqKbBrxbx44d2b17N1ZWVlhbW7Nz506GDBnCvn37WLZsmS55adKkCaNGjcLCwkKXzK1cuZLz58+za9cuYmNj6dmz50NbRuXk5BAaGspLL72ku+/EiRO6161jx473tb262/Xr14mLi6NTp04AzJ07Fz8/P1atWsWNGzdwdXXF39+fZcuWUaNGDQ4dOsThw4d1xwctcbO1tWXWrFlkZWXh7e3Nr7/+Sp06dVi/fj3vvPMOq1at4oMPPuDUqVOYmZnpXrf58+ezdOlSOnbsSGpqKubm5vz8889ER0fzzz//cOXKFVxcXHTxRUREcPjwYZo2bfrA51RUJAkrrIbOMGwTHP8Tts6Adc9DYw9ttKxRO31HVyqoKnzyibZA/a234IMPtEbb5Z2iwMiR4OUFAwZovS1nztTWjJX35FQ82L0t6J577jn69+9PbGwsAwcOfOzpqFu3buk+BL28vHjppZdYtmwZvXv3pnLlygD06dOHnTt35knCvL29GTt2LJcuXeLnn38mKCgIExPto6Zz585Uq1YNABsbG06fPs3Vq1fx8fGhTp06gDZyFh4eTq9evahQoQKBgYGANvJjZmaGqakpdnZ2JCQk3BdzcnIyQ4cOJS4uDkVRyMp69CyFl5cXmzZtynNfSEgIR48e1d2+efMmKSkphTr+vaysrOjQoQMA+/bt4+jRo3T875LpzMxM3N3dqVq1Kubm5owYMYLu3bvnu57Ny8uL8PBwrKysGD16NCtWrODs2bPUrFmzQOvzevXqhZGRETY2Nly8eDHffe68BxISEmjXrh1dunTRbbszHfkwO3fuxN7enmPHjjFlyhTq1asHwJYtW9i4caNutDAjI4PExER27drFxIkTAW300N7eXncsY2NjgoKCAC2RPXz4sC6enJwc6tevD4C9vT2DBg2iV69e9OrVC9CSxNdff51BgwbRp08fGjVqxK5duxg4cCDGxsbUrVsXb29v/v77b6pWrYqrq2uJJGAgSdiTURRo1Q2ad4HIr2D7+/ClH9gGQecZUKOJviM0WJmZMGaMVvG+Vy+torzIy8ZGK/I6cqTWIWDsWKhdW99Ribs97ohVcTl58iTGxsZYWloSExMDQL169TA1NWXr1q188sknj52E3VkPdLeC9hoePHgw3333HevWrWPVqlW6++8ejTM2NiY7O/uhxzQ1NUX5b3bByMhI93gjIyOys7Pv23/69On4+voSHBxMQkICPj4+BYr3Xrm5uezdu5eK9xQiHD9+fIGOb2Jikme9V0ZGhu7fdxJY0F7PLl26sHbt2vuOERERQWhoKOvWrWPJkiWEhYXl2d6pUyeWLl1KYmIic+fOJTg4mA0bNuDl5VWg53j37+JBv4M774Hk5GR69OjB0qVLH3kRxN3uJLjHjx/H09OT3r174+joiKqq/PTTT7Rq1SrP/g97L5ibm2P8XxNeVVVp27Yte/fuvW+/33//nfDwcDZu3Mjs2bM5cuQIU6ZMoXv37mzevJkOHToQEhLy0HPd/TsqbvK9uigYm4DLSzAhCjpNgtjNsMQF/noHbl3Xd3QG59Il6NxZS8CmTdMKllapou+oDJO5uTYdeeCAloDl5Gi9MoW44/Lly4waNYpx48bpEpY7Zs2axbx583QfXk+qU6dO/PLLL6Snp5OWlkZwcHC+H/rDhg3TrRdr2/bhiaqbmxs7duzgypUr5OTksHbtWry9vQsVX3JyMg0bNgRgzZo1hToGQEBAgG5qEdAlow86fpUqVUhJSdHdbtKkCZGRkQBERkZy6tSpfM/ToUMHdu/erVtvl56ezvHjx0lNTSU5OZmnn36aRYsW5Tvi9NRTT3HlyhXi4uKwtrbG09OT+fPn5/v7uDe+x1WtWjUWL17M/PnzCzX617JlS95++23mzZsHQNeuXfn00091iVBUVBQAnp6e/PDDDwAcPXqUf//9N9/jtWrVisuXL+uSsKysLI4cOUJubi5JSUn4+vry4YcfcuPGDVJTUzlx4gR2dnZMnjyZ9u3bExsbS6dOnVi/fj05OTlcvnyZ8PBwXF1dH/u5PSlJwoqSWRXwewcmRIL9c7B3KXziCHuWQLYUgLojNRUSEmDtWq25tUyxPZyiwH8j7UyfDo6OWm0xUX7dmSZq27Yt/v7+BAQE8O677963n4eHh25K5l4jR46kUaNGNGrUCHd39wKd19nZmWHDhuHq6oqbmxsjRozIMxV5R926dWnTpg0vvvjiI49Zv3593n//fXx9fXFwcMDZ2Zlnn322QPHca9KkSbz99tt07Njxia5sW7x4MQcOHMDe3h4bGxvdlaEPOr6vry9Hjx7VLcwPCgri2rVrODo6smzZMlq2bJnveerUqcOaNWsYOHAg9vb2dOjQgdjYWFJSUujRowf29vZ4e3uzcOHCfB/v5uamO7aXlxdnz57F09Pzvv2eeeYZgoOD8yzMf1xOTk44ODiwbt26Qj1+1KhRhIeHc+rUKaZPn05WVhb29vbY2toyffp0AMaMGcPly5ext7dn3rx52Nvb66av71ahQgU2bNjA5MmTcXBwwNHRkT179pCTk8MLL7yAnZ0dTk5OvPbaa1SvXp1FixZha2uLg4MDFStWpFu3bvTu3Rt7e3scHBzw8/Pjww8/1E2XliSloMPLhqJ9+/bqnQWHBu/CYW292IlQqG4F/u9C2z7ldvH+4cPQtq329DMyDLPXo6E7elRbtJ+UBN9+C/366Tui8icmJoY2bdroOwyDlp6ejp2dHZGRkfl+iAqRn5ycHLKysjA3N+fEiRN07tyZ48eP51v+wlDl9/dBUZSDqqq2z29/GYMoTvVsYfDP8MLP2ijZhuHwhR8kFE2tlNJk/XpwdoY7I/ySgBXOnXViLi5aGYsVK/QdkRB5hYSE0Lp1a8aPHy8JmHgs6enpeHp64uDgQO/evVm2bFmpSsAKQ0bCSkpuDvyzDsLmQMo5aNVdK2tRu4W+Iyt2y5dri/A9PeG330D+Lj+59HRtFCwsDOLioFEjfUdUfshImBDiQWQkzFAZGYPTIBh/EPymw6lwWOoGv78BqZf1HV2xUFWYOxdGj9bKLPz1lyRgRaVSJfjlFwgPlwRMCCFKK0nCSlqFStDpTe1KyvYvwoHVWuX98PmQ+fDebKVNbKxW3+qFF+Dnn+Geq73FEzI11aYlAVavhpde0pqdCyGEKB0kCdMXizrQ/WMYux+svSFsttaTMuo7beqyDGjTBvbuha++0hIGUXySkmDVKhgyRCtjIYQQwvBJEqZvtVvAgO/gxT+gan34dQx87g0nwh79WAOkqloF/B9/1G63by8lKErCjBlax4G1a2H4cEnEhBCiNJCPR0Nh5QEjQqHvKrh9E77pDd/00cpclBKqqrUfWrAA9u/XdzTlz+TJMGsWfP01vPKK9vsQZZOxsbGuTpiDgwMLFizQVWjfvn07iqKwcuVK3f5RUVEoiqJrEzNs2LBHNm2+cw5bW1v69etHevrDl0sUpFVOcThw4MAjq7jf3eT73vurVauma+7s7+9fqBiio6PZvHlzoR5bXO5+XbZv356nY0JBfv93BAcHoygKsbGxuvsSEhJQFEVX3wvgypUrmJqaMm7cOID7mpjnp0mTJtjZ2enqoZ0+fbrAz6+skCTMkCiK1vJo3N8QMBfOHoTlnvDLWLh5Tt/RPZSqaq2HPv4Yxo2Djz7Sd0Tl0/Tp2o+1dbktR1cu3Gknc+TIEbZu3crmzZt57733dNvt7OxYv3697va6detwcHAo1DkOHz5MhQoVdAVLDU379u1ZvHhxoR/v5eVFdHQ00dHRhISEFOoYhUnCVFXN09qoqN39utybhD2OtWvX4unpeV+RVmtr6zw9N3/88cdHdkfIz7Zt2zh06BA+Pj7MmTOnUDHeK7+WVsXlSYoCgyRhhsnEDDzGwcRocB8L//4Ai5218ha3C996oji99x68/77W53DxYkkA9GnWrP/vxZmYKCNiZZ2lpSUrVqxgyZIlujYwjRs3JiMjg4sXL6KqKn/++SfdunUr9Dm8vLx0rXUWLFiAra0ttra2utZEdxs8eDC//vqr7vagQYPYuHEja9asoU+fPgQGBtKiRQsmTZqk22ft2rXY2dlha2vL5MmTdfdbWFgwefJk2rVrh7+/PxEREfj4+GBtbc3GjRuBvKNcEREReHh44OTkhIeHB8eOHSvU8718+TJBQUG4uLjg4uLC7t27H3j8zMxMZsyYwfr163UV8+8dBbK1tSUhIYGEhATatGnDmDFjcHZ2JikpiS1btuDu7o6zszP9+vUjNTUVgClTpmBjY4O9vT1vvvnmfTHa2dlx48YNVFWlVq1afP3117rXPyQkRPe6JCQksHz5chYuXJinYn54eDgeHh5YW1s/cFQsNTWV3bt3s3LlyvuSsIoVK9KmTRvulIxav349zz33XKFebwB3d3fOnj2ru/3tt9/i6uqKo6MjI0eO1CU7K1eupGXLlvj4+PDyyy/rRt6GDRvG66+/jq+vL5MnTyYtLY3hw4fj4uKCk5OT7j155MgR3XHt7e2Ji4sjLS2N7t274+DggK2tre4LTGhoKE5OTtjZ2TF8+HBu39Y63zRp0oRZs2bh6enJj3fW3hSSNPA2ZBVrQNe54PoyhM6C8I/g4BrwmQLOQ8HYMFa7qyrcuqWtRfrsM0nADMXp01qLo5EjtfViohj8MQUu5N/frtDq2UG3x/uFWVtbk5uby6VLl3T39e3blx9//BEnJyecnZ3zNGx+HNnZ2fzxxx8EBgZy8OBBVq9ezf79+1FVFTc3N7y9vfO0LhoxYgQLFy7k2WefJTk5mT179vDVV1/x7bffEh0dTVRUFGZmZrRq1Yrx48djbGzM5MmTOXjwIDVq1CAgIIBffvmFXr16kZaWho+PD/PmzaN3795MmzaNrVu3cvToUYYOHUrPnj3zxNq6dWvCw8MxMTEhJCSEqVOn8tNPPz30+e3cuRNHR0cA+vXrxzvvvMPEiRN57bXX8PT0JDExka5duxITE/PA48+aNYsDBw7o+k3OnDnzgec7duwYq1ev5rPPPuPKlSvMmTOHkJAQKleuzLx581iwYAHjxo0jODiY2NhYFEXhxo0b9x2nY8eO7N69GysrK6ytrdm5cydDhgxh3759LFu2TJccNWnShFGjRmFhYaFL5lauXMn58+fZtWsXsbGx9OzZk759+953jl9++YXAwEBatmxJzZo1iYyMxNnZWbd9wIABrFu3jnr16mFsbEyDBg04d65wszZ//vmnrsVWTEwM69evZ/fu3ZiamjJmzBi+++47/P39mT17NpGRkVSpUgU/P788I7zHjx8nJCQEY2Njpk6dip+fH6tWreLGjRu4urri7+/P8uXLmThxIoMGDSIzM5OcnBw2b95MgwYN+P333wGtR2hGRgbDhg0jNDSUli1bMmTIEJYtW8arr74KaA3Fd+3aVajnejdJwkqDGk20tWIdxsKWaVptsX3LocssaNVNr1nPnfZDH3ygJWOyCN9wNG4MAwbAvHla78mJE/UdkShO9xbefu655+jfvz+xsbEMHDjwsaej7vSnBG0k7KWXXmLZsmX07t2bypUrA9CnTx927tyZJwnz9vZm7NixXLp0iZ9//pmgoCBMTLSPms6dO+uq6NvY2HD69GmuXr2Kj48PderUAbSRs/DwcHr16kWFChUIDAwEtJEfMzMzTE1NsbOzIyEh4b6Yk5OTGTp0KHFxcSiKUqBm015eXnmm1UCr+n/06FHd7Zs3b5KSklKo49/LysqKDh06ALBv3z6OHj1Kx44dAcjMzMTd3Z2qVatibm7OiBEj6N69e77r2by8vAgPD8fKyorRo0ezYsUKzp49S82aNQu0Pq9Xr14YGRlhY2PDxYsX891n7dq1uqRjwIABrF27Nk8SFhgYyPTp06lbty79+/d/7NcCtL6bFy9exNLSUjcdGRoaysGDB3H5rwbPrVu3sLS0JCIiAm9vb2rWrAloSfPx48d1x+rXr5+uWf2WLVvYuHGjbkQyIyODxMRE3N3dmTt3LmfOnKFPnz60aNECOzs73nzzTSZPnkyPHj3w8vLin3/+oWnTprrenEOHDmXp0qW616Owz/dekoSVJo3awYub4dhm2PourBsIVh0hYDY0bFfi4fz5J7z8slaE1cZGRsAMjaJobaIuXYLXXoN69bRWR6IIPeaIVXE5efIkxsbGWFpaEhMTA0C9evUwNTVl69atfPLJJ4+dhN1ZE3a3gtaQKH8AACAASURBVHZYGTx4MN999x3r1q1j1apVuvvvHo0zNjYmOzv7occ0NTVF+e8Pi5GRke7xRkZG+a77mT59Or6+vgQHB5OQkICPj0+B4r1Xbm4ue/fupeI9xQ3Hjx9foOObmJjkWe+VkZGh+/edBBa017NLly6sXbv2vmNEREQQGhrKunXrWLJkCWFhea+Y79SpE0uXLiUxMZG5c+cSHBzMhg0b8PLyKtBzvPt3kd/v4OrVq4SFhXH48GEURSEnJwdFUfjwww91+1SoUIF27drx8ccfc+TIEX777bcCnftu27Zto3LlygwbNowZM2awYMECVFVl6NChvP/++3n2DQ4Ofuix7n1tf/rpJ1q1apVnnzZt2uDm5sbvv/9O165d+fLLL/Hz8+PgwYNs3ryZt99+m4CAgPtGWR92rich4xaljaJA6+4wZi88PR8uH9P6UW54Ca6X3JUlf/8NfftCrVpSsd2QGRtrjb47dtRqiEVE6DsiUdQuX77MqFGjGDdunC5huWPWrFnMmzdPNzrwpDp16sQvv/xCeno6aWlpBAcH5/uhP2zYMN16sUct1nZzc2PHjh1cuXKFnJwc1q5di7e3d6HiS05OpmHDhgCsWbOmUMcACAgI0E0tArpk9EHHr1KlCikp/79et0mTJkRGRgIQGRnJqVOn8j1Phw4d2L17t269XXp6OsePHyc1NZXk5GSefvppFi1adF8yDPDUU09x5coV4uLisLa2xtPTk/nz5+f7+7g3voLYsGEDQ4YM4fTp0yQkJJCUlETTpk3vm4J74403mDdvHrVq1Xqs49+tYsWKLFq0iK+//ppr167RuXNnNmzYoJtev3btGqdPn8bV1ZUdO3Zw/fp1srOzHzrV3LVrVz799FNdghkVFQVoX1isra2ZMGECPXv25NChQ5w7d45KlSrxwgsv8OabbxIZGUnr1q1JSEjQ/W6++eabQr8vH0aSsHyUin6axqbaWrEJUeD1JsRugiXttenKW9eL9dTx8Vobojp14I8/oGrVYj2deEIVK8LGjfDqq/CYF8gJA3VnqrBt27b4+/sTEBDAu+++e99+Hh4eunU29xo5ciSNGjWiUaNGuLu7F+i8zs7ODBs2DFdXV9zc3BgxYkSeqcg76tatS5s2bXjxxRcfecz69evz/vvv4+vri4ODA87Ozjz77LMFiudekyZN4u2336Zjx45PdNXa4sWLOXDgAPb29tjY2OiuDH3Q8X19fTl69KhuYX5QUBDXrl3D0dGRZcuW6aa07lWnTh3WrFnDwIEDsbe3p0OHDsTGxpKSkkKPHj10pRsWLlyY7+Pd3Nx0x/by8uLs2bN4enret98zzzxDcHBwnoX5j7J27Vp69+6d576goCC+//77PPe1bduWoUOH5nuMOXPm6N5jjR7xbb1+/foMHDiQpUuXYmNjw5w5cwgICMDe3p4uXbpw/vx5GjZsyNSpU3Fzc8Pf3x8bG5sHNomfPn06WVlZ2NvbY2trqyunsX79emxtbXF0dCQ2NpYhQ4bw77//6hbrz507l2nTpmFubs7q1avp168fdnZ2GBkZMWrUqAK9do9DGnjf49iFFCaui2Ksb3OetquPsVEpmWNLPgvb5kL092BeDbwng8sIMCnaDvSXLoG7O9y8Cbt3wwP+tggDdu0apKZqa8bE45MG3o+Wnp6OnZ0dkZGRD/yQFKIwUlNTsbCwIDs7m969ezN8+PD7kkV9kgbeTyj5VhbZuSrj10YRsHAHwVFnyM4pvlouRaZaQ+j1GYzaCQ2d4a+3YakLHP65SGsUVKoETk6waZMkYKWRqsIzz0C3bloiLURRCwkJoXXr1owfP14SMFHkZs6cqSsi3LRp0weO9JYWMhKWj5xclT8On+fT0HiOXUyhSa1KjPVtTi+nhpgal5K8NT4EtsyAS0egYXsImANWBZtyyI+qwu3b2pWQonQLC4OuXaFLF/jtN23dmCg4GQkTQjyIjIQVAWMjhR72DfhjohfLX2hHpQomvLXhEH4fb2ddRCKZ2aVgZKy5vzYq9uxSuHkWVgfCukFwJb5Qh/vf/8DLC5KTizhOUeL8/ODTT7X1fG+9pe9ohBCi/JIk7CGMjBQCbevx+wRPvhzSnhqVKjDl53/xnb+db/ad5na2gXdJNjIGpxdgfCT4TYOT2+EzN/j9TUi7UuDD/PwzTJsGrVrJIvyyYtQorb3UwoXwzTf6jqb0KW0zCEKI4leYvwsyHfkYVFVlx/HLLA6NIzLxBvWqmjPS25qBro0xNy0Fczqpl2D7B1rVfdNK4PUadBgDphUf+JCoKPD0BDs72L5dpiPLkuxsmDIF3nhDK+YqCubUqVNUqVKFWrVq3VcSQghRPqmqytWrV0lJSaFp06Z5tj1sOlKSsEJQVZU9J67ySUgcEQnXqG1hxihva553a0ylCqWg/u3l4xAyE479DlUbaqNk9gPuK3d/4QK4uGilySIitGKfomzKztaumrS01Hckhi8rK4szZ87kKcAphBDm5uY0atQIU9O8LQUlCStG+05e5dOwOHbHX6VW5QqM8LJmsLsVFmalIBlL2K3VFTsXqfWr6zIbmvnqNp88qVVY/+ILrQehKLsGDYJDh2DfPiiiQtBCCCGQJKxEHEi4xuKweMKPX6Z6JVNGeDZliEcTqpobRpPtB8rNhSM/Q+h7cCNRW9DfZRbU1apcq6q0IyoPtmyBwEB4/nltjZj8zoUQomhIElaCopNu8GloHKGxl6hqbsKLHZsyvGNTqlUy8GQs+zZErIDwj8jNSCE8eRBub79DRUtZLFRezJ4NM2Zo/SbHjtV3NEIIUTZIEqYHh88m82lYHH8duYiFmQlDPax4ydOampWLtoJ9UYuJvMbW6R8zqt0KTM1MUNzHQccJYFZF36GJYpabCz17aqNi4eHQoYO+IxJCiNJPkjA9ijl/kyVh8Ww+fJ6KpsYMdrfiZS9raluYPfrBJezmTW0h/s2b8M/2U1gemqVNVVa2BN+3wWkIGJeCtW6i0K5fh9694cMPwdVV39EIIUTpJ0mYAYi7mMKSbfH89s85KpgYMcjNipGdrLGsahg1H1QV+vaFX3+Fbdu0wqwAnDmgLd5P3Au1W2nrxVp2lUVD5YSsCRRCiCcjSZgBOXE5laXb4vk1+hzGRgrPuzZmpLc19as9uFZXicR1QhsFmzYNXn/9no2qCrG/w9YZcO0ENPGCgNnQwEkvsYril5urVdM3M9O6JQghhCgcScIM0OmraXy27QQ/RZ7BSFF4zqURo7yb0ahGJb3FdP68VgvsgSMfOVlaodft70P6VbDrB37ToYZVSYYpSsjIkbBihdbeKDBQ39EIIUTpJEmYAUu6ls6yHSf48UCSNiXYrhFjfJrTuFbJJGPXr8O6ddoHrlFBm1hl3ITdi2DvUm2UzG0keL0BFasXa6yiZN26BW5uWtHef/6RqvpCCFEYkoSVAudu3GL5jhOs+zuJnFyVXo4NGevbDOs6FsV2TlWFfv20dWD//AM2No95gOQzEDYX/lmrJWCdJoHLCDAx7CtARcHFxEC7duDurl01aVwKunMJIYQhkSSsFLl4M4PPd5zku/2nycrJpadDA8b5Nae5ZdGXiPj8c62R87x5MGnSExzowr/a4v2T26FGE/CfCTa9ZEV3GbFypdbse88ecJJlgEII8VgkCSuFLqVk8OXOU3yz9zQZ2Tk8bVef8X7NaV2vapEc/99/tRIEnTppa34KPBX5IKoK8aGwdTpcOgqNXCBgDjSWYlOlnapCUhI0bqzvSIQQovSRJKwUu5p6m1W7T/HVntOk3s6ma9u6jPdrgW3DaoU+Zk6ONqJx6ZI2DVm3bhEGnJsD0d/DtrmQch5a9wD/96B28yI8idCXH36ALl2gRg19RyKEEKWDJGFlwI30TFbtTmD17lOkZGTTubUl4zu3wPGpwi2G375dG+Hw9X3kroWTmaYt3N/9CWRnQPvh4D0ZKtcuphOK4nbyJLRqpVXV37BBZpuFEKIgJAkrQ5JvZfH1ngS+3HWK5FtZeLesw4TOLWhnVbChieRkqFb4QbTHl3pJK2lx8CuoUBk8X4MOo8FUv3XRROF89JG2fnDlShg+XN/RCCGE4ZMkrAxKvZ3NN3tP88XOk1xLy6Rj81pM8GuBm3WtBz7mwgVwcIB334UxY0owWIDLx2Dru3D8D6jaCPymgX3/IliMJkpSbi74+8Pff2tT2dbW+o5ICCEM28OSsGL9BFQUJVBRlGOKosQrijIln+2NFUXZpihKlKIohxRFebo44ylLLMxMGO3TjF2TfXnn6TYcu5BK/xX76P/5XvbEX+He5FpVYcQIrS+kj48eAq7TCp5fB8N+B4s68MsoWOGtXVEpSg0jI1izRvvvkCFaUiaEEKJwim0kTFEUY+A40AU4A/wNDFRV9ehd+6wAolRVXaYoig2wWVXVJg87royE5e9WZg5rIxL5PPwEF2/epr1VDSZ0boFXi9ooisKKFVpB1k8+gQkT9Bxsbq7WGDzkPUhOhOZdtJ6UdR+3UJnQlx9/1BKxoCB9RyKEEIZNL9ORiqK4AzNVVe363+23AVRVff+ufT4HTqqqOu+//T9WVdXjYceVJOzhMrJy+PFAEsu2n+BccgaOT1Wnb+vmjOxpiYeHwl9/GdAMYFYGRKyA8PmQmQJOL4DvO1Clnr4jE48hOxtMTPQdhRBCGCZ9TUc2BJLuun3mv/vuNhN4QVGUM8BmYHwxxlMumJsaM9i9Cdvf8uX9PnZcSb3NtK0HqPX8LoZPv4CiGNAaQFNz6DgBJkaD2yiIXguLnWDb+3A7Vd/RiQL48kut3tytW/qORAghSp/iTMLyu4D93gxgILBGVdVGwNPAN4qi3BeToiivKIpyQFGUA5cvXy6GUMueCiZGDHRtzLY3ffiwrz1WzbN5e/NBun2yk83/nic314CSsUo1IfB9GBcBLbvCjg/gU2c4sBpysvUdnXiIp56CqCiYOlXfkQghROmj7+nII0CgqqpJ/90+CXRQVfXSg44r05EFFxUF8fFaf8jsnFx+O3SOT8PiOXk5jRaWFozza04P+wYYGxlYwaekv7U2SEn7oE5rbb1YiwApTGWgxo+HJUtg2zY9XfQhhBAGTF9rwkzQFuZ3Bs6iLcx/XlXVI3ft8wewXlXVNYqitAFCgYbqQ4KSJKxgMjPBxQUuX4bjx8Hivz7gObkqmw6dY0lYPHGXUrGuU5lxvs3p6dAAE2NDWSyGdjlnzG8QMhOunYAmXlobpAaO+o5M3CM9XSt9kpMDhw79/3tNCCGEntaEqaqaDYwD/gJigB9UVT2iKMosRVF6/rfbG8DLiqL8A6wFhj0sARMFN3eu9oH4+ed5PxSNjRSedWzIX6924rNBzlQwNuL1H/6h84Id/PB3Elk5BlJzQFHApieM3Q/dPtL6Ua7whp9ehhuJ+o5O3KVSJVi1SusvGRKi72iEEKL0kGKtZVBUlLZYeuBA+Prrh++bm6sSEnORxWFxHD57k0Y1KjLGpzlB7RpiZmJcMgEXREYy7FoI+5Zpo2QdRoHn61CxcG2bRNFLTJQm30IIcS+pmF+OZGdD+/Zw8SIcOQI1axbscaqqsu3YJT4JjeefpBvUr2bOaJ9mPNf+KcxNDSgZSz4DYXPgn3VQsQZ4T4L2L4FJBX1HJv6za5fWIL5yZX1HIoQQ+idJWDmiqvDDD1ClCjxdiP4DqqqyM+4Ki0PjOHD6OpZVzBjp3YznXRtTsYIBJWPn/4Et0+HUDqjRFPxngs2zsnhfz06dgubNYexYWLxY39EIIYT+SRJWTuTmFl0hVlVV2XviKovD4th38hq1LSrwSidrBrlZUdnMQCpzqirEh8DWGdqasUau2uL9xm76jqxcmzhRS8C2bwdvb31HI4QQ+iVJWDmQlaV94L30kvZTlCJOXePTsDh2xl2hRiVTRnhZM8TdiirmpkV7osLKzYHo7yBsLqRegDY9tZGxWs30HVm5lJamXS2pqtrFITItKYQoz/TWwFuUnA8/hL17oVatoj+2a9OafPOSGz+N9sDhqep89NcxPOdtY3FoHMm3sor+hI/LyBich8CESPCZCvGhsNQVNk+CtKv6jq7cqVxZu1ry5EmYMUPf0QghhOGSkbAy4NgxbeShZ09tPVhxO3TmBotD4wmJuUgVcxNe7NiU4R2bUL2SgSyOT7kI29+HyK+gggV4vaG1RTI113dk5cobb4CVlQE0jBdCCD2S6cgyLDcXfH21aZ+YGKhXgr2vj5xLZklYPH8cvoCFmQlD3K0Y4WVNzcoGkoxdioWQd+H4n1DtKfCbDnb9DKiDuRBCiLJOkrAybPdu8PKCL74o+rVgBXXsQgpLtsWz6dA5Kpoa80IHK172sqZOFTP9BHSvU+FaG6Tz/0B9B23xftNO+o6qXFBV+P57SE6GMWP0HY0QQpQ8ScLKuOhobTpS39UZ4i+lsHTbCX6NPksFEyOed7VipLc1dasawDRgbi4c3gChsyA5CVp01XpSWrbWd2RlXt++8Pvv2mhtixb6jkYIIUqWJGFl1LFj0KqVvqO436kraSzdFk9w1FmMjRQGuDzFKO9mNKheUd+hQVYGRHwO4R9DZgo4DQbfqVClBOdxy5nz56FNG3B2htBQ/X9ZEEKIkiRJWBkUHAxBQfDnnxAQoO9o8pd4NZ3Ptsez4eAZFAX6tX+K0d7NeKpmJX2HBunXYMeH8PcXYGwGHuO1HzPpPl0cvvgCXnkFVq6E4cP1HY0QQpQcScLKmORksLGBOnXg77/B1EDKdT3ImevpLN9xgh/+PkOuqtLHuSFjfZtjVcsACkhdPQGh78HRX8GirjYq5vgCGBtIQdoy4u4LSE6ehBo19B2REEKUDEnCyphx42DZMti3D1xc9B1NwZ1PvsXnO06yNiKR7FyVZx0aMNavOc3qGMDoU1KEtng/aT/UaaOtF2vRRebOitDx41o/09699R2JEEKUHEnCypC//wY3Nxg/Hj75RN/RFM6lmxmsCD/Jt/tPczs7l2fsGzDOrzkt61bRb2CqCjEbIWQmXDupXUEZMEe7olIUqexsMJHBRiFEOSAV88uQ2Fho1gxmz9Z3JIVnWdWcaT1s2DXZj5GdmhESc5Gui8IZ891BYs7f1F9giqI1AR+zH7p9CBcOw+fe8PNIuJGkv7jKmC++0BbpZ2ToOxIhhNAvGQkrhbKyDH8d2OO4lpbJql2n+GpPAim3swmwqcuEzi2wbVhNv4FlJMPOBbBvmXa7w2jweh3M9RxXKRcSAl26aC2N3ntP39EIIUTxkunIMuDsWa03ZFBQ2V2mlJyexeo9p1i16xQ3M7Lp3NqS8Z1b4PhUdf0GdiMJwubAoXVQqRZ4T4Z2L4KJgXQGKIUGDYING7SF+oZYZkUIIYqKJGFlQL9+sGkTxMdDw4b6jqZ43czI4us9CXy56xQ30rPo1LIOEzs3p51VTf0Gdv4f2DIdTu2AmtbgPxPa9Cy7WXExunABWreGdu20kTF5CYUQZZWsCSvlNm/WRg2mTSv7CRhAVXNTxvm1YNdkP6Z0a82Rs8kELdvL81/sY9/Jq/oLrL4DDPkVBm3Qaov9MARWddWurBSPpV49+OAD2L4dyuF3KiGEAGQkzOClp4OtLZiZae2JzAykHWNJSs/M5vv9iXwefpLLKbdxbVqTiZ1b4NGsFoq+hlBysiH6O9g2F1Ivagv6/WdqI2SiQHJztZIVdnb6jkQIIYqPTEeWYtOmwdy5sG0b+PjoOxr9ysjKYV1EIst2nODizds4N67OhM4t8G5ZR3/J2O1U2LsEdi+GnExwGQHek6CSnqdOS5mEBGjSRN9RCCFE0ZMkrBT74QeIiID58/UdieHIyMrhx4NnWLYtnnPJGTg0qsZ4vxZ0bmOpv2Qs5QJs+x9EfQMVqkCnN8B1JJgaQPNyA/f99zBkCOzfr60RE0KIskSSMFEmZWbn8lPkGT7bHk/StVu0bVCV8X4tCLCpi5GRnpKxSzGw9V2I+wuqNYbO08G2LxjJ8ssHuXFDu0KyaVPYs0deKiFE2SIL80uhTZvgo4+0mmAifxVMjBjo2piwN3z4qK89abezGfXtQbp9spNNh86Rk6uHLxiWbWDQDzBkI1SsDj+/DF/4wqnwko+llKheXXuv798Pa9boOxohhCg5MhJmgNLTtQbdVapAZGTZKsxanLJzctl06DyfhsVx4nIazS0tGOfbnB729TEx1sP3jdxc+PcHCJ0NN89Ai65aT0rL1iUfi4FTVfDygmPHtJ+asqROCFFGyEhYKfP++3D6NCxdKgnY4zAxNqKXU0O2vObNkuedMFYUXl0fTZeF4Ww4eIasnNySDcjICBwGwPgD2pWTiXthmTv8NhFSLpZsLAZOUbT3e1aW9sVDCCHKAxkJMzDx8dC2rVac9dtv9R1N6Zabq7Ll6AUWh8Zz9PxNnqpZkTE+zQlybkQFEz18/0i7CjvmwYGVWp2xjhPBYxxUqFzysRio1FSwsNB3FEIIUXRkYX4p0r077NypTcnUr6/vaMoGVVUJjbnE4rA4Dp1JpmH1ioz2aUa/9o0wMzEu+YCunoCQmRCzESzqge9UcHoBjPQQiwFSVa1AcbduskhfCFH6SRJWiuzZA4mJMGCAviMpe1RVZcfxy3wSGkdU4g3qVTVnlLc1A1wbY26qhwQocT9seQfO/A2WNtp6seb+5b6Hz++/Q48esHIlDB+u72iEEOLJSBImxF1UVWV3/FUWh8YRkXANyypmvNLJmkFuVlSsUMLJmKrC0V+1kbHrp6CpNwTM1loklVO5udoi/bg4OH5cu3pSCCFKK0nCSoEPPtAW4y9ZAsYyK1Vi9p64yqdhcew5cZXaFhV42cuaFzpYUdnMpGQDyc7U1ortmAe3boB9f63GWLVGJRuHgTh4EFxc4NVXYcECfUcjhBCFJ0mYgUtKgtattTUwGzboO5ry6UDCNRaHxRN+/DI1KpkywsuaIe5WVDEv4ctTb92AXQtg33JtWrLDaPB8DcyrlWwcBuCVV2D1ajh0CNq00Xc0QghROJKEGbiBA+GXXyAmRvrn6VtU4nU+DYsnLPYSVc1NGO7ZlBc9mlKtUgknYzcSIWwOHFoPlWqB9xRo/yIYl5+aJZcvg78/LF4M3t76jkYIIQpHkjADFh6ufcDMmAHvvafvaMQd/55JZnFYHFuPXqSKmQlDPZow3LMpNStXKNlAzkXDlmmQsBNqNoMu70HrHuVm8b6qlpunKoQooyQJM2Du7nD2LMTGQqVK+o5G3OvouZss2RbH5n8vULmCMYPdmzDCqym1LcxKLghVhbgtsHUGXI6FpzpA17nQKN//p8ucjAxYsUKbnjSXfuhCiFJGkjADduqUloR5euo7EvEwxy6ksGRbPJsOncPcxJhBbo15xdsayyolmBXkZEPUN7Dtf5B2Cdr2hs7vQs2mJReDHmzfDr6+MHcuTJ2q72iEEOLxSBJmgLKywMREplpKm/hLqXy2LZ5fos9iaqw1EB/pbU39ahVLLojbqbDnU9izGHKywPVl6PQWVCq7DReDguDPP7Uixo3K5wWjQohSSpIwAzR+vDYK9uuvUpKiNEq4ksbSbfEER53FSFHo174Ro32a0ahGCc4p3zwP2/8HUd+CWRXwehNcXwHTsjdnd+qUdoVknz7w/ff6jkYIIQpOkjADc+QIODjAyJFa02JReiVdS+ez7SfYcDAJgCDnRozxaU7jWiWYjF08qq0Xi98K1RpD5xlgG1Tmev5Mnw5z5sDu3eDhoe9ohBCiYCQJMyCqCoGBEBGhVQSvXVvfEYmicPbGLT7fcYJ1EUnkqCq9nRoy1rc5TWuXYHPuk9u1Kykv/Av1HSFgDjT1KrnzF7PUVBg8WLuS2MlJ39EIIUTBSBJmQO70xVu4UKsGLsqWC8kZfB5+gu/3J5KVk0tPhwaM82tOc8sqJRNAbq5WWyxsNtw8Cy0DtZ6UdVqVzPmFEELkIUmYAfH2hosX4d9/wbT81N0sdy6lZPDlzlN8s/c0Gdk5dLerz3i/FrSqV0LJWNYt2LcMdi2EzDRwHgI+b0OVuiVz/mJ06RJ8+CHMnAkWFvqORgghHu6JkjBFURqrqpr4qPtKSmlPwm7e1NoUtW2r70hESbiaepsvd53i6z0JpGXm0M22HuP8mtO2QQm1IUq7ovWjPLAKTMyh40RwHwsVSnCatIjt3autCZs2DWbP1nc0QgjxcE+ahEWqqur8qPtKSmlNwlJTtUKTJiXcF1oYhhvpmazadYrVuxNIuZ2Nf5u6TOjcHPtG1UsmgCvxEDoTYn6DKvXBdyo4DgKj0nlp7vPPQ3CwVuTYykrf0QghxIMVKglTFKUl0AZYALx216aqwFRVVW2KOtCCKK1J2JgxsGePtiC/Qgl3vhGGI/lWFl/tSWDlrlMk38rCp1Udxvu1oJ1VjZIJIHGftnj/zN9g2RYCZkFz/5I5dxFKSoJWreCZZ2D9en1HI4QQD/awJOxh17C3BfoC1YF+d/14ACOLOsiy7N9/4fPPoVMnScDKu2oVTZnQuQW7JvvyVtdW/JN0g6Blexi8cj8Rp64VfwCNO8BLW6HfGshKg2+D4Ote2hWVpchTT8GkSfDDD7Brl76jEUKIwinIdKSnqqoG82eutI2EqSoEBMDBg1pJilq19B2RMCRpt7P5dt9pvth5kiupmXSwrsmEzi1wt66FUtztFLIz4cBKbc3YrRvgMBD8pkG1hsV73iKSlgZvvQVTpkDjxvqORggh8veka8JWAm+oqnrjv9s1gA9VVX25yCMtgNKWhG3apE2ZLFoEEyfqOxphqG5l5vDd/tN8Hn6Syym3cWlSg/F+LfBqUbv4k7FbN2Dnx7D/c62PlvtY6PgqmFct3vMKIUQ58KRJWJSqqk733CcL8wsoKAgOH9Z+pCSFeJSMrBzW/53E8h0nOJ+cgeNT1ZnYuQU+reoUfzJ2IxFCZ8O/P0Cl2uAzzeuVLAAAIABJREFUBdoNA2PDfuPGxGjNvT//HCqX3os+hRBlVGHXhOn2URRFdz39fyNhhv1X2YCsXw9//SUJmCgYc1Njhno0YftbPsztbcvllNu8uOZvei7ZzdajFynWun7VG0PQF/DyNrBsA5vfhM86QMwmbV7dQF29Ct99B/Pn6zsSIYR4PAUZCXsReAtYD6jAALTpyDXFHl0+SstI2M2b2udWtRIqByXKpszsXIKjzrB02wkSr6XTpn5VJvg1p2vbehgZFePImKrC8T+1npRXjkNjDwiYDY3y/TKnd889p3WjOH4cGpaOJW1CiHLiiUbCVFVdjZZ4JQMpQP+CJmCKogQqinJMUZR4RVGmPGCf5xRFOaooyhFFUb4vyHFLg/fe0y6hv3lT35GI0qyCiRH9XRoT9oY3H/dzICMrh9HfRRL4STgb/zlHTm4xjVApCrTqBqP3QvcFcDUOvuwMP74I104VzzmfwAcfQHa2VsBVCCFKiwK1LVIUpQPQUlXVrxVFqQVU/j/27js8qqLt4/h30gkl9F5DQpeO9JYAIo8PKCIKigUQEAV7V0BBHxUbXRBERVBBEewCSeg19E5CaKG3FEjfnfePgVfEkLqbs0nuz3VxkWzOnnOva5IfM3PuyaxjvlLKHTgEdAeigS3AAK31vhuOCQQWAkFa68tKqfJa63MZnTc/jIRFRkKDBvDwwzB7ttXViILEZtf8uusUU0IjiTx3hdrlivJUUAD/bVwZD/esrC7IoeR4WDcZ1k8BexrcPgw6vQC+pZ13zWx66SUzJRkeDs0tWbEqhBD/ltuF+W8A7YHaWus6SqkqwPda6w6ZPK8tME5rfce1z18F0Fr/74ZjPgAOaa2zHFXyQwi7916zDiwiAipVsroaURDZ7Zo/9pxhSmgEB87EU7OML092DeDuZlXwdGYYizsFYe/C9m/M3ZOdXjSBzMPbedfMothYcxfyc89B8TzaolMIITKT24X5/YBewFUArfVJTNf8zFQBTtzwefS1x25UB6ijlFqnlNqolOqZ3omUUsOUUuFKqfDz589n4dLWWb0aFi82vYskgAlncXNT/KdxJX4f3ZGZg1pQ1NuDF3/YRdcPV7Jg03GS02zOuXCJytBnKjyxDqq2Mt33p7aC3T9Yvnjfzw/GjpUAJoTIP7ISwpK1GS7TAEop3yyeO71Vwzf/lPYAAoEuwABgtlLqX5vpaa1naa1baq1blitXLouXt8Yvv5hu3s8/b3UlojBwc1Pc0bAiv47qwJxHWlKmmDev/bSbrhNXMm/DUZJSnRTGKjSEh36EQT+Bdwn4cQh8HgRH1znnetmwahX07g0pKVZXIoQQGctKCFuslJoG+F27U3IZ8EUWnhcNVLvh86rAqXSOWaq1TtVaHwEOYkJZvjVxolmTUqSI1ZWIwkQpRXD9CiwZ2Y6vBt9OpZJFeHPpXjpPDOOLtUecF8ZqB8HwVXD3DLhyFr7sBd8OhAsRzrleFiQkmH8MzZhhWQlCCJElGW3g7aG1Trv28Z1AD8zo1l9a6z8yPbFSHpiF+cHASczC/IFa6703HNMTs1j/EaVUWWA70FRrffFW53XVNWGJiXDmDNSqZXUlQoDWmg2HLzIpJIJNRy5Rtpg3wzv582Cb6vh6eTjnoqmJsHE6rPkEUhNMo9cur0KxvB291hruuMNsFRYZCaXyaG90IYRIT44W5l97Ugul1Jda60dzeOFewKeAO/CF1vodpdTbQLjW+mdlWoB/BPQEbMA7WuvvMjqnq4awd981bSkOHoSaNa2uRoi/bYy6yJTQCNZFXqR0US+GdqzFw21rUszbSWHsynmzH+XWueDhAx2egTZPgldWVzLk3s6d0KwZPPssfPRRnl1WCCH+JachbA/wP+Bt4Nmbv661/tmRRWaVK4aws2chIACCg2HJEqurESJ9W49dYnJIJKsOnaekrydD2tfikfY1KeHjpO0cLkTCirFw4FcoXtlsDt7kAXBzd871bjJkCMybZ/5hJCPUQgir5DSEdQYeAvoCv9/0Za21ftihVWaRK4awJ54w/cD27oU6dayuRoiM7TgRw5SQCEIOnKO4jwePta/F4PY1Kenr5ZwLHltv7qI8uRUqNILub0NAsHOudYNTp+D33+Gxx8A9b3KfEEL8S05DWF+t9WKl1HCt9UynVpgNrhbC9u2D226DkSNhyhSrqxEi6/acjGVKaAR/7T1LMW8PHmlXgyEd/Cld1AlhTGvYuxhWvAUxx8yC/u7joWIjx19LCCFcSE5D2DatdfPrfzu1wmxwtRA2axa89hocOABly1pdjRDZt/90HFNDI/l9z2mKeLozqE0Nhnb0p1xxJzRgTUuGzZ/D6omQFAtNH4Sg103/MSeZNw8WLYKlS81uTEIIkZdyGsJCADvQEgi7+eta676OLDKrXC2EAcTHS4NIkf9FnI1nalgkv+w8hZeHGwNvr8GIzv6UL+Hj+IslXobVH8LmWaDcoe2TZgG/t+O/kebMgaFDYeFCuO8+h59eCCEylNMQ5oMJYHOBETd/XWsd4sgis8pVQpjNBtu2QatWVlcihGNFnb/CtLDDLNlxEnc3xYBW1RjRpTaV/JzQ/O7yUQh5G/b8CEXLQZdXoPmj4O64OzdtNrOXZHw87N8P3tbvsCSEKERyu3dkJa316ZseUzorO387gauEsC+/NAt+V62CTp2srkYIxzt28SozVh7mh63RuClFv5ZVeaJzbaqVdkKriZNbYdmbcGwdlK0D3d6Cunc6bP5w+XLo0cO0q3juOYecUgghsiSnI2GrtNadr338j15hVq4Tc4UQlpBg7oKsXBk2bZJ1JqJgi76cwIyVh1kYfgKt4d7mVRnZtTY1yhR17IW0hoN/wPIxcDECarSHHuOhSguHnL5nT9i8GY4dk+UDQoi8k9MQtl1r3ezax/8IXTd+La+5Qgh79114/XUZBROFy+nYRGauimLB5uPY7Jo+TSvzVNcA/MsVc+yFbKmw7StY+R5cPQ+N+kHwGChVI1en3bfPtK3o1s1BdQohRBbk6u7Imz9O7/O8ZHUIO3fu78asP/1kWRlCWOZcXBIzV0cxf9MxUtLs/LeJCWOBFRw8vJQcD+smwfqpoG1w+zDo9AIUyf0+RFrLCLYQIm/kNIRFAU9jNvn+mL+75ivgY611bSfUmimrQ9iKFTBgAKxdC3XrWlaGEJa7cCWZz9dEMW/DMRJTbfS6rRKjggKoV7GEYy8UdwpC34Ed88HHDzq/BK2GgkfOVtiPGWOmJL/6yrFlCiFEenIawuZldFKt9SAH1JZtVocwgKQk8HHCXftC5EeXrqYwZ20UX60/xpXkNO5oWIFRQYE0quLn2Aud2WPWix0OgZI1oNtYaNg320NaY8bA+PGwcSO0bu3YEoUQ4ma5ujvS1VgZwsLCoHNncHOz5PJCuLTYhFS+WHeEL9YdIT4pjeB65RkVHEjTaiUde6HIEBPGzu4xi/Z7TIAa7bL89Ph4CAw0f1avlmlJIYRzZRTCJE5k0erVEBRk9ogUQvybn68nz3avw7pXgni+ex22Hr/M3dPW8fAXm9l67JLjLhQQDMNXQ5/pZqpy7p3w3YNwISJLTy9eHN56yywpWLrUcWUJIUR2yUhYFtjt0KYNnD4Nhw5BESf0rBSioLmSnMa8Dcf4fE0Ul66m0D6gDKOCAmnjX8ZxF0lJgI3TYO2nkJoILR+Dzq9AsXIZPi0tDRo3NqNge/bIaJgQwnly26zVQ2udltljecWKEPbttzBwoGnQ+sgjeXppIfK9hJQ05m88zszVUVy4ksztNUszOjiQ9gFlUI5KP1fOmZYWW78ET1/o8DS0eRK8bt1YdtMm8PODevUcU4IQQqQntyHsX+0oClOLiuRk80Pazw+2bgV39zy7tBAFSlKqjQWbjjNz9WHOxiXTokYpRgUF0LlOOceFsfOHYMU4OPgbFK8MQW9AkwfALeNvXLtd1noKIZwjR2vClFLllVJNgCJKqduUUo2v/ekAOGHfEtd09KgJXhMnSgATIjd8PN0Z3KEWq17syvg+DTkdk8ijc7dw97R1hOw/i0OWRpSrAwMWwKO/Q/GKsHQkzOwMh0PTPTwtDfr1g7Fjc39pIYTIroxaVDwGDAaaAjtu+FI8MFdrvcj55f2bFdORqang6ZmnlxSiwEtJs/PjtmimhUUSfTmRhpVLMCookB4NKuDm5oCRMbsd9i6GkLcg5jjUDobub0PFRv84bOBAWLLErPesWjX3lxVCiBvldjqyv9Z6oVMqy4G8DGErVkC7duBbaMb9hMh7qTY7S7afZFpYJEcvJlCvYnFGBwfSs2FFx4SxtGTYPAtWT4SkOGj6IAS9DiUqA2a0u25dE8bmzs395YQQ4ka5DWFewN1ATcDj+uNa63cdWGOW5VUIO3LErAV74gn49FOnX06IQi/NZueXXaeYGhrJ4fNXCShfjFFBAdzVuDLujghjCZdgzUcmkCl3aPcUtH8avIvzwgvw8cewfTs0aZL7SwkhxHW5DWG/AUnAVsB2/XGt9fuOLDKr8iqEXZ+iiIiAKlWcfjkhxDU2u+a33aeZGhrBobNX8C9blCe7BtCnaWU83B2wev7SEQh520xVFi0HXV7lsv8j1A70oGtX+PHH3F9CCCGuy20I26O1bpThQXkoL0JYeDi0agWvvw4TJjj1UkKIW7DbNX/tPcPk0Ej2n46jemlfnuxam77Nq+LpiDAWvRWWvQ7HN0DZOuyu+BaVu9xJmbLSNEwI4Ti5DWGzMRt273NGcdnl7BCmNXTtCvv2QWQklHDwXsRCiOzRWrNi/zmmhEawKzqWKiWLMLJrbfq1qIq3Ry5vWdYaDvwGK8bCxUio0QF79/FQubm0rBBCOERuQ9huoA4QCSQDCtAFtU9YbCz07g333w8jRzrtMkKIbNJas/LQeSatiGDHiRgq+fkwonNt7m9VDR/PXIYxWyps/RJ72Hu4JV4gqmg//IeOgVI1HFO8EKLQym0Iq53e41rrww6oLdvyYjpSa/NH/iUshOvRWrM28gKTVkQQfuwy5Yt7M6yTPw+2rkERr9yFMZ0Ux5dDP2VAzWl4e9tRrYdDx+ehSCkHVS+EKGxytYH3tbBVDmh/7eMYINWxJboWpSSACeGqlFJ0DCzHohFtWfB4a/zLFWXCb/vp+EEoM1cd5mpyzndUUz4lqD1sDAGTt7Fb3wfrp8KkprBhmml1IYQQDpSVkbA3gPZAba11HaVUFeB7rXWHvCjwZlY0axVCuLbNRy4xJTSCNREXKOXrydCO/jzctgbFfXLWZblPHwgLg6ObdlN6y5sQFQYla0C3cdDwHtnxWwiRZbkaCQP6Ab2AqwBa65OALFcXQriM22uVZt6Q1iwe2Y4m1Uoy8a+DdHg/jMkhEcQmZn/g/v33ISEBPv32Nnh4CTz0I3gVgx8eg9nd4NgGJ7wKIURhk5WRsE1a69bXN+1WSvkCG7XWjfOmxH+SkTAhRGZ2RccwOSSSFfvPUtzbg0fb12RIh1qU9PXK8jlCQ6FDB/C6/hS7DXZ+C6ETIP401LsLur0FZQOc8yKEEAVCbhfmvwxUB3oCE4AhwA9aa0v6yEsIE0Jk1d5TsUwNjeSPPWco6uXOw+1qMrRDLcoU887yOVJSbghiACkJsHEarP0U0pKgxWPQ5RUoWtbxL0AIke/lKoRdO8GdQA9Me4q/tNZ/OLbErJMQJoTIroNn4pkSGsFvu0/j4+HOQ22q83gnf8oX98nweTt2wF13wcKFZh/Zf7hyDla+B1u/BE9f6PgstBkJnkWc9jqEEPlPrkPYtZP48s+9I+McU172SAgTQuRU5Ll4poUdZumOk3i6uzHg9uqM6Fybin7ph7GrVyEwEGrWhHXrbrEe//whWDEODv4GJapA0JvQ+H65xVoIAeR+OnIoMB6zb6Sdv5u1Vnd0oVkhIUwIkVtHLlxlelgki7efxF0p7m9VjRFdalOl5L9HsWbPhscfh0WLoF+/DE56dB0sewNObYOKt0H38VC7q/NehBAiX8htCIvA9Ag754zisktCmBDCUU5cSmD6ykh+2BoNQL8WVRnZJYBqpX3//xibDZo2hcREs52ZV0Zr++12szF4yFsQcxwCukP3t6FCAye/EiFEdvz8M1SoAK1bO/9auQ1hfwF9tNZJziguuySECSEcLfpyAp+tOszCLdHYteaeZlV4smsANcsWBeCPP6BXL/juO7OlWabSkmHzLFg9EZLjoemD0PV1KFHJuS9ECJGp+HgICID69WHlSudfL7chrBkwG9iI2TsSAK31c44sMqskhAkhnOV0bCIzV0Xx7ebjpNrs9GlqwljtcsVYu9a0rMhWn9aES7D6QxPI3D2h3ShoNxq8izntNQghMvbmmzBhAmzeDK1aOf96uQ1hm4BNwG7MmjAAtNZzHFlkVkkIE0I427m4JGatjuKbTcdITrNzV+PKjAoKoE6F4iQkgK9v5uf4h0tREPI27P0JipaHrq9Cs4fB3SPz5wohHObkSXOzzd13w4IFeXPN3IawDVrrtk6pLAckhAkh8sqFK8nMXnOErzccJSHFRpPSFVn/eSDhK0pQo0YOThgdbhbvH98AZeua9WJ17pBtkITII4MHw/z5cPCgues5L+Q2hE0AooBf+Od0pLSoEEIUCpevpvDFuiN8sfYoV1PSKJ1YgS9fDKBx1ZLZP5nWcOA3WDEWLkZCzY7QYzxUbub4woUQ//DBB+Ymm7Fj8+6auQ1hJ9J5WFpUCCEKndiEVB4af4QdiUdw90mja91yjAoOpHn1Utk/mS3VNHpd+T9IuAi39YfgN6GkJT9ahRBOktsQ5qm1Ts3ssbwiIUwIYaXYWAion0rVrkfRdY4Qk5hKx8CyjA4OpFXN0tk/YVIcrPsUNkwzo2Sth0PH56FIDkbZhBDpWrUKzpyB/v3zfvY/tyFsm9a6eWaP5RUJYUIIq02bBqNGwbpNaexKOMas1VFcvJpCW/8yjA4OpI1/aVR2f9LHRpvNwXd+ZwJY55eh5RDwyPqm40KIf7PZoFkzswPGvn3gnfWtYx0iRyFMKVUeqAR8B/THdMoHKAHM1lrXc0KtmZIQJoSwWmqq+WHepIn5PDHFxvxNx5i5Oorz8cm0qlmK0cGBdAgom/0wdnoXLH8TolZCqVrQbSw0uFsW7wuRQ3PmwNCh8P33ZiQsr+U0hD0GDAaaAjtu+FI8MFdrvcjRhWaFhDAhhCuJjQU/P/NxUqqN77ecYMbKw5yJS6JZ9ZKMDg6kS51y2QtjWkNkiAlj5/ZB1duhxwSongftvYUoQK5cgTp1Mtn/1clyOx3ZX2u90CmV5YCEMCGEq5g4ET76CA4dghIl/n48Oc3GovBoZqw8zMmYRBpX9WN0UCDB9ctnL4zZbbBjPoS+A1fOQP3e0G0clKnt6JciRIH01lswbhysXw9tLWq2lasQdu0EdwANAZ/rj2mt33VYhdkgIUwI4SrCw03H7VdfhXfT+YmYkmZn8bZopq88zPFLCTSoVILRwQH0aFARN7dshLGUq7B+KqybBLZks1as88tQtIzjXowQBdCPP5oRsI8/tq6G3I6ETQdKAp2AucC9wEat9WBHF5oVEsKEEK5k0CD44QfT/LH6LbpLpNnsLN1xiqlhkRy5cJW6FYrzVFAAvW6rhHt2wlj8WdPSYttX4FUMOj4HrUeAZxHHvBghhMPlNoTt0lo3Vkrt1Fo3UUoVB37UWvdwRrGZkRAmhHAlx49D3brQrx/Mm5fxsTa75tddp5gSGknkuSvULleUp4IC+G/jyni4u2X9oucPwvKxcOgPKFHV9Be7rT+4ZeMcQhRgu3bB77/DM8+Aj0/mxztTRiEsK9+xidf+TlJKVQSSgJoOqk0IIfK16tXh2WdhyRI4fz7jY93dFH2aVuGvZzoxbWBzPNzcePb7nXT/ZDWLwk+QarNnfILrytWFgd/BI79C0bLw03D4vAtErcr16xEiv9Mann/erNlMTMz8eCtlZSRsHPAp0B2YAtiAr7XWrzq9unTISJgQwtXEx5u7sCpVyt7z7HbNsn1nmRwSwb7TcVQtVYQnuwZwb/OqeHlkcVTLboc9P5oNwmOPQ2APsydl+frZfyFCFAB//AG9esGnn8LTT1tdjQMW5t9woiJAEa31pSwe3xOYBLhjeou9d4vj+gGLgFZa6wwTloQwIYSr0hrOnYMKFbL7PE3I/nNMDo1gV3Qslf18eKJrAP1bVsXbwz1rJ0lNgs0zYfVHkBIPzQZB19egeMXsvxAh8qm0NNO/LyUF9u4FLxfodZyr6UilVBGl1KtKqc+01olAaaXUnVl4njswDbgTaAAMUEo1SOe44sBoYFNm5xRCCFf2+OPQsaP5BZAdSim6NajA0ifb8+Vjrajg58ObS/bQ6YMw5q47QlKqLfOTePpA+6fh6R1msf6OBTC5OYT9D5Kv5OwFCZHPzJljGim/955rBLDMZGW8+wtMt/wO1z4/BWSlPcXtQKTWOkprnYLpvN8nnePGAx9g1poJIUS+********************************************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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,6))\n", "expected_te = np.array([te(x_i) for x_i in X_test])\n", "plt.plot(X_test, expected_te, 'b--', label='True effect')\n", "plt.plot(X_test, te_pred_rf_regressor, label='DML Polynomial Features with RF Regressor')\n", "plt.plot(X_test, te_pred_aml_regressor, label='DML Polynomial Features with AML Regressor')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Polynomial Features with Regularization"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["est = DML(model_y=RandomForestRegressor(),\n", "          model_t=RandomForestRegressor(),\n", "          model_final=Lasso(alpha=0.0001, fit_intercept=False),\n", "          featurizer=PolynomialFeatures(degree=5),\n", "          random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_rf_regressor_2=est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["est = DML(model_y=grid_search_reg(),\n", "          model_t=grid_search_reg(),\n", "          model_final=Lasso(alpha=0.0001, fit_intercept=False),\n", "          featurizer=PolynomialFeatures(degree=5),\n", "          random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_cv_regressor_2 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_28-13_56_29 has started.\n", "Experiment model_t_20_01_28-13_56_29 completed.\n", "Experiment model_y_20_01_28-13_58_02 has started.\n", "Experiment model_y_20_01_28-13_58_02 completed.\n", "Experiment model_t_20_01_28-13_59_42 has started.\n", "Experiment model_t_20_01_28-13_59_42 completed.\n", "Experiment model_y_20_01_28-14_01_18 has started.\n", "Experiment model_y_20_01_28-14_01_18 completed.\n"]}], "source": ["AutomatedDML = addAutomatedML(DML)\n", "est = AutomatedDML(model_y=automl_config_reg,\n", "                   model_t=automl_config_reg,\n", "                   model_final=Lasso(alpha=0.0001, fit_intercept=False),\n", "                   featurizer=PolynomialFeatures(degree=5),\n", "                   random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_aml_regressor_2=est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,6))\n", "plt.plot(X_test, te_pred_rf_regressor_2, label='DML Polynomial Features with RF Regressor')\n", "#plt.plot(X_test, te_pred_cv_regressor_2, label='DML Polynomial Features with Grid Search Regressor')\n", "plt.plot(X_test, te_pred_aml_regressor_2, label='DML Polynomial Features with AML Regressor')\n", "expected_te = np.array([te(x_i) for x_i in X_test])\n", "plt.plot(X_test, expected_te, 'b--', label='True effect')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random Forest Final Stage\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["est = CausalForestDML(model_y=RandomForestRegressor(),\n", "                model_t=RandomForestRegressor(),\n", "                discrete_treatment=False,\n", "                n_estimators=1000,\n", "                subsample_fr=.8,\n", "                min_samples_leaf=10,\n", "                min_impurity_decrease=0.001,\n", "                verbose=0, min_weight_fraction_leaf=.01)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_rf_regressor_3 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["est = CausalForestDML(model_y=grid_search_reg(),\n", "                      model_t=grid_search_reg(),\n", "                      discrete_treatment=False,\n", "                      n_estimators=1000,\n", "                      subsample_fr=.8,\n", "                      min_samples_leaf=10,\n", "                      min_impurity_decrease=0.001,\n", "                      verbose=0, min_weight_fraction_leaf=.01)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pcdred_cv_regressor_3 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_28-14_03_08 has started.\n", "Experiment model_t_20_01_28-14_03_08 completed.\n", "Experiment model_y_20_01_28-14_04_43 has started.\n", "Experiment model_y_20_01_28-14_04_43 completed.\n", "Experiment model_t_20_01_28-14_06_20 has started.\n", "Experiment model_t_20_01_28-14_06_20 completed.\n", "Experiment model_y_20_01_28-14_07_56 has started.\n", "Experiment model_y_20_01_28-14_07_56 completed.\n"]}], "source": ["AutomatedForestDML = addAutomatedML(CausalForestDML)\n", "est = AutomatedForestDML(model_y=automl_config_reg,\n", "                         model_t=automl_config_reg,\n", "                         discrete_treatment=False,\n", "                         n_estimators=1000,\n", "                         subsample_fr=.8,\n", "                         min_samples_leaf=10,\n", "                         min_impurity_decrease=0.001,\n", "                         verbose=0, min_weight_fraction_leaf=.01)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_aml_regressor_3 = est.effect(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Performance Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(10,6))\n", "expected_te = np.array([te(x_i) for x_i in X_test])\n", "plt.plot(X_test, expected_te, 'b--', label='True effect')\n", "plt.plot(X_test, te_pred_rf_regressor_3, label='DML Polynomial Features with RF Regressor')\n", "#plt.plot(X_test, te_pred_cv_regressor_3, label='DML Polynomial Features with Grid Search Regressor')\n", "plt.plot(X_test, te_pred_aml_regressor_3, label='DML Polynomial Features with AML Regressor')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AutoML Final Stage Parametric Estimator\n", "\n", "For correctness, all parametric double machine learning estimators (all estimators besides ``NonParamDMLEstimator``) must be trained on linear models. We can specify that using a the parameter ``linear_model_required``.\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["automl_settings_reg = {\n", "    \"experiment_timeout_minutes\" : 10,\n", "    \"enable_early_stopping\" : True,\n", "    \"iteration_timeout_minutes\": 2,\n", "    \"max_cores_per_iteration\": -1,\n", "    \"n_cross_validations\": 2,\n", "    'preprocess': <PERSON><PERSON><PERSON>,\n", "    \"featurization\": 'off',\n", "    \"verbosity\": logging.INFO,\n", "    \"primary_metric\": 'normalized_mean_absolute_error'\n", "}\n", "automl_config_final = EconAutoMLConfig(task = 'regression',\n", "                                       debug_log = 'automl_errors.log',\n", "                                       #For correctness, parametric DML must be linear models\n", "                                       linear_model_required = True,\n", "                                       **automl_settings_reg)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["est = DML(model_y=RandomForestRegressor(),\n", "          model_t=RandomForestRegressor(),\n", "          model_final=RandomForestRegressor(),\n", "          featurizer=PolynomialFeatures(degree=5),\n", "          random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_rf_regressor_4 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["est = DML(model_y=grid_search_reg(),\n", "          model_t=grid_search_reg(),\n", "          model_final=grid_search_reg(),\n", "          featurizer=PolynomialFeatures(degree=5),\n", "          random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_cv_regressor_4 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_28-15_50_01 has started.\n", "Experiment model_t_20_01_28-15_50_01 completed.\n", "Experiment model_y_20_01_28-15_51_44 has started.\n", "Experiment model_y_20_01_28-15_51_44 completed.\n", "Experiment model_t_20_01_28-15_53_26 has started.\n", "Experiment model_t_20_01_28-15_53_26 completed.\n", "Experiment model_y_20_01_28-15_55_30 has started.\n", "Experiment model_y_20_01_28-15_55_30 completed.\n", "Experiment model_final_20_01_28-15_57_16 has started.\n", "Experiment model_final_20_01_28-15_57_16 completed.\n"]}], "source": ["AutomatedDML = addAutomatedML(DML)\n", "est = AutomatedDML(model_y=automl_config_reg,\n", "                   model_t=automl_config_reg,\n", "                   model_final=automl_config_final,\n", "                   featurizer=PolynomialFeatures(degree=5),\n", "                   random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_aml_regressor_4 = est.effect(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Performance Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(10,6))\n", "plt.plot(X_test, te_pred_rf_regressor_4, label='DML Polynomial Features with RF Regressor')\n", "plt.plot(X_test, te_pred_cv_regressor_4, label='DML Polynomial Features with Grid Search Regressor')\n", "plt.plot(X_test, te_pred_aml_regressor_4, label='DML Polynomial Features with AML Regressor')\n", "expected_te = np.array([te(x_i) for x_i in X_test])\n", "plt.plot(X_test, expected_te, 'b--', label='True effect')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## With Nonlinear Models Allowed"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_28-14_48_12 has started.\n", "Experiment model_t_20_01_28-14_48_12 completed.\n", "Experiment model_y_20_01_28-14_50_04 has started.\n", "Experiment model_y_20_01_28-14_50_04 completed.\n", "Experiment model_t_20_01_28-14_51_45 has started.\n", "Experiment model_t_20_01_28-14_51_45 completed.\n", "Experiment model_y_20_01_28-14_53_35 has started.\n", "Experiment model_y_20_01_28-14_53_35 completed.\n", "Experiment model_final_20_01_28-14_55_18 has started.\n", "Experiment model_final_20_01_28-14_55_18 completed.\n"]}], "source": ["AutomatedDML = addAutomatedML(DML)\n", "est = AutomatedDML(model_y=automl_config_reg,\n", "                   model_t=automl_config_reg,\n", "                   model_final=automl_config_reg,\n", "                   featurizer=PolynomialFeatures(degree=5),\n", "                   random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_aml_regressor_4 = est.effect(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Performance Visualization"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,6))\n", "plt.plot(X_test, te_pred_rf_regressor_4, label='DML Polynomial Features with RF Regressor')\n", "plt.plot(X_test, te_pred_aml_regressor_4, label='DML Polynomial Features with AML Regressor')\n", "expected_te = np.array([te(x_i) for x_i in X_test])\n", "plt.plot(X_test, expected_te, 'b--', label='True effect')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AutoML Final Stage Nonparametric Estimator\n", "\n", "For correctness, all nonparametric double machine learning estimators (all estimators besides ``NonParamDMLEstimator``) must be trained on models that enable sample weights. We can specify that using a the parameter ``sample_weights_required``.\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["automl_config_final = EconAutoMLConfig(task = 'regression',\n", "                                       debug_log = 'automl_errors.log',\n", "                                       enable_onnx_compatible_models=True,\n", "                                       model_explainability=True,\n", "                                       #For correctness, parametric DML must have sample weight\n", "                                       sample_weights_required = True,\n", "                                       **automl_settings_reg)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["est = NonParamDML(model_y=RandomForestRegressor(),\n", "                  model_t=RandomForestRegressor(),\n", "                  model_final=RandomForestRegressor(),\n", "                  random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_rf_regressor_6 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["est = NonParamDML(model_y=grid_search_reg(),\n", "                  model_t=grid_search_reg(),\n", "                  model_final=grid_search_reg(),\n", "                random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_cv_regressor_6 = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_28-14_36_01 has started.\n", "Experiment model_t_20_01_28-14_36_01 completed.\n", "Experiment model_y_20_01_28-14_37_57 has started.\n", "Experiment model_y_20_01_28-14_37_57 completed.\n", "Experiment model_t_20_01_28-14_39_43 has started.\n", "Experiment model_t_20_01_28-14_39_43 completed.\n", "Experiment model_y_20_01_28-14_41_26 has started.\n", "Experiment model_y_20_01_28-14_41_26 completed.\n", "Experiment model_final_20_01_28-14_43_03 has started.\n", "Experiment model_final_20_01_28-14_43_03 completed.\n"]}], "source": ["AutomatedNonParamDML = addAutomatedML(NonParamDML)\n", "est = AutomatedNonParamDML(model_y=automl_config_reg,\n", "                           model_t=automl_config_reg,\n", "                           model_final=automl_config_final,\n", "                           random_state=123)\n", "est.fit(Y_train, T_train, X=X_train, W=W_train)\n", "te_pred_aml_regressor_6 = est.effect(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Performance Visualization"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10,6))\n", "plt.plot(X_test, te_pred_cv_regressor_6, label='DML Polynomial Features with Grid Search Regressor')\n", "plt.plot(X_test, te_pred_aml_regressor_6, label='DML Polynomial Features with AML Regressor')\n", "expected_te = np.array([te(x_i) for x_i in X_test])\n", "plt.plot(X_test, expected_te, 'b--', label='True effect')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.ylim(-0.5, 1.5) \n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automated ML for Metalearners\n", "\n", "### DGP\n", "\n", "We use the Response Surface B from [<PERSON> (2011)](https://www.tandfonline.com/doi/pdf/10.1198/jcgs.2010.08162) to generate sythetic outcome surfaces from real-world covariates and treatment assignments (Infant Health Development Program data). Since the original data was part of a randomized trial, a subset of the treated infants (those with non-white mothers) has been removed from the data in order to mimic the observational data setting. For more details, see [<PERSON> (2011)](https://www.tandfonline.com/doi/pdf/10.1198/jcgs.2010.08162).\n", "\n", "\n", "The DGP is described by the following equations:\n", "\n", "$\n", "Y(0) = e^{(X+W)\\beta} + \\epsilon_0, \\;\\epsilon_0 \\sim N(0, 1)\\\\\n", "Y(1) = X\\beta - \\omega + \\epsilon_1, \\;\\epsilon_1 \\sim N(0, 1)\\\\\n", "$\n", "\n", "where $X$ is a covariate matrix, $W$ is a constant matrix with entries equal to $0.5$ and $w$ is a constant calculated such that the CATT equals $4$."]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["from econml.metalearners import TLearner, SLearner, XLearner, DomainAdaptationLearner\n", "from econml.data.dgps import ihdp_surface_B\n", "Y, T, X, expected_te = ihdp_surface_B()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Instantiate T learner\n", "T_learner_rf = TLearner(grid_search_reg())\n", "# Train T_learner\n", "T_learner_rf.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "T_te_rf = T_learner_rf.effect(X)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment models_20_01_27-12_04_27 has started.\n", "Experiment models_20_01_27-12_04_27 completed.\n", "Experiment models_20_01_27-12_06_04 has started.\n", "Experiment models_20_01_27-12_06_04 completed.\n"]}], "source": ["# Instantiate T learner\n", "AutomatedTLearner =  addAutomatedML(TLearner)\n", "T_learner_aml = AutomatedTLearner(models = automl_config_reg)\n", "# Train T_learner\n", "T_learner_aml.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "T_te_aml = T_learner_aml.effect(X)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYgAAAD4CAYAAAD2FnFTAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjAsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+17YcXAAAgAElEQVR4nO3deZSkdX3v8fent+lllp6lWQZmGEm4rhHUEcIl4WI0BjhE3IGbBRO9gx5NYu71mph7jibmJFdNYqJColzFJVFEE1FMCMJJICQRlRlkXwSGGabpWXqmZ3qrXqu+9496Gmqa2qann6qurs/rnD5d9dRT9Xxnpmo+9Xt+y6OIwMzMbL6WehdgZmZLkwPCzMyKckCYmVlRDggzMyvKAWFmZkW11buAxbRhw4bYsmVLvcswM2sYO3bsOBgRfcUeW1YBsWXLFrZv317vMszMGoak3aUe8ykmMzMrygFhZmZFOSDMzKwoB4SZmRXlgDAzs6IcEGZmVpQDwszMilpW8yDMbHm47HN31buERXPDVefWu4QFSy0gJF0HXAIciIiXJdtuAF6Y7NILHImIs4o8dxcwCmSB2YjYmladZtZ8Ht47QgS8dOPqepeypKXZgvgScDXwlbkNEXHZ3G1JfwEMl3n+ayLiYGrVmdmSlfa37ss+dxfT2VxDf7uvhdQCIiLulLSl2GOSBLwd+IW0jm9mZsenXp3UPw/sj4jHSzwewK2SdkjaVu6FJG2TtF3S9sHBwUUv1MysWdUrIK4Ari/z+HkR8UrgIuC9ks4vtWNEXBsRWyNia19f0QUJzcxsAWoeEJLagDcDN5TaJyIGkt8HgBuBs2tTnZmZzalHC+J1wKMR0V/sQUk9klbN3QZeDzxYw/rMzIwUA0LS9cBdwAsl9Ut6Z/LQ5cw7vSRpo6Sbk7snAv8h6T7gR8A/RcQtadVpZmbFpTmK6YoS299RZNsAcHFyeydwZlp1mZkBqN4FNAAvtWFmZkU5IMzMrCgHhJk1JZ9iqswBYWbNSY6IShwQZtaUHA+VOSDMrDk5ISpyQJhZU3I+VOaAMDOzohwQZtaU3IKozAFhZs3Jo5gqckCYmVlRDggza0puP1TmgDAzs6IcEGZmVpQDwszMinJAmFlTinoX0AAcEGZmVpQDwsyak5sQFTkgzKwphROiotQCQtJ1kg5IerBg2x9KekbSvcnPxSWee6GkxyQ9Ien306rRzMxKS7MF8SXgwiLb/zIizkp+bp7/oKRW4BrgIuAlwBWSXpJinWZmVkRqARERdwJDC3jq2cATEbEzIqaBrwOXLmpxZtb0wmeYKqpHH8T7JN2fnIJaW+TxU4A9Bff7k21FSdomabuk7YODg4tdq5ktU86HymodEH8D/BRwFrAX+Isi+xRbIqXkv2VEXBsRWyNia19f3+JUaWbLn5sQFdU0ICJif0RkIyIH/D/yp5Pm6wc2Fdw/FRioRX1m1jwcD5XVNCAknVxw903Ag0V2uxs4Q9ILJHUAlwM31aI+M2seDojK2tJ6YUnXAxcAGyT1Ax8BLpB0Fvl/m13AVcm+G4HPR8TFETEr6X3A94BW4LqIeCitOs2sSTkhKkotICLiiiKbv1Bi3wHg4oL7NwPPGwJrZrZYnA+VeSa1mTWlcCd1RQ4IMzMrygFhZk3J7YfKHBBm1pR8hqkyB4SZNSknRCUOCDNrSm5BVOaAMDOzohwQZtaU3ICozAFhZmZFOSDMzKwoB4SZmRXlgDCzplTswjN2NAeEmTUnJ0RFDggza0pyQlTkgDCzpiTnQ0UOCDNrSs6HyhwQZtaU3IKozAFhZk3KCVGJA8LMmpLjobLUAkLSdZIOSHqwYNufSXpU0v2SbpTUW+K5uyQ9IOleSdvTqtHMmpgToqI0WxBfAi6ct+024GUR8XLgJ8CHyjz/NRFxVkRsTak+M2tizofKUguIiLgTGJq37daImE3u/gA4Na3jm5nZ8alnH8RvAv9c4rEAbpW0Q9K2ci8iaZuk7ZK2Dw4OLnqRZmbNqi4BIen/ALPAV0vscl5EvBK4CHivpPNLvVZEXBsRWyNia19fXwrVmpk1p7ZKO0haAbwF2FK4f0R8dCEHlHQlcAnw2ojiF/2LiIHk9wFJNwJnA3cu5HhmZrYw1bQgvgNcSv4b/3jBzzGTdCHwe8AbIiJTYp8eSavmbgOvBx4stq+ZmaWnYgsCODUi5o9GqkjS9cAFwAZJ/cBHyI9aWgHcpvw0xh9ExLslbQQ+HxEXAycCNyaPtwFfi4hbjvX4ZmbleBRTZdUExPcl/UxEPHAsLxwRVxTZ/IUS+w4AFye3dwJnHsuxzMyOmdfaqKiagPg54B2SngKmyAdvJHMZzMxsmaomIC5KvQozsxpz+6Gyip3UEbEb6AV+OfnpTbaZmTUuJ0RFFQNC0u+Qn69wQvLzd5J+K+3CzMzS5HyorJpTTO8EzomIcQBJHwfuAj6TZmFmZlZf1cyDEJAtuJ/F4WtmtuxV04L4IvDDZEYzwBspMVzVzKxRyN9zK6oYEBHxSUl3kB/uKuA3IuLHaRdmZpYq50NFJQNC0uqIGJG0DtiV/Mw9ti4ihko918zMGl+5FsTXyC+qt4P88ttzlNw/PcW6zMyszkoGRERckvx+Qe3KMTOrkaJrSVuhauZB/Es128zMGkk4ISoq1wfRCXSTX411Lc916awGNtagNjMzq6NyfRBXAe8nHwb3FGwfAa5JsygzM6u/cn0QnwI+Jem3IsKzps1sWfEJpsqqmSg3LOnX52+MiK+kUI+ZWW04ISqqJiBeXXC7E3gt+VNODggzs2WsmpnUR63cKmkN8LepVWRmVgNuQFRWzWJ982WAM6rZUdJ1kg5IerBg2zpJt0l6PPm9tsRzr0z2eVzSlQuo08zMjkM18yC+K+mm5OefgMeA71T5+l8CLpy37feBf4mIM4B/Se7PP+Y64CPAOcDZwEdKBYmZ2YKE2xCVVNMH8ecFt2eB3RHRX82LR8SdkrbM23wpcEFy+8vAHcDvzdvnl4Db5tZ7knQb+aC5vprjmpnZ8avmkqP/Rr7VsAZYRz4kjseJEbE3ee295K9SN98pwJ6C+/3JNjMzq5FqTjG9C/gR8GbgrcAPJP1mynUVW4i3aHtQ0jZJ2yVtHxwcTLksM7PmUU0n9f8GXhER74iIK4FX8fxTQsdiv6STAZLfB4rs0w9sKrh/KjBQ7MUi4tqI2BoRW/v6+o6jLDMzK1RNQPQDowX3Rzn69M+xugmYG5V0JcU7vL8HvF7S2qRz+vXJNjMzq5Fyi/X9z+TmM+QvOfod8qd5LiV/yqkiSdeT75DeIKmf/MikjwHfkPRO4Gngbcm+W4F3R8S7ImJI0h8Ddycv9VFfoMjMFpPHMFVWbhTTquT3k8nPnGqHuBIRV5R46LVF9t0OvKvg/nXAddUey8zMFle5xfr+qJaFNIPLPndX6scIanOp3RuuOrcGRzFLj6dBVFbuFNNfRcT7JX2XIq2xiHhDqpXZgjw8MIwkXnLy6nqXYrakOR8qK3eKaW69pT8vs48dg1p8637LX/8nba0t/oZvVkHOTYiKyp1i2iGpFfgfEfGrNazJjoNUixNMZo0vHBAVlR3mGhFZoE9SR43qsePkfDCrTs75UFE1azHtAv5T0k3A+NzGiPhkWkXZwqkmXdRmjS/nhKiomoAYSH5aeG7oq/9mlyi3IMwqi4BcvYtoANUExMMR8c3CDZLellI9ZmapCwIiiAj325VRzVIbH6pym5lZQ4jInwbJ+jRTWeXmQVwEXAycIunTBQ+t5viX/LaU1GqinFkjm4uF2VzQ1lrXUpa0cqeYBoDtwBuAHQXbR4HfTbMoWzg3mc2qkAxx9VyI8srNg7gPuE/S1yJiBiBZWXVTRByuVYF2bMJNCLOK5mLB+VBeNX0Qt0lanVwn+j7gi5I8xHWJ8vvdrHr+vJRXTUCsiYgR8leU+2JEvAp4Xbpl2UJ5dqiZLZZqAqItufLb24F/TLkeM7Oa8Req8qoJiI+Sv5rbExFxt6TTgcfTLcvMLH0e0FFexYlyySS5bxbc3wm8Jc2ibOH8hjernj8t5ZWbB/HBiPiEpM9Q/HoQv51qZbYgzgczWyzlWhCPJL+316IQWxzOB7PquQeivHLzIL6b/P7yYh5Q0guBGwo2nQ58OCL+qmCfC8hf+/qpZNO3IuKji1nHcuVTTGbVcyd1eeVOMRW91OichV5yNCIeA85KjtEKPAPcWGTXf4+ISxZyjGbmeDCrbO4/tpyXdC2r3CmmuUuNvhk4Cfi75P4V5K8RsRheCzwZEbsX6fXMzCqaazhk3YIoq9wppn8DkPTHEXF+wUPflXTnIh3/cuD6Eo+dK+k+8mtCfSAiHiq2k6RtwDaAzZs3L1JZZra85YNh1k2IsqqZB9GXzH0AQNILgL7jPXByGdM3UDCEtsA9wGkRcSbwGeDbpV4nIq6NiK0RsbWv77jLMrMmMNdwcD6UV80Fg34XuEPSzuT+FpJv7MfpIuCeiNg//4FkaY+52zdL+mtJGyLi4CIc18ya3HPLfTshyqlmotwtks4AXpRsejQiphbh2FdQ4vSSpJOA/RERks4m39I5tAjHNDN7rg/CFwwqq5oWBEkg3LdYB5XUDfwicFXBtncnx/os8FbgPZJmgQng8vB4tKp4tW+zyiJpQzggyqsqIBZbRGSA9fO2fbbg9tXA1bWuaznwBYPMqjDXB+F8KKuaTmprIG5nmR0Lf2DKqRgQks6T1JPc/lVJn5R0Wvql2UL4Eopm1fPHpbxqWhB/A2QknQl8ENgNfCXVqmzB3GQ2s8VSTUDMJh3ElwKfiohPAavSLcsWKueEMKua++vKq6aTelTSh4BfBc5P1k9qT7csW6icz6maVZbkgvOhvGpaEJcBU8A7I2IfcArwZ6lWZQvm97tZ9fx5Ka+aiXL7gE8W3H8a90EsWW4ym1WmJBr8eSmvmlFMPyvpbkljkqYlZSUN16I4O3YtfsObVa3FH5eyqjnFdDX5ZTEeB7qAdwHXpFmULZzzwax68kmmsqpdauMJSa0RkQW+KOn7KddlC+S3u5ktlmoCIpMszX2vpE8Ae4GedMuyhfLEHzNbLNWcYvo1oBV4HzAObALekmZRtnCeSW1mi6WaUUxzlwOdAP4o3XLseHmenFn1wvOGyioZEJK+ERFvl/QARVa0ioiXp1qZLYjf8GbVc4O7vHItiN9Jfl9Si0JscbiT2qyyuS9SPiVbXsmAiIi9ye+5U0xI2gAc8sV7li5P/DGrQhz1y0oo2UmdTJC7Q9K3JL1C0oPAg8B+SRfWrkQ7Fp4oZ1Y9L25ZXrlTTFcDfwCsAf4VuCgifiDpReSvJX1LDeqzY9TqfDCraC4WnA/llRvm2hYRt0bEN4F9EfEDgIh4tDal2UL4FJNZ9XxN6vLKBUSu4PbEvMeO+29V0i5JD0i6V9L2Io9L0qclPSHpfkmvPN5jNgPng1llc92o7qQur9wppjMljZAfGNOV3Ca537lIx39NRBws8dhFwBnJzznkr2x3ziId18ya2FwuzLoFUVa5UUyttSykiEuBryQjpn4gqVfSyXOjq6y4CDzW1ayCuViYzebK7tfsqllqIy0B3Cpph6RtRR4/BdhTcL8/2XYUSdskbZe0fXBwMKVSG4dHIJtVNvc5mcn681JOPQPivIh4JflTSe+VdP68x4t9Dy42o/vaiNgaEVv7+vrSqLOh+O1uVl4299x6A7M5tyDKqVtARMRA8vsAcCNw9rxd+skvDDjnVGCgNtU1LjcgzMqbKTitNOsWRFl1CQhJPZJWzd0GXk9+El6hm4BfT0Yz/Sww7P6Hyvx2NyuvsGM6mwtPliujqgsGpeBE4MZkzH4b8LWIuEXSuwEi4rPAzcDFwBNABviNOtXaUNwHYVbe/LkP2QhaPLKjqLoERETsBM4ssv2zBbcDeG8t61oOHA9m5c1vMWRzQXu9x2wuUfXspLYUuAVhVt78yXH+yJTmgFhm/GY3K29+l4NnU5fmgDCzpjL/olqOh9IcEMuM12Iyq2BeIvi0bGkOiGXGq7malZedFwieK1eaA8LMmsr8yXEzToiSHBDLjFvLZuVNzWbn3XdAlOKAWGZ8ARSz8sanjg6IzNRsnSpZ+hwQy0g2F2RzOY/KMCtjZHLmqPvDEzMl9jQHxDIyNjlL4FaEWSmjkzNMzRx9Sml4YuaoBfzsOQ6IZWR0Kv9NyAFhVtzAkcnnbYuAfcPP324OiGUlM50/t+qAMHu+6dkcA0cmij729FDG8yGKcEAsI3NNZy8dYPZ8Tw6OlfzyNDGd5emhTI0rWvocEMuIx3ObFXdobIpnDhdvPcx5cnCMcY9oOooDYhmZazi4AWH2nMz0LA8OjFTcL5eD+/qPuMO6gANiGXnuHKoTwgzyk+LuffoIM1VOhstMZblvzxH34yUcEMvITLKEgFsQZvl+hR27Dz87eKNaRzIz/Pjpw0x7hrUDYjmZTJYQyEV4RIY1teHMDD/aNURm6tjCYc6RzAzbdw2RmW7uPgkHxDIxPjVLdq4FAYy6s82aUETw1MFxdjw9VPVppVIy01l++NRQyaGxzaDmASFpk6TbJT0i6SFJv1NknwskDUu6N/n5cK3rbDQHRqeOuj84777Zcjc+Ncvduw7z5IGxRVvCO5sNHh4Y4d49R5icWVhrpJG11eGYs8D/ioh7JK0Cdki6LSIenrffv0fEJXWor+FEBHvnfcsZODLBC9b30NLi60PY8jY9m2PXoXH6D2dSu7bDwdEp7spMs2V9D5vXddPaJJ+rmgdEROwF9ia3RyU9ApwCzA8Iq9KB0anndcRNzeTYNzLJxt6uOlVllq5sLtgzlGHXofHnXeMhleNlgycPjNF/OMPpfSvZuKZz2V+gqx4tiGdJ2gK8AvhhkYfPlXQfMAB8ICIeKvEa24BtAJs3b06n0CUsl8u/aYvZOTjOias7m+bbjjWH6dkc/Ycz7Dk8cdz9DAsxNZPjkYERdh0cZ/O6bk5e00lb6/Lszq1bQEhaCfwD8P6ImD+L5R7gtIgYk3Qx8G3gjGKvExHXAtcCbN26temG7uw5nCk5jG9yJsvuQ+Oc3reyxlWZLb7M9CxPD2XYe2RyScxTmJjO8ti+UZ4cHOPUtd2curaLzvbWepe1qOoSEJLayYfDVyPiW/MfLwyMiLhZ0l9L2hARB2tZ51I3OZNl5+B42X12HRrnpDWddHfUtbFotiC5XDA4NsUzRyYYGpuudzlFzWaDXQfH2X1onL5VK9jY28X6no5lcfqp5v9rKP+39gXgkYj4ZIl9TgL2R0RIOpv8aKtDNSxzyYsIHhoYrvhNKpeDhwZGeNXmte6wtoYxNjXLwJEJ9g5P1uU00kJEwIGRKQ6MTNHZ3srG3k429jZ2q6IeXyvPA34NeEDSvcm2PwA2A0TEZ4G3Au+RNAtMAJeHZ34d5fEDYxwer+5KWMOZGR7bP8qLT16dclVmCzc1m+XAyBR7hycZafCrvM217ncOjrO2p4OT13RywqoVDddXUY9RTP8BlP0qGxFXA1fXpqLG8+TgGE8fOraliZ85PEFbi/jpE1Yui6avLQ/ZXDA4OsXe4QmGxqeX5TIxh8enOTw+zaMt0Leyk5PWdLK+p6MhWvQ+Md1Asrngkb0jC7761e5DGSZncrz45FUN903Glo+I4ND4NPuGJxkcnVoSHc61kMvB/pFJ9o9M0t7WwomrV3DS6k7WdLUv2S9tDogGMTI5w4PPDC94bZk5+0cmGZ2c4aUb17Cmu32RqjOrbDgzw76RSfaNNE6/QlpmZnP0D03QPzRBZ3srJ63JtyxWrlha/yUvrWrseWazOZ46OJ5cEnFxXjMzneXuXUNsWtfN6X09tLs1YSmZnMmyd3iSvUcmjnlV1WYxOZNl18Fxdh0cZ1VnGxt7uzhpTeeS+Fw6IJaoiGDv8CRPDo49eynRxbZnKMO+kUl+qq+HU3q7lmwz1xpLLhccGJ1iYHjhQ1M/8b1HF7mqo+1JLi+a9nEAPvhLL6p639HJWR7bN8rjB0bZsLL+Q2YdEEtMRLB/ZIqdB8eO+3RSNWZmczy6d5SnD2XYsqGHk1Z3NkTnmS09U7NZ+g9P0F+nGc7LSS733JDZro5WNq3tZmNv7WdsOyCWiOnZHANH8h+ueqwamZnO8vDAyLOzQjf2drKirXHHb1vtjE8lM5yHJxZtsbxj+da9EHMth7SPsxgmprP8ZP8oOw+OceraLk5d212zuRUOiDqaG+K3b2SSofGp1FaiPBZTMzmePDDGzsEx1vV0cPKaLjas7PCoJ3uebC7YOTi2qP1jVlp+xnaGPUMTnN6XX1U27VNPDogam5zJcmh8moOjUwyNTy/ZIX4RcGhsmkNj07S0wLqeFWxY2cGGlSsaemaoLY5DY1M8um+UCXc811w2Fzy+f4x9w5O8eONqVnemNxrRAZGybC44kpnmcCb/n+3oZONd6S2Xy6+Hf3B0ChhlZWcb63s6WNfTQW93h1eLbTJD49P8+Okj9S6j6Y1OzrJj12HOOX1damutOSAWWS4XjEzOcDgzw9D4NMMT00vi1NFiGpucZWxylt2HMrS0wJqudtZ25wNjdWe7O7mXsenZHA8NDNe7DEtkc8H9/cOcvWVdKp87B8RxighGp2Y5PD7N0Pg0RzIzS/a0URpyOTg8PsPh8Rl2Do7T2iLWdLezrruDtT0drO5s8/DZZWQ6m0tt2LUtzPjULLkIWsqvYLQgDogFmOtHODSW70eo9mpWtRhzXavx3aVGf2RzwdDY9LPj39taxdruDta7/2JZ6Olopa1VNbmCm1Vn5Yq21AaROCCqNDwxw+DoJIOj04xPNV4/Qr3MZvMjtQaT/ovuFa30rVzBCas6Wd3l1kWjkcRZm3q5v3+Yac91qLvujlZedsqa1F7fAVHG3Jr0B0amFmVuQi3GXC/18d2ZqSy7pzLsPpRhRXsLJ6zqZGNvJ6tSHIlhi6u3u4OzX7CO+/uHG35Z7ka2YdUKXrpxdapLcjgg5snlgv2jk+wZmvCbP2VTMzn2DGXYM5RhVWcbp67r5mTP5G4Ine2tvHrLWgZHp3h6KMORjD8rtbJ+ZQenre9hXU9H6sdyQBQYODLBzsHxusxkbnajk7M8MjDCzsExTu9bySm9XfUuySqQxAmrOzlhdSfDmRl2D40zODrlSXMpaGmBk1Z3sXl9d01XfHVAJO7vP8KBkal6l9H0pmZyPDIwwoGRSc7a1Os+igaxprudl3f3Mj2bY3Bsiv0jkxxephcAqpWWFtiQ9NfVazUDBwT5RcYOjjkclpLDmWmmZnMe9dRgOtpaOKW3i1N6u5iezXFwLiwyy28+UBpaW5QPhdUrWN9T/yVuHBDAirZWfvb09Ty+f2xJL3/RDFpbxNqeDs44YaXDocF1tLWwsbeLjb1dzGZzz04ePTQ25WtDFFjZ2caGlR2s61lBb9fSmmhal4CQdCHwKaAV+HxEfGze4yuArwCvAg4Bl0XErjRr6u5o48xNveRy+Ylv+eUxZhibnHWfRIo621vpWdHK2u4O1nZ3sKqzbUl9QGxxtLW20LdqBX2rVgCrmJjOt9qHxqcZykyTbaJ5Fe1tLc8uVbOup2NJfxGqeUBIagWuAX4R6AfulnRTRDxcsNs7gcMR8dOSLgc+DlxWi/paWsSarnbWdLVz2vr8tmwuyEzPkpnOkpnOMj6Vvz0xk/W691Vob2uhq72V7o78T8+KtuR2m9dxalJdHa1sWtfNpnXdzy5Pc2h8msPj0wxPzCyrvouWlvzQ4HXdHaxb2cGqFY0z/6ceLYizgSciYieApK8DlwKFAXEp8IfJ7b8HrpakiPq8bVpbxKrO9qJj9bO5YHImy+RMPjAmZ3LP3p+cyZErKPlPb34k9Vr3DE0A8Oe3Ppbqcf7g4hc/e7tForO9hc721uQnf7srue8QsHJaWkRvd37hR/ryl9l9++fuYjYbzORyqYRF2p+TFuVbTe0t4hvv/q8N+xmoR0CcAuwpuN8PnFNqn4iYlTQMrAcOzn8xSduAbQCbN29Oo96yWltEz4o2eqoYenbN7U+kXs/LT01vVmWh8/9LX02OY82nrbWF9tYW2luhi3ROv9TqcwI0bDhAfQKi2N/W/O8I1eyT3xhxLXAtwNatW5d0w/SGq86tdwlmDcGflaWhHmOo+oFNBfdPBQZK7SOpDVgDDNWkOjMzA+oTEHcDZ0h6gaQO4HLgpnn73ARcmdx+K/Cv9ep/MDNrVjU/xZT0KbwP+B75Ya7XRcRDkj4KbI+Im4AvAH8r6QnyLYfLa12nmVmzq8s8iIi4Gbh53rYPF9yeBN5W67rMzOw59Z3HbWZmS5YDwszMinJAmJlZUQ4IMzMrSstp9KikQWB3vetYAjZQZNa5mR3Fn5O80yKi6NIIyyogLE/S9ojYWu86zJYyf04q8ykmMzMrygFhZmZFOSCWp2vrXYBZA/DnpAL3QZiZWVFuQZiZWVEOCDMzK8oBkTJJJ0r6mqSdknZIukvSm0rsu1HS35d47A5JzxuSJ+kSST+WdJ+khyVdtdh/hmrqMKs1SW+SFJJeVMW+75fUXcV+uyT9+7xt90p6MLl9gaR/XHjVjcUBkSLlr0z+beDOiDg9Il5FfunyU4vs2xYRAxHx1mN4/XbyHW2/HBFnAq8A7liEuuuyyq/ZMboC+A+quxzA+4GKAZFYJWnugmUvrrTzcuaASNcvANMR8dm5DRGxOyI+AyDpHZK+Kem7wK2SthR8U+mS9HVJ90u6Aegq8vqryC/Zfih57amIeCx5fp+kf5B0d/JzXrL9bEnfT1od35f0wmK1JNs+KOmBpHXysYLjvk3SjyT9RNLPL+5fmVllklYC5wHvJAmI+d/uJV2dvK9/G9gI3C7p9uSxK5L39oOSPj7v5b8BXJbcvgK4PuU/zpLlgEjXS4F7KuxzLnBlRPzCvO3vATIR8XLgT4BXzX9iRAyRv/rebknXS/oVSXP/pp8C/jIiXg28Bfh8sv1R4PyIeAXwYeBPi9Ui6SLgjcA5SevkEwX7tUXE2eS/lX2kwp/PLA1vBG6JiJ8AQ5JeWWrHiPg0+csavyYiXiNpI/Bx8l/gzgJeLemNBU/5e+DNye1fBr6bxh+gEfhUQiiV7jMAAAI2SURBVA1Jugb4OfKtilcnm29L/qOf73zg0wARcb+k+4u9ZkS8S9LPAK8DPgD8IvCO5P5L8me5AFgtaRX563t/WdIZQADtBS9XWMvrgC9GRCY5TmGN30p+7wC2VPFHN1tsVwB/ldz+enL/n6p87quBOyJiEEDSV8l/3r6dPD4EHJZ0OfAIkFmsohuNAyJdD5H/9g5ARLxX0gZge8E+42WeX9UklYh4AHhA0t8CT5EPiBbg3IiYKNxX0meA2yPiTZK2cHSfRWEtKnP8qeR3Fr+HrMYkrSf/7f9lkoL8pYuDfGu68KxIZ6mXqOIwNwDXkP8sNS2fYkrXvwKdkt5TsK3ajrI7gV8BkPQy4OXzd5C0UtIFBZvO4rnVbG8F3lew71nJzTXAM8ntd5Q5/q3Ab86N/JC0rsq6zdL2VuArEXFaRGyJiE3kvxhBvtW8QtIa4LUFzxkl32cH8EPgv0naIKmVfOvj3+Yd40byp1W/l9qfogE4IFIU+WnqbyT/ZnxK0o+ALwO/V8XT/wZYmZxa+iDwoyL7CPigpMck3Qv8Ec/9p//bwNakk/th4N3J9k8A/1fSf5L/5lWq9lvIfyPbnrz2B6qo2awWriD/H3ihfwD+O/kO5vuBrwI/Lnj8WuCfJd0eEXuBDwG3A/cB90TEdwpfLCJGI+LjETFd5PivldRf8HPu4vyxlh4vtWFmZkW5BWFmZkU5IMzMrCgHhJmZFeWAMDOzohwQZmZWlAPCzMyKckCYmVlR/x88OVLYFtRjeQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualization of bias distribution\n", "plt.violinplot([np.abs(T_te_rf - expected_te),\n", "                np.abs(T_te_aml - expected_te)\n", "               ], showmeans=True)\n", "plt.ylabel(\"Bias distribution\")\n", "plt.xticks([1, 2], ['Grid Search', 'AutoML'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON> <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# Instantiate S learner\n", "overall_model = grid_search_reg()\n", "S_learner_rf = SLearner(overall_model)\n", "# Train S_learner\n", "S_learner_rf.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "S_te_rf = S_learner_rf.effect(X)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment overall_model_20_01_27-12_16_18 has started.\n", "Experiment overall_model_20_01_27-12_16_18 completed.\n"]}], "source": ["# Instantiate S learner\n", "AutomatedSLearner =  addAutomatedML(SLearner)\n", "S_learner_aml = AutomatedSLearner(overall_model= automl_config_reg)\n", "# Train S_learner\n", "S_learner_aml.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "S_te_aml = S_learner_aml.effect(X)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualization of bias distribution\n", "plt.violinplot([np.abs(S_te_rf - expected_te),\n", "                np.abs(S_te_aml - expected_te)\n", "               ], showmeans=True)\n", "plt.ylabel(\"Bias distribution\")\n", "plt.xticks([1, 2], ['Grid Search', 'AutoML'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Domain Adaptation Learner"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["models = grid_search_reg()\n", "final_models = grid_search_reg()\n", "DA_learner_rf = DomainAdaptationLearner(models=models, final_models=final_models)\n", "# Train DA_learner\n", "DA_learner_rf.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "DA_rf_te = DA_learner_rf.effect(X)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment models_20_01_27-13_19_40 has started.\n", "Experiment models_20_01_27-13_19_40 completed.\n", "Experiment models_20_01_27-13_21_20 has started.\n", "Experiment models_20_01_27-13_21_20 completed.\n", "Experiment final_models_20_01_27-13_23_01 has started.\n", "Experiment final_models_20_01_27-13_23_01 completed.\n"]}], "source": ["models = automl_config_reg\n", "final_models = automl_config_reg\n", "AutomatedDomainAdaptationLearner =  addAutomatedML(DomainAdaptationLearner)\n", "DA_learner_aml = AutomatedDomainAdaptationLearner(models=models, final_models=final_models)\n", "# Train X_learner\n", "DA_learner_aml.fit(Y, T, X=X)\n", "# Estimate treatment effects on test data\n", "DA_te_aml = DA_learner_aml.effect(X)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualization of bias distribution\n", "plt.violinplot([np.abs(DA_rf_te - expected_te),\n", "                np.abs(DA_te_aml - expected_te)\n", "               ], showmeans=True)\n", "plt.ylabel(\"Bias distribution\")\n", "plt.xticks([1, 2], ['Grid Search', 'AutoML'])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment model_t_20_01_27-17_24_47 has started.\n", "Experiment model_t_20_01_27-17_24_47 completed.\n", "Experiment model_y_20_01_27-17_27_27 has started.\n", "Experiment model_y_20_01_27-17_27_27 completed.\n", "Experiment model_t_20_01_27-17_29_51 has started.\n", "Experiment model_t_20_01_27-17_29_51 completed.\n", "Experiment model_y_20_01_27-17_32_20 has started.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', OSError(\"(10054, 'WSAECONNRESET')\",))': /azureml/ExperimentRun/dcid.AutoML_ddd4742b-4d4c-46bc-b036-e42e9a60b9a1_2/outputs/scoring_file_v_1_0_0.py?sv=2019-02-02&sr=b&sig=Z%2FSYw75Z5M38OuM6L4l3run1WpF%2BB6tr5%2FIdiJkJumM%3D&st=2020-01-27T22%3A24%3A24Z&se=2020-01-28T22%3A34%3A24Z&sp=rcw&comp=block&blockid=TURBd01EQXdNREF3TURBd01EQXdNREF3TURBd01EQXdNREF3TURBd01EQSUzRA%3D%3D&timeout=30\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Experiment model_y_20_01_27-17_32_20 completed.\n", "Experiment model_final_20_01_27-17_34_55 has started.\n", "Experiment model_final_20_01_27-17_34_55 completed.\n"]}], "source": ["Y, T, X, _ = ihdp_surface_B()\n", "est = AutomatedNonParamDML(model_y=automl_config_reg,\n", "                           model_t=automl_config_clf,\n", "                           model_final=automl_config_reg, featurizer=None,\n", "                           discrete_treatment=True)\n", "est.fit(Y, T, X=X)\n", "_ = est.effect(X)"]}], "metadata": {"authors": [{"name": "anuma<PERSON>"}], "category": "tutorial", "compute": ["AML"], "datasets": ["Bankmarketing"], "deployment": ["ACI"], "exclude_from_index": false, "framework": ["None"], "friendly_name": "Automated ML run with basic edition features.", "index_order": 5, "kernelspec": {"display_name": "Python (azure_automl)", "language": "python", "name": "azure_automl"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}, "tags": ["featurization", "explainability", "remote_run", "AutomatedML"], "task": "Classification"}, "nbformat": 4, "nbformat_minor": 2}