# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions
name: CI

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/ci.yml'
      - 'dowhy/**'
      - 'tests/**'
      - 'docs/source/example_notebooks/**'
      - 'pyproject.toml'
      - 'poetry.lock'
  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/ci.yml'
      - 'dowhy/**'
      - 'tests/**'
      - 'docs/source/example_notebooks/**'
      - 'pyproject.toml'
      - 'poetry.lock'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11", "3.12"]
        poetry-version: [1.5.1]
        test-group: [1, 2, 3, 4, 5, 6]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry ${{ matrix.poetry-version }}
      uses: abatilo/actions-poetry@v4.0.0
      with:
        poetry-version: ${{ matrix.poetry-version }}

    # At the moment needed to support scipy in Python 3.12
    - name: Install LLVM and Clang
      uses: KyleMayes/install-llvm-action@v2.0.5
      with:
        version: "14.0"
        directory: ${{ runner.temp }}/llvm

    - name: Install graphviz
      run: |
        sudo apt install graphviz libgraphviz-dev graphviz-dev pkg-config

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        echo "installing poetry dependencies"
        poetry install -E plotting -E pygraphviz

    - name: Lint
      run: poetry run poe lint

    - name: Check Formatting
      run: poetry run poe format_check

    - name: Test
      run: poetry run poe test --splits 6 --group ${{ matrix.test-group }}

    - name: Install EconML dependency
      run: |
        poetry install -E plotting -E pygraphviz -E econml

    - name: Test EconML based functionality
      run: poetry run poe test_econml

    - name: Test README errors for PyPI
      run: |
          pip install poetry-dynamic-versioning
          pip install twine
          poetry-dynamic-versioning
          poetry build
          twine check dist/*

    - name: Notify Discord
      if: failure() && github.ref == 'refs/heads/main'
      uses: th0th/notify-discord@v0.4.1
      env:
        DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK }}
        GITHUB_ACTOR: ${{ github.actor }}
        GITHUB_JOB_NAME: CI Failed on Main
        GITHUB_JOB_STATUS: ${{ job.status }}
