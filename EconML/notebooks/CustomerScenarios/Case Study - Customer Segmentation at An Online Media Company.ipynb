{"cells": [{"cell_type": "markdown", "source": ["<img src=\"https://www.microsoft.com/en-us/research/uploads/prod/2020/05/Segmentation.png\" width=\"400\"/>"], "metadata": {}}, {"cell_type": "markdown", "source": ["# Customer Segmentation: Estimate Individualized Responses to Incentives\n", "\n", "Nowadays, business decision makers rely on estimating the causal effect of interventions to answer what-if questions about shifts in strategy, such as promoting specific product with discount, adding new features to a website or increasing investment from a sales team. However, rather than learning whether to take action for a specific intervention for all users, people are increasingly interested in understanding the different responses from different users to the two alternatives. Identifying the characteristics of users having the strongest response for the intervention could help make rules to segment the future users into different groups. This can help optimize the policy to use the least resources and get the most profit.\n", "\n", "In this case study, we will use a personalized pricing example to explain how the [EconML](https://aka.ms/econml) library could fit into this problem and provide robust and reliable causal solutions.\n", "\n", "### Summary\n", "\n", "1. [Background](#background)\n", "2. [Data](#data)\n", "3. [Get Causal Effects with EconML](#estimate)\n", "4. [Understand Treatment Effects with EconML](#interpret)\n", "5. [Make Policy Decisions with EconML](#policy)\n", "6. [Conclusions](#conclusion)\n"], "metadata": {}}, {"cell_type": "markdown", "source": ["# Background <a id=\"background\"></a>\n", "\n", "<img src=\"https://cdn.pixabay.com/photo/2018/08/16/11/59/radio-3610287_960_720.png\" width=\"400\" />\n", "\n", "The global online media market is growing fast over the years. Media companies are always interested in attracting more users into the market and encouraging them to buy more songs or become members. In this example, we'll consider a scenario where one experiment a media company is running is to give small discount (10%, 20% or 0) to their current users based on their income level in order to boost the likelihood of their purchase. The goal is to understand the **heterogeneous price elasticity of demand** for people with different income level, learning which users would respond most strongly to a small discount. Furthermore, their end goal is to make sure that despite decreasing the price for some consumers, the demand is raised enough to boost the overall revenue.\n", "\n", "EconML’s `DML` based estimators can be used to take the discount variation in existing data, along with a rich set of user features, to estimate heterogeneous price sensitivities that vary with multiple customer features. Then, the `SingleTreeCateInterpreter` provides a presentation-ready summary of the key features that explain the biggest differences in responsiveness to a discount, and the `SingleTreePolicyInterpreter` recommends a policy on who should receive a discount in order to increase revenue (not only demand), which could help the company to set an optimal price for those users in the future. "], "metadata": {}}, {"cell_type": "code", "execution_count": 1, "source": ["# Some imports to get us started\r\n", "# Utilities\r\n", "import os\r\n", "import urllib.request\r\n", "import numpy as np\r\n", "import pandas as pd\r\n", "\r\n", "# Generic ML imports\r\n", "from sklearn.preprocessing import PolynomialFeatures\r\n", "from sklearn.ensemble import GradientBoostingRegressor\r\n", "\r\n", "# EconML imports\r\n", "from econml.dml import LinearDML, CausalForestDML\r\n", "from econml.cate_interpreter import SingleTreeCateInterpreter, SingleTreePolicyInterpreter\r\n", "\r\n", "import matplotlib.pyplot as plt\r\n", "\r\n", "%matplotlib inline"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["# Data <a id=\"data\"></a>\n", "\n", "\n", "The dataset* has ~10,000 observations and includes 9 continuous and categorical variables that represent user's characteristics and online behaviour history such as age, log income, previous purchase, previous online time per week, etc. \n", "\n", "We define the following variables:\n", "\n", "Feature Name|Type|Details \n", ":--- |:---|:--- \n", "**account_age** |W| user's account age\n", "**age** |W|user's age\n", "**avg_hours** |W| the average hours user was online per week in the past\n", "**days_visited** |W| the average number of days user visited the website per week in the past\n", "**friend_count** |W| number of friends user connected in the account \n", "**has_membership** |W| whether the user had membership\n", "**is_US** |W| whether the user accesses the website from the US \n", "**songs_purchased** |W| the average songs user purchased per week in the past\n", "**income** |X| user's income\n", "**price** |T| the price user was exposed during the discount season (baseline price * small discount)\n", "**demand** |Y| songs user purchased during the discount season\n", "\n", "**To protect the privacy of the company, we use the simulated data as an example here. The data is synthetically generated and the feature distributions don't correspond to real distributions. However, the feature names have preserved their names and meaning.*\n", "\n", "\n", "The treatment and outcome are generated using the following functions:\n", "$$\n", "T = \n", "\\begin{cases}\n", "  1 & \\text{with } p=0.2,  \\\\\n", "  0.9 & \\text{with }p=0.3, & \\text{if income}<1 \\\\\n", "  0.8 & \\text{with }p=0.5, \\\\\n", "  \\\\\n", "    1 & \\text{with }p=0.7, \\\\\n", "  0.9 & \\text{with }p=0.2, & \\text{if income}\\ge1 \\\\\n", "  0.8 & \\text{with }p=0.1, \\\\\n", "\\end{cases}\n", "$$\n", "\n", "\n", "\\begin{align}\n", "\\gamma(X) & = -3 - 14 \\cdot \\{\\text{income}<1\\} \\\\\n", "\\beta(X,W) & = 20 + 0.5 \\cdot \\text{avg_hours} + 5 \\cdot \\{\\text{days_visited}>4\\} \\\\\n", "Y &= \\gamma(X) \\cdot T + \\beta(X,W)\n", "\\end{align}\n", "\n"], "metadata": {}}, {"cell_type": "code", "execution_count": 2, "source": ["# Import the sample pricing data\n", "file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/Pricing/pricing_sample.csv\"\n", "train_data = pd.read_csv(file_url)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 3, "source": ["# Data sample\n", "train_data.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   account_age  age  avg_hours  days_visited  friends_count  has_membership  \\\n", "0            3   53   1.834234             2              8               1   \n", "1            5   54   7.171411             7              9               0   \n", "2            3   33   5.351920             6              9               0   \n", "3            2   34   6.723551             0              8               0   \n", "4            4   30   2.448247             5              8               1   \n", "\n", "   is_US  songs_purchased    income  price     demand  \n", "0      1         4.903237  0.960863    1.0   3.917117  \n", "1      1         3.330161  0.732487    1.0  11.585706  \n", "2      1         3.036203  1.130937    1.0  24.675960  \n", "3      1         7.911926  0.929197    1.0   6.361776  \n", "4      0         7.148967  0.533527    0.8  12.624123  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>account_age</th>\n", "      <th>age</th>\n", "      <th>avg_hours</th>\n", "      <th>days_visited</th>\n", "      <th>friends_count</th>\n", "      <th>has_membership</th>\n", "      <th>is_US</th>\n", "      <th>songs_purchased</th>\n", "      <th>income</th>\n", "      <th>price</th>\n", "      <th>demand</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>53</td>\n", "      <td>1.834234</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4.903237</td>\n", "      <td>0.960863</td>\n", "      <td>1.0</td>\n", "      <td>3.917117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>54</td>\n", "      <td>7.171411</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3.330161</td>\n", "      <td>0.732487</td>\n", "      <td>1.0</td>\n", "      <td>11.585706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>33</td>\n", "      <td>5.351920</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3.036203</td>\n", "      <td>1.130937</td>\n", "      <td>1.0</td>\n", "      <td>24.675960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>34</td>\n", "      <td>6.723551</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>7.911926</td>\n", "      <td>0.929197</td>\n", "      <td>1.0</td>\n", "      <td>6.361776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>30</td>\n", "      <td>2.448247</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>7.148967</td>\n", "      <td>0.533527</td>\n", "      <td>0.8</td>\n", "      <td>12.624123</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 3}], "metadata": {}}, {"cell_type": "code", "execution_count": 4, "source": ["# Define estimator inputs\n", "Y = train_data[\"demand\"]  # outcome of interest\n", "T = train_data[\"price\"]  # intervention, or treatment\n", "X = train_data[[\"income\"]]  # features\n", "W = train_data.drop(columns=[\"demand\", \"price\", \"income\"])  # confounders"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 5, "source": ["# Get test data\n", "X_test = np.linspace(0, 5, 100).reshape(-1, 1)\n", "X_test_data = pd.DataFrame(X_test, columns=[\"income\"])"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["# Get Causal Effects with EconML <a id=\"estimate\"></a>\n", "To learn the price elasticity on demand as a function of income, we fit the model as follows:\n", "\n", "\n", "\\begin{align}\n", "log(Y) & = \\theta(X) \\cdot log(T) + f(X,W) + \\epsilon \\\\\n", "log(T) & = g(X,W) + \\eta\n", "\\end{align}\n", "\n", "\n", "where $\\epsilon, \\eta$ are uncorrelated error terms. \n", "\n", "The models we fit here aren't an exact match for the data generation function above, but if they are a good approximation, they will allow us to create a good discount policy.  Although the model is misspecified, we hope to see that our `DML` based estimators can still capture the right trend of $\\theta(X)$ and that the recommended policy beats other baseline policies (such as always giving a discount) on revenue.  Because of the mismatch between the data generating process and the model we're fitting, there isn't a single true $\\theta(X)$ (the true elasticity varies with not only X but also T and W), but given how we generate the data above, we can still calculate the range of true $\\theta(X)$ to compare against."], "metadata": {}}, {"cell_type": "code", "execution_count": 6, "source": ["# Define underlying treatment effect function given DGP\n", "def gamma_fn(X):\n", "    return -3 - 14 * (X[\"income\"] < 1)\n", "\n", "def beta_fn(X):\n", "    return 20 + 0.5 * (X[\"avg_hours\"]) + 5 * (X[\"days_visited\"] > 4)\n", "\n", "def demand_fn(data, T):\n", "    Y = gamma_fn(data) * T + beta_fn(data)\n", "    return Y\n", "\n", "def true_te(x, n, stats):\n", "    if x < 1:\n", "        subdata = train_data[train_data[\"income\"] < 1].sample(n=n, replace=True)\n", "    else:\n", "        subdata = train_data[train_data[\"income\"] >= 1].sample(n=n, replace=True)\n", "    te_array = subdata[\"price\"] * gamma_fn(subdata) / (subdata[\"demand\"])\n", "    if stats == \"mean\":\n", "        return np.mean(te_array)\n", "    elif stats == \"median\":\n", "        return np.median(te_array)\n", "    elif isinstance(stats, int):\n", "        return np.percentile(te_array, stats)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 7, "source": ["# Get the estimate and range of true treatment effect\r\n", "truth_te_estimate = np.apply_along_axis(true_te, 1, X_test, 1000, \"mean\")  # estimate\r\n", "truth_te_upper = np.apply_along_axis(true_te, 1, X_test, 1000, 95)  # upper level\r\n", "truth_te_lower = np.apply_along_axis(true_te, 1, X_test, 1000, 5)  # lower level"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["## Parametric heterogeneity\n", "First of all, we can try to learn a **linear projection of the treatment effect** assuming a polynomial form of $\\theta(X)$. We use the `LinearDML` estimator. Since we don't have any priors on these models, we use a generic gradient boosting tree estimators to learn the expected price and demand from the data."], "metadata": {}}, {"cell_type": "code", "execution_count": 8, "source": ["# Get log_T and log_Y\r\n", "log_T = np.log(T)\r\n", "log_Y = np.log(Y)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 9, "source": ["# Train EconML model\r\n", "est = LinearDML(\r\n", "    model_y=GradientBoostingRegressor(),\r\n", "    model_t=GradientBoostingRegressor(),\r\n", "    featurizer=PolynomialFeatures(degree=2, include_bias=False),\r\n", ")\r\n", "est.fit(log_Y, log_T, X=X, W=W, inference=\"statsmodels\")\r\n", "# Get treatment effect and its confidence interval\r\n", "te_pred = est.effect(X_test)\r\n", "te_pred_interval = est.effect_interval(X_test)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 10, "source": ["# Compare the estimate and the truth\r\n", "plt.figure(figsize=(10, 6))\r\n", "plt.plot(X_test.flatten(), te_pred, label=\"Sales Elasticity Prediction\")\r\n", "plt.plot(X_test.flatten(), truth_te_estimate, \"--\", label=\"True Elasticity\")\r\n", "plt.fill_between(\r\n", "    X_test.flatten(),\r\n", "    te_pred_interval[0],\r\n", "    te_pred_interval[1],\r\n", "    alpha=0.2,\r\n", "    label=\"95% Confidence Interval\",\r\n", ")\r\n", "plt.fill_between(\r\n", "    X_test.flatten(),\r\n", "    truth_te_lower,\r\n", "    truth_te_upper,\r\n", "    alpha=0.2,\r\n", "    label=\"True Elasticity Range\",\r\n", ")\r\n", "plt.xlabel(\"Income\")\r\n", "plt.ylabel(\"Songs Sales Elasticity\")\r\n", "plt.title(\"Songs Sales Elasticity vs Income\")\r\n", "plt.legend(loc=\"lower right\")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.legend.Legend at 0x1f533b30d68>"]}, "metadata": {}, "execution_count": 10}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 720x432 with 1 Axes>"], "image/png": "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********************************************************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"}, "metadata": {"needs_background": "light"}}], "metadata": {}}, {"cell_type": "markdown", "source": ["From the plot above, it's clear to see that the true treatment effect is a **nonlinear** function of income, with elasticity around -1.75 when income is smaller than 1 and a small negative value when income is larger than 1. The model fits a quadratic treatment effect, which is not a great fit. But it still captures the overall trend: the elasticity is negative and people are less sensitive to the price change if they have higher income."], "metadata": {}}, {"cell_type": "code", "execution_count": 11, "source": ["# Get the final coefficient and intercept summary\r\n", "est.summary()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                      Coefficient Results                      \n", "===============================================================\n", "         point_estimate stderr  zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------------\n", "income            2.427  0.075  32.189    0.0     2.28    2.575\n", "income^2         -0.437  0.026 -16.907    0.0   -0.487   -0.386\n", "                        CATE Intercept Results                       \n", "=====================================================================\n", "               point_estimate stderr  zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------------------\n", "cate_intercept          -3.02  0.046 -65.081    0.0   -3.111   -2.929\n", "---------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""], "text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "      <td></td>     <th>point_estimate</th> <th>stderr</th>  <th>zstat</th>  <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>income</th>        <td>2.427</td>      <td>0.075</td> <td>32.189</td>    <td>0.0</td>    <td>2.28</td>     <td>2.575</td> \n", "</tr>\n", "<tr>\n", "  <th>income^2</th>     <td>-0.437</td>      <td>0.026</td> <td>-16.907</td>   <td>0.0</td>   <td>-0.487</td>   <td>-0.386</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th>  <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>-3.02</td>      <td>0.046</td> <td>-65.081</td>   <td>0.0</td>   <td>-3.111</td>   <td>-2.929</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"]}, "metadata": {}, "execution_count": 11}], "metadata": {}}, {"cell_type": "markdown", "source": ["`LinearDML` estimator can also return the summary of the coefficients and intercept for the final model, including point estimates, p-values and confidence intervals. From the table above, we notice that $income$ has positive effect and ${income}^2$ has negative effect, and both of them are statistically significant."], "metadata": {}}, {"cell_type": "markdown", "source": ["## Nonparametric Heterogeneity\n", "Since we already know the true treatment effect function is nonlinear, let us fit another model using `CausalForestDML`, which assumes a fully **nonparametric estimation of the treatment effect**."], "metadata": {}}, {"cell_type": "code", "execution_count": 12, "source": ["# Train EconML model\r\n", "est = CausalForestDML(\r\n", "    model_y=GradientBoostingRegressor(), model_t=GradientBoostingRegressor()\r\n", ")\r\n", "est.fit(log_Y, log_T, X=X, W=W, inference=\"blb\")\r\n", "# Get treatment effect and its confidence interval\r\n", "te_pred = est.effect(X_test)\r\n", "te_pred_interval = est.effect_interval(X_test)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 13, "source": ["# Compare the estimate and the truth\r\n", "plt.figure(figsize=(10, 6))\r\n", "plt.plot(X_test.flatten(), te_pred, label=\"Sales Elasticity Prediction\")\r\n", "plt.plot(X_test.flatten(), truth_te_estimate, \"--\", label=\"True Elasticity\")\r\n", "plt.fill_between(\r\n", "    X_test.flatten(),\r\n", "    te_pred_interval[0],\r\n", "    te_pred_interval[1],\r\n", "    alpha=0.2,\r\n", "    label=\"95% Confidence Interval\",\r\n", ")\r\n", "plt.fill_between(\r\n", "    X_test.flatten(),\r\n", "    truth_te_lower,\r\n", "    truth_te_upper,\r\n", "    alpha=0.2,\r\n", "    label=\"True Elasticity Range\",\r\n", ")\r\n", "plt.xlabel(\"Income\")\r\n", "plt.ylabel(\"Songs Sales Elasticity\")\r\n", "plt.title(\"Songs Sales Elasticity vs Income\")\r\n", "plt.legend(loc=\"lower right\")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.legend.Legend at 0x1f533c69208>"]}, "metadata": {}, "execution_count": 13}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 720x432 with 1 Axes>"], "image/png": "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***************************/3I+h3AKx6AV67I/WZbPD6fe4W6DUCPngSltzdoP1UG6f+EvL7wzsPwbJ/1B+7tpfgzNshUgRvPwArHvPL0WfeCUMnpf/S6UQiskRVJze93yppJmvkb3+HA5zVRIuncvioERx6YD+iySSBp4rxvCQ1nkNNQqj2HMbnDOTbfSKEHY/46gP9rgCBfMcjTIKvDevPl4YX4sWCDHo1B3WKUDeE54RIuiHGDB7Jdwbk4ER7EXrnECpjftdVYdjBEThon6HkDs4nXNMbhh0MqoRSX/J5ySTf6NuPys8CPPCRckB4ICMKHRxRauIe0aRyz0sxnqjaxKhEjG8He1MYcuid61Kc65JUmNW7iP2jIdicz9LyfQg4Sp+wR1EoSb+A8sepfcjp15fAmgiRN6v9bqeoP+bGkwCuxkkA6oZJBvNBHFQcwCEgDt8ozeOkcXksWTKMB7YeirpBBhcGWbcziZeMM6Z/Hr8+tBdTakbBpwcSjcXYXlFDWVWUaCzGyF4OF0wMc1o8l76bI4Cg4tAboTwZ4OS8EIs+jTPJyaewT38G5PqJggMQyqU4P7UkSP9R4CRTCawQ9KBYC7hQwryxMYGXP5bqYBG9gx4OHq4m2T9SzD0j8/nPJzE+/aSARLIIEegdEfrnuZw0aACnH1Hs/16u7gcVtX1UHiD06teHAyb38o+/aADU5OCJQxI/GTqt1wgG9c7j1XUJVqwcQ3U0iioEXYi4sHJNMcsrKumbI5zHPsRqqkjEagh41eSygx26gYMHuRw/LJ8xn+UQjvTFDUb842mSA4YWc8B+eRD1YF1xqk+qvov5S/sP5Usj++GVK/pcbzSZ8Cs1OCQVvj20gLMK84htzye8ciDRJFQm/bc/moTnNwR5fk2UMZIgP1CIg0fIFfKCQm4QPvw0Rm6snMC2JCOTRUQ9IZbEv6jD316JscLbwagYnJkYTtST1B8pgiL8+7MA5eEEBW4eH8ooQk6SkJMk4AihQICvj8ynV58CRteMpHDzFIKug+MIrvjjNe+cOgjCBbByH1hVWp/kpMwcnQ+BCCQGQGxEqjs1lQh4SQr6BvyEoXgEJA7xkxSoH3tVK68Yeg1vUApTCBc23l40LNUvmOrWzSuu395reN0x/RiSEMyr3+6k4qhLZBw/AWj4+HBBoz/O6DWyfvugEohXpbalkoni/eq3jzgCkvEGiYYD/cakNgqMmZlKkOL+fsm4fzzwE7ahh9THl4z5iVgk9bmvjTenDwTC/usdCEHRUH+7G4LBpfVjDeuef25qe9B/rWpfg7rnn2o/mAN99m3eDR7I8X+G8qF4f+qTrFSiVfv6hvKhzz7+/bXd+OL4cQLk9oEB46lLXmtfo9pxdwWDYMjB1CeAtePZUtvDhf5zVc9/3hlklTSTNZ7/58846r0bKf/8vRT0GdDifglPiSc8HEdSy0J0/FhJz19WIprw6n4KUBgJUhAJttlmNOGxdns1yzbG+cMbNXy4PTWOSKB/rjA432FYocuhQ0JMGxWhX16QQIPumcpYgu2Vcarj9V9eAhREAvTOC7XY9VZL1Z/pWR1PEkt6fhdYwCEccAi5DqqwozrOtsoo729NcvdbNby+Pklpf5dLSsJM2yefgnDzv/E8z5/NWZN6TeIJfyxXQwFH6qo3uSGXgnCwQ2O/Vf1jVMUSBFOzP8OuSzDV9et5UJVIUB1NUlaT4K1Nccb0DTK8T5j8TppQkPCUymiSymiCdTtjvLY+wbtbk2ytVrZUeWytVnZE/S6uwfnCgX0DHFjsMravy6heDkFH6JPnd8V29jwSz4O45xFLeiST/uD02oHpCc8f07ijxmNDpX9ZU+7x8fYkH+/wWLPTSzvcxxG/q7ww7Cdz+SEhLwh5QaFvjsPgfIchhS4jCgP0z3cIuq7fdecIrisEZDe6VY3JpOLREMpre7/dZJU0k/U0Xg1AuN9IyC+mpQHdAZE0H/wm+7U6EBxclFyE3OY71e+ntSNWGsSR+hlGGNxbiRdV8ot9lfc3VVEQdhiQHyQUEIrzQvQrCLc47DUvdamOJdlWWYPrOvTNjxB0GvzlKdJ8QHjqtqiSg5LTwoBuQehdBAWektMvyv8MT1IR9SjKdRjRJ49IoMGkBuqfmwMUilDY4DVIeH4iK0A44OI6LcwOazFbaXi/P9avIHVJN/jJAfJFyAf6IYzcz+/mq/urva57hMavVUPNBtI3fg8DQJEqRcBAz2P0fjEqYwmq4/5MYNXaRF7JDbl17bgO5AQd+hfmNJ7h2nRGYaNYauOuGwHVPKaGz1+VMBBuFH9zSa92pqI/SaYqlmR7ZZxPttawdmecgrBLn7wAfXODFEX8rvKA64+DDAXqx7WFXIdw0G08uaTu/4GmfY/SavT8Us+t2We5SXuN2m7htWn0/yDd49oMLP3jmn522pNtt3dGatP4WptN2dZ+6Z5rS8+/o38ttfbYRq9PKyP/W3xfm/weTVXTG30uWmqvrhvUq99W93inQRt1wTZ4vNcgJq++EpshlqSZrPF20bH8JpbHfcX7tj2dvBuIACNzClm5pZIxw/xultywy5BeOc2WqGhJTi4M6dV1MQaAwYXQO5ZkW1WMAQUtL4HRWhuZ/EXT1cd2gMJ8aNBR5i/DEveIJpN+EhNwCQW612K+buoCkJ/6OUKVMXF/5qSbWuYh6PpxBxzZ48vHGLO3syTNZI1tUsg77oFID0jQaoUDLqOK8/hsaxV98kL0zQ9nOqS0ckIuQ0I5mQ6jx/DXznOBnvNZBH9gfm4oYOvMGdNN2CgBkzWKy97iDPfFTIfRYeGAy/4DCrptgmaMMSYzLEkzWWPc1ieZw12ZDsMYY4zpFJakmazhJKqJSWYHeRpjjDGdxZI0kzXcZNSSNGOMMVnDkjSTNZxkDXGxcV3GGGOygyVpJmsEvCgJS9KMMcZkCZtnbbLGryNfo39ekP/LdCDGGGNMJ7AkzWSN1cm+RHIL297RGGOM6QGsu9NkjenRhRwUtXO2GmOMyQ6WpJms8aXkgxxa8XSmwzDGGGM6hSVpJmuENYrnRjIdhjHGGNMpLEkzWSNEDA1YkmaMMSY7WJJmskIykSRC3JI0Y4wxWcOSNJMVampqiBCDoCVpxhhjsoMlaSYr1MRiHBb9DR+MvDDToRhjjDGdwpI0kxVq4gk20gcnr0+mQzHGGGM6hS1ma7JCtGI7V7kP0L8yBIzIdDjGGGPMbrNKmskKifLNfCv4AH2rPsp0KMYYY0ynsCTNZIV4TSUAbig3w5EYY4wxncOSNJMVErVJWjgnw5EYY4wxncOSNJMVElE/SQuErZJmjDEmO1iSZrJCMlYNQNCSNGOMMVnCkjSTFdYUlDCx5k500MGZDsUYY4zpFBlJ0kSkj4g8JSIfpn72bmG/pIgsS10e3dNxmp6jJqHsJI9IxM44YIwxJjtkqpI2B3hGVfcHnkndTqdaVUtTl9P2XHimpynY9jZzAn8nJ1me6VCMMcaYTpGpJO104C+p638BzshQHCZLFJV/wGWBxwhLItOhGGOMMZ0iU0naAFVdn7q+ARjQwn4REVksIq+IyBl7JjTT43geGo8CEM6xiQPGGGOyQ5edFkpEngYGptl0bcMbqqoioi00M0JV14rIPsCzIvKWqn7cwvEuBS4FGD58+G5EbnocLwEJP0mToCVpxhhjskOXJWmqelxL20Rko4gMUtX1IjII2NRCG2tTPz8RkUXAQUDaJE1V7wDuAJg8eXJLSZ/JRl4CklES6hBwg5mOxhhjjOkUmerufBS4KHX9IuCRpjuISG8RCaeuFwNHAO/usQhNz6FJSMaJSijTkRhjjDGdJlNJ2s3A8SLyIXBc6jYiMllE/pTa50BgsYgsBxYCN6uqJWmmOS/B/UWzOTl0d6YjMcYYYzpNl3V3tkZVtwIz0ty/GPhK6vpLwIQ9HJrpibwkNQklGLSuTmOMMdkjI0maMZ3KS3JE+X+YligHjs90NMYYY0ynsCTN9HxeggnRpfTxtmc6EmOMMabT2Lk7Tc/nJQh4MRKOTRwwxhiTPSxJMz2flyCoURJOONORGGOMMZ3GkjTT86lHUGMkrZJmjDEmi1iSZno+L0FMXeKB/ExHYowxxnQamzhgej4vwRe9mzh9WG8OznQsxhhjTCexSprp+bwk0aSSE3AzHYkxxhjTaSxJMz2bl0TV43+d31NavjDT0RhjjDGdxpI007N5SaJJON15iQHRVZmOxhhjjOk0lqSZns1LUBOLExAPAjmZjsYYY4zpNJakmZ7NSxCN1gAgQVsnzRhjTPawJM30bJokGo0C4AStkmaMMSZ7WJJmejYvSSyeYL32gUhRpqMxxhhjOo0laaZn8xJUBPtyWPS3bBsxM9PRGGOMMZ3GkjTTs3lJahIKQCRoH2djjDHZw77VTM/mJXC3r+Tu4C30qvgo09EYY4wxncaSNNOzeQmo2cZ0dzk5Wp3paIwxxphOY0ma6dnUIxnzZ3cGwza70xhjTPawJM30bF6CRDyVpEVyMxyMMcYY03ksSTM9m5fASyVpobAlacYYY7KHJWmmZ/OSVGmYj71BhHLyMx2NMcYY02kCmQ7AmF3mJQHlnYLDuSw2kY97Dcx0RMYYY0ynsUqa6bm8BAA1CY+AA65rH2djjDHZw77VTM/lJQEYv/U//D14E4h9nI0xxmQP+1YzPVeqklZUs44J8jEgmY3HGGOM6USWpJmeK5WkOckYUUIglqQZY4zJHpakmZ5LPQAcL0qMEFZJM8YYk00sSTM9V6qS5nox4mKVNGOMMdnFluAwPVcqSVsv/UkEYIhV0owxxmQRS9JMz5Wa3Tk39AXygsJRVkkzxhiTRay7szMkExAt9y+qmY5mz/E8qN4OiVjb+8YqIRHtvGPHqiBRA0B1QgkHBBuTZowxJptYJW1XqEL5BoinEoVkgyRFHAjl+5dwPgRz2z9WKpmAWIWf0MQqIVENbggCEb+dYOqnuH6bDdtNJurjiVf7P8WBYE7qsTl+O01j8bzUAHz1f9Zeah+bjpeEyi1QuRm8uH9fMA9yekNOL3CD/msUq4DqHVBTVr+fE4RQHoQL/NdIxI81EYNktD6RC0RSzzv1EyC6E2p2+slwbXvA92tuQ6QPyLHte52NMcaYHsCStF2hHlRsaHlbdKd/KQcQCIRTyVYY3DA4rp/oJGN+spFM+NeTaSpNiRr/UrOjlYAEaKGCF6tosJsDTqBxMtYacRskVHl+7JWb/YsmG+8br/QvO9f6+yZq6saMNeLF/efS6vPBf/3aaYS3hgoFq6QZY4zJJpakdTmtT7Q6sbev2THatZvXuOrX5v7J+oSzI7E0TAz3gJDG8Byb3WmMMSa7ZGRMmoicIyLviIgnIpNb2e8kEXlfRD4SkTl7MkbTc4SJ4blhS9KMMcZklUxNHHgbOAt4rqUdRMQFfgd8DhgLfEFExu6Z8ExPoaqEiaFuJNOhGGOMMZ0qI92dqroCQFqvfBwCfKSqn6T2/SdwOvBulwdoeoy4B695B+DkDst0KMYYY0yn6s5LcAwBVje4vSZ1nzF1ahLKV+Pf5aOBJ2c6FGOMMaZTdVklTUSeBgam2XStqj7SBce7FLgUYPjw4Z3dvOmmahL+pIlIwMajGWOMyS5dlqSp6nG72cRaoGEf1tDUfS0d7w7gDoDJkyfvRSvK7t3ilWW8GL6CNdsuBo7IdDjGGGNMp+nO3Z2vA/uLyCgRCQHnA49mOCbTzcSi1QyRrUQk2fbOxhhjTA+SqSU4zhSRNcBhwOMi8kTq/sEisgBAVRPA5cATwArgPlV9JxPxmu6rJuovPucEwxmOxBhjjOlcmZrd+RDwUJr71wEzG9xeACzYg6GZHiYe88/f6YZsCQ5jjDHZpTt3dxrTpnjMr6QFLEkzxhiTZSxJMz1aBTn8OzkFye+f6VCMMcaYTmVJmunRtoaH8fX4t6B4dKZDMcYYYzqVJWmmR6uO166T5mY4EmOMMaZzWZJmerRBm/7LG+FLyanZkOlQjDHGmE5lSZrp2eJV9JYKwqFgpiMxxhhjOpUlaaZH04Q/uzMcyclwJMYYY0znsiTN9GyJGABBW4LDGGNMlmkzSRORvnsiEGN2haQqaRK0Spoxxpjs0p5K2isicr+IzBQR6fKIjOmA1YERPKTTwLHZncYYY7JLe5K00cAdwJeAD0Xkf0XEFqUy3cLrkancErgMsL8fjDHGZJc2kzT1PaWqXwC+ClwEvCYi/xWRw7o8QmNaEY17hAOAFXmNMcZkmTZPsJ4ak3YBfiVtI3AF8ChQCtwPjOrC+Ixp1eztv2ZY7GPgyUyHYowxxnSqNpM04GXgr8AZqrqmwf2LReT2rgnLmPYJeFFUXKukGWOMyTrtGZN2nare2DBBE5FzAFT1li6LzJh2CHgx4hLCxqQZY4zJNu1J0uakue/7nR2IMbsioDESTsgqacYYY7JOi92dIvI5YCYwRER+3WBTIZDo6sCMaY+gRklIPlZJM8YYk21aG5O2DlgMnAYsaXB/OfCtrgzKmPZ6msPol5vHeKukGWOMyTItJmmquhxYLiLzVNUqZ6ZbultPZkavMF8QO8OZMcaY7NJad+d9qnou8IaIaNPtqjqxSyMzph3cZDU5bhDr7jTGGJNtWuvuvCr185Q9EYgxu+JJ50o+3no0yG8yHYoxxhjTqVrr7lyfuuoA61W1BkBEcoABeyA200OVVScoCAdwurgHMukpEeJowJbgMMYYk33a8zV6P+A1uJ1M3WdMWlWxBGU18a4/TtQjQgzcsC3BYYwxJuu0J0kLqGqs9kbqeqjrQjI9XTThsaM6hjYbydi5quJRHFEkEO7aAxljjDEZ0J4kbbOInFZ7Q0ROB7Z0XUimJ0t6EE96JJJKRbRrJwVHa2oA/CTNKmnGGGOyTHvO3XkZME9Efos/8Gc1cGGXRmV6rJpEfWK2vSpGQaQ9H7FdU5V0+VXiLCYUHYCNSTPGGJNt2vwGVdWPgUNFJD91u6LLozI9VjThNbpeGUuQF+qaRK2KMLclPs/v+va2Spoxxpis09o6aReo6t9E5NtN7gdAVX/RxbGZHiga9xrd3l4Z77IkLRqN0Y8d5Dh2WihjjDHZp7UxaXmpnwVpLvldHJfpoWoSyUa3q+NJapokbp3F3fEJr0e+waDtb1glzRhjTNZpbZ20P6auPq2qLzbcJiJHdGlUpkdKeEoi2XxK5/aqGIOKIp1+vGQsCkAwFMYqacYYY7JNe2Z3plvK3ZZ3N81Em1TRalVGE8SSnV9NS8T82Z3BcMQqacYYY7JOa2PSDgMOB/o1GZdWCLhdHZjpeZqOR6ulwI6qOP0LOnc9s2TcKmnGGGOyV2uVtBD+2LMAjcej7QQ+3/WhmZ6m4czOpipqOn/NNC9Rm6RZJc0YY0z2aW1M2n+B/4rIXFX9FEBEHCBfVXfuqQBNz1ETT9/dCZBUJZb0CLmdd0LPdaGR/G/8C/y/gr5YJc0YY0y2ac835k9FpFBE8oC3gXdF5Ooujst0QMLr4vMvtUPc0zbjqIl17ri09YFh3JE8lXBugVXSjDHGZJ32JGljU5WzM4B/A6OAL+3OQUXkHBF5R0Q8EZncyn6rROQtEVkmIot355jZbEdVHK9rVrlot5YmDTTUdHmO3eVEyxgp6wk7HlZJM8YYk23ak6QFRSSIn6Q9qqpx/LHgu+Nt4CzguXbsO11VS1W1xWRub1cRTRBNdm4C1FGxdqyF1lp36K4o2fZvFoW/g6BWSTPGGJN12pOk/RFYhb+47XMiMgJ/8sAuU9UVqvr+7rRhfNGERzzptTizck9pz4K10YRHZ67EIckYcXXBCWCVNGOMMdmmzSRNVX+tqkNUdab6PgWm74HYwK/YPSkiS0Tk0j10zB6lMurPmmxPd+Puaq1Ltb3Hb3gC9t0liShRCaVuWJJmjDEmu7TrpIoicjIwDmi4bPwNbTzmaWBgmk3Xquoj7YzvSFVdKyL9gadE5D1VTdtFmkriLgUYPnx4O5vv+SpSSVqsleUvOsv6nTUMKAwTcBonRO2ZNFArGvfIC+1+LOU1CSQZJUZtY5akGWOMyS5tJmkicjuQi189+xP+GmmvtfU4VT1ud4NT1bWpn5tE5CHgEFoYx6aqdwB3AEyePDnz0x33gFjSq1ubLJrw0C4cmhX3lKpYgs3lNDvFU0fGmlV3UrfstqoYrsbqkzSrpBljjMky7RmTdriqXghsV9X/AQ4DRndtWCAieSJSUHsdOAF/woFJqYzWJ0dK64vJ7q6q1LEqognKmyxM25EqXk1897s7y2sSxBIezwSO4Z7w+f6dlqQZY4zJMu1J0qpTP6tEZDAQBwbtzkFF5EwRWYOf8D0uIk+k7h8sIgtSuw0AXhCR5fiVu8dV9T+7c9xsU9vVWatLk7RY/bE2ldcQb3Ai9fZMGqjl6e7FqepX0QCWOuN5OXIU1tVpjDEmG7VnTNpjItILuBVYil+0+dPuHFRVHwIeSnP/OmBm6vonQMnuHCebxT1t1s3oD95v1zDDDlFtnKR5CpvKowzp5Xd7Rju4tEZN3CMc2LUzD9RW0QAGxleTHwmBpBv6aIwxxvRsbX6jq+qNqasPiMhjQERVy7o2LNOWqmjzxKi9y3AkPCWe8IgllXjSIz8cIBJsOWmqjidpOi+gKpZge1Wc/HCApHZsCGB1PEFRTseTyYZVNICron9EkhHg9x1uyxhjjOnuWvymFJGzWtmGqj7YNSGZ9mja1Ql+Ja21yQMJT/lsa1WzpCrheQwMRtI/CKiKpa+Uba2IoruwrnFHukcb2lmTIN5gobWQxqhxCm08mjHGmKzUWjnj1Fa2KWBJWoYkPaiONU/SFH/GZ0tdiZXRZNqqV0VNgni+EnTSJztVaY5Ve7ytFbG021oTT3okPG22lEdr/CpatNF9IeJUuiFsTJoxxphs1GKSpqoX78lATPtVxRIt1q+irYz3Sld9Az/Z2lkdp2+aBcziSe2SCQnV8SQF4fZ3eZZVx0kkGz/rkMbwnLBV0owxxmSlFgciicgvG1y/qsm2uV0XkmlLS8kWtLzyf8LTtNW3Wjur46QbWlbb1Zn0lBVb27d8xoYKD6+NcWqtjZ/zPH8GaHnUH/e2qTzKtsrmFbswMTzHKmnGGGOyU2tT7I5ucP2iJtsmdkEsph08r/5UUOnUtFD1qowmWx09lvA0bfJX29W56LMEVz5VxXOr463G997WJBc9XsEf34i2ul91CzNCy6oTfLylgs+2VbGhrIYtFVHKquPNumlVlR/EL+HtPsdbJc0YY0xWai1JkxaumwxRhR01sVaTrVgLlbTWqm+1dlQ3TsBU6xPCxRv8n3cuqyGWTB+BqvKHpTV4Cg9/GOOTHS0vzRGNJ5tV7sqjCTaX17QZJ0AsCU94U9heMBr7eBpjjMlGrSVpjoj0FpG+Da73EZE+gLuH4jP4yVJZdYJPt1W1OVA/3WKxbXV11qqJJxvNvKyO+9U3VWXphgRDCxw2VCoPfpA+hoWfJXh3a5JLS8PkB4XfLK5BW+j2VKCmQUJZHUuysaym3XNFo4kkRzhvUextsUqaMcaYrNRaklYELAEWA4X4C9kuSV0Kuj40owo7quKs2lbpr/Ifj9P7w/kEy1e3+rimp2lqq6uzobLq+gSsMpXYfbbTY1uNcu4BIQ4dHOAf70TZXtP4GDUJ5U/La9ivt8NZo0N8pSTM21uSPL2q5e7R2oQwmvBYV1bdocU8krEa5oV+ygE7X8QqacYYY7JRa7M7R+7BOHoUVWV7moHsu9Um/uD8pKckPMVTJZH0Gi0i2/fdv9DnowcpH3xUbSBpq0g1iSQFDd7a9nR11iqvSVCcD65TP2lg6Ub/8QcPDDChn8tX/p1g7ptRvnVITt3j7n8vxuYqZc6hObiOcOI+QRZ8EufO5VEOHxIkL5QmzniSeDLAuh3VzRbLbUs85o95k0DIKmnGGGOyUuefQ2gv4Cls3cUkLW/9qwSqN7NzxImoG2z34/LXvkCfjx5kx6iZJPIGgHoMfvl6qvpPYsc+J4NT/1Y2nDnZ3q7OWgrsrPHPJFBbkVu6IcngfIcBeX7h9fT9Qzz8YYzT9g+xb2+XzVUe962IctSwABP7+3E4IlwxKcJPn1zFB68s4qCjTvGTKU2C+L3lNfEka3dUk+hohgYk436S5gTCWCXNGGNMNtq1EyiadpNknMiWt+tuF618nP5v3s7QF75HoGpzu9oI7VzFgKW/pLrPgWye8FUAnHgVIPR7+06GL7ySwk+fwq3eAjTu7uxIV2etsup4XfUt4Slvbkpw8MD6YYgXjA+TFxRuf8Mfc3bXm1GSCl8t8c9a4FZvoddHD3PsW9fwQuQqzt32Rzas/wyAQa/9lIGv3Ux4x0f+6amS7VyDTZPg1Y9hSyb8JNkNRqySZowxJitZktZVNEnv9+9l1L9nMfSF7+PWbANg48HfZP2UOQTLVzNs0TfJ2fxmG+14DFz8c7xADuunzAHHr755oXzWHXY966b+EPHiDHjjV+zzxGwiW94mqUq8aieUr6d6+3rcmu040TKcWAVpF0NrIp706rpz39+WpCoBBw+or9QVhIQLx4dZtinJ3LeiPL0qztljQgzKd8jd9Aajnvgy/d7+E+LFWTP6QmZ6v+SWd4tRzyNWMIzcTUsZvuibDH7ph+StfxVJpqlKapKilQvI3fA6Eq9i2HNX0/vjh+o211bS3KAtZmuMMSY7tdndKSL7AmtUNSoi0/DXSLtHVXd0bWg9lxvdwYDFPydv8xtUDDqUspGfIxkqBCAZ6UPFkCOJFo5g0Gv/y+CXr2fVCX8mGemdvjFx2DDpWzjJKMmcvk22CZWDplI58BBCO1eRu2kp0d77A+At/ye89XcGNmnuw9MeBgkQKluFF8xle6AfVXFlQK7ixCvxQoVIooaBr95I2YgTWLZ9CgKU9G/8UTllvyD/+ijGI++W8bXIC3yxqA8xplPddyzbxpxL+dBpxAuGAnBSOMYvXq/h2c+SzBh7Edv3/zxFK/9Nr48fZvCrN7J5/CXs2O9MP1lTj1DFGvov+x2RHR+yc9h0qgZOIRHpQ5/3/kH54CNJ5A1ke3Ags2NXc0XxgVh3pzHGmGzUnjFpDwCTRWQ/4A7gEeDvwMyuDKzH8uIM/e93CdRsZWPpFewccULaSk+8YBirj/45OdveJRnpjRsto2DNIpKhIpLhQpKhIpx4JdX9JhIr2qf1Y4oQKxpFrGhU3V01g6fi5Q9mZ3UNoh54SUSTIH7xtPdbd1G4ZSmeDqGaPuwT/Jjq4gmsP/SHqBtGEjUMWnwrX5d+FBecTJF7Ckptd+ZWCjcu4W95r9Enuox8aijffBQbRk5H3TDbDrygUXgn7hPkwQ9iPPxBlBkjg3jBPLaP/jzb9zud3M1vEk3Fnb/uRfov+y2SjJEM92LDpO9SPvQYADZPvIwRz3yd/st/z7rD/ocKyWWRdxDfzOtjlTRjjDFZqT1JmqeqCRE5E/iNqv5GRN7o6sC6NfXI2fwmyWA+XigfL5iPF4j4CZATZOvYC4kVDG0zudJgLlUDJgMQrFhHv7fubLbPhoO/RfnwGa22szPqkRMQgm59slJZNMa/NJk0UBlXHv4gypKNX+AQbyyn5bxF79gOXs85kuHD/VgQYc3RtxJc+xpbX7uPy3UuyScfZO3hNxLttS/9l/+e/A2vEs8pZsfwaWwbdTzRPmNajM8R4cRRQf64LMqnZUlGFKXGtzlBqgZMqtsvWjiC5yLTKfMijD/2C3jBvLptiZxithz4Jfq/dQf5a58jUDWGE5x3yNNjsEqaMcaYbNSeJC0uIl/APzXUqan72j8tMRvV7GToiz9odveGSd+hfNh0KoYeneZBbTTZZwwfz/w7brQMN7YTN7oT0SSVA6e2+rjqhPLlBZUcNzLIZQdF6u+PJ5oNP3t+dZzbXq+hPKYcPmQEk8ePJrf3+fzkpSpeXZfgb33zKazdWRxedCZxbWwsf570KVN2PoXTZyR4sO2AL7L1wAuIFY5sdxVrxsggf1oe5cmVcb5amn4t5NWBkXx524V4Cn+pyWFwk09Z2T4nU7h6IUUr/01xrscdodtYFx8Hsm+7YjDGGGN6kvYkaRcDlwE/UdWVIjIK+GvXhtXNhXJZc8T/4sQrcGMV/s94BersRu4qDl6oEC9USOtnx2xs4adxyqLKM6vifLUkjOv4SVPTVS281CmbinOEn07LZUyf+kRp1rgw//0swQPvx7h4Yn2it3RjkqADA0aNZ2NgAiN657J2ezXRXh1PinpHHKYODvD0qjhfnlgfZ0OPfuhPIHAEnlwZY3aDWAAQl/WH/IBkuAhdshCAcMiW4DDGGJOd2kzSVPVdEfkeMDx1eyVwS1cH1q25Iar7df055lUVaaVSpar866MYYRd2RJU3NiaZPCj9W/rWpiSbq5WvlkYaJWgAI4tcjh4W4OEPYpw9JkRh2B+39sbGBOP7uYQDQtB1CLkOvXJDbKlo/eTpLTlhVJCX1iZ4fX2CQ4c0TmirE8qCj2McOTRAVVx5clWcCyeEcZo8/0RuP/9ndRkAOZEcG5NmjDEmK7W5BIeInAosA/6Tul0qIo92cVx7tfUVHj9/tZpT5pfz2rqW62rvbUvy0XaPL08MkxuEhZ+1vO/Tn8bJCcBhQ9IncbPGhalOwAPv+9Ws7TUen+zw6pbeyAn6H5WiSBB3F5OiqYMD9AoLT6xsHufTq+JUxOHM0SFO3CfE5ipl2cYWThYfU07bfg8AuTlWSTPGGJOd2rNO2vXAIcAOAFVdBrQx3dDsitrkbPbjFTzzaZzcgHD7sijJFlbk/9eHfuJ10j4hjhwa5IU1cWLJ5vvGkspzq+McOTRIJJA+oRnVq76atjNVlQM4aKCfpOWGUmcScKBX7q516wYcYcbIIK+sS1AWrV/EVlV5+IMY+/d2GFfscviQAPlBeGJl+rM6PPphjHOiP2LNwOMglG+VNGOMMVmpXRMHVLWsSbdbO5eJz07/eH01H368a11+LVlX4fHMp3Fc8U+7dO6BId7fmuT6F6r5zydxTt4v1Gj/nVHlv6vjnDAqSG5QmD48yJMr47y2PsGRQxsnUS+vTVAVh+NGtp5czRoX5rnVCR58P8rWaiU/CPv18vP4SLC+i7QoJ8T2qliHz7cJcOKoIA+8H+PZT+OcOToMwJINST7b6XHN1AgiQsiF6SOCPLEyTmVMG533syahPPhBjNEDxxM97JBUgmZJmjHGmOzTniTtHRH5IuCKyP7AlcBLXRtW9zbv1dWs2NC5SVrYrU/OinP8xKjvEGFcscs9b0c5dkSQnGB9MvLUqhixJJyyr5+8HTTApVdYWPhpvFmS9syncfrmCCX908+qrDWql8tRwwI89EGMSEAoHRDAdfzxaA2X93Ad6JUbYtsunL90VC+X/Xs7PPFJfZL20AcxekeEY4bXx33iqBD/+ijOos8aJ6j/+cSfKPHFsaH68WpWSTPGGJOF2pOkXQFcC0SBfwBPADd2ZVDd3SPfOIxP3n65U9sUodkgeRHh0tIwVz1dxfz3Y3xpvJ/UqCqPfRRnbF+XfXv7iZfrCEcNC/DEyjhVcSU3ldCVRT1eW5fgrDGhtDMqm7ogVU2rSmjdeLRIsHmveFFOkO2VsQ6fFxTghFEhfre0ho+2J4m48Nr6BBeODxNqkAiO7uMwotDhiZX1SVo8qdz3XpTxxS7j+wUaFNAsSTPGGJN92hyTpqpVqnqtqk5R1cmp6zV7IrjuynWk0y9NE7RaY4sDHDU0wH3vRdlW7fcyv7ExyZpyj1P2a1wxO3ZEkFjS796s9dxnCZIKM0a0bxxZ7dg08KtzALnB5rl8wBGKdnFs2rEjggQdeGJlnIc/jBFw4OR9G7clIpy4T5AVW5N8ttMfH/fsp3E2VylfGOsnbVZJM8YYk81arKSJyL+g5UKJqp7WJRGZZr48McxLaxP89Z0oV03O4bGPYhSGGncPAowtdumXKyz8LM6M1Pizpz+NM7LIYZ9e7Zkj4vt/B0c4bEiCoYV+khYJpX9sr9wQZVXxDlfTCsPCYUMCPLMqTsJTpg0P0ien+TFmjKhfAPfiCQ73roixby+HKallRupTM0vSjDHGZJ/Wujt/tseiMK0aWuhy8n5BHvsozjHDgry4NsHZY0KNugfBryxNH+4PzN8Z9aiMw7tbklxSEm51vbWm+uQ4HDfSr1YFXCHkpk/Sgo5QlBNkR3VHlt/1nTgqyHOr/YrfmaPrx5zVRqmpOA4ZFOCplXH26+2yutzjusNz6p6LYJU0Y4wx2avFJE1V/7snAzGtu2BcmKdXxvnh81V4CifvG0q73/QRQe57L8bzqxNsq1EEOHb4rp8JISfY+mSDPnlhKmIJEmmW/mjNpIEB+uUIA/IcRjdYXDcSdFGgJu53cZ4wyl+y41evVzOkwOHIoQ0+sjYmzRhjTBZrc+JAakbnT4GxQN15elTV1krbg3pHHM49MMzct6JMGugypCB9dWvfXg5DCxwWfhZnS7VS0t+lf17LXZ2CP8Yu0cJ6GjlpxqM15DowsCDCmh3V7X4upI75ixl5hJs0nxt2SSa1Lkk7dHCAwpCwM6ZcWtp48oONSTPGGJPN2jNQ6W7gD0ACmA7cA/ytK4My6Z09JsQRQwN1szzTERGmDw+wfFOSteVe3di0dFwRhvTOoX9hy+3ltDAerfE+Lr1z01f2WjMw36F3pHH7eaEAoUB9ZS3oCqfsF2RIgdNsnTdLzYwxxmSz9iRpOar6DCCq+qmqXg+c3LVhGaF+lf9akYBw/ZG5jCtuvbo1PTWTM+jAUUPTJ2lB12Fonxxygi55oQD5TUta+DM4WxqP1lTfvBDhQPsnJ6QTcIVwwCHU5KwIsyeEuWtmXqO12qBBAc0qacYYY7JQe9ZJi4qIA3woIpcDa4H8rg1r7ybAgKIIIdfhs22JNvdvalihy8R+LoPznUar9deKBF0GFUUINOg6LC4IUxlNNJqpmRNqfTxao5gFBhRGWL2tapfWTgPITY1/C7suQv3UYhFJWzUTG5NmjDEmi7UnSbsKyMU/08CNwLHARV0Z1N7MERhclFOXILkiJLXjac+tx+amTV3ywwEGFERwmhS9go7QNz/Mlor6MylEAu1P0gDCAadZGx2RG64/P2gw4BBLtH72sfoczZI0Y4wx2afNJE1VX09drRCRbwM7VHchazBtCjjC4F45jboNc0IuFdGOV9PSLY4bcIVBRZE0e/t65QQpr4kTTSVHHamk1eqdG6QymqA6NfC/vYTGi+aG3LaTtPrnaEmaMcaY7NPiICIR+ZGIHJC6HhaRhcDHwEYROW5PBbi3CLoOQ3rnNBvXFWljCYyOKIq0vhSHCPTL9ycRuCK7PMZsQFGEgNuxxCkcdGk4/C2c5lRUzdiYNGOMMVmstUraedSfo7O2e7MfMBr4C/D0rh5URG4FTgVi+Infxaq6I81+JwG/AlzgT6p6864eszO5jrB//z0zLK+tdcraS4CCnLbXS8sJuRREAuxOrTToCIOLcliz3V/TrT3ywo2fZ6gdCaJV0owxxmSz1r4JYw26NU8E/qmqSVVdQfvGsrXmKWC8qk4EPgC+33QHEXGB3wGfw1+j7QsiMnY3j9vjRIIO7TgvepvywgGC7WyoOD+cdrZnR4QDDgOLIu1On/KazGQNt2M8nI1JM8YYk81aS9KiIjJeRPrhr4/2ZINtubtzUFV9UlVrB1q9AgxNs9shwEeq+omqxoB/AqfvznF7qkgbC8q2R0FO+9sIOEJBZPePmRcKUJzf8hpsDY/XtGs16AhuG8mXWCXNGGNMFmstSbsKmA+8B9ymqisBRGQm8EYnxvBl4N9p7h8CrG5we03qvr1OexaUbU3AEfI6IdHbFb1yg/TKbb2bNbeFCQrt6fIErJJmjDEmK7V27s5XgQPS3L8AWNBWwyLyNDAwzaZrVfWR1D7X4p/JYF57A27leJcClwIMHz58d5vrVnI6uBRGU4U5wYzmMf3ywySS2uIs1aaL9tYKB51WZ4namDRjjDHZrMvKK6ra6gxQEZkNnALMaGFJj7XAsAa3h6bua+l4dwB3AEyePDmrlgiJBBsv7tpRhW3M6twTBhREgJpmiVq6MyvUCrsuEG+xTcdmdxpjjMliu9ePtotSszavAU5T1aoWdnsd2F9ERolICDgfeHRPxdidiOz6Uhy5oUCz0yllguPAoKJIs3N8Nl16o6F2d3daJc0YY0wWykiSBvwWKACeEpFlInI7gIgMFpEFAKmJBZcDTwArgPtU9Z0MxZtxrS0s67Zw2iSAwk6YANCZivND9C+on/XZdOmNhsIBp9X0S2o/vVZJM8YYk4Xa/AYXkXOA/6hquYhcBxwM3KSqS3f1oKq6Xwv3rwNmNrjdrvFve4NIK4u79isIEwo4bNpZQ02DVfpdkd1eSqMrFOUECLo5rC+rbnSWgaZE/GpaNM2ZB1wRcgO1j7UkzRhjTPZpTyXth6kE7UjgOODPwB+6NizTVE4gkDYViQT9xWfDAYehvXPpmx+q268gJ9Bti0y5IZehvXNbTT6h5S7Pwpxg/flHJVMFYWOMMabrtOfbrXZ63cnAHar6OBBqZX/TBRwn/QKv/RqsQyYCfXJDDOuTSyTodosJA61pz2mn0j1nAYoaLuvRXTNRY4wxZje0J0lbKyJ/xD9N1AIRCbfzcaaTRZqsl1YYCaStRIUDDsPSnAe0JwoFmidg+ZEmZ0+wJM0YY0wWas+3+Ln4g/dPTJ1fsw9wdVcGZdJrOHnAEejbjtX8e7p0lbReOVbINcYYk/3aM6o8AiwCEJE+QBRY2IUxmRbkBOrfrj55YQKdcVLPbi7gCAFHSKTO1J4TdJtUD7P/NTDGGLN3ak8lbSmwGf9E6B+mrq8SkaUiMqkrgzONuY7flRl0HXrldO/xZp0p1KCaVtT0FFPW1WmMMSZLtSdJewqYqarFqtoX+BzwOPAN4PddGZxpLhJ0KS4I7VW5Se3YuqDrUNB0SRGb2WmMMSZLtecb7lBVfaL2hqo+mbrvFSD7B0V1M71yg+S3cBqlbFWbpKWvHu5F2aoxxpi9Snu+7deLyPeAf6ZunwdsFBEXaL7KqOlSoZbOoZTFQgEHR1o4B+neVFI0xhizV2nPN/4X8U9u/nDqMjx1n4s/89OYLhVyHXrlhuoXr23EkjRjjDHZqc1KmqpuAa5oYfNHnRuOMc2JQO+Wlt2wSpoxxpgs1Z5zd44GvguMbLi/qh7bdWEZ01j6KhpYJc0YY0y2as+YtPuB24E/UX+KKGO6B6ukGWOMyVLtSdISqmonVDfdlCVpxhhjslN7Jg78S0S+ISKDRKRP7aXLIzOmPaySZowxJku1p5J2Uepnw/N1KrBP54djTEdZkmaMMSY7tWd256g9EYgxu8QqacYYY7JUe2Z3BoGvA0en7loE/FFV410YlzHGGGPMXq093Z1/AILUn6fzS6n7vtJVQRnTblZJM8YYk6Xak6RNUdWSBrefFZHlXRWQMR1jSZoxxpjs1J7ZnUkR2bf2hojsg62XZroLq6QZY4zJUu2ppF0NLBSRT/DLFiOAi7s0KmPazZI0Y4wx2ak9szufEZH9gTGpu95X1WjXhmVMO1klzRhjTJZq+YyIIlNEZCBAKikrBW4EbrXFbE33YUmaMcaY7NTamLQ/AjEAETkauBm4BygD7uj60IxpB6ukGWOMyVKtdXe6qrotdf084A5VfQB4QESWdXlkxrSLJWnGGGOyU2uVNFdEapO4GcCzDba1Z8KBMV3PKmnGGGOyVGvJ1j+A/4rIFqAaeB5ARPbD7/I0phuwJM0YY0x2ajFJU9WfiMgzwCDgSVXV1CYHuGJPBGdMm6ySZowxJku12m2pqq+kue+DrgvHmI6yJM0YY0x2as8ZB4zpvqySZowxJktZkmZ6OEvSjDHGZCdL0kzPZpU0Y4wxWcqSNNPDWZJmjDEmO1mSZno2q6QZY4zJUhlZlFZEbgVOxT/t1MfAxaq6I81+q4ByIAkkVHXyHgzTGGOMMSZjMlVJewoYr6oTgQ+A77ey73RVLbUEzaRllTRjjDFZKiNJmqo+qaqJ1M1XgKGZiMNkA0vSjDHGZKfuMCbty8C/W9imwJMiskRELt2DMZmeQrrDR9gYY4zpfF02Jk1EngYGptl0rao+ktrnWiABzGuhmSNVda2I9AeeEpH3VPW5Fo53KXApwPDhw3c7ftNDWHenMcaYLNVlSZqqHtfadhGZDZwCzGhwXtCmbaxN/dwkIg8BhwBpkzRVvQO4A2Dy5Mlp2zPZyJI0Y4wx2SkjfUUichJwDXCaqla1sE+eiBTUXgdOAN7ec1GaHsEqacYYY7JUpgb0/BYowO/CXCYitwOIyGARWZDaZwDwgogsB14DHlfV/2QmXNN9WZJmjDEmO2VknTRV3a+F+9cBM1PXPwFK9mRcpgeySpoxxpgsZVPjTA9nSZoxxpjsZEma6dmskmaMMSZLWZJmejhL0owxxmQnS9JMz2aVNGOMMVnKkjTTw1mSZowxJjtZkmZ6NqukGWOMyVKWpJkezpI0Y4wx2cmSNNOzWSXNGGNMlrIkzfRslqQZY4zJUpakmR7MEjRjjDHZy5I003NZFc0YY0wWsyTN9GCWpBljjMlelqSZnssqacYYY7KYJWmmB7MkzRhjTPayJM30XFZJM8YYk8UsSTM9mCVpxhhjspclaabnskqaMcaYLGZJmunBLEkzxhiTvSxJMz2XVdKMMcZkMUvSTA9mSZoxxpjsZUma6bmskmaMMSaLWZJmejBL0owxxmQvS9JMz2WVNGOMMVnMkjRjjDHGmG4okOkAjNllVkkzxrRDPB5nzZo11NTUZDoUs5eLRCIMHTqUYDDYrv0tSTM9mCVpxpi2rVmzhoKCAkaOHInYH3cmQ1SVrVu3smbNGkaNGtWux1h3p+m57JetMaYdampq6Nu3ryVoJqNEhL59+3aoomtJmunB7BeuMaZ9LEEz3UFHP4eWpJmey37pGmN6iJ/85CeMGzeOiRMnUlpayquvvtrq/rNnz2b+/Pm7dcxFixZRVFREaWlp3eXpp58GID8/f5fa/OUvf0lVVVXd7ZkzZ7Jjx44W97/99tu55557AJg7dy7r1q3r0PGmTZvGmDFjKCkp4YgjjuD999/fpbhrj3/55Zc3iyudVatW8fe//73u9uLFi7nyyit3+di7ysakmR7MkjRjTPf38ssv89hjj7F06VLC4TBbtmwhFovtkWMfddRRPPbYY53W3i9/+UsuuOACcnNzAViwYEGr+1922WV11+fOncv48eMZPHhwh445b948Jk+ezB133MHVV1/No48+2mh7MpnEdd0OtdkwrnRqk7QvfvGLAEyePJnJkyd36BidwSpppueySpoxpgdYv349xcXFhMNhAIqLi+sSlRtuuIEpU6Ywfvx4Lr30UlS12eOXLFnCMcccw6RJkzjxxBNZv349AL/+9a8ZO3YsEydO5Pzzz9+l2CoqKpgxYwYHH3wwEyZM4JFHHgGgsrKSk08+mZKSEsaPH8+9997Lr3/9a9atW8f06dOZPn06ACNHjmTLli0A3HPPPUycOJGSkhK+9KUvAXD99dfzs5/9jPnz57N48WJmzZpFaWkpjz/+OGeccUZdHE899RRnnnlmq7EeffTRfPTRR4BfCfzOd75DSUkJL7/8Mn/729845JBDKC0t5Wtf+xrJZBKAu+++m9GjR3PIIYfw4osv1rVVGxfARx99xHHHHUdJSQkHH3wwH3/8MXPmzOH555+ntLSU2267jUWLFnHKKacAsG3bNs444wwmTpzIoYceyptvvlnX5pe//GWmTZvGPvvsw69//etdek8askqa6cEsSTPGdMz//Osd3l23s1PbHDu4kB+fOq7F7SeccAI33HADo0eP5rjjjuO8887jmGOOAeDyyy/nRz/6EQBf+tKXeOyxxzj11FPrHhuPx7niiit45JFH6NevH/feey/XXnstd911FzfffDMrV64kHA632OVYm2jUeuCBB9h3333rbkciER566CEKCwvZsmULhx56KKeddhr/+c9/GDx4MI8//jgAZWVlFBUV8Ytf/IKFCxdSXFzc6DjvvPMON910Ey+99BLFxcVs27at0fbPf/7z/Pa3v+VnP/sZkydPRlX5zne+w+bNm+nXrx933303X/7yl1t9nf/1r38xYcIEwE8ip06dys9//nNWrFjBLbfcwosvvkgwGOQb3/gG8+bN4/jjj+fHP/4xS5YsoaioiOnTp3PQQQc1a3fWrFnMmTOHM888k5qaGjzP4+abb+ZnP/tZXRVy0aJFdfv/+Mc/5qCDDuLhhx/m2Wef5cILL2TZsmUAvPfeeyxcuJDy8nLGjBnD17/+9XYvt5GOJWmm5xIrBBtjur/8/HyWLFnC888/z8KFCznvvPO4+eabmT17NgsXLuT//u//qKqqYtu2bYwbN65Rkvb+++/z9ttvc/zxxwN+196gQYMAmDhxIrNmzeKMM85oVJVqqK3uTlXlBz/4Ac899xyO47B27Vo2btzIhAkT+M53vsP3vvc9TjnlFI466qhWn+Ozzz7LOeecU5e89enTp9X9RYQvfelL/O1vf+Piiy/m5ZdfbnGM2KxZs8jJyWHkyJH85je/AcB1Xc4++2wAnnnmGZYsWcKUKVMAqK6upn///rz66qtMmzaNfv36AXDeeefxwQcfNGq7vLyctWvX1lXxIpFIq3EDvPDCCzzwwAMAHHvssWzdupWdO/3E/+STTyYcDhMOh+nfvz8bN25k6NChbbbZEkvSTM9l3Z3GmA5qreLVlVzXZdq0aUybNo0JEybwl7/8hfPPP59vfOMbLF68mGHDhnH99dc3W55BVRk3bhwvv/xyszYff/xxnnvuOf71r3/xk5/8hLfeeotAoGNf6/PmzWPz5s0sWbKEYDDIyJEjqampYfTo0SxdupQFCxZw3XXXMWPGjLqKX2e5+OKLOfXUU4lEIpxzzjktxl47Jq2hSCRSNw5NVbnooov46U9/2mifhx9+uFPjbY/aLm3w3/NEIrFb7VkpwvRglqQZY7q/999/nw8//LDu9rJlyxgxYkRdQlZcXExFRUXa2Zxjxoxh8+bNdUlaPB7nnXfewfM8Vq9ezfTp07nlllsoKyujoqKiw7GVlZXRv39/gsEgCxcu5NNPPwVg3bp15ObmcsEFF3D11VezdOlSAAoKCigvL2/WzrHHHsv999/P1q1bAZp1d6Z77ODBgxk8eDA33XQTF198cYdjrzVjxgzmz5/Ppk2b6o796aefMnXqVP773/+ydetW4vE4999/f9qYhg4dWpfQRaNRqqqqWnye4Fcn582bB/jdoMXFxRQWFu5y/K3JWCVNRG4ETgc8YBMwW1Wbzc0VkYuA61I3b1LVv+y5KE23ZpU0Y0wPUFFRwRVXXMGOHTsIBALst99+3HHHHfTq1YuvfvWrjB8/noEDB9Z11zUUCoWYP38+V155JWVlZSQSCb75zW8yevRoLrjgAsrKylBVrrzySnr16tXs8U3HpF133XV8/vOfr7s9a9YsTj31VCZMmMDkyZM54IADAHjrrbe4+uqrcRyHYDDIH/7wBwAuvfRSTjrpJAYPHszChQvr2hk3bhzXXnstxxxzDK7rctBBBzF37txGscyePZvLLruMnJwcXn75ZXJycpg1axabN2/mwAMP3OXXd+zYsdx0002ccMIJeJ5HMBjkd7/7HYceeijXX389hx12GL169Wr0OjT017/+la997Wv86Ec/IhgMcv/99zNx4kRc16WkpITZs2c3GstWO0Fg4sSJ5Obm8pe/dF1aIulmkuwJIlKoqjtT168ExqrqZU326QMsBiYDCiwBJqnq9tbanjx5si5evLhrAgfwkrDhza5r37RP4VDI75fpKIwx3dyKFSt2KwkwXefyyy/noIMO4pJLLsl0KHtMus+jiCxR1WZrfGSsu7M2QUvJw0/CmjoReEpVt6USs6eAk/ZEfKYHsEqaMcb0WJMmTeLNN9/kggsuyHQo3VZGJw6IyE+AC4EyYHqaXYYAqxvcXpO6L11blwKXAgwfPrxzAzXdlCVpxhjTUy1ZsiTTIXR7XVpJE5GnReTtNJfTAVT1WlUdBswDLt+dY6nqHao6WVUn1063NVnOKmnGGGOyWJdW0lT1uHbuOg9YAPy4yf1rgWkNbg8FFu12YMYYY4wx3VzGxqSJyP4Nbp4OvJdmtyeAE0Skt4j0Bk5I3WeMVdKMMcZktUyOSbtZRMbgL8HxKXAZgIhMBi5T1a+o6rbUUh2vpx5zg6o2X3zF7KUsSTPGGJO9MpakqerZLdy/GPhKg9t3AXftqbhMD2KVNGNMN7d161ZmzJgBwIYNG3Bdt+40Ra+99hqhUGi3jzFt2jTWr19PTk4OAPvttx/z58/n+uuvJz8/n+9+97sdam/ZsmWsW7eOmTNnAvDoo4/y7rvvMmfOnBYfc/jhh/PSSy+xatUqXnrpJb74xS/u+hMydey0UN1ZuBCinXsi4KwSzMt0BMYY06q+ffvWnXw7XdKUSCQ6fCqndNKdOmlXLVu2jMWLF9claaeddhqnnXZaq4956aWXAFi1ahV///vfLUnrJHZaqO6sYCC4u/9XVlYK5oFrf2MYY3qe2pX3p06dyjXXXMP111/Pz372s7rt48ePZ9WqVQD87W9/45BDDqG0tJSvfe1rJJPJXTrmnXfeyZQpUygpKeHss8+mqqoKgPvvv5/x48dTUlLC0UcfTSwW40c/+hH33nsvpaWl3HvvvcydO5fLL/cXYNi4cSNnnnkmJSUllJSU1CVn+fn5AMyZM6fuLAe33XYbRx99dF2SCnDkkUeyfPnyXXoOeyP7luuuxIFgLuT2hfL1mY6m+4kUZToCY0xPdffJze8bdwYc8lWIVcG8c5pvL/0iHDQLKrfCfRc23nbx4x0OYc2aNbz00ku4rsv111+fdp8VK1Zw77338uKLLxIMBvnGN77BvHnzuPDCC5vtO2vWrLruzuOPP55bb7210fazzjqLr371q4B/aqg///nPXHHFFdxwww088cQTDBkyhB07dhAKhbjhhhtYvHgxv/3tbwEand7pyiuv5JhjjuGhhx4imUw2O1/ozTffzM9+9jMee+wxAPr06cPcuXP55S9/yQcffEBNTQ0lJSUdfr32VpakdVehfH/MVU6fnpekOQHwEl17jEjXnMzWGGP2hHPOOQfXdVvd55lnnmHJkiV15/Ssrq6mf//+afdtq7vz7bff5rrrrmPHjh1UVFRw4oknAnDEEUcwe/Zszj33XM4666w243722We55557AHBdl6Ki1v9gPuecc7jxxhu59dZbueuuu5g9e3abxzD1LEnrrkKp8VaBUM8bm9ZrBOz4tOsSNTcEwZyuadsYk/1aq3yFclvfntd3lypnzZrJqx9TGwgE8Dyv7nZNTQ0AqspFF13ET3/6090+3uzZs3n44YcpKSlh7ty5LFq0CIDbb7+dV199lccff5xJkyZ1+lkAcnNzOf7443nkkUe477777CwDHWRj0rqrUH799dw+mYujo4K5fpUrp3fXHcO6Oo0xWWTkyJEsXboUgKVLl7Jy5UoAZsyYwfz589m0aRMA27Zt49NPP92lY5SXlzNo0CDi8Tjz5s2ru//jjz9m6tSp3HDDDfTr14/Vq1dTUFBAeXl52nZmzJjBH/7wBwCSySRlZWWNtqd77Fe+8hWuvPJKpkyZQu/eXfjdkIUsSeuWxE92akV6+V2IPUFe6pRcuX277hhh6+o0xmSPs88+m23btjFu3Dh++9vfMnr0aADGjh3LTTfdxAknnMDEiRM5/vjjWb8+/fCXWbNmUVpaSmlpKccd1/xkPzfeeCNTp07liCOO4IADDqi7/+qrr2bChAmMHz+eww8/nJKSEqZPn867775bN3GgoV/96lcsXLiQCRMmMGnSJN59991G2ydOnIjrupSUlHDbbbcB/onUCwsLufjii3frddobiapmOoZON3nyZF28eHHXHcBLwoY3u679UD4U79/4vrI1ULm5647ZGdwQ9B9bv37Z5vchXtW5xxAXBk6wNdKMMe22YsUKDjzwwEyHsddat24d06ZN47333sNxrDaU7vMoIktUtdmgQnu1uqNQmvW/OqMyJS4EIrvfTktyixsnT11RTQsXWIJmjDE9xD333MPUqVP5yU9+YgnaLrBXbFeI4yc8XaXheLRawZzGXaC7onAw9N1/99tJRxzIK258X05v//7OZOPRjDGmx7jwwgtZvXo155yTZlkT0yZL0naFyO4N5m91fJmkr6RB+spUIAeKR6dP7BoKFfhJlBuAvvu1vX9H5fYFp0ni6ridn1TZeDRjjDF7CUvSdlXtAPmOCOb6CVXRsFb2yWme7NRqVJkSKBgE/cb4SV3vkS2fnUAc6NXgmI4Lffbt3ISnpdejM7s8Q/l2lgFjjDF7DUvSdlUg3P4kRxwoHJKqeOVBTi+/ApZOS1U0qK9MBXP95KxgYP34LDfoJ2qkGa+VP9CPt1FbDvTZx585ursiRc3brxUuALeFbQWD/cV628uqaMYYY/YiVpbYHXnFbS8yGymCwqH+orQN5ff3F3xtqq1uyMIhfndpusHzoTy/Slf2Wf19wVz/WOmIQJ9R/mlOojshVrFrC9DmtdB+rdy+UL6u4YH9OPNSVTZxoGpL28exswwYY4zZi1glbXdEilquEoE/DqzPPs0TNPC7LtM9tq0kzQ22Prsxr68/yxIAgV7D254NmdfXT9YGToDiMX4i2NrzaiiYB+E2Ys7tQ32FT/yKX16DbtBew9pO9NywnWXAGNNj/epXv2L8+PGMGzeOX/7yl3X3X3/99QwZMqRujbMFCxYA8OKLLzJx4kQmT57Mhx9+CMCOHTs44YQTGp2doKF4PM6cOXPYf//9OfjggznssMP497//vUvxbt68malTp3LQQQfx/PPPM3PmTHbs2NFsv6Ynh99TGp70vSWrVq3i73//+x6Jp/YE853NKmm7K68f7FzT/P6m48CabRfIH9C46hWIdM6Yq6KhkKjxE76OJjahXP+i2qT61YL2TKBwg363Z6zSTwbDBWliHuK/ZhUb0rdhVTRjTCd5a01Z2zt1wIShrU+Qevvtt7nzzjt57bXXCIVCnHTSSZxyyinst99+AHzrW9/iu9/9bqPH/PznP2fBggWsWrWK22+/nZ///OfcdNNN/OAHP2hxKYsf/vCHrF+/nrfffptwOMzGjRv573//u0vP6ZlnnmHChAn86U9/AuCoo47apXYyqTZJ++IXv9juxyQSCQKB7pMaWSVtd+X2Sb/MROHQlsdpNXxsw8H+rY1H6wgR6D3KH7O2q9IlUmn3a2fylN/fn1XaWruFg/wqXu0SJ07Qf30Ckc4ZO2eMMRmwYsUKpk6dSm5uLoFAgGOOOYYHH3yw1ccEg0GqqqqoqqoiGAzy8ccfs3r1aqZNm5Z2/6qqKu68805+85vfEA773z0DBgzg3HPPBeAf//hH3ZkFvve979U9Lj8/n2uvvZaSkhIOPfRQNm7cyLJly7jmmmt45JFHKC0tpbq6mpEjR7Jliz8s5Sc/+QmjR4/myCOP5P33369r6+OPP+akk05i0qRJHHXUUbz33nuAf97QK6+8ksMPP5x99tmH+fPn1z3mlltuYcKECZSUlDBnzpxW22lJS+3PmTOH559/ntLSUm677TaSySRXX301U6ZMYeLEifzxj38EYNGiRRx11FGcdtppjB07ljlz5vC73/2urv3aamFFRQUzZszg4IMPZsKECTzyyCOtxtUpVDXrLpMmTdI9avtnqmuX1l+2fNT+x5Zvqn9c5daui3FXrH+r8fNqetn4bqYjNMaYNr37buPfVW+u3tGpl/Ycf//999ctW7ZoZWWlHnrooXr55ZerquqPf/xjHTFihE6YMEEvvvhi3bZtm6qqvvHGGzp16lSdNm2arl69Ws877zz94IMPWjzG8uXLtbS0NO22tWvX6rBhw3TTpk0aj8d1+vTp+tBDD6mqKqCPPvqoqqpeffXVeuONN6qq6t13363/7//9v7o2RowYoZs3b9bFixfr+PHjtbKyUsvKynTffffVW2+9VVVVjz322LoYX3nlFZ0+fbqqql500UX6+c9/XpPJpL7zzju67777qqrqggUL9LDDDtPKykpVVd26dWur7TTUML6W2l+4cKGefPLJdY/54x//WPf8ampqdNKkSfrJJ5/owoULNTc3Vz/55BNVVV26dKkeffTRdY878MAD9bPPPtN4PK5lZWWqqrp582bdd9991fM8VVXNy8tr8b1pqunnUVUVWKxp8pnuU9PryfKK6we+i+uPA2uv3L5+F5+X6Py1y3ZXuACqt7Wy3bogjTGmLQceeCDf+973OOGEE8jLy6O0tBTX9Zda+vrXv84Pf/hDRIQf/vCHfOc73+Guu+6itLSUV155BYDnnnuOQYMGoaqcd955BINBfv7znzNgwIB2Hf/1119n2rRp9OvnL5U0a9YsnnvuOc444wxCoRCnnHIK4J9j86mnnmq1reeff54zzzyT3Fx/UfTTTjsNgIqKCl566aVGi9ZGo9G662eccQaO4zB27Fg2btwIwNNPP83FF19c11afPn3abKcl6dpv6sknn+TNN9+sq7SVlZXx4YcfEgqFOOSQQxg1ahQABx10EJs2bWLdunVs3ryZ3r17M2zYMOLxOD/4wQ947rnncByHtWvXsnHjRgYO3I1eqzZYktYZgjn+JIFYuT8ezA22/7GO4w+ar9qSfoJBJrWZpLWzS9QYY/Zyl1xyCZdccgkAP/jBDxg6dChAo0Trq1/9al3CVEtVuemmm/jnP//JFVdcwf/93/+xatUqfv3rX/OTn/ykbr/99tuPzz77jJ07d1JY2P4/oIPBIJKaXOa6LonELszwBzzPo1evXixbtizt9tou2NrntKvttKQ97asqv/nNbzjxxBMb3b9o0SLy8hoPNzrnnHOYP38+GzZs4LzzzgNg3rx5bN68mSVLlhAMBhk5ciQ1NTUdirOjbExaZ8kr9sdN7cqZCPKKu+fpjlqrlIljSZoxxrTTpk2bAPjss8948MEH6wazr1+/vm6fhx56iPHjxzd63D333MPMmTPp06cPVVVVOI6D4zhUVVU12i83N5dLLrmEq666ilgsBvgzNO+//34OOeQQ/vvf/7JlyxaSyST/+Mc/OOaYY3bpeRx99NE8/PDDVFdXU15ezr/+9S8ACgsLGTVqFPfffz/gJ0TLly9vta3jjz+eu+++u+65bNu2bZfaaUlBQQHl5eV1t0888UT+8Ic/EI/HAfjggw+orKxM+9jzzjuPf/7zn8yfP7+uqldWVkb//v0JBoMsXLiQTz9Ns4xWJ7NKWmeJFO16d6Xj+mcP6G7cgL/ERjzNhziUbyc6N8aYdjr77LPZunUrwWCQ3/3ud/Tq1QuAa665hmXLliEijBw5sm4wO/iTAebOncuTTz4JwLe//W1mzpxJKBRKu7TETTfdxHXXXcfYsWOJRCLk5eVxww03MGjQIG6++WamT5+OqnLyySdz+umn79LzOPjggznvvPMoKSmhf//+TJkypW7bvHnz+PrXv85NN91EPB7n/PPPp6SkpMW2TjrpJJYtW8bkyZMJhULMnDmT//3f/+1wOy2ZOHEirutSUlLC7Nmzueqqq1i1ahUHH3wwqkq/fv14+OGH0z523LhxlJeXM2TIEAYN8r+fZ82axamnnsqECROYPHkyBxxwQIdj6ihprezYU02ePFkXL16c6TCyw8716ZfFKBrW/ITqxhjTDa1YsYIDDzww02EYA6T/PIrIElWd3HRf6+40rWupS9MmDRhjjDFdypI007pQnj9jtaFApPtNcjDGGGOyjCVppnUizatpVkUzxhhjupwlaaZtTZMym9VpjDHGdDlL0kzbGiZltvSGMcYYs0dYkmbaFghBIHWidlt6wxhjjNkjLEkz7VNbPeuOi+4aY0w3tXXrVkpLSyktLWXgwIEMGTKk7nbtorO7a9q0aYwZM6au3c9//vNA/YnBO2rZsmUsWLCg7vajjz7KzTff3OpjDj/8cABWrVqVdg231ixatIiioiJKS0s54IAD+O53v9vhmLOVLWZr2idcAJWbrKvTGNPzrXujc9sbfFCLm/r27Vt3iqPrr7+e/Pz8RklIIpEgENj9r+J58+YxeXKzZbZ2ybJly1i8eDEzZ84E/PNz1p6jsyUvvfQSUJ+k1Z5Rob2OOuooHnvsMaqrqznooIM488wzOeKII3btCWQRq6SZ9gkXQDAXAuG29zXGGNOi2bNnc9lllzF16lSuueaaZhWv8ePHs2rVKgD+9re/ccghh1BaWsrXvvY1ksnkLh3zzjvvZMqUKZSUlHD22WfXnYrp/vvvZ/z48ZSUlHD00UcTi8X40Y9+xL333ktpaSn33nsvc+fO5fLLLwdg48aNnHnmmZSUlFBSUlKXnOXn+2fcmTNnDs8//zylpaXcdtttHH300Y3Ow3nkkUe2epqnnJwcSktLWbt2batxz549myuvvJLDDz+cffbZp+6k6Z7n8Y1vfIMDDjiA448/npkzZ9ZtW7JkCccccwyTJk3ixBNPbHRKru7KkjTTPiLd89RVxhjTA61Zs4aXXnqJX/ziFy3us2LFCu69915efPFFli1bhuu6zJs3L+2+s2bNquvuvPrqq5ttP+uss3j99ddZvnw5Bx54IH/+858BuOGGG3jiiSdYvnw5jz76KKFQiBtuuIHzzjuPZcuW1Z1cvNaVV17JMcccw/Lly1m6dCnjxo1rtP3mm2/mqKOOYtmyZXzrW9/ikksuYe7cuYB/rsyamppWT/G0fft2PvzwQ44++uhW4wb/vKcvvPACjz32GHPmzAHgwQcfZNWqVbz77rv89a9/5eWXXwYgHo9zxRVXMH/+fJYsWcKXv/xlrr322hbj6C6su9O0X8TWRzPGmM5wzjnn4Lpuq/s888wzLFmypO78mNXV1fTv3z/tvm11d7799ttcd9117Nixg4qKCk488UQAjjjiCGbPns25557LWWed1Wbczz77LPfccw8ArutSVNT6OOVzzjmHG2+8kVtvvZW77rqL2bNnp93v+eefp6SkhA8//JBvfvObDBw4sNW4Ac444wwcx2Hs2LFs3LgRgBdeeIFzzjkHx3EYOHAg06dPB+D999/n7bff5vjjjwcgmUzWnZOzO7MkzRhjjNnD8vLy6q4HAgE8z6u7XVNTA4CqctFFF/HTn/50t483e/ZsHn74YUpKSpg7dy6LFi0C4Pbbb+fVV1/l8ccfZ9KkSSxZsmS3j9VQbm4uxx9/PI888gj33Xdfi+3XjklbuXIlhx56KOeeey6lpaUtxg0QDtcPv2nrPOSqyrhx4+oqaz1FRro7ReRGEXlTRJaJyJMiMriF/ZKpfZaJyKN7Ok5jjDGmq40cOZKlS5cCsHTpUlauXAnAjBkzmD9/Pps2bQJg27ZtfPrpp7t0jPLycgYNGkQ8Hm/UZfrxxx8zdepUbrjhBvr168fq1aspKCigvLw8bTszZszgD3/4A+BXo8rKyhptT/fYr3zlK1x55ZVMmTKF3r17txrnqFGjmDNnDrfcckurcbfkiCOO4IEHHsDzPDZu3FiX1I0ZM4bNmzc36v5855132mwv0zI1Ju1WVZ2oqqXAY8CPWtivWlVLU5fWp5YYY4wxPdDZZ5/Ntm3bGDduHL/97W8ZPXo0AGPHjuWmm27ihBNOYOLEiRx//PEtDnZvOCbtuOOOa7b9xhtvZOrUqRxxxBEccMABdfdfffXVTJgwgfHjx3P44YdTUlLC9OnTeffdd+smDjT0q1/9ioULFzJhwgQmTZrEu+++22j7xIkTcV2XkpISbrvtNgAmTZpEYWEhF198cbtej8suu4znnnuOVatWtRh3S84++2yGDh3K2LFjueCCCzj44IMpKioiFAoxf/58vve971FSUkJpaWndpIfuTNoqEXZ5ACLfB4ar6tfTbKtQ1fyOtjl58mRdvHhxp8RnjDGmZ1uxYgUHHnhgpsPYa61bt45p06bx3nvv4ThdXxuqqKggPz+frVu3csghh/Diiy/WjXHrDtJ9HkVkiao2G1SYsTFpIvIT4EKgDJjewm4REVkMJICbVfXhVtq7FLgUYPjw4Z0brDHGGGM67J577uHaa6/lF7/4xR5J0ABOOeUUduzYQSwW44c//GG3StA6qssqaSLyNJDulblWVR9psN/3gYiq/jhNG0NUda2I7AM8C8xQ1Y/bOrZV0owxxtSySprpTrpFJU1Vm3eKpzcPWAA0S9JUdW3q5ycisgg4CGgzSTPGGGOM6ekyNbtz/wY3TwfeS7NPbxEJp64XA0cA7zbdzxhjjGlLpsdfGwMd/xxmanbnzSLytoi8CZwAXAUgIpNF5E+pfQ4EFovIcmAh/pg0S9KMMcZ0SCQSYevWrZaomYxSVbZu3UokEmn3YzI+u7Mr2Jg0Y4wxteLxOGvWrKlbJNaYTIlEIgwdOpRgMNjo/m43u9MYY4zZE4LBIKNGjcp0GMZ0mJ1g3RhjjDGmG7IkzRhjjDGmG7IkzRhjjDGmG8rKiQMishnYtbPQtl8xsKWLj2E6xt6T7snel+7H3pPuyd6X7mdPvScjVLVf0zuzMknbE0RkcbqZGCZz7D3pnux96X7sPeme7H3pfjL9nlh3pzHGGGNMN2RJmjHGGGNMN2RJ2q67I9MBmGbsPeme7H3pfuw96Z7sfel+Mvqe2Jg0Y4wxxphuyCppxhhjjDHdkCVpHSQiJ4nI+yLykYjMyXQ8BkTkLhHZJCJvZzoW4xORYSKyUETeFZF3ROSqTMdkQEQiIvKaiCxPvS//k+mYjE9EXBF5Q0Qey3Qsxiciq0TkLRFZJiIZOSG4dXd2gIi4wAfA8cAa4HXgC6r6bkYD28uJyNFABXCPqo7PdDwGRGQQMEhVl4pIAbAEOMP+r2SWiAiQp6oVIhIEXgCuUtVXMhzaXk9Evg1MBgpV9ZRMx2P8JA2YrKoZW7vOKmkdcwjwkap+oqox4J/A6RmOaa+nqs8B2zIdh6mnqutVdWnqejmwAhiS2aiM+ipSN4Opi/2lnmEiMhQ4GfhTpmMx3YslaR0zBFjd4PYa7IvHmFaJyEjgIODVDIdiqOtWWwZsAp5SVXtfMu+XwDWAl+E4TGMKPCkiS0Tk0kwEYEmaMabLiEg+8ADwTVXdmel4DKhqUlVLgaHAISJiQwQySEROATap6pJMx2KaOVJVDwY+B/y/1NCaPcqStI5ZCwxrcHto6j5jTBOpMU8PAPNU9cFMx2MaU9UdwELgpAyHsrc7AjgtNf7pn8CxIvK3zIZkAFR1bernJuAh/CFPe5QlaR3zOrC/iIwSkRBwPvBohmMypttJDVD/M7BCVX+R6XiMT0T6iUiv1PUc/ElQ72U0qL2cqn5fVYeq6kj875RnVfWCDIe11xORvNSkJ0QkDzgB2OMrCFiS1gGqmgAuB57AHwh9n6q+k9mojIj8A3gZGCMia0TkkkzHZDgC+BJ+VWBZ6jIz00EZBgELReRN/D86n1JVW/LBmOYGAC+IyHLgNeBxVf3Png7CluAwxhhjjOmGrJJmjDHGGNMNWZJmjDHGGNMNWZJmjDHGGNMNWZJmjDHGGNMNWZJmjDHGGNMNWZJmjMl6IlLR9l7GGNO9WJJmjDHGGNMNWZJmjNlriMg0EVkkIvNF5D0RmZc6OwIiMkVEXhKR5SLymogUiEhERO4WkbdE5A0RmZ7ad7aIPCwiT4nIKhG5XES+ndrnFRHpk9pvXxH5T+oEzc+LyAGZfP7GmJ4lkOkAjDFmDzsIGAesA14EjhCR14B7gfNU9XURKQSqgasAVdUJqQTrSREZnWpnfKqtCPAR8D1VPUhEbgMuBH4J3AFcpqofishU4PfAsXvqiRpjejZL0owxe5vXVHUNgIgsA0YCZcB6VX0dQFV3prYfCfwmdd97IvIpUJukLVTVcqBcRMqAf6XufwuYKCL5wOHA/aliHUC4a5+aMSabWJJmjNnbRBtcT7LrvwcbtuM1uO2l2nSAHapauovtG2P2cjYmzRhj4H1gkIhMAUiNRwsAzwOzUveNBoan9m1Tqhq3UkTOST1eRKSkK4I3xmQnS9KMMXs9VY0B5wG/EZHlwFP4Y81+Dzgi8hb+mLXZqhptuaVmZgGXpNp8Bzi9cyM3xmQzUdVMx2CMMcYYY5qwSpoxxhhjTDdkSZoxxhhjTDdkSZoxxhhjTDdkSZoxxhhjTDdkSZoxxhhjTDdkSZoxxhhjTDdkSZoxxhhjTDdkSZoxxhhjTDf0/wEI1r04NmRBXwAAAABJRU5ErkJggg=="}, "metadata": {"needs_background": "light"}}], "metadata": {}}, {"cell_type": "markdown", "source": ["We notice that this model fits much better than the `LinearDML`, the 95% confidence interval correctly covers the true treatment effect estimate and captures the variation when income is around 1. Overall, the model shows that people with low income are much more sensitive to the price changes than higher income people."], "metadata": {}}, {"cell_type": "markdown", "source": ["# Understand Treatment Effects with EconML <a id=\"interpret\"></a>\n", "EconML includes interpretability tools to better understand treatment effects. Treatment effects can be complex, but oftentimes we are interested in simple rules that can differentiate between users who respond positively, users who remain neutral and users who respond negatively to the proposed changes.\n", "\n", "The EconML `SingleTreeCateInterpreter` provides interperetability by training a single decision tree on the treatment effects outputted by any of the EconML estimators. In the figure below we can see in dark red users respond strongly to the discount and the in white users respond lightly to the discount."], "metadata": {}}, {"cell_type": "code", "execution_count": 14, "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\r\n", "intrp.interpret(est, X_test)\r\n", "plt.figure(figsize=(25, 5))\r\n", "intrp.plot(feature_names=X.columns, fontsize=12)"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1800x360 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABYEAAAEeCAYAAADcsNowAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAADKFklEQVR4nOzddZhUZR/G8e+zHWzBLt3SSwtItyAgIB0GCLZYr2KAgBjYYpGK0o2AgoF0gyAl3d25wfbz/jHrwLo0C7Ms9+e6zvXunPPMnPusL7NnfvOc3zHWWkREREREREREREQkY3JzdQARERERERERERERuXVUBBYRERERERERERHJwFQEFhEREREREREREcnAPFwdQERERETSH2NMzYBMmZ7y8PC411rr6eo8clXWGHP09JkzY4EfrbXnXR1IRERERNIPoxvDiYiIiMjFPD09O2Xy9x/Y/cVuvhXKljPe3l6ujiRXkZSUxP6Dh/huxMjotf9sWBcZGVXHWhvr6lwiIiIikj6oCCwiIiIiTsYYDx8fn1Nzpk8NCC9WzNVx5DolJiZSp2nzyPUbNz5urZ3s6jwiIiIikj6oJ7CIiIiIXKxc1rBQVAC+M7m7u/NI2zaZAjJlesjVWUREREQk/VARWEREREQuFpItNCzpvysr12/AomXLXZFHrlO2sDA8PT2yuTqHiIiIiKQfKgKLiIiISArGmFTrls+eRY0qlV2Q5s6wYPESKtatR46ixXmwXQf2HThw2bErVq2mbrPm5C5RkqoNH2DZX3+l2D7kx+GUrlaDPOGlqP1gsxTbP+z/JaH3FCZX8XDnsmffvpQ7MAZI/d9QRERERO5eKgKLiIiIyF3LWsuJkydv6jVOnjrFo888S89X/8fudWspV7oUXbq9cMmxp8+coX3XJ3jx6afYu2EdLz39NO27PMGZs2cBWLVmDX0//oSRgwey75/1PNquLY889QyJiYnO12j5YBMObt7oXPLnzXtT+UVEREQk41MRWERERESuqlS16sxfvBhwzEbt/NzzPP3K/8hdoiSV6zdgzfr1zrEHDh3ikaee4Z5y91KgTDm69+oNQFJSEp9+/Q0lq1ajUPkKPP3K/zh77hwAe/cfIDhfAUZPnER45arkK1WGH0aP4e9166ja8AHylirtfJ1/jZowkUp165OvVBlaPvrYFWff/teeffvo90V/SlevweiJk27qd/PL779TrHBhHmrSBB8fb9585WX+2bSZbTt2phq7YvVqsoWF8VCTJri7u9OuZQtCs2Tm599+B2DfgYMUK1KYsqVKYYyhfauWnDx1iuMnbq5QLSIiIiJ3NxWBRUREROS6/TZ7Nq2aNmXvhnU0ur8+3Xv3ASAxMZF2XbqSJ3cu1i9ZxOYVy2nZrCkAYyZNZuzkKcwYP461ixYQFRXtfN6/Vq9dy+r58/hxwDe89e67fPbtAKaPGc3yP2cxdeavLF7u6Es8c9YsvhgwkFFDBrNzzWqqVKzIEy+8dMXM0efPM27KFB5s14E6TZtz4uRJfvz2G15+9hnnmLylSl926T9w0CVfd/O27ZQsUdz52N/PjwL58rF527ZLjrfW/ucxzrH1a9ciMTGJVWvWkJiYyOgJEylVogTZsoY5x/8+Zy75S5elcv0GDBs1+orHLCIiIiIC4OHqACIiIiJy56lcoSIN6tYBoF2LFgwa9gMAq9eu48jRY7zX4y08PBynmlUqVgRg0rTpPP9EV2f7gj5vdKdKgwcY+Nmnztft/uIL+Ph4U7dmTfx8/WjdrBlhoaHO11m/cRPVK1fmx9FjeeW5ZylauBAAr3Z7ni8GDGTfgQPkzZ07Vd4XXn+DX37/g4rlytH10YdpfP/9eHt7pxq3b8P6VOuuJioqmtAsmVOsCwwIIDIqKtXYSuXLc/jYUSZP/5nmjRsxafrP7N67l/PnzwMQkCkTzRo9wAOt22KtJSgwkMkjfnT2aW7RpAmdO3Yga2goq9as5bFnniUoMJDWzZtdd24RERERuXtoJrCIiIiIXLdsYaHOn319fYmJjSUhIYGDhw+RJ1cuZwH4YkeOHiVPrlzOx3ly5SYhIYFjJ04412UNveh1fXycBWDHY2+ikgur+w8e5K2+7zpn6eYvUxZrLYePHL1k3s3btuHl5Ump8BKEFyt+yQLwjfL39yMiIjLFuojISDL5+6camzkkhLHfDWXA98MofG9F5ixYQO3q1ciZPTsAo8ZPYMykySz/8w+O79jG0C/7065LVw4fdRxXsSKFyZEtG+7u7txX4V6e6fI403/9Lc2ORUREREQyJs0EFhEREZE0kytHTg4cOkRCQkKqQnD2bNnYf/Cg8/GBQwfx8PAga2goBw8fub795MzBq92ep22Lh65p/OxpU9m6fQdjJk2iWYeO5MyRnfYtW9K6eTMyh4RceN3i4Zd9jf89/xyvdns+1friRQozbvJPzsdR0dHs3ruX4kWKXPJ1qleuzLxfpgOQkJBA2Rq16PbkEwBs2LSJhvXqUqhgQcDRHiJb1qysXLWa5k0ap3otY0yq9hIiIiIiIv+lmcAiIiIikmbuLVuGbFnDeOejj4mKjiYmJpblf60CoHWzpgwc9gN79u0nMiqKdz/5lJYPNrnkrOGrefzhh+k/cJCzl+7Zc+eYNnPmFZ9TtHAh3u3xFhuXL+WNl15iyYoVlK5Wg5HjJzjHHNy88bLLpQrAAA82bMjmbduY/utvxMTE8slXXxNevBhFCt1zyfHr/tlIfHw85yIiePuDfuTKkYN6tWoBUK5MGWbNncueffuw1jJv0SJ27tpN8aJFAUcv5DNnz2KtZfXatQz5cTiNG9x/3b8/EREREbm7aCawiIiIiKQZd3d3xg/7njfe6UvJKtUwxtC6eTMqV6zAI+3acvjYMZq0bUdMbCz1atbgk3f73tB+mj7QkKjoKLp2e5H9Bw8SGBBA7erVeahJk2vK2LBeXRrWq8vpM2c4euz4DWX4V2iWLIwcPJDuvfvw9MuvcG+5sgz75hvn9ld69ASgf78PAPh6yBD+nDcfgHq1ajJ66GDn2A6tWrJ7714ebNeBM2fPkjN7dvp/+IGzoPzTLzPo1v0N4uLiyJkjOy89+zQdW7e6qfwiIiIikvEZXT4mIiIiIv8yxjSoVL78xFlTpwS5OovcmOm//sYrPXrOPnnqlKYIi4iIiAigdhAiIiIiIiIiIiIiGZqKwCIiIiIiIiIiIiIZmIrAIiIiIiIiIiIiIhmYisAiIiIiIiIiIiIiGZiKwCIiIiJyx3v21dd4/9PPXB1DRERERCRdUhFYRERERCQNTZ0xgwYtWpGjaHGatGufavv6jZuo1aQpOYoWp1aTpqzfuMm5zVpLnw8/okCZchQoU44+H36EtfZ2xhcRERGRDEhFYBERERGRNBQSHMyzXR/n5eeeTbUtLi6Ojk8+SdsWD7Fn/Vo6tG5JxyefJC4uDoDhY8cxc9YsFv/+K0v++I3fZ8/hxzFjb/chiIiIiEgGoyKwiIiIiNyULwcNpnilyuQuUZIKdeqyYPESAFavXcv9D7Ukb6nSFK1Qie69ejuLnQDB+Qrw/chRlK9Vh9wlSvL+Z5+ze+9eGrRoRZ7wUnR+7nnn+EXLllPivip8/u0ACpYtT6lq1Zk4ddplM/0+Zw7VGzUmb6nSNGjRin82b75q3rRSu3p1Wjz4IDmyZk21bfHy5SQmJPJc1y54e3vzzOOPg4WFS5cBMG7yFLo9+QS5cuQgZ/bsPP/kE4ydPDlN84mIiIjI3cfD1QFERERE5M61fedOvhsxkrm/TCdHtmzs3X+ApKREANzd3enXuxflSpfi4OEjtOnUme9Hjea5rl2cz5+zcCHzZ/zMwcOHqdWkKStX/83Qr/oTEhxMgxatmPzzL3Rs3QqAo8ePc/LUaTavWMZfa9bQtnMXypUuReF77kmRad0/G+nW/Q3GD/uOcqVLM2HqNDo88SSr5s5h34EDl837X/0HDqL/oEGXPfZ9G9Zf9+9r87bthBcrhjHGuS68WDE2b9tG/dq12LJ9OyWLF3duK1WiOFu2bb/u/YiIiIiIXEwzgUVERETkhrm7uxMbF8fW7duJj48nX57cFMiXD4CypUpRsXw5PDw8yJcnN50f7siSFStSPP+lp58mMCCA4kWKULxIEerWrEH+vHkJCgykfu1arN+4McX4nq/9D29vb6pXrkyDunWYOmNmqkwjxo2jc8cOVChXDnd3dzq2boW3lxd/rVlzxbz/9cpzz7Jvw/rLLjciKiqKwMCAFOsCAwKIjIoCIDIqisCAwFTb1BdYRERERG6GisAiIiIicsMK5s/Ph7178VH/ryhUvgJdur3A4aNHAdixaxftHu9KkQoVyRNeivc++ZRTp06leH7WsFDnz74+PoSFpnwclVwcBQgOCsLfz8/5OE+uXBw5dixVpv0HDjLgu+/JW6q0czl46DBHjh67Yt7bwd/fn4iIyBTrzkVGkMnfH4BM/v5EREY4t0VERpLJ3z/FzGERERERkeulIrCIiIiI3JQ2DzXn9ymT2LB0McYY+nz4EQD/69mLwvcUZPX8eezfuIFer3fnZia0njl7lqjoaOfjA4cOkf0SfXdz5czBq92eTzFr9/DWzbRu3uyKef/r828HkKt4+GWXG1G8SGE2btmSYmbvxi1bKF6kCADFChdO0b94w6bNFCtS+Ib2JSIiIiLyLxWBRUREROSGbd+5kwVLlhIbG4uPtzc+3j64uTlOMSOjIgkICCCTvz/bduzkh9Gjb3p/H37Rn7i4OJauXMkfc+byUJMmqcZ06tCeH8eMZdWaNVhriYqO5o85c4mIjLxi3v96tdvzHNy88bLL5SQmJhITE0tCYiJJSUnExMQSHx8PQPXKlXFzd2Pwj8OJjY1l6PARANSsWgWA9q1aMuC7YRw6coTDR48y4Lvv6di69c3+2kRERETkLqcbw4mIiIjIDYuNi6Pvxx+zbcdOPDw8uO/ee/nyo34AvNezBy+/1YOvBw+hdHgJWjz4IIuWLrvhfWULCyM4KIhilSrj5+vLF/3ep0ihe1KNK1e6NF999CHde7/Dzj278fX2oXLFClS9r9IV86aV8T9N5fnXujsfZy9ajA6tWzHo88/w8vJizNChvPjGm/T96GOKFCrEmKFD8fLyAuDxhzuyZ98+qjZ4AIDH2rfj8Yc7pmk+EREREbn7GN1kQkRERET+ZYxpUKl8+Ymzpk4JcnWWiy1atpynX36FTStuvIh8t5j+62+80qPn7JOnTt3v6iwiIiIikj6oHYSIiIiIiIiIiIhIBqYisIiIiIiIiIiIiEgGpiKwiIiIiKR7NapUVisIEREREZEbpCKwiIiIiIiIiIiISAamIrCIiIiIiIiIiIhIBqYisIiIiIjcVpOmTaf2g83IVTycohUq0fqxziz7668UY8ZMmkxwvgL89MsMAJauXEmu4uHkKh5OzmIlCM5XwPk4V/Fw9h88SJN27clWpGiK9e26dHXFIYqIiIiIpCserg4gIiIiInePb7/7ni8HDeaLD96nXq2aeHl6MnvBAn6d9SdVKlZ0jhs3eQohwcGMn/ITLZs+SNVKlTi4eSMAe/cfoEz1GuzdsA4Pj5Sns5/27ctjHdrf1mMSEREREUnvNBNYRERERG6Ls+fO8eEX/fnsvb40a/QA/n5+eHp60qh+fd7r2cM5bt+BAyxZsYIvP+zHnIULOXrseJpnGTNpMg1btuatd98jb6nSlKlekxWrVjNm0mTCK1elUPkKjJ08xTk+NjaWt9//gJJVqlH43gq80qMn52NiADhz9iztHu/KPeXuJV+pMrR7vCsHDx92PrdJu/a8/9nnNGzZmtwlStLikUc5eepUmh+TiIiIiMjlqAgsIiIiIrfFX3+vISY2lgcbNrziuPFTfqJc6VI0b9yIooUKMXHatFuSZ9XatYQXK8butWto07wZXV94kTXr1vH3gnkM/fILXu/dh8ioKADe+fgTduzezaLfZvL3gvkcOnKET776GoCkpCQ6tmnNhqWL+WfZEnx8vOneu0+KfU2e/jMDPvuE7X+vIi4+nm+GfndLjklERERE5FJUBBYRERGR2+LU6dNkyRySqoXDf43/aSqtmzcHoHXzZoz/6adr3scb7/Qlb6nSzuX9zz6/7Nh8efLwSNs2uLu706Lpgxw4dIjXX3oRb29v6tasiaenJ7v27MVay4ix4/iwdy9CgoMJyJSJV59/nik//wJA5pAQmjduhJ+vr2Nbt24sWb4ixb4ebtOaQgUL4uvjQ4smTdiwadM1H5OIiIiIyM1ST2ARERERuS0yh4Rw8tRpEhISLlsIXv7XKvbu30+rpk0BRxH4vU8/Y/3GTZQOL3HVfXz8Tp9r7gmcNTTU+bOvj49jXVhYinVR0VGcOHmS6PPnqfVg0wtPtpCYmAhA9Pnz9Hj3PWYvWMDZs+cAiIiMJDExEXd3dwCyXfy6vr5ERUVfU0YRERERkbSgIrCIiIiI3BYVy5fD28uLmX/MonmTxpccM27KFKy11GjcONX6aykC3wpZMmfG18eH5X/OImf27Km2fzv0O7bv2sWcadPIljWM9Rs3UbNxE6y1LkgrIiIiIpKa2kGIiIiIyG0RFBjIW/97hdd692bGH7OIPn+e+Ph4/pw3n979PiQmJpapM2fy5Yf9WPTrr87lk77vMHn6dBISElyS283Njcc6tKfHu+9x/MQJAA4dOcKcBQsAiIyKwtfbh6DAQE6fOcPHX33lkpwiIiIiIpejIrCIiIiI3DYvPPUkH7z9Np998y2Fyt1LeJWqfDdiJE0aNGDmrFn4evvQoVVLsmUNcy6PtGtLQkIis5OLrlfSvU8fchUPdy61mjS96nOuRd8336Rg/vzUf6glecJL8VDHR9i+axcAz3btwvnYGO4pV576D7Wgfq1aabJPEREREZG0YnSZmoiIiIj8yxjToFL58hNnTZ0S5OoscmOm//obr/ToOfvkqVP3uzqLiIiIiKQPmgksIiIiIiIiIiIikoGpCCwiIiIiIiIiIiKSgakILCIiIiIiIiIiIpKBqQgsIiIiIiIiIiIikoGpCCwiIiIiIiIiIiKSgakILCIiIiIiIiIiIpKBqQgsIiIiInesAd8Po0iFiuQJL8Xzr71ObGzsVZ/z8VdfE5yvAPMXL3aue/bV1wgrVIRcxcOdS2JionN79PnzvNqzFwXLlidvydI0atP2lhyPiIiIiMitoCKwiIiIiNyR5ixYwJeDBjF97Bg2LF3Mnv37+LD/l1d8zu69e5k+81eyZ82aattLTz/Fwc0bnYu7u7tz28tvvsXps2dYOedPdq9bw4e9e6X14YiIiIiI3DIqAouIiIjIHWnc5J94tF07ihcpQnBQEK+/8AJjJ0++4nNe69Wbd958A08vz2vez7YdO/lt9hy+/LAfoVmy4O7uTtlSpW42voiIiIjIbaMisIiIiIjckTZv30bJ4sWdj0uWKM6x4yc4dfr0JcdPmzkTLy8vGtStc8nt348aTf7SZanVpCnTf/3NuX71urXkyZWLD/t/ScGy5ana4IEU20VERERE0jsPVwcQEREREbkRUVHRBAYEOB//+3NEZBSZQ0JSjI2IjOTdTz5j6uhRl3ytpzt35oO3exIYEMDchYvo0u0FsoWFUbliBQ4dPsKmrVtp2ugBtqxczsq//6bd410pVrgwRQsXunUHKCIiIiKSRjQTWERERETuCBOnTnPetK31Y53x9/cjIjLCuT0iMhKAgEz+qZ77Uf8vadeyBfny5L7ka5ctVZLMISF4eHjQoG4d2jzUnF9+/x0AHx8fPD096f5CN7y8vKheuTLVq1Rm7qJFt+AoRURERETSnorAIiIiInJHaNviIedN2yaPHE7xwkX4Z9Nm5/YNmzaTNSw01SxggAVLlzLkx+EUqVCRIhUqcvDQYTo/140vBw2+5L6MMVjr+LlksWKX3C4iIiIicqdQEVhERERE7kjtW7Vk1MSJbNm2nTNnz/HZN9/SsXXrS479eewYls36g0W//sqiX38lR7ZsfPnhBzzx2KMATJ/5K5FRUSQlJTF34UImTp1Go/vrA1D1vkrkzpmTLwYMJCEhgeV/rWLxsuXUq1nzth2riIiIiMjNUBFYRERERO5I9WvX4sWnn6Zphw6UqlqNPLlz8dYrLzu3V67fgIlTpwGQOSSEbFnDnIubuxvBQUFk8ne0jhj844+UuK8K+UqVoVe/D/nqow+pUaUyAJ6enoz9fih/zptP3pKleenNtxj0xecUKXTP7T5kEREREZEbYuy/17mJiIiIyF3PGNOgUvnyE2dNnRLk6ixyY6b/+huv9Og5++SpU/e7OouIiIiIpA+aCSwiIiIiIiIiIiKSgakILCIiIiIAGGNyAnUTk5J8XZ1Fbk5iYmJ+Y0wFY4y7q7OIiIiIiOt5uDqAiIiIiLiGMSYzUBuoB9QFsgKbjCHRlbkkTXgDI4AcxpgFwFxgDrDZqh+ciIiIyF1HM4FFRERE7hLGmEzGmAeMMZ8aY1YDe4Ank//3YRxF4PfcjFuc61JmDMH5CrBrzx6X7d/d3X2rtTYcCAcmAWWBX4FDxpgxxpguxpj8LgsoIiIiIreVisAiIiIiGZQxxtsYU9MY09cYswg4ArwFRAIvAaHW2kbW2k+ttX9ba9PFDOBJ06ZT+8Fm5CoeTtEKlWj9WGeW/fVXijFjJk0mOF8BfvplBgBLV64kV/FwchUPJ2exEgTnK+B8nKt4OPsPHqRJu/ZkK1I0xfp2Xbpedz5XF3ivh7X2sLV2rLW2q7U2P1ANmAfcD6wwxuw0xnxnjGlvjMnm0rAiIiIicsuoHYSIiIhIBpHc/7UcF9o7VAW24GgF8B6wxFob5bqEV/ftd9/z5aDBfPHB+9SrVRMvT09mL1jAr7P+pErFis5x4yZPISQ4mPFTfqJl0wepWqkSBzdvBGDv/gOUqV6DvRvW4eGR8nT30759eaxD+9t6TOmJtXYXsAv43hhjcMwUrgt0AAYbYw7gaBsxB1horT3jqqwiIiIiknY0E1hERETkDmUcShhjXjDGTAWO4+gDmxsYBOS11la01r5hrZ2V3gvAZ8+d48Mv+vPZe31p1ugB/P388PT0pFH9+rzXs4dz3L4DB1iyYgVfftiPOQsXcvTY8TTPsmvPHhq3bUfekqUpWLY8jz/fDYBGbdoCUP2BxuQqHu6cifz14CEUrVCJYhXvY9SEiWme51awDv9Ya7+21jYHQoEuwGHgBWC/MWaFMeZDY0x9Y4yfSwOLiIiIyA3TTGARERGRO0hyH9d/Z/rWBWJxzNqcBDxnrT3sunQ356+/1xATG8uDDRtecdz4KT9RrnQpmjduRNFChZg4bRovPPVkmmb54PMvqFujBjPGjyMuLp41G9YD8NukiQTnK8Di33+lYP78AMyev4BvvvuO6WPHkC9PHl564600zXK7WGsTgJXJy0fGGG+gCo7/n/UFyhhjVnFhpvBf1tp4V+UVERERkWunmcAiIiIi6ZgxJpsxpkNy39ZdwAqgPo6+rtWstfmT+72OvZMLwACnTp8mS+aQVC0c/mv8T1Np3bw5AK2bN2P8Tz9d8z7eeKcveUuVdi7vf/b5Jcd5eHiw/+BBDh89io+Pd4pWFP81dcZMHm7ThhJFi+Lv58ebr7x0zXnSM2ttrLV2vrW2t7W2GpAD+AQIAgYAJ4wxM40xrxpjyhlj9NlCREREJJ3SiZqIiIhIOmKMCTbGNDfGfGWM+QfYCrQHNgDNgOzW2g7W2u+T+7tmGJlDQjh56jQJCQmXHbP8r1Xs3b+fVk2bAo4i8KYtW1m/cdM17ePjd/qwb8N65/L2a69ecty7b72FtZZ6zR6icv0GV2zxcOTYUXLlyOF8nCdXrmvKcqex1kZYa3+11r5mrS0H3AP8mPy/44FjxpjJxphnjTFFknsOi4iIiEg6oHYQIiIiIi6U3Ge1Go5L7usBxYHlOC637wL8nXyZfoZXsXw5vL28mPnHLJo3aXzJMeOmTMFaS43GjVOtLx1eIs2yZMsaxtcffwTAsr/+4qGHH6HafZWcLSBSjs3KwcMXJmEfOHQozXKkZ9baE8Dk5AVjTG4utCnp4Vhl5uC4MeFca+1+V2UVERERudtpJrCIiIjIbWSM8TTGVDXG9DLGzAeOAe8A8cDrQKi19n5r7UfW2pV3SwEYICgwkLf+9wqv9e7NjD9mEX3+PPHx8fw5bz69+31ITEwsU2fO5MsP+7Ho11+dyyd932Hy9OlXnEF8vabNnOks7AYHBWEwuLk5Tp2zhoWyZ98+59gWTZowdvJktmzbTvT583z85VdpluNOYq09YK0daa3tDOQF6gDLgCbA38aYbcaYwcaYNsaYUFdmFREREbnbqAgsIiIicgsZY9yS+6W+aoyZCZzA0U81CEd/1RzW2mrJfVfnW2tjXRrYxV546kk+ePttPvvmWwqVu5fwKlX5bsRImjRowMxZs/D19qFDq5ZkyxrmXB5p15aEhERmL1hw1dfv3qcPuYqHO5daTZpectzf69ZTv3kLchUPp0PXJ/nond7kz5sXgDdffpln//caeUuVZuqMGdxfpzbPdulCs44dKV+zNjWqVk3LX8kdyTpst9YOtta2BbIBbYBtQCdgpzFmrTHmc2NME2NMgEsDi4iIiGRwxlrr6gwiIiIiGUZyH9TCOFo71ANqAydxtHeYC8xPvow+XTLGNKhUvvzEWVOnBLk6i9yY6b/+xis9es4+eerU/a7OcjnGGE+gAo5/I3WBSsB6Lvw7WWatjXFdQhEREZGMRT2BRURERG6SMSYPF3qh1gMsjmLWz8DL1toDLownku5Ya+NxtIpYBrxvjPEFquL49/MhEG6MWcGFovDqu6k1ioiIiEhaUxFYRERE5Dol9zOtw4VZjCHAPBzFqveBHVaXW4lcM2vteRwF3zkAxpggoCaOf2PfA3mMMQu5UBT+R//GRERERK6disAiIiIiV5Hcr7QmF2b6FgAW4ShGDQI2WGuTXJdQJGOx1p4FfkleMMZkw9FapR7wAhBgjJnHhaLwLhWFRURERC5PRWARERGR/zDG+ABVuFD0LQ2sxFFsehZYlXw5u4jcBtbao8CE5AVjTD4u/Pt8F4g1xszFURSeZ6095KqsIiIiIumRisAiIiJy1zPGeAD3cqGodB+wEUfRtxewNPlydRFJB6y1e4EfgR+Tb8ZYDMe/31bAN8aYIzj+/c4BFlhrT7ksrIiIiEg6oCKwiIiI3HWSi0YluVD0rQnsw1E0+gpYmHw5uoikc8ltIDYnLwOMMe5AWRz/tp8GRhhjtnGhKLzYWhvlorgiIiIiLuHm6gAiIiIit5pxuMcY86QxZjxwBJgKhANjgKLW2tLW2pettb+oAJz+nT5zhoefepqcxUpQsmo1Jk2bftmxazf8Q6M2bclVPJzC91Zg0A8/ptg+6IcfKV2tBjmLlaBS3frs2LXLue3EyZM88cJL5C1ZmnylyvDkiy/fqkOSNGKtTbTWrrbWfmKtfQAIBV4BooCewFFjzEJjzDvGmBrGGC+XBhYRERG5DTQTWERERDIkY0xOoA6O2YB1AS8cMwH/AN5Ivpxc7lCv9eqNl6cn21b/xYZNm2j3eFdKlihO8SJFUow7eeoUrTt1pl+vt2neuBFx8fEcOnzEuX3kuPGMmjCBiT/+QNHChdizbx/BQUHO7Y8+/QzlSpdhw9LF+Pn6smnrttt2jJI2rLVxOG7kuAjoa4zxB6rjeG/oDxQxxizlwkzhtdbaRFflFREREbkVVAQWERGRDMEYkxmoxYWib3ZgPo6izqfAluTLxuUOFxUdzc+//c6yWb+Tyd+fKhUr8kD9ekz4aSrvvPlGirEDvh9G3Zo1aNviIQC8vb0pWrgQAElJSXz81dcM/OxTihUpDECBfPmcz527cCEHDh9mxoTxuLu7A1CmZPhtOEK5lZJbQfyRvPz3vWMUkN0YM58LRWG9d4iIiMgdT+0gRERE5I5kjPE3xjQ0xnxijFkF7MHR/3Mv8CgQZq1taa0dYK3drCJOxrFj12483N0pVLCgc12p4sXZvC31LN2//l5DSHAwDVq0olD5CrTr0pX9Bw8CcPDwYQ4ePsymbdsIr1yV0tVq0O+L/iQlJTmeu2YthQsW5Nn/vUqBMuWo07Q5i5cvvz0HKbeNtfaUtXaqtbabtbYEjn7hU4DywO/AQWPMaGNMF2NMviu+mIiIiEg6pSKwiIiI3BGMMV7J/TvfMcYsBI7i6O8ZhaPfZ6i19gFr7afJ/UB1OXcGFRUdRUBAphTrAgMDiIxKfa+vQ0cOM27KFD56pzf/LF1Cvjx5eOKFlxzbkttCzFu4iCV//M4v48cx5eefGTVhQvL2w8xduIjqVaqwbdVKuj35BB2ffIqTp07d4iMUV7LWHrLWjrHWdgHyAzWABUAD4C9jzE5jzFBjTHtjTFZXZhURERG5VioCi4iISLpkjHE3xtxrjHndGPM7cAJH/05f4AMgm7W2prW2r7V2UXLfT7kL+Pv5ExERmWLduYhIMvn7pxrr4+PDgw0bUr5MGXx8vHnz5ZdYsXo1Z8+dw8fHB4AXn3mK4KBA8uXJTeeOHZk1b77zuXlz5+ax9u3w9PSkVbOm5M6Rk+WrVt3yY5T0wTrstNZ+Z61tD2QDHgI2Ah2BbcaYDcaYL40xzYwxQVd6PRERERFXURFYRERE0gXjUNwY080Y8xNwHEd/zjzAECC/tbaCtfYNa+0fyX095S5UqGABEhIT2bl7t3PdP5s3p7opHEB4sWIYjPOxMRd+LnxPQby8vFKsu/jn8GLFUjx2DEiLI5A7VXJReIO19itrbTMgFHgCx5UJL+JoHbHCGNPPGFPfGOPr0sAiIiIiyVQEFhEREZcxxuRL7rM5GjgI/AaUw9GPM9xaW8Ja+0Jyv05dgy8A+Pv50fSBhvT7oj9R0dEs/2sVv/05m3YtW6Qa+3CbNsz44w/Wb9xEfHw8n3z9DVUqViAoMBA/X19aPNiErwcPJSIykoOHDzNi3Dga1q0LQNMHGnLm7FnGTp5CYmIi02f+yqHDR6hcocLtPmRJp6y1CdbaFdbaD6219XEUhd8EEoF3gePGmHnGmLeNMVWNMZ4uDSwiIiJ3LRWBRURE5LYxxmRN7qM51BizE1gJ3I+j32Z1oIC1tmtyP87DLg0r6drn77/H+ZgYCpevwBMvvsTn779H8SJFWLpyJbmKhzvH1apWld6vd6fd410oVL4Cu/bs4buvv3Ju//Tdvvj7+1G8UmUatGhF6+bNebRdWwBCgoMZN+w7vh36HXlLlqb/oMGM/X4oWTJnvu3HK3cGa22MtXaetbaXtbYqkAP4DAgBBgInjDEzjTH/M8aUNcbo85iIiIjcFkY3yhaRO5kxxg+4B/BydRYBIBbYpt6s8q/k/pi1gLpAPRytHRYCc5KXjVYnI+mKMaZBpfLlJ86aOkW9Te9Q03/9jVd69Jx98tSp+12dRVIyxoQCdbjwnpgZmIfj/XAusF3vifIvY0xeIMzVOQQACxyx1h5ydRARkRvl4eoAIiI3whjj5efl+Z2nu1vbYD/fOE93d31gSgdiExLczp2Pdff39hoaHRf/qrU2ydWZ5PZK7n9ZjQsFjhLAchzFjSeA1dbaBNclFBFxHWvtCWBS8oIxJg+O98u6wNuANcbMJbkobK094Kqs4jrGmFqBgYHDAwMDs4WFhcW5uWnCuKslJSaaI0ePegUHB+8+e/ZsB2vtOldnEhG5XioCi8gdyc/Lc1jxHNla/a9BTZ8gP18fV+eRC05ERPH+jNlPHjh15jzQw9V55NZK7m9ZkQtF34rAehwFjDeA5dbaGNcllBuQGJ8Q7+oMchMSEhKw1uo/4h3AWrsfGAGMMI67EBbG8V7aFPjCGHMSx5doc4D5yUVkycCMMYX9/Pxmjho50v/BBx/Ezc1NNxdMJxISEhg9Zkyx5557boExppD+PYrInUZfKYrIHccY4x+fmNjm1Ya1fIP8dF6c3oQG+PNqw1r+Fp5Rr8OMxxjjltzH8n/GmJnACWAAjn6XnwE5rLVVk/thzlcB+I60Y/vOXd4JCZqwfada+88/8dHnz29wdQ65PtZhm7V2kLW2DZAVaAfsAB4Hdhpj1hhjPjfGNDbGBLg0sNwSXl5ej3Xt2tWzWbNmaAZw+uLh4UHnTp3M/fff7wG0dHUeEZHrpb8qInInKpzZ3y82UBOA0608mYMxBl/Ux+6OZxyKGGOeMcZMAo4BE3HMVvsRuMdaW85a+6q1dqa1NsKlgeWmWWv3GmO2fz1kaIJak955tu/cyfAxY+NjY2PHujqL3BxrbZK1dq219nNrbRMgFHgeOAN0Bw4bY5YYY94zxtQ2xujEKAMICAioVrtWLd3rIh27v359/4CAgCquziEicr3UDkJE7kRel+oB/NyoKTxTpyqlc+dwRSb5D09398SY+AR9iLkDGWNyc6G9Q13A4LgUeQbwv+TLlyUDi4iMbPL5twMWfD9yVNh9Fe41Pt4+xtWZ5MqSkpLYvW9fwtoNG7ySkpK6qV9lxpPc4mNp8vJe8s1xq+J4r/4YKGGM+bcH+xzgb/Vgv/MYY7x9fFLW88NLlmTAt99Su3Zt14SSFHx9ffFwd9fliCJyx1ERWEQyjIGPtnJ1hHRt7b5DDJ6/lOMRkRTJnpVX7q9B1sArX0m64cBh3pryK+0qluHRqhUAsNYyetlq/ty0nZj4eAqGZeHZOlXJlyXkdhyG3ALJd6uvzYWibxYcd6ufC3yA7lZ/17HW7jfG3BMVHV1h6oyZJQFPV2eSq7I4ZurPsdZGujqM3HrW2mhgdvKCMSYYqInjvXwYkNsYs5ALReGNei+/M2385x9XR0i3Nm3axGOdOrFz504A7r33Xr7+6itKlChxxedt376dUqVL07p1a0aPGuVcf/z4cV56+WVmzpyJm5sbjRs3Zszo0bf0GEREbhcVgUVE0jlrLefOx3Az/Y/Pno+h38zZvFi/BpUK5GH0sr/5+Ld5fN6u2WWfk5CYxNAFyymaPWVHh8Xbd/Pnpu180qYJYQGZGLVsNV/8sYCvOj50w/nk9kruI1mDC0XfgsBiHEWCIcB6a22S6xJKepBcLPoreRGRdM5aewb4OXnBGJMNqIPjvf5FIJMxZi7JRWFr7S4XRRUBHOe4J06cICzsxruH5cyZk8mTJpEvXz6SkpIYMGAA7Tt0YP26K18M8Xy3blSsWDHV+patWlGxQgX27d2Ln58f/6gALyIZiHoCi0iG0eWHCazddxCAMcv/5qNf5/L5HwtoM3Akz42awvajx51jj0dE8sGM2XQcOoYOQ0YzaN5SAJKsZfzKNTz+w3geHjqGz/9YQFRsHABHz0Xw4FfD+HPjNjoPG0+7waP4df1mth05TrfRP9Fu0Cjn6/xr1sZtPDNyMu0Gj6LX1N85du7a26UeOXuOMcv+puuPE/lz07ab+t0s27GHvFlCqF64AF4eHnSsXI7dx0+x/9SZyz5n6t8bKJcvF7lDglOsP3oughI5s5E9KBB3NzfqFCvEviu8jrieMcYnuV/ke8aYJcBhHP0kz+DoLxlqrW1irf0iuf+kCsAiInc4a+1Ra+14a+2T1tp7gPuAP3Fc+bHEGLPbGDPMGPOwMUa9tNKx/AUKMHv2bADeeecd2rZrx2OdOhEQGEh4yZKsWrXKOXb//v20bNWKsKxZyRIaSrdu3QBHy5j333+ffPnzkzVbNh7r1ImzZ88CsGfPHoybGz/++CN58uYlJHNmBg8ezF9//UXpMmUIDglxvs6/fvjhB4qXKEFI5sw0fOAB9u7de83Hs2vXLvr06UOBggX54Ycfbup3ExwcTP78+THGYK3F3d2dHTt2XPE548ePJzg4mHp166ZYP2vWLPbv38+nn35KUFAQnp6elCtX7qbyiYikJyoCi0iGtWLXPmoWKcj4Zx6hUsG8DJ6/DIDEpCT6/vwnWQMy8cPjbRnRtT01ixQEYPam7czZtJ0PWzXm+85tiYmPZ/D8lIXdrUeOM7RTa95oVIfvFq5g4l9reb9lIwY82pLF23ez4cBhAJbv3Mukv9bS88H6jH3qYcJzZeOT3+dfMXNMfAJzNm/nrSm/8sr4nzlz/jxvNK5D6wplnGPaDRp12WXSX5ee9bD31GkKhGZ2Pvbx9CR7cAD7Tp6+5Phj5yL4c9M2OlRKfeJbs0hBDp89x8HTZ0lITGLO5u3cmy/XFY9Lbi9jjIcxppIx5i1jzJ/AcRz9Ij2APkBWa20da+171tqlyX0mRUQkA7PW7rHW/mCtfRjICTQB1gCtgU3GmE3GmG+MMS2MMerxlI79/PPPtG/XjjOnT9OsaVO6vfACAImJiTzYtCn58uZlz+7dHDxwgPbt2wMwfPhwho8Ywby5c9m1cyeRkZHO5/1rxYoVbN+2jQnjx/PyK6/wQb9+zP7zTzb+8w8TJ01iwYIFAEyfPp1+H37IT1OmcPzYMWpUr06Hjh2vmDk6OpqRI0dSp25dKlaqxLFjx5gwfjxvvPGGc0xwSMhll48++uiKrx8cEoKPry8vvPgiPd5667Ljzp07R+8+ffji889TbVu+fDlFixalU+fOZAkNpWKlSs5jFhHJCNQOQkQyrBI5s1GxQB4A6hYrxM9rNgKw7ehxTkVF06VGJdzdHN+FhefKDsD8LTt4qFxJsgcFAtCpWgWeH/0TL99f0/m67e8ri5eHB+Xz5cbH04OaRe8hOLlVQ3jO7Ow6fpJSuXPw24YttKlQhjyZgwFoW7Esk/5ax7FzEZfsxfv17EUs3bGHotmz0rhUMSoXzIenh3uqcROeffS6fxcxcQkE+aa8yYi/lxfn4y9d+xuyYDmPVLkXX6/UbUBD/P0okTMbT4+cjJsxhAX480HLxtedSdKOMcYA4Vxo71ATOIDjkt9vgDbJlwmLiIj82+5lU/LyrTHGHSiH42/IM8BIY8wWLvQTXmKtjXJVXkmpevXqNG7sOPd69NFH+fKrrwBYuXIlhw4d4tNPP8XDw8M5FmDM2LH875VXKFjQMfHhw379KFmqFD9eNBO3V69e+Pj40KBBA/z9/enQvj1Zs2YFoEaNGqxZs4ZatWoxeMgQ3nrzTYoXLw5Ajx496Pfhh+zdu5d8+fKlyvvEE0/w09SpVK5cmWefeYbmzZvj7e2datyZ05eenHAtzpw+TVRUFCNGjLhkhouPsWuXLuTOnTvVtgMHDjBr1iy+/+47fvzhB6ZMmULzhx5ix/bthIaG3nA2EZH0QkVgEcmwQi7qoevt6UFcYiKJSUmciIgia0AmZwH4YqeioskamMn5OGtAJhKTLKejz1/ydb083J0F4H8f/1tYPRYRydCFyxm2aKVzu7VwMjL6kkXgvSdP4+HuTsGwLOQPzXzJAvCN8vHyIDouLsW66Lh4fD1TF3lX7NrH+bh45+zo/xq/Yg3bj55geJf2hPj7Mm/LDnr+9CsDHmmFj6f+rNwuxpiCXCj61gUicXxYHwc8Za096sJ4IiJyB7HWJgKrkpdPjDHeONpH1AV6AeWMMau5UBReaa2Nu9zrya2VPXt2589+fn7ExMSQkJDA/v37yZcvn7MAfLFDhw6lKI7my5ePhIQEjh69cLqQLVs258++vr6pHkdGOu45uXfvXl56+WVefe0153ZrLQcPHrxkAfafjRvx8vKibJkylC5d+pIF4LTg7+/PM888Q1jWrGzetMlZwP7X2rVrmT1nDmv+/vuSz/f19SV//vx07doVgPbt2/NBv34sWbKE5s2b35LMIiK3kz6ti8hdJzTAn+MRkSQmJaUqBGf29+PYuQs3VT8eEYW7myHEz5cTkdc3ASY0kz9tK5ahTrFC1zT+83bN2H/qDH9u2kbPn34jSyY/6hYvTK0iBQm8aBZv64EjLvsabSuUoW2lsqnW58scwpzN252PY+LjOXL2HHmzpL7ac93+Q2w/doJHvhsLQHRsHG5uhj0nT9Or6f3sOn6KGoULEBrgD0D9EkX4bsEK9p86TeFsN35jD7my5H6NdS9afHB8GP8TeMtau8d16UREJCOx1sYCC5OXd4wxmYDqOP7+fAUUTu4x/29ReF1yIVlcKE+ePOzbt4+EhIRUheCcOXOm6Nu7b98+PDw8yJYtGwcOHLju/fTs0YOHH374msYvX7aMzZs38+OPP1K3Xj1y587NY48+SocOHciSJYtzXKaA1JMk/tXjrbfo0aPHVfeVlJREdHQ0Bw8eTFUEnj9/Pnv27CFvcqE6MjKSxMRENm3axN+rV1O6dGl+mTEjxXMcF1uJiGQMKgKLyF2nSLYwQvz9GL7kLx6uXB43Y9hx7CQlcmajVtF7mLxqPffmz0OQrw8jlq6iRuGCl5w1fDWNShVj9PLVFAzLQr4sIUTFxrFm30GqFy5w2efkyRxMl+qV6FS1An/vPcDsTdsZuWQVT9a8jwYliwIw+blO152lyj35+GHxSpZs303FAnkYt2IN+UMzO1tVXOyRKuVpU6G08/HQBcvJ7O9H+/sc/YELZwtlyfY91CxyD0F+PszfsoOEpCRyBAdedy65vOR+jLVxfOCuB+QA5uP4sP05sDn5cl4REZFbylobCfyevGCMycyFv1FjgKzGmPlcKApv1d+o269SpUrkyJGDN998k759++Lu7s7q1aupVq0aHdq35+NPPqFRo0aEhYXRo2dP2rVrd8lZw1fzzNNP06t3b8qWLUt4eDhnz55l1qxZtGnT5rLPKV68OJ988gkffvghv//+O8NHjOCtHj34sn9/58zbyIhrv4Hyv/78809CQ0MpXbo0UVFRvP3224SEhDhbVVzsqaeecvZIBvjss8/Ys3cvgwYOBKBFixa81r07I0aM4JFHHmHq1KkcOHCAatWqXXcuEZH0SEVgEbnruLu50bvp/QxdsIzHf5iAAWoVvYcSObNxf3gRTkVF8+bkmcQlJlI+by6eqV3lhvZTtVB+YuLj+eS3eRyLiMTfy4uyeXNesQh8ccaKBfJSsUBeImJiOR0VfUMZ/hXk50uPJvUYPH8Zn/+xgCLZw3i9UR3n9m/nLAGgW71q+Hl54efl5dzm5eGBj6cHAT6OS/daVyjN2fMxvDh2KjEJCeQMCqRHk3pkukWX9t0tjDH+XJhlVQ8oCizF8WH6MWCNZlmJiEh6YK09BfyUvGCMycmFK1VeBzyMMXNJLgpba/e5KuvdxN3dnV9+/pkXX3qJvPnyYYyhY4cOVKtWjS5dunDo0CFq1qpFTEwMDRs25Juvv76h/bRo0YLIyEjad+jA3r17CQoK4v769a9YBL44Y5MmTWjSpAmnTp3iyJEjN5ThX2fOnOGFF1/kwIED+Pr6UqlSJX7/7Td8fBxX0fXr149Fixfz26+/4ufnh5+fn/O5mTJlwsfHh7Awx5VsmTNn5ufp03nu+ed5vls3ihUrxvRp09QPWEQyDKMvaEXkTmOMqZQ7JGjW4MdaB7k6i1xehyGjoyJiYotba/e7Okt6ZIzx4kK/xXpAeRx3aZ+D40PziuTLcUVERO4YyTcr/W/f+nNc+Ps2z1p7zHUJ07ewsLAlI4YPr/rvjd8k/Rk2bBjdu3efcOr06fZXHy0ikn5oJrCIiMhtkHzn9bJcKPpWBbbj+FDcD1icfLmtiIjIHSu5DcTO5GWoMcYNCMfxt+9hYIgxZh8XisILrLXnXJVXRETkbqEisIiIyC2QPBOqGBeKvrWBozg+9A4FOiZfTisiIpJhWWuTgA3Jy5fGGA/gXhx/G18CxhpjNnKhKLzUWnveVXlFREQyKhWBRURE0ogxJh8Xir51gXgcH2p/ArpZaw+5MJ6IiIjLWWsTgBXJSz9jjA9QBcffzveA0saYlVwoCv+V/BwRERG5CSoCi4iI3CBjTFagDheKvoE4PrDOBfoAu3R3dBERkcuz1sYA85IXjDGBQA0cf1sHAQWMMYu4UBTekDy7WERERK6DisAiIrdJ/1kLCc3kx6NVK7g6itwgY0wQUJMLRd+8wEIcH0q/ATbqg6mIiMiNS+4PPDN5wRgThqOlUj3gWSDEGDOPC0XhHfrC1fU6P/44uXPl4v3333d1FBERuQwVgUVE7kLDFq1g+c59nIk+T5ZMfrSpWIZ6xQs7t+86fpKvZi/iwKkz5M4czEv1a1AwLIsLE7uGMcYXxw3c/i36huO4fHUu8CSwWpeoioiI3DrW2uPApOQFY0xeLlyF0xtINMbMJbkobK096Kqskj6cOnWKZ597jtmzZ2OMoWHDhgwaOJDAwEAA9uzZw+NdurBixQry5s3Lt998Q/369V2cWkTk1nNzdQAREbn9fDw86d3sfiY8+yivNKjJ0AXL2XzoKADxiYm898uf1ClaiPFPP0q94oV575c/iU9MdHHqW88Y42GMqWKM6Zn8gfI4jv6EScBbQJi1tr61tp+1doUKwCIiIreXtXaftXaEtfYxIDdwP44vaJsD640xW4wxA40xrYwxd9832MLbb7/N6dOn2b1rFzt37ODo0aO88847zu0dOnakXNmynDxxgg/ef5/Wbdpw/Phx1wUWEblNVAQWkbvC5FXreOz7cbQZOJKnR0xm7T7H/bm2HjnOqxN+pt2gUTz63VgGzVuaotj54FfDmLluE08On0SbgSMZtWw1h8+c47WJv9Bm0Eg++nWuc/z6A4fpNGwcE1eupeOQ0XT5YQLztuy4bKaVu/bxwpiptBs0itcm/sLu46eumjetPFylPHkyB+NmDEWzZyU8Z3Y2HzkGwIYDh0lMsjQvF46nhzvNyoaDhfX7D6dphvTAGONmjCljjHnFGDMDOImj/2AW4HMgp7W2qrX2bWvtvOS+hSIiIpIOWIet1tpB1trWQBjQAdgJdAV2G2P+NsZ8ZoxpZIzJ5NLAt8jHH39Mrty5CQgMpGixYsyZMweAlStXUqVqVYJDQsiRMyfdunUjLi7O+Tzj5sbAgQMpXKQIAYGB9OrVi507d1K1WjUCg4Jo266dc/z8+fPJnScP/fr1IzQsjPwFCjBmzJjLZpoxYwZly5UjOCSEqtWqsX79+qvmTSu79+zhoebNCQwMJCgoiBYPPcTGTZsA2LZtG3///Td9+/bF19eXVq1aUapUKaZMmZKmGURE0iO1gxCRDO/A6TPMWLeZ/u2bkSWTP0fPRZCU5Ggd524MT9asTOFsoZyIjKLPtD/4df1mmpcr6Xz+3/sO8lWH5hyPjOKlcdPYfOgorzWsRYCPD69N/IWFW3dRr4SjlcLpqPOcjYlhRNcObDlyjHemz6JwtlByhwSnyLTz2Am+mr2I3s3up1DWUOZv2cl7v/zJkMdaczQi4rJ5/2vSX+uYvGr9JbcBTHj20av+fmITEth+9DhNShcHYN/JMxQIzYwxxjkmf2hm9p46zb35c1/19dIz4zioQjhaO9TDcTnpaRztHUYAjydfdioiIiJ3mOS+/GuSl8+NMZ5AJRx/998AJhlj1uL4uz8HWG6tjXVR3DSxdetWvh0wgL9WriRnzpzs2bOHxOQJCu7u7vT/4gsqVKjAgQMHaNS4MQMHDuTll192Pv+PWbNYvWoV+/fvp/y997J02TJGjxpFlixZqFK1KuPGjaNTp04AHDlyhBMnTnDwwAGWL19O4yZNqFChAkWLFk2Rac2aNXTp2pVffv6ZChUqMHr0aJo1b87WLVvYs2fPZfP+10cffcRHH3982WM/c/r0Jdc//9xzDBw0iA4dOgAw5aefaNa0KQAbN26kYMGCBAQEOMeXKV2ajRs3XuU3LSJy51MRWEQyPDfjRnxiIvtPnSHI15dsgRdO+gplC3X+nC0wgEalirHh4JEUReBW95bGz9uLfN5e5MsSQvl8ucge5Ogpdm++3Ow8fpJ6XOin+2iVe/H0cKdU7hxULJCHRdt20+G+ciky/f7PVh4oVYyi2bMCUK9EYSb+tY4tR46RJZP/ZfP+V5uKZWhTscxN/X4GzF1CgbDMlM+XC4Dz8fH4eXumGOPn7cX5uPib2o+rGGNycaHoWxdwx/HBbybwmrV2nwvjiYiIyC1irY0HliQv7xlj/IBqOM4HPgWKG2OWcaEo/Le19o7qf+Xu7k5sbCybNm0iLCyM/PnzO7fde++9zp/z58/P0089xYKFC1MUgV/v3p3AwEDCw8MpWbIkDe6/n4IFCwLQ6IEHWLNmjbMIDPDee+/h7e1NrVq1aNKkCRMnTqRXr14pMg0dOpSnn3qK++67D4BOnTrR78MPWb58Obly5bps3v968803efPNN6/7d1K+fHni4uLIEuo4z69Xrx7PPfccAJGRkQQFBaUYHxQUxMFDaXvVnYhIeqQisIhkeDmDA3myZmXGLl/D3lNzKZ8vF0/UuI8smfw5ePos3y9cwfZjJ4hNSCApKYl7soameH6wn6/zZ293j5SPPdw5HX3e+TiTjzc+nhcKqFkDMnEqKjpVpmPnIpm7eTsz1m5yrktISuRUVDSlcue4bN609sOilew9eZoPWzZ2zvz19fQkOjZlwTc6Lg5fL89LvUS6k9z/rzYXir5hwL93Ef8Q2Ka7iIuIiNx9rLXRwJ/JC8aYYKAWjvOFH4FcxpgFXCgKb0rv5wyFChXiy/79eadvXzZu3EjDhg354vPPyZkzJ9u2beN/r77KqlWriI6OJiEhIUVhGCBbtmzOn319fVM9PnLkiPNxSEgI/v4Xzkfz5c3LoUsUT/fu28eIkSP55ttvnevi4uI4dOgQtWrVumzetNK2XTtKlyrF9GnTsNby2muv8cijjzJxwgQyZcrEuXPnUow/d+4cAZkyZKcQEZEUVAQWkbtC7WL3ULvYPUTHxvHt3CUMX/IXrzaszYC5S7gnaxa6N6qNn5cX09f8w5Lte254P5ExscTExzsLwccjIsmbJSTVuLAAf9pWLEu7SmWvK+9/TVy5lomr1l02z+TnOl1225hlf7Nq734+atUEP28v5/q8WYKZumYD1lpnYXjPiVM8WLrEZV/LlZL7+9XgQtG3ELAYx4e374B1yZeHioiIiDhZa88A05MXjDHZcbSKqgu8DPgl3yh2LjDHWrvbNUmvrGPHjnTs2JFz587x9DPP8MabbzJq5Eiefe45ypUty7ixYwkICODLL79k8k30vj19+jRRUVHOQvC+/fspGR6ealye3Lnp2aMHPXv2vK68/9WvXz/6ffjhZfNERkRccv3atWsZ8O23zpzPPPMM1WvUACA8PJxdu3YRERHhbAmxbv16Oia3jhARych0YzgRyfAOnD7Duv2HiE9IxNPDHS8Pd2dx83x8PH5eXvh6erL/1Bl+Xb/lpvc3ZvnfxCcm8s/BI6zcvZ/qhQukGtOwZFF+27CFrUeOYa0lJj6ev3bvIzou7op5/6ttpbJMfq7TZZfLmfjXOuZv3ckHLRoR6OuTYlup3DlwM4af124kPiGRX9Y5ZiuXzpPjJn4raccY422MqWWMedcYsxg4gqPP31ngBSCLtbaxtfZza+0aFYBFRETkWlhrj1hrx1lrn7TWFgSq4PhSuQ6wzBiz2xjzvTGmY3LB2OW2bt3K3LlziY2NxcfHB18fH9zcHB/zIyIiCAwMJFOmTGzZsoVBgwff9P769OlDXFwcixYtYsaMGbRp0ybVmCeffJLBQ4awYsUKrLVERUUxc+ZMIiIirpj3v3r06EFkRMRll8upWLEi33//PefPn+f8+fMMHTqU0qVLA1CkSBHKli1L3759iYmJYerUqaxfv55WrVrd9O9GRCS900xgEcnw4hOSGL7kLw6cOou7m6F4jmx0q1cNgK41KvHtnCVMWb2egmFZqFGkAOv3H77hfYX4+5LJ25tO34/D28OD5+tWJU/m4FTjCmcL44V61Rk8bxmHzpzDy8OdEjmzEZ4r+xXzppWRS1fh4e7GkyMmOde1rVCGtpXK4unuztsP1ufrOYsZsWQVeTIH8/aD9fF0d0/TDNfKGOMOlOfCTN8qwGYcH8r6AkuSL+8UERERSTPJM3+HAcOSby5bHMf5SBtggDHmEI7zkbnA/OSZxbdVbGwsb771Fps3b8bT05OqVasydMgQAD779FOeevppPvn0U8qVK0e7tm2ZO2/eDe8re/bshISEkDNXLvz8/Bg8aBDFihVLNa5ChQp8N3Qo3V54ge3bt+Pr60v16tWpWbPmFfOmlR+GDePFl14id548WGupVKkSI4YPd24fP24cnR9/nJDMmcmbNy+TJ00iLCwsTTOIiKRHJp23OBIRScUYUyl3SNCswY+1Drr66Ntn/YHDfP7HfEZ01eVkAB2GjI6KiIktbq3d/99txphmQGZr7fBLbDNACS4UfWsBB7nwIWuBKz5kiYiIiPwr+Uvqclw4X6kKbOHC+criS31JbYzJjaPVRE9rbex/t4eFhS0ZMXx41caNG9/C9Ndv/vz5PPLooxzYn+q07q4zbNgwunfvPuHU6dPtXZ1FROR6aCawiIjcVsaYl4DXgQcvWleACx+i6gLROD5ETQCesdYeucRLiYiIiLiEtTYRWJW8fGyM8Qbuw3E+0wsoZ4xZzYWi8EprbRxwHMgP/GGMaWGtPe2K/CIicvdREVhERG6L5BkznwENgYeA4saY53F8WPLlwp24e6bXG6+IiIiIXEryrN6FyUufi25cWxf4GihkjFmC41znQ6AjsMQY09hau8c1qUVE5G6iIrCISBopnTuHWkFchjEmAPgDyAtEALOABTg+CPUHNln1JxIREZEMwlobCfyWvGCMyQLUxlEU7gpkBfYDa40xHay1v7ko6jWpXbu2WkGIiNzhVAQWEZHboTGOm7slAHuA8cAKYLn6+4qIiEhGZ609aYyZBuwGNgB1gGqAP/AmycViERGRW8XN1QFERCTjs9ZOsNb6AEWBz3G0f3gdGOzSYCIiIiK3T2NgJFAJRxuspoCftbaWS1OJiMhdQTOBRUSSzd+yk2lr/uHA6TP4enpSMCwLbSuWITxXdueY2Zu28eWfi3i9UR1qFinIPweP8M70PwCwFmITEvDxvPDWOvCRVnwxawFbjxzH3c0415fKnYM+zRrcvoNLJ6y1B4GpyYuIiIjIXcNa+wvwy+3e79ixY/mif3+2bNlCQEAAZcuWpWePHlSvXt05Zvjw4TzepQvjx42jXbt2LFq0iEaNG/+bm+joaPz9/Z3jN23cyGOdOrF8+XI8PC6c+9apU4dffv759h2ciIhcMxWBRUSAqX9vYPKq9Txftxrl8+XCw82d1XsPsGLXvhRF4DmbdxDg483czdupWaQgJXNlZ/JznQA4ei6Crj9OZMIzj+LulvJCi2dqV6FhyaK39ZhERERE5O72xRdf8NHHHzN40CAaNmyIl5cXv//+O9OnT09RBB4xciSZM2dm5KhRtGvXjho1ahAZEQHAnj17KFCwIGdOn05R8AX49ptveOKJJ27rMYmIyI1REVhE7npRsXGMWf43L99fk6qF8jvX31cwL/cVzOt8fOxcBP8cOMybjevy8W/zOB0VTYi/X5pmmb1pG3/8s5Ui2cKYvWk7mXy8ea1hLQ6eOcvoZX8Tn5hIl+qVqFeiMADxCYmMXLaKxdt2E5+YSOV78vNkrfvw9vAgMiaWz/9YwNajx0lMSqJEjmw8X7caoQGOWRxvTp5JeK7srN9/iD0nTlMsRxivPVCHIF+fND0mEREREbn9zp49S+8+ffjxhx9o2bKlc33Tpk1p2rSp8/HevXtZsGABkyZOpF379hw5coTs2bNf6iVv2PDhw/nu+++pVLEiPw4fTubMmRk9ahTbtm2jV+/exMbG8uknn9Cpk2NyRWxsLD179mTipEnExsbS4qGH6N+/P76+vpw+fZpHH3uMFStWkJCQQLVq1Rg8aBC5c+cGoHadOtSoXp258+axfv16qlSpwtgxYwgNDU3TYxIRudOoJ7CI3PW2HD5GXEIiVe7Jd8VxczbvoFC2UKoVLkCezMHM27rzluTZeuQ4+UMzM/bph6ldtCCf/DaP7UdP8F2nNrzasBaD5y/jfFw8AMOX/MXB0+f4+uEWDO3chpNRUYxbsQaAJGupX6IwPzzejh+7tMPLw53B85em2NeCrTt5+f6ajH6qI/GJSUxdveGWHJOIiIiI3F7Lli0jJiaGFi1aXHHcyJEjqVChAq1ataJ48eKMGTPmluRZsWIFpUuX5uSJE3Ts0IH2HTrw119/sWP7dkaPGkW3F14gMjISgDfffJNt27ezds0admzfzsFDh3j33XcBSEpK4vHOndm7Zw/79u7F19eXbi+8kGJfY8eN48cffuDY0aPExcXx2Wef3ZJjEhG5k6gILCJ3vXMxMQT6+qRq4fBfczfvoHbRewCoVfQe5m7ecc37GLpgGe0GjXIuo5atvuzYbIEB3B9eBHc3N2oUKcjxyCjaVyqHp4c75fPlxsPdjcNnz2Gt5fd/tvJkzfsI8PHGz8uLthXLsGjbLgACfX2oVrgAPp4e+Hl50a5SWTYcPJJiX/VLFCZXSBDeHh7UKFyAXSdOXvMxiYiIiEj6dfLkSUJDQ1O1cPivkaNG0bFDBwA6dujAyFGjrnkfL770EsEhIc6lV69elx1boEABHn/8cdzd3WnXrh379++nd+/eeHt706BBA7y8vNixYwfWWoZ+9x39v/iCzJkzExAQQI+33mL8hAkAZMmShVatWuHn50dAQAA9e/RgwYIFKfb1eOfOFClSBF9fX9q2acPadeuu+ZhERDIqtYMQkbteoI8P587HkJiUdNlC8KZDRzl6LoKaRQoCULtoQUYtXcWu4ycpGJblqvt4qta19wQO8fN1/uyVfNIe4n/xOnfOx8Vz9nwMsQkJvDxuunObxZJkLQAx8Ql8v3A5q/ceIDI2DoDzcfEpjjPE70I7C29PD2LiEq4po4iIiIikb1myZOHEiRMkJCRcthC8ZMkSdu/eTfv27QHo2LEjPd9+m7Vr11K2bNmr7uPrr7665p7A2bJlc/7s6+t7yXWRkZEcP36c6Oho7q1QwbnNWktiYiIA0dHRvPLKK/z+xx+cPn0agIiICBITE3F3dwdI0c7Cz8/POcNYRORupiKwiNz1iuXIiqe7O8t27qV64QKXHDNn83YAXhg7LeX6TdspWOvqReBbIdDXB28PdwY82pLQTP6ptk/9ewMHTp/li3bNCPH3Y9fxk7z4n/wiIiIikjFVqVIFb29vpk2bRuvWrS85ZsSIEVhrKVuuXKr111IEvhVCQ0Px9fVl4z//kCtXrlTbP//8c7Zu28aK5cvJnj07a9eupVz58tjkiRAiInJpagchInc9f28vHq5SnsHzlrJs5x5i4hNISExi1Z79/LB4JXEJCSzetptudavxTceHnMvTtauwYOtOEpOSXJLbzRgalCzK9wtXcCb6PAAnIqNYvfcAAOfj4/H28MDf24uImFjGJvcKFhEREZGMLygoiHf79uX5bt2YNm0a0dHRxMfH89tvv/H6668TExPDxEmTGDpkCGvXrHEu33z9NWPHjSMhwTVXiLm5ufHkE0/wyv/+x7FjxwA4ePAgf/zxB+CY9evr60twcDCnTp2ib3KvYBERuTIVgUVEgJblS9G15n1MWLmOh4eOofMP45mxbhNVCuZj2c69eHm4U7d4YUL8/ZzL/eFFSLSW1XsOXPX1B89fRuuBI5zLS+OmpUnux6tVJEdQIK9O+IU2g0by9k+/cfD0WQCalw0nNiGBjkPH8OqEn7k3X+402aeIiIiI3BleffVVvvj8c97/4APCsmYlT968fDtgAA899BDTpk3D19eXxx57jOzZszuXLl26kJCQwO+//37V1+/2wgtkCghwLhe3cLgZH3/8MYXuuYfKVaoQGBRE/fvvZ+vWrQC8/PLLnD9/ntCwMCpXqcIDDRumyT5FRDI6o0smROROY4yplDskaNbgx1oHuTqLXF6HIaOjImJii1tr97s6i4iIiMidICwsbMmI4cOrNm7c2NVR5DKGDRtG9+7dJ5w6fbq9q7OIiFwPzQQWERERERERERERycBUBBYRERERERERERHJwFQEFhEREREREREREcnAVAQWERERERERERERycBUBBYRERERERERERHJwDxcHUBEJCPZc+IUwxatZOexE5yLiWXGS10vO/afg0d4Z/ofKdbFxCfwVuO6VCtcAGsto5et5s9N24mJj6dgWBaerVOVfFlCAOg/ayELtu7Ew/3C93kTnnkUdzd9vyciIiIiaa9///58/MknREdH07pVKwYNGoS3t3eqcXFxcXR8+GFWrVrF3r17mTd3LrVr13Zu//TTTxkxciR79+4lNDSU5559lu7duzu316lbl3/++YfY2FgKFCjAu3370rx589txiCIiGZaKwCIiacjD3Y0ahQvQpHRx3p8x+4pjS+bKzuTnOjkfrz9wmPd+/pN78+cGYPH23fy5aTuftGlCWEAmRi1bzRd/LOCrjg85n9Pq3lI8WrXCLTkWEREREZF//fHHH3z08cfMnTOHnDlz0qJlS/r06cNHH310yfHVq1Xj5Zdeok3btqm2WWsZOWIEpUuXZufOnTRo2JA8efLQvn17AL768ktKlCiBh4cHK1asoP7997Nt61Zy5MhxS49RRCQj03QxEZE0lDskmAYli5I3ebbu9Zi7aTvVCuXHx9MTgKPnIiiRMxvZgwJxd3OjTrFC7Dt1Jo0Ti4iIiIhc3YiRI+napQvh4eGEhITQ6+23GT5ixCXHenl58fLLL1O9enXc3d1TbX/99dcpX748Hh4eFC1alObNmrFkyRLn9tKlS+Ph4ZizZowhPj6e/fv335oDExG5S6gILCKSDsTEx7Nkxx7qlijsXFezSEEOnz3HwdNnSUhMYs7m7dybL1eK581cv5n2g0fx0rhpLNm++3bHFhEREZG7xMaNGylTpozzcZkyZTh69CgnT568qde11rJo8WLCw8NTrH+waVN8fH25r3JlateuTYUKuvpNRORmqB2EiEg6sHTHHgJ9vSmVK7tzXYi/HyVyZuPpkZNxM4awAH8+aNnYub1p2RJ0rVEJf28v/t57kE9+m+d8joiIiIhIWoqMjCQoKMj5+N+fIyIiyJIlyw2/7jvvvENSUhKPP/54ivUzfvmF+Ph4Zs+ezebNm3HTfS9ERG6K3kVFRG7CvC07aD1wBK0HjqDPtD+u/oTLmLN5B3WLFcYY41w3fsUath89wfAu7ZnarTMd7itHz59+JSY+AYBCWUMJ9PXB3c2NigXyUKvoPSzdsedmD0lEREREhDFjxpApIIBMAQE0atyYTJkyce7cOef2f38OCAi44X18++23jBw1ipkzZlzyBnOenp40atSIWX/+yc8//3zD+xERERWBRURuSp1ihZj8XCcmP9eJvg81vKHXOB4RyYYDh6lbvFCK9buOn6JG4QKEBvjj7uZG/RJFiIyJY/+p05d8HWPAYm8og4iIiIjIxR5++GEiIyKIjIjgt19/JTw8nHXr1jm3r1u3jmzZst3wLOAffviBjz7+mDmzZ5M7d+4rjk1ISGDnzp03tB8REXFQEVhEJA1Za4lLSCAhMRGAuIQE4hMSr/iceZt3UDxHVnIEB6ZYXzhbKEu27+F01HmSrGXu5u0kJCU5xy3evpvzcfEkWcvfew8wf8tO7iuY79YcmIiIiIjc1R579FGG/fADmzZt4syZM7z/wQd07tTpsuNjY2OJiYkBIC4ujpiYGKx1TFgYM2YMPXr25M9ZsyhYsGCK523ZsoXffvuN8+fPEx8fz+jRo1m4cCG1atW6dQcnInIXUE9gEZE0dCwikq4/TnQ+bjlgBFkDMvFDl3YA9Jn2B+E5s9G2UlnnmLlbdtCyfKlUr9W6QmnOno/hxbFTiUlIIGdQID2a1CNT8qVyP6/dyNezF2GBbIEBdKtXjdK5c9zS4xMRERGRu9MDDzzA6927U6duXc6fP0+rVq3o27evc3t4yZL0eOstHn74YQCKFivG3r17AWj4wAMA7N61i/z58/N2r16cPHmSipUqOZ//yMMPM3jwYKy1vNO3L5vatcPd3Z3ChQszYfx4ypcvfxuPVkQk4zH/fhMnInKnMMZUyh0SNGvwY62Drj5aXKXDkNFRETGxxa21+12dRUREROROEBYWtmTE8OFVGzdufPXB4hLDhg2je/fuE06dPt3e1VlERK6H2kGIiIiIiIiIiIiIZGAqAouIiIiIiIiIiIhkYCoCi4iIiIiIiIiIiGRgKgKLiIiIiIiIiIiIZGAqAouI3CUe/GoYh86cc3UMEREREZE0Zdzc2LFjh6tjiIikax6uDiAicieYv2Un09b8w4HTZ/D19KRgWBbaVixDeK7szjGzN23jyz8X8XqjOtQsUpB/Dh7hnel/AGAtxCYk4ON54W134COt+GLWArYeOY67m3GuL5U7B32aNbiufA9+NYyhndqQMzjwJo9URERERO4mY8eO5Yv+/dmyZQsBAQGULVuWnj16UL16deeY4cOH83iXLowfN4527dqxaNEiGjVuDIC1lujoaPz9/Z3jN23cyGOdOrF8+XI8PC6c/9apU4dffv75uvIZNze2b9tGoUKFbvJIRUTubioCi4hcxdS/NzB51Xqer1uN8vly4eHmzuq9B1ixa1+KIvCczTsI8PFm7ubt1CxSkJK5sjP5uU4AHD0XQdcfJzLhmUdxd0t5EcYztavQsGTR23pMIiIiIiJffPEFH338MYMHDaJhw4Z4eXnx+++/M3369BRF4BEjR5I5c2ZGjhpFu3btqFGjBpEREQDs2bOHAgULcub06RQFX4Bvv/mGJ5544rYek4iIXJraQYiIXEFUbBxjlv/Ns3WqUrVQfnw8PfFwd+O+gnnpUqOSc9yxcxH8c+Aw3epW4++9BzkdFZ3mWQ6dOcebk2fSdtBIOg4Zzce/zgXgjUkzAHhhzFRaDxzBwm27AJiyej2PfjeWx74fx6yN29I8j4iIiIjcuc6ePUvvPn0Y8O23tGzZEn9/fzw9PWnatCmffvqpc9zevXtZsGABQ4cM4Y8//uDIkSNpnmXHjh3Uql2boOBgQsPCaNe+PQA1a9UCoEzZsmQKCGDChAkAfPrpp+TImZOcuXLxww8/pHkeEZGMSDOBRUSuYMvhY8QlJFLlnnxXHDdn8w4KZQulWuEC5Fmxhnlbd9KyfKk0zTJ62WrK5c1Fv1aNSUhMZPvREwB83OZBHvxqGN883MLZDmL1ngNMXb2B91s2IntQAN/MXpymWURERETkzrZs2TJiYmJo0aLFFceNHDmSChUq0KpVK4oXL86YMWN49dVX0zRLr969aXD//cybO5e4uDhWrVoFwMIFCzBubqxbu9bZDuL333/ns88/Z87s2RQoUIAnn3oqTbOIiGRUmgksInIF52JiCPT1SdXC4b/mbt5B7aL3AFCr6D3M3XztN6YYumAZ7QaNci6jlq2+5Dh3NzeORURyKjIaLw+PFK0o/mvR9l3UL1GE/KGZ8fH0pGPl8tecR0REREQyvpMnTxIaGpqqhcN/jRw1io4dOgDQsUMHRo4adc37ePGllwgOCXEuvXr1uuQ4T09P9u7dy6FDh/Dx8UnRiuK/Jk6cyOOdO1OyZEn8/f15p0+fa84jInI3UxFYROQKAn18OHc+hsSkpMuO2XToKEfPRVCzSEEAahctyN4Tp9h1/OQ17eOpWlWY8OyjzuXRKvdeclyX6hWx1vK/CT/z3KgpV2zxcCoqmtCACzfnyBqQ6ZqyiIiIiMjdIUuWLJw4cYKEhITLjlmyZAm7d++mfXJ7ho4dO7JhwwbWrl17Tfv4+quvOHP6tHN57733Ljnuk48/xlpLpfvuI7xkySu2eDh0+DB58uRxPs6X78pX7ImIiIPaQYiIXEGxHFnxdHdn2c69VC9c4JJj5mzeDsALY6elXL9pOwVrZUmzLCH+frxYvwYAGw8e4e2pv1MyV3ZnC4iLZfbz40RElPPxsYjINMshIiIiIne+KlWq4O3tzbRp02jduvUlx4wYMQJrLWXLlUu1vmzZsmmWJXv27Hz33XcALF68mPr330/NmjWdLSAuliNHDvbv3+98vG/fvjTLISKSkWkmsIjIFfh7e/FwlfIMnreUZTv3EBOfQEJiEqv27OeHxSuJS0hg8bbddKtbjW86PuRcnq5dhQVbd15xBvH1Wrx9t7Owm8nHGwO4Gce2YD9fjpw95xxbvUgBZm/ezr6Tp4mJT2DcijVplkNERERE7nxBQUG827cvz3frxrRp04iOjiY+Pp7ffvuN119/nZiYGCZOmsTQIUNYu2aNc/nm668ZO27cFWcQX69JkyZx4MABAEJCQjDG4Jbcji1btmzs2rXLObZtmzYMHzGCTZs2ER0dTd93302zHCIiGZlmAouIXEXL8qUI8fNlwsp1fPb7Any9PCmUNQvtKpZl2c69eHm4U7d4YTzcL3yvdn94EcYs/5vVew5QqWDeK77+4PnL+G7hcufjXCFBfNXhoVTjth09ztAFy4mOiyPYz5enalUme5BjFnDH+8rRf9ZC4hIS6VavGjWKFKR52XB6/PQbbsbwSJV7mb91Z9r8QkREREQkQ3j11VfJnj0773/wAQ8/8ggBAQHce++99OzRg2nTpuHr68tjjz2Gp6en8zldunShd58+/P777zz44INXfP1uL7zAy6+84nxctGhRViff9O1if/31Fy+/8gpnz54lW7ZsfPXllxQs6Gi19k6fPnTq3Jnz588zdMgQ2rZty8svvUTdevVwc3Pj/ffeY8yYMWn0GxERybiMtdbVGURErosxpmqukKDZQx5r7evqLHJ57QePio2MjStqrd3r6iwiIiIid4LQ0NA1I0eMKNu4cWNXR5HLGDZsGK++9tqMM2fONHV1FhGR66F2ECJyxzDGeBhjOgOTsNbzauPF5TyBWcaYVsYY4+owIiIiIumVMeYeY8yohISEcFdnkatLSEhoYIwZYIzJ6eosIiLXSkVgEUn3jDFuxpi2wD/A48A7xpioqzxNXMxgzgMfAD2BVcaYRioGi4iIiFxgjMltjBkCrAC2e3h4/O3qTHJ1nh4evwLngX+MMZ8ZY0JdnUlE5GpUBBaRdMs4PAj8DXQHXgRqA+tcmUuukaPcOw+oAHwIfA4sNMbUcmEqEREREZczxmQ1xvTHcV57GihqrX3XGJPo4mhyDYwxsdba14CSgB+w1RjT1xgT5OJoIiKXpSKwiKRLxpi6wFIcxcN3gErW2llWjczvONbaJGvtZKAUMBT4wRgzyxhT0cXRRERERG4rY0yIMeYDYDPgDoRba9+01p50cTS5AdbaQ9ba53BMesgHbDfGvGGM8XdxNBGRVFQEFpF0xRhTxRgzBxgCfAOUtdZOU/H3zmetTbTWjgKKAVOAqcaYqcaYUi6OJiIiInJLGWMyGWN6AtuAbEB5a+2L1tojLo4macBau9ta2xmoBdyLoxj8gjHG27XJREQuUBFYRNIFY0xZY8wvwARgHFDCWjvWWntHXRIXERPL+zNm02rACB7/YTzzt+y85DhrLT8uXkmHIaPpMGQ0Py5eyb917n8OHqH1wBEplge/GsaS7bsBiE9I5LsFy3ns+3G0GzyKgXOXkJCYdNuO8WZZa+OttUOAwsAiYLYxZqwxprCLo4mIiIikKWOMjzHmFWAHEA5Us9Y+Ya3d6+Jo1+XUqVO0aNkS/0yZyJc/P2PHjr3kOGstb7zxBllCQ8kSGsobb7zBxXM5nnrqKYoWK4abuzvDhw9P8dzx48dTtFgxgoKDyZotG506d+bcuXO38rDSnLV2s7W2LdAEaAhsM8Y8YYzxcHE0EREVgUXEtYwxxYwxE4DfgD+BItba76218S6OdkMGzVuKp5sbo5/syGsNazNw3hL2njydatzv/2xl+c59fNOxBd883IKVu/fz24YtAJTMlZ3Jz3VyLr2bNcDX05N78+cGYNKqdWw/doIBj7Rk6GOt2Xn8JBNWrr2dh5kmrLXnrbVfAIWATcAyY8z3xpi8Lo4mIiIiclOMMZ7GmKeB7Thmh95vre1ord3m4mg35Plu3fDy8uLokSOMGT2aZ597jo0bN6YaN3ToUKZNn866tWtZv24dv8yYwZAhQ5zby5Qpw8ABAyhfvnyq51arVo0lixdz9swZdu3cSUJCAm+//fYtPa5bxVq7xlr7INAe6AhsNsZ0MMaoBiMiLqM3IBFxCWNMfmPMjzhmgq4BCllrv7bWxrg42g2LiY9n6Y49PFLlXny9PAnPlZ37CuZl3uYdqcbO2bydFuVLEhrgT2gmf1qUK8mczdsv+bpzN22nWqH8+Hh6ArBy936alQ0nwMebID9fmpYJ589Nd+TnCQCstRHW2vdxzAw+CqwxxnxtjMnu4mgiIiIi18UY426MeRTYArQCWllrH7LWbnBxtBsWFRXFlClTeO/dd8mUKRPVq1enWbNmjBo1KtXYESNH8ur//kfu3LnJlSsXr/7vfwwfMcK5/fnnn6devXr4+Pikem6ePHkIDQ11PnZ3d2fHzktfVXensNYus9bWBZ4BXgLWGmOaG2OMi6OJyF1IRWARua2MMTmNMQOA1cB+oLC19iNrbZSLo920g6fP4u5myBVy4abABUKzsPdU6pnA+06epkBY5gvjwjKz7+SZVONi4uNZsmMPdUuk7JRw8WV1FsuJyCiiYuPS4Chcx1p72lrbEygBJAEbjTEfGmMyX+WpIiIiIi5lHFoB64GngS7W2gbW2pUujnbTtm3bhoeHB0WKFHGuK1O6NBs3bUo1duPGjZQpU+bCuDJlLjlj+HIWL15MUHAwAYGBTJkyhZdfeunmwqcT1to5QBWgJ/AusMIY00DFYBG5nVQEFpHbwhgTaoz5FPgHOA8Us9b2ttaecW2ytHM+PgFfL68U6/y8PTkfl7qzRUx8An4XjfXz8uJ8fHyK4i7A0h17CPT1plSuC5Ni782Xi5/XbuRs9HlOR0Xzy1rHCXhsQkJaHo7LWGuPWmtfBsoCWXD0UuttjAlwaTARERGR/0gu/jYCVgE9gNeAGtbaBa5NlnYiIyMJDAxMsS4oKIiIiIhLjg0KCkoxLjIyMtU57uVUr16ds2fOcGD/frq/9hr58+e/qezpiXX4BSgHfI7jJtjzjDHVXJtMRO4WKgKLyC1ljAkyxvQFtgL+QClr7WvW2uMujpbmfD09OB+XcjZudFw8vl6eqcb6eHoQfVFx+HxcPL6envx3MsCczTuoW6xwivVtK5WlYFgWXhg7jdcmzqDyPfnwcHMj2M83jY/Itay1+621TwGVgSLADmPMa8aYjHWgIiIickcyxtTC0drsc6AfUMFa+5u91ornHSJTpkypbtB27tw5AgJSfz//37Hnzp0jU6ZMqc5xryZXrlw88MADtO/Q4cZCp2PW2iRr7QQcNwocAYwxxvxqjEndKFlEJA2pCCwit4Qxxt8Y8waOm2Hkw3FS/Jy19qCLo90yuUKCSEyyHDx91rlu9/FT5Msckmps3iwh7D5x0vl414lT5M0SnGLM8YhINhw4TN3ihVKs9/bw4Nk6VRn5RAeGPd6WQB9v7smaBbcMejWZtXaHtfYRoB5QFUcx+FljjNdVnioiIiKS5owxFY0xs4AfgCE4JjlMyWjF338VKVKEhIQEtm+/cP+KdevXE16iRKqx4eHhrFu37sK4desIDw+/of0mJCSw8w7vCXwl1toEa+2PQFFgJvCLMWayMSb1L1ZEJA2oCCwiacoY422MeQFH8fdeoJa1trO1dreLo91yPp6eVCmUjzHL/yYmPp5Nh46yYtde6vyniAtQt1ghpv39DyciozgZGcW0vzdQr3jKvr/zNu+geI6s5AhOefndv8+x1rLl8DHGr1zLw5Uz/sQBa+0/1tqWQPPkZYsxppMxxsPF0UREROQuYIwpZYyZBkwFpuBobzbKWpvo2mS3lr+/Py1btqR3nz5ERUWxZMkSpk+fzqOPPppq7GOPPsoX/ftz8OBBDh06xOdffEHnTp2c2+Pi4oiJicFaS3x8PDExMSQlJQEwZswY9u3bB8DevXvp+fbb1KtX7/YcpAtZa2OttQNw3CR5BTDfGDPSGHOPi6OJSAajIrCIpAljjIcxpiuwDWgINLHWtrXWbnZxtNvquTrViEtI4OGhY/n0t3k8V6ca+bKE8M/BI7QeeOHOyI1KFaNSwbx0G/0Tz4/+iQoF8tCoVLEUrzV3y45UhWGAI2fP0X3iDFoPHEH/WQvpVK0C5fPlvuXHll5Ya1dZax8AOgFdgQ3GmLbGGP1NExERkTRnjClsjBkL/AkswHFj4yHW2tQ3fsigBg4YwPnz58maLRsdOnZk0MCBhIeHs2jRIjJd1Bbi6aefpumDD1KqdGlKlipFk8aNefrpp53bGzRsiK+fH0uXLuWpp5/G18+PhQsXArBp0yaqVquGf6ZMVKtenaJFivDd0KG3/VhdxVobba39FCgE7MRx87jBxpi750RfRG4pk0GvWBGR2yS58NYO6AscAN621i69xfuslDskaNbgx1oHXX20uEqHIaOjImJii1tr99+qfSTfUbkB8D7gAfQCZmbUyzFFRETk9jHG5AV647gC6UvgK2tt5K3cZ1hY2JIRw4dXbdy48a3cjdyEYcOG0b179wmnTp9ufyv3Y4zJArwOPIGjd/BH1tpjt3KfIpKxadaUiNyQ5DshNwfWAi8Bz1pr697qArDIxZLvsvwHUAl4F/gIWGKMqePaZCIiInKnMsZkN8Z8DawBjgJFrLUf3OoCsMjFrLUnrbVvACUBT2CzMeZ9Y0ywa5OJyJ1KRWARuS7Jxd/7geU4im49gSrW2jmuTSbpjbX2tt2pLrkYPBUoAwwAvjPGzDbGVL5dGUREROTOZozJbIz5CNgIJALFrbU9rbWnb1cGay26oCl9s9Zi4bb9R7LWHrbWvgCUB3IA240xPYwxmW5XBhHJGFQEFpFrZoypBswDvgW+AMpZa39xwaX30efj4vX+lY4lWUtcQqIHEHU792utTbTWjgGKAxOAicaYX4wxZW5nDhEREblzGGMCjDG9cdzbIgQoa619xRWX3ltrI8+dO3e7dyvX4dy5c8THx5+93fu11u611nYFqgGlgB3GmJeNMT63O4uI3JlURBGRqzLGlDfG/AqMwdGPKtxaO8Fam+SiSFsiYmM5fEYnyOnVpkNH8XB3O2KtPeWK/Vtr46213wFFgNnA78aY8caYoq7IIyIiIumPMcbXGPMasAPHOUNla+3Tt/J+Bldz5syZ36ZPn37eVfuXK7PWMmny5IioqCiXXQVprd1mre2A474YdXDMDH7KGOPpqkwicmdQEVhELssYU8IYMxn4BZgJFLXW/mitTXBlLmttgsF83Gva71Fr9x0kLsGlcSSZtZaY+HhW7NrHhzPnRMclJPZJB5lirLVf4bjL8jpgsTHmB2NM/v+ONcaE3u58IiIicvsZY7yMMc/hKP5WAepaax+x1u5wcTSSkpLG/DJjRmSv3r2T9u3bR1KSq+ZcyMUSExPZsWMH3V54If6fDRtO4Phs5FLW2vXW2uZAa6ANjp7Bjxhj3C8eZ4wJU4FYRACM+g2JyH8ZYwoC7wAPAJ8CA6y10S4N9R/GGONuzJM+Xp7/Ox8XXyjJWverP0tuNTdjEvy8PDdGxcb1S7J2oqvz/FfyjTReBZ4DxgEfWGsPG2P8gL1AHWvtPy6MKCIiIreIMcYDeAToA2wB3rbWrnZtqtSMMfkDAwM/jo+Pb3T+/PkAV+cRAKyfn99ZNze3qZGRkW9Ya4+7OtB/GWNqAx8AwUAvYKq11hpjPgGCrLVPuy6diKQHKgKLiJMxJjfwNo5vk78B+ltr1XNBMhxjTBjwJvA4MAz4GGiKo0Bc0VqryzBFRETuQMmzIHNYaw9ctM4Nx/ntu8AxoKe1dpGLIorcMsYYAzQC3geScHy2Wwr8DbxprZ3swngi4mIqAmdgySc7mQEvV2eRWy4BOGmtTbyRJxtjsuIoiHUCvgc+sdaeTMN8IumSMSYXjpPjtji++AgHjltrn7vMeD8csytELicRx/ux+tSIpCGd10qyq57zGmP6ATmttZ2TC2JNgPeSn/s2MMsFNzUWua2S3zNb4vji4yQwCkdhuKK1du8lxrvjeI9V2wi5kihr7W2/KaKkHRWBMyBjjJuPh8d7Fp4B6+/h5n5DhUG5cyTaJLekJJvo4e42Pjou/oVLzWI0xgQCZS6e9ZB8afxrwLPAWKCftfbw7cotkl4kt0DpAzTG0S//OWvthIu2V87k7fVtTHxCaW8Pj3iMq5JKepeUZE1CUhKe7m7TouPin9HVFCI3xxjj5u/v/x7wjLXW38vLS+e1d7GEhAS3hISERC8vr/Hnzp1Ldc5rjKkHjATKASVxFL0CcFwaP13FX7nbJBd3H8bR6i8eOA9U+PfLamOMT0BAwFfx8fEd3d3dPTw9PdWEWi7JWsv58+c9fX19D0RHR78dFxc31tWZ5PqpCJwB+Xl5fZkrJPDJV+rX8MubOdjVceQ2OXYuku8Wr4xZf+DwoqjYuAYXb0ueBTEO2G+t7W6MyQS8CLwC/Ay8e6lvhEUyOmNMWWABjsvl/p25GQxYwDu5j1pRbw+PVd3qVc9Uo0hBvDw8XBNW7hgnI6P4cfHK2OU796yPjou/T0UHkRsXGBj4ZZEiRZ4cMXy4X4kSJVwdR9KBvXv38sr//hczb968RWfOnHGe8ya3elqDo8XTQ0BeHF/wTrjRq+VE7mTGmN+B2jjOcRMAD8Af+Mha+xZAcHDwLzVq1Kj3zTff+ObPn99VUeUOkZSUxIIFC2jTps35kydPPmytnerqTHJ9VATOYIwx3p7u7qe+e7SVX5ZMfq6OI7dZQmISHb4fGxMTn1Ds4qKuMaYL8DJQE+iMo/XDPKCPtXabK7KKpBfGmAAcJ8UXL8Zauw/A28Pj06Zlw1/qWrOyLo+Ta5ZkLQ8PGRV19nxMFWvtBlfnEbkTGWO8fXx8Tu3Yvt0vZ86cro4j6Uh8fDyZs2SJiYqKKmat3Zs84WE+kAtHy5B3gRFAgr6Ik7tV8ixgTy6c33oC3sBha22iMSaHn5/f7pMnT3r7+Pi4MqrcYX766SeeeOKJv06dOlXJ1Vnk+ri5OoCkuaLBvj4JKgDfnTzc3SiePWsc4HwzNsYUwzEjYgqwAagDNLDWdlABWASstRHW2tPW2uPW2sPW2v3/FoABvD096pbLl1sFYLkubsZQNm8uA1R2dRaRO1jRrFmzJqgALP/l6elJtapVLz7nfRLHZIc8QBZgABADrHdNQhHXs9YmWmtjrLWR1tozyee6By6aGV+xYsWKMSoAy/WqV68ekZGRZVydQ66fisAZj4+3p0eG/7a7/+xFjFr+t6tjpEu+Xp5ugA+AMcYX+ANwB+4H/gf0ANTMXeTa+fh43t0tIL74Yz4jlvzl6hh3HD8vL+f7sYjcEB8/P78Mf157sccff5y3e/VydYw7QqaAgIvfY78DQoGsyUsgjhnBpV2TTuSO4JMpU6a7+k4XnTt35u2333Z1jDuOv78/CQkJmiRzB1IRWOQmLdq+m+6TZ9Jq8Cje+um3VNt3HT/JyxN+odXgUbw84Rd2HT95S/P85694OyAbEAfkwHFzjInAwFsaQkTkFlm4dSevjp9Oi2+G8cakX1Jt33nsBC+O+YkW3wzjxTE/sfPYCRekdLirP1WJSIZx6tQpsmbLRo2aNVOs//777ylcpAgBgYE0atSIQ4cO3dZcjg4QDtbhpLX2rLU2ylobmzwL8q76EkFE7lyvv/46efLkITAwkHz58tGvX78U25966imKFi2Km5sbw4cPd03IZBe//8qdRUVgkZsU4ONNszIlaF2+VKpt8YmJvP/rXGoXLcj4JztSt9g9vP/rXOITb8+9Kay1w621Ptba7Nbae6y1Ra214dbaJrclgIhIGgvw8aZ5uZK0qVg21bb4xETe/XkWdYoXYuKznalXogjv/jzrtr3niohkRG+8+SbFixdPsW7+/Pn0fPttpk2dyskTJ8hfoAAdH37YRQlFRO58Xbt2ZcuWLZw7d46lS5cyZswYfvrpJ+f2MmXKMHDgQMqXL+/ClHKnUxFYrtvk1Rvo9ONE2g4ZzTOjf2Ldfse3/tuOHue1STNpP3QMj/0wgcELlqf44N302+HM3LCFp0ZNoe2Q0Yxe/jeHz56j++SZtB0yho9+n+8cv+HAYTr/OJGJq9bT8ftxdB0xiflbd14208rd+3lx/HTaDx1D98kz2X3i1FXzppWyeXJSo3ABMvun7sO84eAREpMszcuUwNPdnWZlSmAtrD9wOE0ziEjGNemvtTw6dDStvv2RJ4dPYO2+gwBsPXKM/42fRpuBw3l4yCgGzl2c4j23cf+hzFi3kSd+HE+rb39k5NK/OHzmHK+On06rAT/Sb8Zs5/j1+w/x6HdjmLByDe0HjaDzsLHM27z9splW7NpLt9FTaDNwOK+On87ui65wuFzetFIuX25qFr2HzP7+qbZtOHCYpKQkHipXCk8Pd5qXKwnYNH/fF5GM6eNPPiF3njwEBgVRrHhx5syZA8DKlSupWq0aIZkzkzNXLrq98AJxcXHO57m5uzNw0CCKFC1KYFAQvXr3ZufOnVSrXp2g4GDatWvnHD9//nzy5M1Lvw8/JCxrVgoULMiYMWMum2nGjBmUK1+ekMyZqVa9OuvXX2hxe7m8aWnp0qVs3LiRzp06pcw1cyatW7cmPDwcLy8ver39NgsXLmTnzsufr4uIXOzjjz8mV65cBAQEULRo0RTvuVWqVCE4OJgcOXLQrVu3FO+5xhgGDhxI4cKFCQgIoFevXuzcuZOqVasSGBhI27ZtU7zn5s6dm379+hEaGkr+/Pmv+p5btmxZgoODqVq1asr33MvkTStFixbF/6LzWzc3N3bs2OF8/Pzzz1OvXj3Uw1luxt3d5FCu24HTZ5m5YTNftHmQLJn8OHougqTkq6zcjOGJGhUpnDWUE5FRvPPLbH7dsIXmZcOdz1+z7yBftmvK8YgoXp7wC5uPHOfV+2sS4ONN98kzWbhtN/WKFwLgdPR5zp2PYcTjbdly5Dh9f5lNoayh5A4JSpFp5/GTfD13Cb2a1KNQ1izM37qL92fOYfAjLTl6LvKyef9r0ur1TFl9+Ru4j3/q+mc37Dt1hvxZQlJcLpE/Swj7Tp3h3ny5r/v1ROTucuDUGX5Zu5EvO7YgSyZ/jp5N+Z77VK0q/L+9+w6L4mr7OP5dYIGlNwURRRRFxRJ7711jbBEVu0k0scUk75OiscSYPPqYaGIviQoItmg0mtiiWGJvWEBFRbCj9LosC/v+gVlBukFRc3+ua66L3Tk7c8+Y/BjOnjlT1bEMUYnJTNu6k9/Ph9A7210JZ8PvsMC7L48Sk5gQsIXL9yL5v67tsFKZ8vH6rRy8coOOntUAiE1OIT5Vjd97Q7jyIJJpv+6iqmMZXOxsctR042EUP+w5yPReXanq6EDg5et89dtuVg4fQGRCYr71Pm3jySA2nQ7K99g3jR1R7PMVER1DpTL2OTPXwZ5b0bE0rFSh2NsTQvx7XL16lcWLF3PyxAmcnZ0JDw8n4/EXZYaGhsz7/nsaNmzInTt36N6jB0uWLmXShx/qP79n925OnzrF7du3adCwIceOHcPP1xd7e3uat2jBunXrGP64I/XBgwdERUVx5/Ztjh8/To8336Rhw4Z4eHjkqOncuXO88+67/LZtGw0bNmTt2rX06t2bK5cvEx4enm+9T5s9Zw5z5szJ99hjY2LyfD8jI4MJEyeyYvlyLl7MfY2cfaaFv3++dOkSVapUyXdfQggBWZm7aNEiTp06lWfmzp8/X5+53bp1Y8mSJUyaNEn/+d27d3PmzBlu375N/fr1OXr0KGvXrsXe3p5mzZrlmbl3797l+PHjdO/ePd/MHTVqFNu3b9dn7ltvvcXVq1cJDw/Pt96nzZ49m9mzZ+d77HFxcfmumz17NrNmzSI5ORk3Nze8vb2LeEaFKBoZCSyKxUChID0jk9uxcWgzMnG0sqSctRUA7mUdqO5UFkMDAxytLOnqWY1L9yJzfL5v/VqYGRvjam+Lq70t9So442RtibmJMQ1cXQiLyjlf7pCm9VAaGlK7vBMNK7nw1/XwXDXtDg6lq2c1PJzKYGhgQIca7igNDbny4FGB9T6tf4M6rB89ON/lWajTtZib5Jwv3dzEmFRN+jNtTwjx72JgoCA9I4Nb0bFZGWZtSTmbrAyr6liG6uUcszLX2pJutWtw8am7DPo1rIuZiTGuDnZUsrejvqsL5WysMDcxpmGlCtx4lHO+3GHNG6I0MqS2izON3CpyKDQsV007L16mW50aVC+XlfcdPas9ztzIAut9mlfjN9g0dkS+y7NI1WgxN86duSnZRo8IIUReDA0NSUtLIyQkhPT0dCpVqqTvzGzQoAFNmzbFyMiISpUqMXr0aA4dPJjj8//5z3+wsrLC09OTWrVq0alTJypXroy1tTVdu3blXFBQjvZfz5yJiYkJbdq0oUf37mzctClXTStWrmT06NE0adIEQ0NDhg8fjomJCcePHy+w3qd9/tlnxMbE5LvkZ8HChTRu3JgGDRrkWte1Sxc2bdrEhQsXSE1NZebXX6NQKEhJSSnsVAshRLEyd8yYMRx8KnM//fTTHJnbuXNnfeZ269aNc+fO5Wj/9ddfP8ncHj3YuHFjrppWrFjBmDFj/nnmfv45cXFx+S4F+fzzz0lMTOTs2bMMHToUa2vrAtsLUVwyElgUi7ONFe+2bEzAySBuxcRRv6Iz77RojL2FGXdj4/npr1NcfxRFWnoGGbpM3MvY5/i8jUql/9nYyBAbM9Mcr2NTnvyhbmFijKnyyR/zZS3NiUnOfWH5MDGJfVeus+PCZf176ZmZxCSnULu8U771vgimSiNSnurwTdFoUBnLgzSFEIVztrFmdNvm+B8/w63oP6nv6sJ7bZphb2HOndg4Vh48zvXIR6i1WjIzM3EvWybH523Nn87c7K+NiM32x7qFqUmOzHW0siAmOTlXTQ8TktgXEsr2oGD9e9qMDKKTUqjt4pxvvS+CyjiPzE3TYGZs/EL2L4R4dbm7uzN/3jy+mjmT4OBgunTuzPfff4+zszOhoaF88sknnD5zhpSUFLRaba6OUUdHR/3PKpUKx7Jlc7x+8OCB/rWtrW2OW34rurrm+VC1WxER+Pr6smjRIv17Go2Ge/fu0aZNm3zrLQn37t1j4cKFnD51Ks/1HTt2ZMaMGbzdvz8JCQl8+OGHWFpa4uIid7oJIQrn7u7ODz/8wIwZM7IyrEsX5s2bp8/cjz/+mNOnTxc9c596XVDmuuaTuREREfj4+LBw4UL9e9kzN796S5pCoaBevXrs3r2b6dOnM2/evBLfh/j3kk5gUWxtPSrT1qMyKRoNiwOPsebYaT7p1JolB49T2cGO/3Rpg5mxkm1BwRy5EfHM+0lK06BOT9d3SjxKTMbV3jZXOwcLc7wa1mFAw7rFqvdpG09fYNOZC3lsIcumMUOKfQwV7WzYei4YnU6nvz05PDqWHrWrF3tbQoh/p3bV3WlX3Z2UNA0L9x1m1eET/Kdbexbv+4sqZR34rHt7zIyN2Xr2In9dyz1yt6iS1Gk5MvdhYhKu9na52pWxNGdA43oMbJL3Qynyq/dpG06eY8PJc3lsIcuW8aOKfQyu9nZsOXMxR+bejIrhzWzTEgkhRH68vb3x9vYmISGB999/n88//xxfX1/GjhvHG2+8QUBAAJaWlvzw449s3rz5mfcTGxtLcnKyvlPi9q1beNaqlaudS4UKTJ48mSmTJxer3qd9+9//8t///jffehITEnK9d/LkSe7fv6+vKzU1ldTUVMo5O3Pn9m0MDQ0ZN3Ys48aOBSA0NJRvvvmGWnkchxBC5CV7ho0ZM4bPPvsMPz8/PvjgA+rVq8e6deuyMveHH/jll1+eeT9PZ+6tW7fyzKoKFSowZcoUpkyZUqx6n/btt9/y7bff5ltPUlJSkerWarUyz7oocdIJLIrlTmw80ckp1CxXFqWhIcZGhmRmZs0BlqpJx8xYiUppxO3YOHZeuoqV6p9NWu5/IohhzeoTGhnFqfA7eDd5I1ebLjWr8e3O/bzh4kw1RwfStFou3n2Ap7MTMckp+db7NK+GdfBqWKfYNWZkZpKRmUmmLhMdoNFqMVAYYGRoQO3yThgYKNh+4TLdanmwOzgUgDou5Yq9HyHEv8+dmDiikpLxdHZCaZRf5iq5HRPH7xdCsP6Hmbv22BmGt2jE1fsPORl2iyHNGuZq06VWDWZt38MbFV3wcCpDmlbLhdv3qeXiRExSSr71Pm1A43oMaFyv2DX+nblZuavLmbku5TBQKNh27hI96tRk16WsO0TqVij5URpCiNfL1atXuXv3Li1atMDU1BRTlYrMx/M9JiYmYmVlhYWFBVeuXGHZsmWUKVOmkC0WbPqMGXz7zTecOHGCHb//zowZM3K1ee/dd+nbrx8dO3SgcePGpKSkcODAAVq3bs29e/fyrfdpk7/4gslffFGs+rp168bNsCdfLG7YuJF169ax9ddfMTQ0RK1Wc/36dTw9Pbl9+zZj3n+fiRMnYmube8CGEEI87enMValU+jl2n87cpUuX/vPMnT6db7/9Nitzd+zgq6++ytXmvffeo0+fPnTs2LHQzM1e79MmT57M5Hy+vMtPZmYmK1euxMvLCxsbG06dOsXixYv5Ilt2azQaMjMz0el0pKeno1arMTY2xsBAZnkVRSedwKJY0jMy8Dl6hjuxcRgaGFDdqSzj2zUHYFSLhiwKPMqWc5eo7GBHy6puXHhqfsrisDVTYWFqzPDVGzExMmJs22ZUsLXJ1a6qowPj2zVn2aHj3I9LwNjIiJrlyuLp7FRgvSUl8OoNftx3RP+637K1tK9ehY86tkJpaMiU7u1ZuP8oPkfP4GJnzZTu7VEaGpZoDUKI11N6RgZr/jrJ7ZisDKvh7MjEjq0AeKd1Uxb+eYhfTp+nShkHWlerwvnbd595X7bmZliYGDN0xVpMlEaM79CSCk89FA6gmlMZJnZqzdLAI9yLi8fY0BDP8k7UcnEqsN6Ssv/yNebveTIvXO+Fq+hYsxofd2mL0tCQaW915se9h1jz10kq2Nsw7a3OkrlCiEKlpaXxxeTJXL58GaVSSfNmzVi+fDkAc//3P8a8/z5z586lXr16eHl5ERgY+Mz7cnJywtbWlvIuLpiZmbF0yRKqV899l1jDhg1ZsXw5EyZO5Nq1a6hUKlq2aEHr1q0LrLckmJiY4OTkpH9tbWWFUqnUv6dWqxk8ZAg3btzA0tKSESNG8PXMmSW2fyHE6y0tLY3PP//8SYY1b86KFSsA+O677xg9ejT/+9//qFevHgMGDGD//v3PvK+/M9fZ2RkzMzOWLVuWb+auXLmS8ePHP8ncli31mZtfvSXl119/5YsvvkCj0eDs7MyECROYMGGCfn3nzp31cyMfPXqU0aNHExgYSNu2bUu0DvF6U+jyeWq3eDUpFIrGLrbWe5YO7vNKzyB+8c59vt97mDUjvUq7lFfO7J2BSUduRIzV6XS5700RQhSblco0eHqvLjVrOjsV3vgVdeH2PebuCsTvvWd7CKbI26I/D6v/uHj5U51Ot7Dw1kKIpykUisbVq1ffExIc/Epf12Z34MABhg4bxu1bt0q7lFdefy+vpM2bN8s1rxDPSKFQePXo0WPljh078n6K72vgwIEDDBkyhDt37pR2Ka+VjIwMlEqlLjMzU4Yhv2LkH0wIIYQQQgghhBBCCCFeY9IJLIQQQgghhBBCCCGEEK8x6QQWL6XaLuVkKgghhHhB6lRwlqkghBDiBWjbtq1MBSGEEC9I27ZtZSoIIbKRTmAhhBBCCCGEEEIIIYR4jUknsBBCCCGEEEIIIYQQQrzGjEq7APH6OXA1jG1BwdyJi0elVOLmYIdXwzp4Ojvq2/x5+Ro/7jvCp13a0KqqG8H3IpmxfS8AOh2kabWYKp/857nYuzfz9x7mauQjDA2efHdRu7wT097s+OIOTgghXjKBV67z65kL3ImNQ6VUUrmsAwMb18OzvJO+zd7gq8zfc5DPunegjUcVLt25z7StO4G8M3fZMC++3x3IlfsPMTRQ6N+v4+LMjN5dX9zBCSHESyYgIID5P/zAlStXsLS05I26dZk8eTItW7bUt1mzZg2j3nmHdQEBDBgwgMOHD9O9Rw8AdDodKSkpmJub69sHX7rE8BEjOH78OEZGT7K4Xdu2/Pbbby/u4IQQ4iUSEBDAvHnznuTtG28wZcqUXHk7cuRI1q9fr8/bbt26AXnnbUhICMOGDcudt+3asX379hd3cEKUEukEFiVq67lgfjl7kbFtm1G/ojNGBoacvXWXEzdv5egE3n/lBpYmJuy/coNWVd3wdHZk05ghAEQmJPKu72bWv+edo8MXYEzrpnTxrPZCj0kIIV5WW85cYNOpIMZ3aEWDSi4YGRhyJvw2x2+E5+gE3hcSiqWpCfsvh9LGowq1XMqxZfwoACLjExm5ah2bxo7IlbkftGtB19rVX+gxCSHEy2re/PnMmTOHpUuW0KVLF4yNjdm1axfbfvstR6eEr58fdnZ2+Pn5MWDAAFq1akViQgIA4eHhVK5ShdiYmBwdEAALFyzg3XfffaHHJIQQL6N58+Yxe/Zsli1bljNvt23Lkbc+Pj7Y2dnh6+urz9ukpCQgK2/d3NyIi4vLlbeLFi2SvBX/StIJLEpMcpoG/5Pn+LBDS5pXcdW/39itAo3dKuhfP0xI4tLdB3zWtS3/232Q2OQUbM3NSrSWPy9fY0/wNao6OrDv8jUsTE34pFNr7sbF43/iHOkZmYxs3pAONdwBSM/IwPfYWf66Ho42I4OmlSvybqvGmBgZkaRO4/u9hwmNfESGTkcNp7KMa9cMB4usbxS/2LITT2dHLtx5QHh0DB5OZfm/zq2xVpmW6DEJIUR2yWka1h47zUed29Kiqpv+/SZVXGmSLYMjExK5eOc+X7zZkdm/7yMmOQW7Es7cvcFX2XXxCh5OZdgbEoqliQn/160dd2Pj8Tt6mvSMDN5p1ZSOj7/ES9dm4HP0FIdDb5CekUmzKpUY3bYZJkZGJKrT+G5XIFcfPCQzM5Mazk5M6NASB0sLAD7btB3P8k6cv3WP8KgYqpcry6fdO0jmCiGeq/j4eKZPn86qn3+mb9+++vd79uxJz5499a8jIiI4ePAgGzdsYOCgQTx48AAnJ6e8NvnM1qxZw08//0yjRo1Ys2ZNVoezry+hoaFMmz6dtLQ0/jdnDsOHDwcgLS2NKV9+yaZNm0hLS6N3797MnzcPlUpFbGwsw4YN48TJk2i1Wlo0b87SpUtxcXEBoF379rRs2ZLAwEAuXLhAs6ZN8ff3x8HBoUSPSQgh/hYfH8+0adNYvXp1kfJ206ZNDBgw4Lnl7cqVK2ncuDGrV6/Gzs6OtWvXEhoaytSpU0lLS2Pu3Lk583bKFDZu3EhaWhp9+vRh/vz5+rwdOnQoJ06cyMrbFi1YtmyZPm/btm1Lq1at2L9/f1beNmtGQECA5K0oUTInsCgxVx48RKPNoFnligW223/1Bu5lHWjhXokKttYcCA17LvVcjXyEm70t/u8Ook21yvxv90GuPYxmxdB+fNypFcsPHSdVkw7AmqNnuBeXwIKBb7F8aD+ik1NYf/I8AJk6HR1ruPPz8P6sGt4fEyNDlh08nmNfB0Nv8mGHFvi9MxBtRga/nrv0XI5JCCH+dvl+JBptBs3dKxXYbl/INao6lqFl1cpUsLPhwJXrz6Weqw8eUqmMPevfH0ab6u7M+WMfoZGP+HnkQP7TrT1LA4/oM3f1Xye4GxvHoiH9+GnkAKKTkgk4fhbIunWvk2c11rzjzZp3vTExMmRJ4JEc+zpw5TofdWlDwPtD0WZmsuX0+edyTEII8bdjx46hVqvp06dPge18/fxo2LAh/fr1o0aNGvgHBDyXek6cOEGd2rWJevSIQYMGMcjbm1OnT3MtNBQ/X18mTJyoHw33+RdfcC00lHNnz3ItNJR7d+8y8+uvAcjMzGTEiBGE37xJRHg4KpWKCRMm5NjXunXrWPXzz0Q+eIAmPZ3vvv/+uRyTEEJAMfLW1zdn3vr7P5d6Tpw4QZ06dYiOjsbb25uBAwdy6tQprl+/ztq1axk/fvyTvP38c0JDQwkKCuL69evcvXuXmTNnAll5O3LkSCIiIrh16xYqlYrx48fn2FdAQACrV6/m4cOHaDQavvvuu+dyTOLfSzqBRYlJVKdhpTLJdTvx0/ZfuU6balmj1tpUq8z+KzeKvI8Vh08wcIW/fln7uNMgL45WFnSsWRVDAwNaubsRlZTMwEZ1URoaUr9ieYwMDLkfn4BOp2N3cCjvtmqEpakJZsZKvBrU4dC1mwBYqUxp4V4JU6VR1rqGdbh0LzLHvjrWcKe8rTUmRka0rOrGzaiYIh+TEEI8i8RUNdYq08Iz93Iobatn3fXQtro7+0JCi7yP5QeO0H/JGv3ie/RUvm2drC3p7OmBoYEBrT2q8CgxGe8m9VEaGVLf1QUjQwPuxcWj0+nYefEKo9s0x9LUFDNjYwY0rsehq1m/C6xUprSsWvlx5matu3Tnfo59dfL0wMXWBhMjI1pVq0zYo+giH5MQQjyL6OhoHBwcct1S/DQ/Pz8GDRoEwKBBg/Dz8yvyPj6cNAlbOzv9MnXatHzburm5MXLkSAwNDRng5cXt27eZNnUqJiYmdO7cGWNjY65fv45Op2PlypXMmzcPOzs7LC0t+eKLL9iwYQMA9vb29OvXDzMzMywtLZk8eTIHDx3Ksa8RI0ZQrVo1VCoV/fv353xQUJGPSQghiquoeevr64u3tzcA3t7e+Pr6FnkfEydOxMbGRr9MnTo137Y58nbAgKy8nTYtz7xdsWIF8+fP1+ft5MmTWb9+PZA7b6dMmcLBgwdz7GvkyJH6vPXy8iJI8laUMJkOQpQYS1MTElLTyMjMzLdTIuR+JJEJSbSu+qQT2O/4WcIeRVO5jH2h+xjdqkmR5wS2MVPpfzY2MgTA9qn3UtO1xKeqSdNq+WjDk4ngdWSNAAZQp2v56a+TnL11lyS1BoDU9PQcx5l9XyaPtyuEEM+TpcqU+FR1gZkbfPcBD+ITae1RBcjqBPY9coobD6OoUrbwW8vGtC36nMBP5yCQY6ofYyMj1Nkyd2LAFv06nU6XI3NXHjzK6fA7JKWlAZCqyZm5tmZPtmtiZERqenqRahRCiGdlb29PVFQUWq02346JI0eOcPPmTQYOGACA96BBfPnllwQFBfHGG28Uuo8ff/ihyHNUOjo+edaGSqXK872kpCQePXpESkoKDRs10q/T6XRkZGQAkJKSwkcff8zu3buJjY0FIDExkYyMDAwNs7LcKdt2zVQqkpKTi1SjEEI8i2Ll7cCBQFYn8JQpU4qctwuKMQd7cfO2QYMG+nW58vajj9i1a1f+eZttOgszMzP9CGMhSop0AosSU92pLEpDQ46H3aJFPrcn77+cNdJr4oacTzred+VGkTqBnwcrlSnGRoYs9u6NvYV5rvVbg4K5GxvP92/3wNbcjLBH0Xy4QZ4cKoQoXTXKOaI0NOTY9XBaVqucZ5u/R/1OWLs51/tF6QR+HqxUppgYGbJ0WH/93OrZbTlzgTux8cwf1Bs7czNuPIxigv+WPLYkhBAvTrNmzTAxMWHr1q28/fbbebbx8fVFp9NRr379XO8XpVPieXBwcEClUnHp4kXKly+fa/338+YRGhrK8WPHcHJyIigoiPoNGqB7/MWcEEK8aEXKWx8fdDpdrmz18fEp9bwNDg7OO2+//56rV69y4sQJfd7Wq1dP8la8UDIdhCgx5ibGDG7yBssOHudYWATqdC3ajExOR9xh9ZHTaLRa/rp+k3HtmrFgwFv6ZUzrJhwKDSMjM7NU6jZQKOhSsxor/zpFXEoqANFJyZyNuAtkjUAzNjLC3MSYRHUa607J3JNCiNJnbmLM0OYNWRJ4hKPXw/WZe+rmLX4+dByNVsvha2FM6NiKhUP66Zf327XgwNUbpZu5tWqw4sAxfeZGJSVzJvw2AKkaDcZGRliYGJOoVuvnChZCiNJkbW3NV199xfgJE9i6dSspKSmkp6ezc+dOPv3sM9RqNZs2bWL5smWcO3tWvyxYsIB169ah1ZbOXWIGBga8++67fPzxxzx8+BCAu3fvsnv3biBrFJrK1BQbGxtiYmL0c1cKIURpsba2ZubMmYwbNy533n76KWq1mo0bN7JixQqCgoL0y8KFCwkICCjVvH3vvff46KOP8s9blUqft1999VWp1Cn+3aQTWJSoPvVq8U7LRmw8dYEhP69npM9Gfr9whaaVK3I87BbGRka093DH1txMv3SsWZWMTB1nHne6FmT5oeP0X75Wv0wqoRG5I5o3wNnakv/75Xe8lvvz5bY93ImLB+CtujXRaLUM/nk9/7fpd+pXzP2tnhBClIa+DerwXuumrD9xlkHLfBn+kz87zgfTzL0Sx26EY2xoSIca1bAzN9MvnWt5kJGZyenHna4FWRp4hL6LVumXiSU0IndUq8Y421jx8bqt9Fu8mimbf+dObFbm9q5fG41Wy8Blvny0bhsNKrmUyD6FEOKf+uTjj/n+u+/45ttvKevoSEVXVxYvXkzvXr3YunUrKpWKYcOG4eTkpF9GjRyJVqtl165dhW5/wsSJWFpZ6ZfsUzj8E3Nmz6aKuzvNmjfH2saGTp07czU0606RSR9+SKpaTZmyZWnWvDldunQpkX0KIcQ/8cknnzBv3jxmzZpFmTJlqFChAosWLaJ379755+2oUUXO2/Hjx2NhYaFfsk/h8E/MmTMHd3d3mjZtipWVFR07duTq1asATJo0idTUVBwcHGjatCldu3YtkX0KURwKGXr+elEoFI1dbK33LB3cx7q0axGlY/bOwKQjNyLG6nS6oj+JRAiRLyuVafD0Xl1q1nR2KryxENks+vOw+o+Llz/V6XQLS7sWIV5FCoWicfXq1feEBAfLda3Ipb+XV9LmzZvlmleIZ6RQKLx69OixcseOHValXYt4tWRkZKBUKnWZmZkysPQVI/9gQgghhBBCCCGEEEII8RqTTmAhhBBCCCGEEEIIIYR4jUknsBBCCCGEEEIIIYQQQrzGpBNYCCGEEEIIIYQQQgghXmPSCSxeOukZGYz1/5WY5JTSLuWlFZuSygf+v5KekVHapQghXnJnwm8z87fdpV3Ga+u3c5dYdfhEaZchhChFLVu14ty5c6VdxmspMjKSmp6epKWllXYpQoiXUEhICA0bNkSn05V2Ka+l7du3M2DAgNIuQ5Qg6QT+l4qIjmXatj14/7SOnovWFNr+/J37fLjhN7yW+/Ou7y/sunRVvy4mOYWvd+xj+KoN9Fy0hsiExByfHRuwlf7L1+qXXot9mLnjz3z3tetSKJ7OjtiZm+W5/sqDh0zdtptBKwMY/NM6Zu8MLLDDOFGdxjd/7OftZWsZ5bOJA1fDCj3e/BR2rHn57XwI7/j8wtvL1vKB/6/cjY0H4MKd+4wP2MrAFf54r1zHN3/sJzopWf+51UdOM3LNRryW+zPKZxMbT1/Qr7M1U1G7vBO7LoU+87EIIUpOeFQMX275g4FLfeg+f0WBbS/duU/fRatyLN3nr+Cva1nZtDf4Km/+sDLH+gu37+XazsU79+g+fwU+R04VuD+fI6fwavRGvuvP377H55u28/bi1Yz4OaDQY9118QrvrFpP30WrmLrljxy5NfXXnTnqfuvHn/jAd9Mz116Q4pxzgO7zV9Bn4ZPafth7UL/u17MXGPXzOvotXs2QFWtZceAoGZmZRaq7a+3qBF65TlxK6jMfixDin5v/ww+Uc3bG2saGUe+8k2+noUajoX///rhVroyBoSEHDhzItZ0q7u5Y29hQ3sWFjz7+GK1Wm+9+t2/fjqWFBfXq1cu3TVBQEA0bNcLcwoKGjRoRFBSUb9tFixfTqHFjTFUqRo4cmWNdeHg4BoaGWFpZ6ZevZ83Sr9+4cSMtWrbE3MKCdu3b57uPoiqpugH27dtHjZo1MbewoH2HDkREROjX3b17l969e2Pv4ECFihVZtmyZfp2joyNt27ZlxcqV//h4hBDPbv78+Tg5OWFlZcWoUaMKzNi3336bSpUqoVAocmVsYGAg7dq1w9ramkqVKuVY9/DhQwYNGoSzszPW1ta0aNGCEycK/qJ96tSp/N///R8KheK51z537lxq1aqFpaUlbm5uzJ079x/VXpiAgABcXV0xNzend+/exMTE5Nt29OjReHh4YGBgwJo1a3Ktz+8cFFZ3z549CQ4O5sKFC7m2KV5N0gn8L2VoYEDLqpWY2L5FoW21GZl8+8d+unp6sGG0N592acvPR05xMyorhBQKBfVdy/NFt3Z5fn6Jd282jRnCpjFD2Dh6MA4W5rR0r5Tv/nYGX6Vd9Sr5rk9K09DF04Ofh7/Nz8P7ozJW8uO+v/Jtv+zgcYwMDPAbNYBPOrVm6cFjRETHFnrceSnsWJ+2OziUvSHXmN6zI5vGDGbamx2wUpkCUMHOhq/e6sz60YPxGeWFs7UVSw4c13+2U82qLB3ch41jBjO3X3cOhIZx9MaTC+a2HpXZFXw11z6FEC+ekYEBrapV5sPObQptW8ulHFvGj9IvM3p1RaVU0rBSBX2b6uXK5mhTp4Jzjm1oMzJZfuAYHk5lC9xX6IOHpGg0VC/nmG8bU6URnTw9eKdV00Jrv3D7Hj5HTjLtrc5s+GA4jtaWzPljv37913265ai7hrMjrapVfqbaC1Occ/63xUP76Wub1OnJ55pWrsSCwX3ZPG4kS4e9TVhUDNvOXSpS3cZGRjSsVIF9IfKlnBClZffu3cyZM4c/9+4l/OZNbt68yfQZM/Jt36JlS/x8fXFycsq17q2ePTlz+jTxcXFcvHCBCxcusGDhwny3tXz5coYMGZLveo1GQ+8+fRg8eDAx0dEMGzaM3n36oNFo8mzvXK4cUyZPzrMj9W+xMTEkJiSQmJDA1C+/1L9vZ2fHhxMn8tlnn+X72aIqybqjoqLo9/bbzPzqK6KjomjQoAEDBw3Srx86bBiV3Nx4cP8+O7ZvZ8qXXxIYGKhfP9jbmxUrCv+yTwjxfOzevZvZs2ezb98+IiIiCAsLY/r06fm2b9myJWvXrs0zY83NzRk1alSODtS/JSUl0ahRI86cOUNMTAzDhw+nR48eJCUl5bmf+/fvExgYSO/evV9I7TqdDl9fX2JjY9m1axeLFi1i/fr1z1R7YYKDgxkzZgx+fn5ERkZiZmbG2LFj821ft25dlixZQv369XOtK+gcFKXuQYMGSQa/RqQT+F/KxdaazjWrUdHOptC2iWlppGjSaedRBYVCQTVHB1xsrbkVEwdkjUrtUbs6VR0dCt3WpXuRJKrVNK/imuf6h4lJRMYn4uFYJt9tNHR1oaV7JcyMjTFVGtGjdg0u33+YZ1t1ejpHb0QwpEk9VMZKPJ0daexWgcCrNwqtNS/FOdZMnY71p4J4t2UjKtrZoFAoKGdthaWpiX5b9hZPRjsbKBTcj0/Qv3axtcZUqXyyHnKs93Asw4OERB4mPNsvFiFEyXGxs6FLreq42tsW+7N/hoTSoqpbjv/fC7PlzAXqVSxPhUIy/HT4bWq5lCuwjYdTWTrUrIaTtWWh+z158xYtq1XG1cEOpaEhg5rU59Ld+9yPS8jVNjI+keC7D+hQo9oz1V6Yf3LOn1bOxgqLx9ms04EBilzHVFDddVzKcermrX9chxDi2fj6+jJq1Cg8PT2xtbXlyylT8PHxybOtsbExkz78kJYtW2JoaJhrfZUqVbCxsQGy/uA3MDDg+vXreW5Lo9GwPzCQNm3y/zLqwIEDaLVaJn34ISYmJkycMAGdTsf+/fvzbN+3b9+sUbH29oUcdW4dO3bEy8sL53IF535RlGTdW7ZswdPTk/79+2NqasqM6dM5f/48V65cISkpiQMHDjBl8mSUSiV169alX79+rF69Wv/5Jk2aEBYWlmP0sBDixfHx8eGdd97RZ+zUqVPzHG0KjzN20qR8M7Zx48YMHTqUypUr51pXuXJlPv74Y8qVK4ehoSGjR49Go9Fw9WreA5/27t1L/fr1MTU1fSG1f/rpp9SvXx8jIyM8PDzo1asXR44ceabaC+Pv70/Pnj1p3bo1FhYWfP3112zZsoXExLzvRB43bhwdOnTI81wUdA6KUnfbtm35/fffn+k4xMtHOoFFoWzNVLSu6sa+y9fIyMzkyv2HPEpMpmYBI8vys//KdZpVcc23syMiOhZHa0sMDYr+n2bwvQf5dmbfjUvAwEBBeVtr/Xtu9nb6DuznKSopmaikFCJi4hi5ZiPv+PyC/4lzZGabr+hhYhIDV/jTb6kfvwZdom/9Wjm2senMBfovX8uINZtQa7W0yTaiztDAAGdrK25G539biBDi5aZOT+fItZt0rJmzo/TGw2gGLvXh3dUbCDh+NsfUBJEJiewJvop30waFbj88KgYXW5uSLVqX+8fwPHJo3+VQPMs74Zitc7k4tT8Pn27czuDlfszavofI+JwX0YFXrtNv8WoGLvMlLCqabnVq6NcVVncFO1vCHkkWC1FagkNCqFunjv513bp1iYyMJDo6+pm2FxAQgLWNDWXKluX8+fOMGT06z3bXrl3DwMAAFxeXAmurU7t2jluV69SuTXBIyDPVBlDJzY0KFSsyatQooqKinnk7BSnJuoNDQqiT7d/H3NycKlWqEBwcrJ/HM/t8njqdjkvBwfrXRkZGuLu7c/78+Wc5FCHEPxQcHEzdunX1r/9pxhZVUFAQGo0Gd3f3PNdfvHgRDw+PArfxvGrX6XQcPnwYT0/PPNcXVnthnq67SpUqGBsbExpa/DvPinMO8qq7Ro0ahIeHk5CQe9CHePVIJ7AokjbVKrP+1Hn6LvXjsy07Gdq0PmUszYu1DXW6liPXI+hYPf8gTE7TYKY0KvI2b0bFsP7UeUa2aJTvPs2Mc3Y4m5sYk6pJL/I+nlV0UtY8xedu3WPhoF5826cLh0Jvsjfkmr5NWUsL1o8ejP+7gxjSpD4u2TqrAfo3qMPG0YP5YUBP2nlUwczYOMd6lVJJclret+UJIV5+R67dxEplSu1so3VrlS/H0mFvE/D+MKb07MTBq9f55fSTP3yXBx5laPOGqIwLHzmclKZBVYwRxoVp4OrC4dAb3HwUTZpWy7rjZ1AAaem558vcF3ItV+d2cWovaXP692T1O4NYPtwLO3MzZmzblaNzvV11dzaPG8nKEQPoXqcGNmaqItetMlaSks8t0kKI5y8pKQlr6yfXUH//nN+IqcJ4e3sTHxfH1StXGDNmDI6OeQ98iIuLw9Ky4Lsonq7t7/qepTYHBwdOnjhB+M2bnD51isTExAKnovgnSrLugrZlaWlJixYt+HrWLNRqNWfPnmXLli2kpOR83oelpSVxcXHF3rcQ4p8r6YwtioSEBIYOHcr06dNz5cffniWDS6r2GTNmkJmZmecUOEWpvTDPM4PzOwf51f33OZYMfj1IJ/C/wIGrN/QPZZv+295if/52bBz/232Qjzq24texw1js3ZvNZy9yKvx2sbZzLCwCS1NjapXPPb/O3yxMTEjJ1pnwMDEpx0PlsrsXl8CM7X/yXqsmeDrnfXFuqjQi5akO3xSNpkgdEMH3IvX7HRuwtdD2TzM2yrqFpG/9WliYmOBoZUnXWtU4HXEnV1tLUxM6VK/CN7/vz/UwIoVCQZUy9hgbGRJwIueTp1PT0zE3ydkxLIR4vgIvX9M/XGzqrzv/0bb2hVyjQ42qOUZalbOxwsnaCgOFAjcHO7yb1ufItZsAnLgRQUp6Om088p83PTsLExNS059k4IaT5/S1L/zzcLHrrefqwuBmDflmx15G/ryOslaWqIyVOFjk/FIw+O4DYlNSaFn1yd0Lxa09u5I457VdyqE0NMTC1IQxbZvzID4xz7tCytta42pvy5L9fxW57lRNeq4v6YQQz4e/v7/+oWjdu3cHwMLCIscIpb9/LqxzoDBVq1bFs2ZNxo0bl+d6W1vbXH9EZ39o261bt3LV9nd9z1KbhYUFDRs2xMjICEdHRxYuXMievXtLpCPmeddd0LbW+vkRfvMmFV1dGTtuHIMHD841ujoxMVE/TYcQ4vnx9/fHwsICCwsLunXrBjy/jM1PamoqPXv2pGnTpnzxxRf5tns6g19U7YsWLcLX15fff/8dExOTZ6o9u8OHD+vr/ntk8fPM4LzOQUF1/32OJYNfD0UfcileWW09qtD2Gf7o/tut6DjK21hR37U8kDVXbaNKLpyJuEujbA8yKsz+K9dpV929wCd3VnKwJTIhkYzMTAwNDChracGmMblHODxMSGLqtt0MbFSH9gU8RK68jRWZmTruxSXgbGMFwM2o2CLNhezp7JjnvouqvI01RgYGZD9aBfkfe4ZOR1yqmhRNun7e4OwyM3U8SHjySy4jM5N78Qm42ds9c41CiOJrV6Mq7WpU/cfbeZSYxIU795jQsVUhLRXoHk+8EHT7LtciHzF4uR+QdfeEgYGCiKgYpvXqkuuTbmXsuBsbr389oHE9BjTO/wn2RdHzDU96vpF1gXonNo71J87h6pAzh/4MCaW5u1uOL9yKW3t2JXXOs1MoyJoAOA8ZmTr9nMBFqft2TCyVy0gWC/EiDB48mMGDB+d4z7NmTc5fuICXlxcA58+fx9HR8Znm1X2aVqvlRlhYnuvc3d3R6XTcvXuX8uWzrpMTn/qj3bNmTebNm4dOp9NfA1+4eLHAB/wU1d/by3xqAMGzeJ51e9asia+fn/51cnIyN27c0Hd2uLq6sn37dv36wYMH06jRk7v8tFot169fz3E7sxDi+cgzYz09OX/+/HPJ2KelpaXRu3dvXFxcWL58eYFt69Spk2P+9xdR+6pVq5g9ezaHDh3K9WVVcWrPrlWrVrkeIPd33X8LCwsjLS2NatWqPf3xQhV2Dgqr+/Lly1SqVAkrK6ti71u8fGQk8L+UTqdDo9WifXzRqNFqSc/IyLNt5TJ23ItP4Pyd++h0Ou7HJ3Aq/A6Vsj2MJ/vn0zMy0Whz3hoclZTMhTsP6FBAhy2Ag4U5ztZWhEbmP79ZdFIyU7bupkftGnSrVb3A7ZkqlTSrUhH/E+dQp6cTcj+SEzdv0e5xp3hkQiI9F60hMqHoIygKO9Yn+zaiVVU3Np+9RIomnaikZHYFX6VRpaxfFkdvRHAnNp5MnY74VDU//XWKymXssDQ1IVOnY+elqySp09DpdIRGPuL3i1eok+2W8dDIKBwtLShrZVHk2oUQz4c+UzOyZao270z92/7L16jh7Eg5m5wXVKdu3iI2Oes22Nsxcaw/cZamlSsBMLR5Q1aOGMDCIf1YOKQfTaq40rVWdT7qkvdDiRpVqsjFO/cLrCMz2+8Dna7g3wcarZbwqBh0Oh0PE5JY+OdhetWrleOLqzStlsOhN3JNBVFY7Rdu36P7/KI/ebg45zwiKoYbD6PIyMwkVZPOT4eOY29hTgW7rN9juy5eIS4lFYBb0bFsPBlE3Yrli1Q3wMU792lQjC9FhRAla+jQoaxatYqQkBDi4uL45ttvGT58eL7t09LSUKvVQNbD3dRqtX5O2p9++omHD7MeOBwSEsLsOXNo3759ntsxNjamY4cOHDx4MN99tW3bFkNDQxYsXEhaWhqLFi8GyHebWq0WtVpNRkYGGRkZqNVqtI+vNU+cOMHVq1fJzMwkOjqaDz/8kLZt2+pv283ePjMzE7VaTXq2u0HcKlfO94FIz7PuPn36cOnSJTZv3oxarWbm119Tp04dqlfPuo6/fPkyiYmJaDQa1q5dy569e/n4o4/02z558iSVKlXC1TXvB0sLIZ6vYcOG8fPPP+szdtasWYwYMSLf9gVlbPZs0ul0qNVqNI+n1EpPT+ftt99GpVLh4+ODQSHPCerUqRNnz57V7+t51+7v78/kyZPZu3dvrgfbFVZ7eHg4CoWC8PDwAo/pb4MHD2b79u0cPnyY5ORkpk2bRt++ffMdCZy91vT0dNRqtf4LwoLOQVHO+cGDB/Ujq8WrTzqB/6UeJibRb9laxj2e5qDfsrW8v3aLfv303/ay8fQFAMpZWzGxfQtWHDrBgBX+fLFlF82ruNLZ88kf+P2WraX/cn8APvD/lX7Lck7dEHjlBtWdylDOuvBvj7p6ViPw6o181+8JucaDhETWnQrKc6qIjacv5Jj24oM2zUjTahny8wbm7j7EB22a6Z8mH5WUQllLc+zNiz6/cUHHujjwKIsDj+pfv9+mCSpjI0as3sB/fvmdNtUq0+nxaLbopBSm/7YXr+X+jF+3FQMFTOn25ML6eFgE7/ltxmuFP9/vOcybdWrQM9uDig6EhtG1VsET4QshXoyHCUn0XriK9303AdB74Sre89mgXz/1151sOJlzOpd9IaG5OkoBzt++x7i1m+mzcBXTf91Jc3c3/ehdM2Nj7MzN9IuJkREmSiWW+TwV2d3RAXMTY67cf5hv7Zfu3Kf3wlVM37qLR4lZx/Hllj/069/32UTg5ay5zDXaDP63cz99F63mo3W/Ur2cI0ObN8yxvWPXwzE3MaFuBecc7xdW+6PEJGoU44GjxTnnsSmpzP5jH28vXsOoVeuITEhkRq+uGBlmXQaF3HvAWL9f6LNwFdO27qSRWwVGPJ5rvrC6NVotp8Jv5/lvKYR4Mbp27cp//vMf2nfogGulSrhWrMhXM2bo19eqXRt/f3/96+o1amBmbs7du3fp2q0bZubmREREAHD06FHq1K2LhaUlPd58k27duvHtN9/ku+/Ro0ezdu3afNcbGxvz65Yt+Pn5YWtnx+rVq/l1yxaMH08h8+1//6uf1gJg1jffYGZuzpw5c1jr74+ZuTmzHu8/LCyMbt27Y2VtTe06dTAxMSEg23H5+flhZm7O2HHjOHz4MGbm5ox+/FA7jUZDdHQ0TZs2LdI5Lcm6y5Qpwy+bNvHl1KnY2dtz8uRJ1gUE6D+7e88eqri7Y2dvz/IVK9j5xx+UKVNGv94/IIAxY8YUqW4hRMnr2rUrn376Ke3ataNixYq4urry1Vdf6dd7enrmyFgPDw9UKhV3796lS5cuqFQqfcYeOnQIlUpF9+7duXXrFiqVis6dOwNZ+btjxw727NmDjY2NfoqEw4fznsLM0dGR9u3bs23bthdS+5dffkl0dDSNGjXS1/b+++8Xqfbbt2/j6uqqv2ukMJ6enixbtozBgwdTtmxZEhMTWbJkiX59t27d+Pbbb/WvO3fujEql4ujRo4wePRqVSsWhQ4cKPQdFOefr1q2TDH6NKHT53AopXk0KhaKxi631nqWD+zzbDOQvgfSMDD5c/xuzenfBztzsue5rw6nzWKlM6faKdabGpaTyxa+7+HFAT4yNcs7qMntnYNKRGxFjdTqdXz4fF0IUg5XKNHh6ry41azrnP5/5y+xsxB12nA9m2lsFT7lQ2n7Ye5BWVSu/ciNqfzt3iUeJSbzTOnfHyqI/D6v/uHj5U51Ot7AUShPiladQKBpXr159T0hw8Et9XduyVSsWLlhAvXr/bLqd5+mvv/5iyZIlBGTrfH0VPHz4kLbt2nH2zBlMn/rCs7+XV9LmzZvlmleIZ6RQKLx69OixcseOHa/sff4hISEMHz6ckydPFjjtZGmbNWsWZcqUeeU6U7dv346fnx8bN27M8X5GRgZKpVKXmZkpA0tfMTInsHjpKA0NWTK4zwvZ14BGr+bcYjZmKpa+oHMkhHi11Xd1ob6rS+ENS9mkTnlPafGye6terdIuQQhRyv7KZ5Tay6Rly5a0bNmytMsotrJlyxISHFzaZQghXlI1a9bk1KlTpV1Gob788svSLuGZ9OzZk549e5Z2GaIESa+9EEIIIYQQQgghhBBCvMakE1gIIYQQQgghhBBCCCFeY9IJLIQQQgghhBBCCCGEEK8x6QQW4hn0XLSGe3EJpV2GEEL8K3Sfv4J7cfGlXYYQQrz2DAwNuX79emmXIYQQ/woKhUIyV7xQ8mA48cIduBrGtqBg7sTFo1IqcXOww6thHTydHfVt/rx8jR/3HeHTLm1oVdWN4HuRzNi+FwCdDtK0WkyVT/7zXezdm/l7D3M18hGGBk++26hd3olpb3YsVn09F61h+ZC+ONu8sg9JFUIIvcAr1/n1zAXuxMahUiqpXNaBgY3r4VneSd9mb/BV5u85yGfdO9DGowqX7txn2tadQN6Zu2yYF9/vDuTK/YcYGjx5EnMdF2dm9O5arPq6z1/BTyMH4Gxj/Q+PVAghSldAQADzf/iBK1euYGlpyRt16zJ58uQcD2Rbs2YNo955h3UBAQwYMIDDhw/TvUcPAHQ6HSkpKZibm+vbB1+6xPARIzh+/DhGRk9yuF3btvz222/Fqs/A0JDQq1dxd3f/h0cqhBClLyAggHnz5j3J3DfeYMqUKbkyd+TIkaxfv16fud26dQPyztyQkBCGDRuWO3PbtWP79u3Fqk+hUHDt2jXJXPFSkU5g8UJtPRfML2cvMrZtM+pXdMbIwJCzt+5y4uatHJ3A+6/cwNLEhP1XbtCqqhuezo5sGjMEgMiERN713cz697xzdPgCjGndlC6e1V7oMQkhxMtqy5kLbDoVxPgOrWhQyQUjA0POhN/m+I3wHJ3A+0JCsTQ1Yf/lUNp4VKGWSzm2jB8FQGR8IiNXrWPT2BG5MveDdi3oWrv6Cz0mIYR4Gc2bP585c+awdMkSunTpgrGxMbt27WLbb7/l6JDw9fPDzs4OPz8/BgwYQKtWrUhMyLq7LDw8nMpVqhAbE5Oj8wFg4YIFvPvuuy/0mIQQ4mU1b948Zs+ezbJly3Jm7rZtOTLXx8cHOzs7fH199ZmblJQEZGWum5sbcXFxuTJ30aJFkrnitSTTQYgXJjlNg//Jc7zfpinNq7hiqlRiZGhAY7cKjGrRSN/uYUISl+4+YFy7Zpy9dZfY5JQSr+VeXAKfb9nJgBX+eP+0jjm7DgDw+ZaskW8T1/9G/+VrOXztJgBbzl5i2KoNDF+1gb0h10q8HiGEKGnJaRrWHjvN2PYtaVHVTZ+5Taq48k7rpvp2kQmJXLxznwkdW3Em/A4xzyVz4/l043beXryagUt9+O/vfwLwn41Zo9jG+W2m76JVHLx6A4BfTp9n8HI/hqxYy55LV0q8HiGEKEnx8fFMnz6dRQsX0rdvX8zNzVEqlfTs2ZO5//ufvl1ERAQHDx5k+bJl7N6zhwcPHpR4LdevX6dtu3bY2NpSpmxZBg4cCECbtm0BeKNePSytrNiwYQMAc7/7Dufy5Snv4sKqVatKvB4hhChp8fHxTJs2jcWLF+fO3Llz9e3+ztwVK1awe/fu55a5bdq0wdraGgcHBwYMGABA69atAahbty4WFhZPMnfuXMqVK4ezs7NkrigVMhJYvDBXHjxEo82gWeWKBbbbf/UG7mUdaOFeiQongzgQGkaferVKtBb/E+eoV9GZb/t0RZuRwbWH0QDM7tuNnovWsGDgW/rpIM5E3OHXc5eY1bsLjlYWLNx/tERrEUKI5+Hy/Ug02gyau1cqsN2+kGtUdSxDy6qVqWB3hgNXrtO3QZ0SrcXv6Gnqu5Zndv83szI3MgqAuV5v0X3+ChYP7aefDuJ0+G22nLnAt/164GRtyY97D5VoLUIIUdKOHTuGWq2mT58+Bbbz9fOjYcOG9OvXjxo1auAfEMAnH39corVMmzaNTp06sX/fPjQaDadPnwbg4IEDGBgaEnTunP7W5F27dvH999/z5969uLm5MXr06BKtRQghnociZ66vb87M9ffnk08+KdFapk6dSufOnQkMDMyRuYcOHUKhUHD+/Pkcmfvdd9+xb98+3NzceO+990q0FiGKQkYCixcmUZ2Glcok1+3ET9t/5TptqrkB0KZaZfZfuVHkfaw4fIKBK/z1y9rjZ/NsZ2hgwKOEZGKSUzA2MsoxFcXT/roeToca7rja22KqVOLd+I0i1yOEEKUlMVWNtcq08My9HErb6lkXp22ru7MvJLTI+1h+4Aj9l6zRL75HT+XZztDAgIcJScQkJWdlbrapKJ52OPQGnWpWo5KDHaZKJYObNShyPUIIURqio6NxcHDIdTvx0/z8/Bg0aBAAgwYNws/Pr8j7+HDSJGzt7PTL1GnT8mynVCqJiIjg3r17mJqa5rgt+mkbN21ixIgR1KpVC3Nzc6ZPn17keoQQorQUNXN9fX3x9vYGwNvbG19f3yLvY+LEidjY2OiXqVOn5tmuWJm7cSMjR47UZ+6MGTOKXI8QJUU6gcULY2lqQkJqGhmZmfm2CbkfSWRCEq2rPukEjoiOJexRdJH2MbpVE9aPHqxfhjStn2e7kc0boEPHJxt3MDZga4FTPMQkp1DG4slk8WWtLIpUixBClCZLlSnxqeoCMzf47gMexCfS2qMKkNUJHB4Vw42HUUXax5i2Ldg0doR+Gda8UZ7tRrVqgg6YtG4r7/tsKnCKh5ikFBwsn+Sso6VlkWoRQojSYm9vT1RUFFqtNt82R44c4ebNmwx8fKuw96BBXLx4kaCgoCLt48cffiA2Jka/fD1zZp7t5syZg06no0nTptSqXbvA243v37tHhQoV9K9dXV2LVIsQQpSmYmXu4ylxvL29i5W5CxYsIC4uTr98/fXXebb73//+h06no3Hjxnh6ehaYufckc8VLQKaDEC9MdaeyKA0NOR52ixb53J68/3LWqN+JG3I+7XjflRtULmNfYrXYmpsxoX0LAILvRTJ12248nR31U0DkaGtmxqOkZP3rR4nJudoIIcTLpkY5R5SGhhy7Hk7LapXzbPP3qN8Jazfner9KWYcSq8XO3IwPO2XNjRZ89wGTN/9OLZdy+ikgsrM1NyMqMUn/+mG2n4UQ4mXUrFkzTExM2Lp1K2+//XaebXx8fdHpdNSrXz/X+2+88UaJ1eLk5MTKFSsA+Ouvv+jUuTOtW7fO8+n05cqV4/bt2/rXt27dKrE6hBDieSlS5vr4oNPpcuWrj49PyWfuypVAVuZ27NhRMle81GQksHhhzE2MGdzkDZYdPM6xsAjU6Vq0GZmcjrjD6iOn0Wi1/HX9JuPaNWPBgLf0y5jWTTgUGlbgaLbi+ut6OFGPO3YtTIxRoMBAoQDAxsyUBwmJ+rYtq1Zi35Xr3IqJQ52uZd3JoBKrQwghnhdzE2OGNm/IksAjHL0ers/cUzdv8fOh42i0Wg5fC2NCx1YsHNJPv7zfrgUHrt4o0cw9HBqm79jNylxQkJW5tmYqHsQ/ydxW1SrzZ0got6JjUadrCTh+psTqEEKI58Ha2pqvvvqK8RMmsHXrVlJSUkhPT2fnzp18+tlnqNVqNm3axPJlyzh39qx+WbBgAevWrStwNFtxbdq0iTt37gBga2uLQqHA4PG0QI6OjoSFhenb9u/fHx8fH0JCQkhJSWFmPqOLhRDiZWJtbc3MmTMZN25c7sz99FPUajUbN25kxYoVBAUF6ZeFCxcSEBBQapnr5eXFmjVr9Jn71VdflVgdQhSVjAQWL1SferWwNVOx8dQFvt9zGJWxEe5lHPBqWIfjYbcwNjKivYc7RoZPvp/oWLMq/ieCOBNxl8ZuFQrYOiw/dJyf/jqpf13expofBvTM1e5aZBQrD58kRaPBRqXivVaNcbLOuuXYu/Eb/PDnX2i0Wsa1a06rqm68VbcmU7buwgAFQ5rW50BoWK5tCiHEy6ZvgzrYmqlYf+Isc3fux8xYibujAwMa1+PYjXCMDQ3pUKNajsztXMuDtcdOczr8Nk0qF3yb2tLAI6w4+ORhmS62NiwY3DdXu9AHD1lx4CjJGg02ZirGtG1Oucd3Xng3bcD3uwLRZGQwoUMrWntUoVe92nzxyw4UCgXDmjck8Mr1EjojQgjxfHzy8cc4OTryzbffMmToUCwtLWlQvz6TJ09m69atqFQqhg0bhlKp1H9m1MiRTJ8+nV27dvHmm28WuP0JEyfyUbaHyHl4eHD6VO552E+dPs1HH39MfHw8jo6O/DB/PpUrZ90NMn3aNEaMHElqairLly3Dy8uLDz/8kA4dO2JgYMDXM2fiHxBQQmdECCGen08++QQnJydmzZrF4MGDszK3QQOmTJmSf+aOGsW0adOKlLnjx49n0qRJ+tceHh6cOZN7YMKpU6eYNGmSPnN//PFHfebOmDGD4cOHk5qayooVK/Dy8mLSpEm0b98eAwMDZs2ahb+/f8mcECGKSKHT6Uq7BlGCFApFYxdb6z1LB/fJfY+t+FeYvTMw6ciNiLE6na7oTxsRQuTLSmUaPL1Xl5o1nfN/mJkQeVn052H1Hxcvf6rT6RaWdi1CvIoUCkXj6tWr7wkJDpbrWpFLfy+vpM2bN8s1rxDPSKFQePXo0WPljh07cs+JKEQBMjIyUCqVuszMTJld4BUj/2BCCCGEEEIIIYQQQgjxGpNOYCGEEEIIIYQQQgghhHiNSSewEEIIIYQQQgghhBBCvMakE1gIIYQQQgghhBBCCCFeY9IJLIQQQgghhBBCCCGEEK8x6QQWr4REdRrf/LGft5etZZTPJg5cDcuznU6nY83R03ivXIf3ynWsOXoanU6nX99z0RreXraW/suzlgX7j7yoQxBCiJdWolrN17/toc/CVQz/KYDAK9fzbKfT6Vh1+AQDlvowYKkPqw6fyJGxQbfuMsF/M/0Wr2bUz+vYeeFyjs+uP3GW4T/502/xamb//icpaZrnfmxCCPGyiYmJoW/fvlhYWlLJzY2AgIA82+l0Oj77/HMcypTBoUwZPvv88xyZO3rMGKrXqIGhkRFr1qzJ8VkfHx8aNmqEtY0NFSpW5NPPPkOr1T7PwxJCiJdSTEwMffr0wdzcHFdX14Iz97PPsLe3x97ens8++yzndW5QEA0aNMDMzIwGDRoQFBSkXzd37lxq1aqFpaUlbm5uzJ0793kflhDPxKi0CxCiKJYdPI6RgQF+owYQFhXDzB1/4uZgi6u9bY52u4JDOR52iwWD3kIBTN22B0crC7rVqq5vs2DgWzjbWL3gIxBCiJfXkv1HMDI0IGDMUMIeRTN9604qO9jh6mCXo93Oi5c5diOcxUP6gULBlM2/42hlSY+6NdFmZDJr+x5GtWpCt9o1uBb5iM9/2YFHubJULmPPvpBr7L98je8G9MLCxIS5O/ezNPAIn3RtV0pHLYQQpWP8+PEYGxvz4P59goKCeLNnT+rWrYunp2eOditWrGDbtm0EnTuHQqGgc5cuuFWqxPvvvw9A3bp1GeDlxedffJFrHykpKcyfN48mTZrw6NEjevXuzXfff8/nn332Qo5RCCFeFuPGjcPY2JjIyEiCgoLo0aNHvpm7detWzp8/j0KhoFOnTri5ufH++++j0Wjo1asXkyZNYuzYsSxfvpxevXpx7do1jI2N0el0+Pr6UqdOHW7cuEHnzp2pUKECAwcOLKWjFiJvMhJYvPTU6ekcvRHBkCb1UBkr8XR2pLFbBQKv3sjVdv+V6/R+wxMHC3PsLczpXc+TfZfzHtEmhBAiK2OPXLvJ0OYNszK2vBNNKruy//K1XG33hVyjb/06OFha4GBhTt8GdfgzJBTIGk2cokmnfY1qKBQKqjmVpYKdLbeiYwE4ERZB51rVKWNpgcpYyduN6nIoNAx1uoxME0L8eyQnJ7N5yxZmzpyJhYUFLVu25K2ePfFbuzZXW18/Pz7+6CNcXFwoX748H3/0ET6+vvr148aOpUOHDpiamub67AcffECrVq0wNjamfPnyeHt7c/SI3AEnhPh3SU5OZvPmzXz99ddPMvett/Dz88vV1sfHh08++USfuZ988on+LosDBw6g1WqZNGkSJiYmTJw4EZ1Ox/79+wH49NNPqV+/PkZGRnh4eNCrVy+OSOaKl5B0AouX3t24BAwMFJS3tda/52Zvx62YuFxtb8XE4ZZt5Fpe7b7YspOhq9bz7R/7iUxIfF5lCyHEK+FubDyGBgpcbG3071UuY0/E487b7CKiY3ArY69/7eZgr+/ktTU3o41HFfYGXyUjM5PL9yJ5mJCIZ3knffvst9TpdJCekcG9uPjncFRCCPFyCg0NxcjIiGrVqunfq1O3LiHBwbnaBgcHU7duXf3runXrEpxHu6I4fOgQNZ8a9SaEEK+7vDI3vywtKHODg4OpU6cOCoVCv75OnTp5bken03H48OFcI42FeBnIdBDipadO12JmrMzxnrmJMama9LzbmihztkvXotPpUCgU/LdPVzycypCm1bL2+Dlm7tjHgoFvYWgg34cIIf6dUjXpmBkb53gvKzvzzlhzE+Nc7f7O2LYe7vz45yGWHzgKwPgOLSljaQFAg0oV+OX0eVpXq4KFqQm/nA4CIE1GAgsh/kWSkpKwsso5LZm1lRWJSUl5trW2fjIIwtramqSkJH3mFtWqVas4feYMK1eufPbChRDiFZRn5lpbk5iYezBYQZn79LqCtjNjxgwyMzMZOXJkCR2FECVHOoHFS89UaUTKUx2+KRoNqqc6hv9um71zOEWjQaU00l8o13o8Ik1paMh7rRozYEUAt2PiqeRgm2tbQgjxb6AyVpKiyfmAthRNOipl3hmbvW1WxipRKBTcjolj9h/7+LJnJ+q5unAvNp4Z23ZhZ25O48oV6VzLg6jEJD77ZTsZmTr6NqjNibBbOFiaP/djFEKIl4WFhQUJCQk53ktITMTSwqLQtgkJCVhYWBSrA3jr1q1MnjKFvXv24ODg8OyFCyHEKyjPzE1IwNLSstC22TO3qNtZtGgRvr6+HD58GBMTkxI8EiFKhgx/FC+98jZWZGbquBf3JHRvRsVS0c4mV9uKdjbcjIottN3fFArQoct3vRBCvO7K21qTkanjbuyTaRnCHkXnevAmgKu9HTcfRetf33wUTcXH7SKiYihva02DShUwUChwsbOhkVtFToffAsBAoWBI84aseccbv/cG42pvh/3j+duFEOLfolq1ami1Wq5dezLv+oXz5/OcqsHT05Pz58/rX58/f75Ytxfv2rWL0WPG8Nu2bdSuXfufFS6EEK+gvDI3vywtKHM9PT25cOFCjqnNLly4kGM7q1atYvbs2ezbtw8XF5fncThC/GPSCSxeeqZKJc2qVMT/xDnU6emE3I/kxM1btPOokqtt++pV2BoUTHRSMtFJKfwaFEyHGu4ARETHEvYomozMTFI16aw6cgp7czMqZJsHUwgh/m1MlUqau1di7bHTqNPTCb77gOM3wmlfo2qutu1rVOXXsxeJSkomOimZLWcu0rFm1hxrVco6cC8unqBbd9HpdNyPS+Bk2C3cHLLmEE5Uq7kfl4BOp+NWdCwrDx7Du0l9DIoxok0IIV515ubm9O3Th+nTp5OcnMyRI0fY9ttvDB0yJFfboUOGMP+HH7h79y737t1j3vz5DB82TL9eo9GgVqvR6XSkp6ejVqvJzMwEYP/+/QwZOpRfNm2icePGL+z4hBDiZWJubk7fvn2ZNm3ak8zdto2hQ4fmajts2DDmzZunz9zvv/+eESNGANC2bVsMDQ1ZsGABaWlpLFq0CID27dsD4O/vz+TJk9m7dy+VK1d+YccnRHHJdBDilfBBm2b8uO8vhvy8AUtTEz5o0wxXe1uC70UyY/teNo3JunDu6unBg/gkxq/bBkDnmtXo6ukBQFyqmqUHjhGVlIKp0ojqTmWY9mZHjAzluxAhxL/buA4tmb/nIIOW+WGlMmFch1a4Othx6c59pm3dyZbxowDoXqcGD+ITGOv7CwBdannQvU4NAMrZWDGpUxuWHzjKw4QkzEyMaVfdnS61qwMQn6rmq227iUpMwkqlole9WnR7/FkhhPg3Wbx4Me+88w6OTk7Y29uzZPFiPD09OXz4MN179CDx8S3HY8aMIezmTeo8flDRO++8w5gxY/Tb6dK1KwcPHgTg6NGjjHn/ffbv20fbtm2Z9c03xMfH0+PNN/XtW7VsyR9//PECj1QIIUrfkiVLGDVqFGXLlsXe3p6lS5fqM7dbt24kPZ6TfcyYMYSFhenvnHj33Xf1mWtsbMzWrVt59913+fzzz6lRowZbt27F+PFzNb788kuio6Np1KiRfr9Dhgxh2bJlL/hohSiYIvtwdvHqUygUjV1srfcsHdzHuvDW4nU0e2dg0pEbEWN1Op1fadcixOvASmUaPL1Xl5o1nZ1KuxTxiln052H1Hxcvf6rT6RaWdi1CvIoUCkXj6tWr7wkJDpbrWpFLfy+vpM2bN8s1rxDPSKFQePXo0WPljh07rApvLcQTGRkZKJVKXWZmpoyoe8XIP5gQQgghhBBCCCGEEEK8xqQTWIjXjIztF0KIl4PksRBCPD9yR6sQQpQOyd9Xl3QCv37UaelaecrOv1iqJj0TUJd2HUK8RtTqdG1p1yBeQSkajeSxEP+MOiUlRa5rRZ6SEhMlY4X4Z9RJSUnSmyeKLTk5GSMjo/TSrkMUn3QCv36uxqWqjaKTUkq7DlEK0jMyuPzgoTFwsrRrEeJ1kZau3X8u4o5c5IhiydTpOHfrrg44Xtq1CPEKu/rw4UOje/fulXYd4iWj0Wg4cvSoXPMK8c+cOnXqlKlaLd+liOLZt28fFhYW50u7DlF80gn8mtHpdGlGBgYrZ/2xLyUiOra0yxEv0MOEJP63+6DaQKE4rNPpIkq7HiFeF5qMjJ92nA9J2xcSikYrI4JF4aKSkpm3+0CaNiMjBLhU2vUI8arS6XRpSqVyZa/evVOCg4NLuxzxkoiIiGDgoEFqIyMjueYV4h/Q6XT3lUrl3v79+6fevHmztMsRr4CMjAwCAwMZPXp0amxs7H9Lux5RfAqZy+P1o1AoDEyNjL7WwfugMzcyMMwo7ZrE85WhyzTIzNRpDQ0MNqSmp0/Q6XSppV2TEK8ThULR1MLEeKE6XVvX2MhQq1Ao5JenyFNGps4gIzMzU2louDVFo/lAp9MllHZNQrzKFAqFgbm5+dfA+zqdztzY2Fiua//FtFqtgVar1SqVyg2JiYlyzSvEP6RQKEwtLS1/1Gq1gwwMDJRKpTKztGsSLyedTkdqaqpSpVLdTklJmarRaAJKuyZRfNIJ/BpTKBQKwB4wLu1axHOnBaJ1Op38YSTEc6RQKMwAa0DmqBT50QIxOp1Oho0LUYLkulY8Jte8QjwHCoXCELADlKVdi3ipJet0uvjSLkI8O+kEFkIIIYQQQgghhBBCiNeYzAkshBBCCCGEEEIIIYQQrzHpBBZCCCGEEEIIIYQQQojXmHQCCyGEEEIIIYQQQgghxGtMOoGFEEIIIYQQQgghhBDiNSadwEIIIYQQQgghhBBCCPEak05gIYQQQgghhBBCCCGEeI1JJ7AQQgghhBBCCCGEEEK8xqQTWAghhBBCCCGEEEIIIV5j0gkshBBCCCGEEEIIIYQQrzHpBBZCCCGEEEIIIYQQQojX2P8DGL3XbWzh+6IAAAAASUVORK5CYII="}, "metadata": {"needs_background": "light"}}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Make Policy Decision with EconML <a id=\"policy\"></a>\n", "We want to make policy decisions to maximum the **revenue** instead of the demand. In this scenario,\n", "\n", "\n", "\\begin{align}\n", "Rev & = Y \\cdot T \\\\\n", "    & = \\exp^{log(Y)} \\cdot T\\\\\n", "    & = \\exp^{(\\theta(X) \\cdot log(T) + f(X,W) + \\epsilon)} \\cdot T \\\\\n", "    & = \\exp^{(f(X,W) + \\epsilon)} \\cdot T^{(\\theta(X)+1)}\n", "\\end{align}\n", "\n", "\n", "With the decrease of price, revenue will increase only if $\\theta(X)+1<0$. Thus, we set `sample_treatment_cast=-1` here to learn **what kinds of customers we should give a small discount to maximum the revenue**.\n", "\n", "The EconML library includes policy interpretability tools such as `SingleTreePolicyInterpreter` that take in a treatment cost and the treatment effects to learn simple rules about which customers to target profitably. In the figure below we can see the model recommends to give discount for people with income less than $0.985$ and give original price for the others."], "metadata": {}}, {"cell_type": "code", "execution_count": 15, "source": ["intrp = SingleTreePolicyInterpreter(risk_level=0.05, max_depth=2, min_samples_leaf=1, min_impurity_decrease=0.001)\r\n", "intrp.interpret(est, X_test, sample_treatment_costs=-1)\r\n", "plt.figure(figsize=(25, 5))\r\n", "intrp.plot(feature_names=X.columns, treatment_names=[\"Discount\", \"No-Discount\"], fontsize=12)"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1800x360 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {"scrolled": true}}, {"cell_type": "markdown", "source": ["Now, let us compare our policy with other baseline policies! Our model says which customers to give a small discount to, and for this experiment, we will set a discount level of 10% for those users. Because the model is misspecified we would not expect good results with large discounts. Here, because we know the ground truth, we can evaluate the value of this policy."], "metadata": {}}, {"cell_type": "code", "execution_count": 16, "source": ["# define function to compute revenue\r\n", "def revenue_fn(data, discount_level1, discount_level2, baseline_T, policy):\r\n", "    policy_price = baseline_T * (1 - discount_level1) * policy + baseline_T * (1 - discount_level2) * (1 - policy)\r\n", "    demand = demand_fn(data, policy_price)\r\n", "    rev = demand * policy_price\r\n", "    return rev"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 17, "source": ["policy_dic = {}\r\n", "# our policy above\r\n", "policy = intrp.treat(X)\r\n", "policy_dic[\"Our Policy\"] = np.mean(revenue_fn(train_data, 0, 0.1, 1, policy))\r\n", "\r\n", "## previous strategy\r\n", "policy_dic[\"Previous Strategy\"] = np.mean(train_data[\"price\"] * train_data[\"demand\"])\r\n", "\r\n", "## give everyone discount\r\n", "policy_dic[\"Give Everyone Discount\"] = np.mean(revenue_fn(train_data, 0.1, 0, 1, np.ones(len(X))))\r\n", "\r\n", "## don't give discount\r\n", "policy_dic[\"Give No One Discount\"] = np.mean(revenue_fn(train_data, 0, 0.1, 1, np.ones(len(X))))\r\n", "\r\n", "## follow our policy, but give -10% discount for the group doesn't recommend to give discount\r\n", "policy_dic[\"Our Policy + Give Negative Discount for No-Discount Group\"] = np.mean(revenue_fn(train_data, -0.1, 0.1, 1, policy))\r\n", "\r\n", "## give everyone -10% discount\r\n", "policy_dic[\"Give Everyone Negative Discount\"] = np.mean(revenue_fn(train_data, -0.1, 0, 1, np.ones(len(X))))"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 18, "source": ["# get policy summary table\r\n", "res = pd.DataFrame.from_dict(policy_dic, orient=\"index\", columns=[\"Revenue\"])\r\n", "res[\"Rank\"] = res[\"Revenue\"].rank(ascending=False)\r\n", "res"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                                      Revenue  Rank\n", "Our Policy                                          14.686241   2.0\n", "Previous Strategy                                   14.349342   4.0\n", "Give Everyone Discount                              13.774469   6.0\n", "Give No One Discount                                14.294606   5.0\n", "Our Policy + Give Negative Discount for No-Disc...  15.564411   1.0\n", "Give Everyone Negative Discount                     14.612670   3.0"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue</th>\n", "      <th>Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Our Policy</th>\n", "      <td>14.686241</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Previous Strategy</th>\n", "      <td>14.349342</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Give Everyone Discount</th>\n", "      <td>13.774469</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Give No One Discount</th>\n", "      <td>14.294606</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Our Policy + Give Negative Discount for No-Discount Group</th>\n", "      <td>15.564411</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Give Everyone Negative Discount</th>\n", "      <td>14.612670</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 18}], "metadata": {}}, {"cell_type": "markdown", "source": ["**We beat the baseline policies!** Our policy gets the highest revenue except for the one raising the price for the No-Discount group. That means our currently baseline price is low, but the way we segment the user does help increase the revenue!"], "metadata": {}}, {"cell_type": "markdown", "source": ["# Conclusions <a id=\"conclusion\"></a>\n", "\n", "In this notebook, we have demonstrated the power of using EconML to:\n", "\n", "* Estimate the treatment effect correctly even the model is misspecified\n", "* Interpret the resulting individual-level treatment effects\n", "* Make the policy decision beats the previous and baseline policies\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). "], "metadata": {}}], "metadata": {"kernelspec": {"name": "python3", "display_name": "Python 3.6.6 64-bit"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}, "interpreter": {"hash": "2e5c6628eef985e7fd2fa2aad22c988c5b8aa1d2648cf9c51c543a2a2637c546"}}, "nbformat": 4, "nbformat_minor": 2}