{"cells": [{"cell_type": "markdown", "source": ["<img src=\"https://www.microsoft.com/en-us/research/uploads/prod/2020/05/Attribution.png\" width=\"400\">\n", "\n", "<h1 align=\"left\">Multi-investment Attribution: Distinguish the Effects of Multiple Outreach Efforts</h1>\n", "\n", "A startup that sells software would like to know whether its multiple outreach efforts were successful in attracting new customers or boosting consumption among existing customers. They would also like to distinguish the effects of several incentives on different kinds of customers. In other words, they would like to learn the **heterogeneous treatment effect** of each investment on customers' software usage. \n", "\n", "In an ideal world, the startup would run several randomized experiments where each customer would receive a random assortment of investments. However, this can be logistically prohibitive or strategically unsound: the startup might not have the resources to design such experiments or they might not want to risk losing out on big opportunities due to lack of incentives.\n", "\n", "In this customer scenario walkthrough, we show how tools from the [EconML](https://aka.ms/econml) library can use historical investment data to learn the effects of multiple investments."], "metadata": {}}, {"cell_type": "markdown", "source": ["### Summary\n", "\n", "1. [Background](#Background)\n", "2. [Data](#Data)\n", "3. [Get Causal Effects with EconML](#Get-Causal-Effects-with-EconML)\n", "4. [Understand Treatment Effects with EconML](#Understand-Treatment-Effects-with-EconML)\n", "5. [Make Policy Decisions with EconML](#Make-Policy-Decisions-with-EconML)\n", "6. [Conclusions](#Conclusions)"], "metadata": {}}, {"cell_type": "markdown", "source": ["# Background\n", "\n", "<img src=\"https://get.pxhere.com/photo/update-software-upgrade-laptop-computer-install-program-screen-system-repair-data-development-electronic-load-pc-process-progress-support-technical-load-1565823.jpg\" width=\"400\">\n", "\n", "In this scenario, a startup that sells software provides two types of incentives to its customers: technical support and discounts. A customer might be given one, both or none of these incentives. \n", "\n", "The startup has historical data on these two investments for 2,000 customers, as well as how much revenue these customers generated in the year after the investments were made. They would like to use this data to learn the optimal incentive policy for each existing or new customer in order to maximize the return on investment (ROI).\n", "\n", "The startup faces two challenges: 1) the dataset is biased because historically the larger customers received the most incentives and 2) the observed outcome combines effects from two different investments. Thus, they need a causal model that can accommodate multiple concurrent interventions. \n", "\n", "**Solution:** EconML’s `Doubly Robust Learner` model jointly estimates the effects of multiple discrete treatments. The model uses flexible functions of observed customer features to filter out spurious correlations in existing data and deliver the causal effect of each intervention on revenue.\n"], "metadata": {}}, {"cell_type": "code", "execution_count": 1, "source": ["# Some imports to get us started\r\n", "import warnings\r\n", "warnings.simplefilter('ignore')\r\n", "# Utilities\r\n", "import os\r\n", "import urllib.request\r\n", "import numpy as np\r\n", "import pandas as pd\r\n", "\r\n", "# Generic ML imports\r\n", "from xgboost import XGBRegressor, XGBClassifier\r\n", "\r\n", "# EconML imports\r\n", "from econml.dr import LinearDRLearner\r\n", "\r\n", "import matplotlib.pyplot as plt\r\n", "import seaborn as sns\r\n", "\r\n", "%matplotlib inline"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["# Data\n", "\n", "The data* contains ~2,000 customers and is comprised of:\n", "\n", "* Customer features: details about the industry, size, revenue, and technology profile of each customer.\n", "* Interventions: information about which incentive was given to a customer.\n", "* Outcome: the amount of product the customer bought in the year after the incentives were given.\n", "\n", "Feature Name | Type | Details \n", ":--- |:--- |:--- \n", "**Global Flag** | W | whether the customer has global offices\n", "**Major Flag** | W | whether the customer is a large consumer in their industry (as opposed to SMC - Small Medium Corporation - or SMB - Small Medium Business)\n", "**SMC Flag** | W | whether the customer is a Small Medium Corporation (SMC, as opposed to major and SMB)\n", "**Commercial Flag** | W | whether the customer's business is commercial (as opposed to public secor)\n", "**IT Spend** | W | \\\\$ spent on IT-related purchases \n", "**Employee Count** | W | number of employees\n", "**PC Count** | W | number of PCs used by the customer\n", "**Size** | X | customer's size given by their yearly total revenue \n", "**Tech Support** | T | whether the customer received tech support (binary)\n", "**Discount** | T | whether the customer was given a discount (binary)\n", "**Revenue** | Y | \\\\$ Revenue from customer given by the amount of software purchased\n", "\n", "**To protect the privacy of the startup's customers, the data used in this scenario is synthetically generated and the feature distributions don't correspond to real distributions. However, the feature names have preserved their names and meaning.*"], "metadata": {}}, {"cell_type": "code", "execution_count": 2, "source": ["# Import the sample multi-attribution data\n", "file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/ROI/multi_attribution_sample.csv\"\n", "multi_data = pd.read_csv(file_url)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 3, "source": ["# Data sample\n", "multi_data.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Global Flag  Major Flag  SMC Flag  Commercial Flag  IT Spend  \\\n", "0            1           0         1                0     45537   \n", "1            0           0         1                1     20842   \n", "2            0           0         0                1     82171   \n", "3            0           0         0                0     30288   \n", "4            0           0         1                0     25930   \n", "\n", "   Employee Count  PC Count    <PERSON>ze  Tech Support  Discount      Revenue  \n", "0              26        26  152205             0         1  17688.36300  \n", "1             107        70  159038             0         1  14981.43559  \n", "2              10         7  264935             1         1  32917.13894  \n", "3              40        39   77522             1         1  14773.76855  \n", "4              37        43   91446             1         1  17098.69823  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Global Flag</th>\n", "      <th>Major Flag</th>\n", "      <th>SMC Flag</th>\n", "      <th>Commercial Flag</th>\n", "      <th>IT Spend</th>\n", "      <th>Employee Count</th>\n", "      <th>PC Count</th>\n", "      <th>Size</th>\n", "      <th>Tech Support</th>\n", "      <th>Discount</th>\n", "      <th>Revenue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>45537</td>\n", "      <td>26</td>\n", "      <td>26</td>\n", "      <td>152205</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>17688.36300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>20842</td>\n", "      <td>107</td>\n", "      <td>70</td>\n", "      <td>159038</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>14981.43559</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>82171</td>\n", "      <td>10</td>\n", "      <td>7</td>\n", "      <td>264935</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32917.13894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>30288</td>\n", "      <td>40</td>\n", "      <td>39</td>\n", "      <td>77522</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14773.76855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>25930</td>\n", "      <td>37</td>\n", "      <td>43</td>\n", "      <td>91446</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>17098.69823</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 3}], "metadata": {}}, {"cell_type": "code", "execution_count": 4, "source": ["# Define estimator inputs\n", "T_bin = multi_data[\n", "    [\"Tech Support\", \"Discount\"]\n", "]  # multiple interventions, or treatments\n", "Y = multi_data[\"Revenue\"]  # amount of product purchased, or outcome\n", "X = multi_data[[\"Size\"]]  # heterogeneity feature\n", "W = multi_data.drop(\n", "    columns=[\"Tech Support\", \"Discount\", \"Revenue\", \"Size\"]\n", ")  # controls"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["We investigate below whether the number of investments given is correlated with the size of the customer. We note that the average customer size is larger for more incentives given. "], "metadata": {}}, {"cell_type": "code", "execution_count": 5, "source": ["# Average customer size per incentive combination\n", "multi_data[[\"<PERSON><PERSON>\", \"Tech Support\", \"Discount\"]].groupby(\n", "    by=[\"Tech Support\", \"Discount\"], as_index=False\n", ").mean().astype(int)"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Tech Support  Discount    Size\n", "0             0         0   70943\n", "1             0         1   96466\n", "2             1         0  108978\n", "3             1         1  171466"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Tech Support</th>\n", "      <th>Discount</th>\n", "      <th>Size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>70943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>96466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>108978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>171466</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 5}], "metadata": {}}, {"cell_type": "markdown", "source": ["The data was generated using the following underlying treatment effect function:\n", "\n", "$$\n", "\\text{treatment_effect(Size)} = (5,000 + 2\\% \\cdot \\text{Size}) \\cdot I_\\text{Tech Support} + (5\\% \\cdot \\text{Size}) \\cdot I_\\text{Discount}\n", "$$\n", "\n", "Therefore, the treatment effect depends on the customer's size as follows: tech support provides an consumption boost of \\$5,000 + 2\\% Size and a discount provides an consumption boost of 5\\% Size.**This is the relationship we seek to learn from the data.**"], "metadata": {}}, {"cell_type": "code", "execution_count": 6, "source": ["# Define underlying treatment effect function\n", "TE_fn = lambda X: np.hstack([5000 + 2 / 100 * X, 5 / 100 * X])\n", "true_TE = TE_fn(X)\n", "\n", "# Define true coefficients for the three treatments\n", "# The third coefficient is just the sum of the first two since we assume an additive effect\n", "true_coefs = [2 / 100, 5 / 100, 7 / 100]\n", "true_intercepts = [5000, 0, 5000]\n", "treatment_names = [\"Tech Support\", \"Discount\", \"Tech Support & Discount\"]"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["# Get Causal Effects with EconML"], "metadata": {}}, {"cell_type": "markdown", "source": ["To get causal effects, we use EconML's `LinearDRLearner`* estimator. This estimator requires a set of discrete treatments $T$ that corresponds to different types of interventions. Thus, we first map the binary interventions tech support and discount into one categorical variable:\n", "\n", "Tech support| Discount| Treatment encoding| Details\n", ":--- |:--- |:--- |:---\n", "0 | 0 | 0 | no incentive\n", "1 | 0 | 1 | tech support only\n", "0 | 1 | 2 | discount only\n", "1 | 1 | 3 | both incentives\n", "\n", "The estimator takes as input the outcome of interest $Y$ (amount of product purchased), a discrete treatment $T$ (interventions given), heterogeneity features $X$ (here, customer's size) and controls $W$ (all other customer features).\n", "\n", "\n", "The LinearDRLearner also requires two auxiliary models to model the relationships $T\\sim (W, X)$ (`model_propensity`) and $Y \\sim (W, X)$(`model_regression`). These can be generic, flexible classification and regression models, respectively.  \n", "\n", "\n", "**This estimator assumes a linear relationship between the treatment effect and a transformation of the features $X$ (e.g. a polynomial basis expansion). For more generic forms of the treatment effect, see the `DRLearner` estimator.*"], "metadata": {}}, {"cell_type": "code", "execution_count": 7, "source": ["# Transform T to one-dimensional array with consecutive integer encoding\r\n", "def treat_map(t):\r\n", "    return np.dot(t, 2 ** np.arange(t.shape[0]))\r\n", "\r\n", "\r\n", "T = np.apply_along_axis(treat_map, 1, T_bin).astype(int)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 8, "source": ["# Train EconML model with generic helper models\r\n", "model = LinearDRLearner(\r\n", "    model_regression=XGBRegressor(learning_rate=0.1, max_depth=3),\r\n", "    model_propensity=XGBClassifier(learning_rate=0.1, max_depth=3, objective=\"multi:softprob\"),\r\n", "    random_state=1,\r\n", ")\r\n", "# Specify final stage inference type and fit model\r\n", "model.fit(Y=Y, T=T, X=X, W=W, inference=\"statsmodels\")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<econml.dr._drlearner.LinearDRLearner at 0x237737da668>"]}, "metadata": {}, "execution_count": 8}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Understand Treatment Effects with EconML\n", "\n", "We can obtain a summary of the coefficient values as well as confidence intervals by calling the `summary` function on the fitted model for each treatment."], "metadata": {}}, {"cell_type": "code", "execution_count": 9, "source": ["for i in range(model._d_t[0]):\r\n", "    print(f\"Investment: {treatment_names[i]}\")\r\n", "    print(f\"True treatment effect: {true_intercepts[i]} + {true_coefs[i]}*Size\")\r\n", "    display(model.summary(T=i + 1))"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Investment: Tech Support\n", "True treatment effect: 5000 + 0.02*Size\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                   Coefficient Results                   \n", "=========================================================\n", "     point_estimate stderr zstat pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "Size          0.021  0.012 1.749   0.08   -0.002    0.044\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate  stderr zstat pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept       5326.611 845.551   6.3    0.0 3669.361 6983.861\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""], "text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>   <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>Size</th>      <td>0.021</td>      <td>0.012</td> <td>1.749</td>  <td>0.08</td>   <td>-0.002</td>    <td>0.044</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>    <td>5326.611</td>    <td>845.551</td>  <td>6.3</td>    <td>0.0</td>  <td>3669.361</td> <td>6983.861</td>\n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Investment: Discount\n", "True treatment effect: 0 + 0.05*Size\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                   Coefficient Results                   \n", "=========================================================\n", "     point_estimate stderr zstat pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "Size          0.052  0.012 4.371    0.0    0.029    0.075\n", "                        CATE Intercept Results                       \n", "=====================================================================\n", "               point_estimate  stderr zstat pvalue  ci_lower ci_upper\n", "---------------------------------------------------------------------\n", "cate_intercept        358.699 848.771 0.423  0.673 -1304.861 2022.258\n", "---------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""], "text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>   <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>Size</th>      <td>0.052</td>      <td>0.012</td> <td>4.371</td>   <td>0.0</td>    <td>0.029</td>    <td>0.075</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th>  <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>     <td>358.699</td>    <td>848.771</td> <td>0.423</td>  <td>0.673</td> <td>-1304.861</td> <td>2022.258</td>\n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Investment: Tech Support & Discount\n", "True treatment effect: 5000 + 0.07*Size\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                   Coefficient Results                   \n", "=========================================================\n", "     point_estimate stderr zstat pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "Size          0.074  0.012 6.292    0.0    0.051    0.096\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept       4899.208 851.54 5.753    0.0  3230.22 6568.196\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""], "text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "    <td></td>   <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>Size</th>      <td>0.074</td>      <td>0.012</td> <td>6.292</td>   <td>0.0</td>    <td>0.051</td>    <td>0.096</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>    <td>4899.208</td>    <td>851.54</td> <td>5.753</td>   <td>0.0</td>   <td>3230.22</td> <td>6568.196</td>\n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"]}, "metadata": {}}], "metadata": {}}, {"cell_type": "markdown", "source": ["From the summary panels, we see that the learned coefficients/intercepts are close to the true coefficients/intercepts and the p-values are small for most of these. \n", "\n", "We further use the `coef_, coef__interval` and the `intercept_, intercept__interval` methods to obtain the learned coefficient values and build confidence intervals. We compare the true and the learned coefficients through the plots below."], "metadata": {}}, {"cell_type": "code", "execution_count": 10, "source": ["# Compare learned coefficients with true model coefficients\r\n", "# Aggregate data\r\n", "coef_indices = np.arange(model._d_t[0])\r\n", "coefs = np.hstack([model.coef_(T=i) for i in 1 + coef_indices])\r\n", "intercepts = np.hstack([model.intercept_(T=i) for i in 1 + coef_indices])\r\n", "\r\n", "# Calculate coefficient error bars for 95% confidence interval\r\n", "coef_error = np.hstack([model.coef__interval(T=i) for i in 1 + coef_indices])\r\n", "coef_error[0, :] = coefs - coef_error[0, :]\r\n", "coef_error[1, :] = coef_error[1, :] - coefs\r\n", "\r\n", "# Calculate intercept error bars for 95% confidence interval\r\n", "intercept_error = np.vstack(\r\n", "    [model.intercept__interval(T=i) for i in 1 + coef_indices]\r\n", ").T\r\n", "intercept_error[0, :] = intercepts - intercept_error[0, :]\r\n", "intercept_error[1, :] = intercept_error[1, :] - intercepts"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 11, "source": ["# Plot coefficients\r\n", "plt.figure(figsize=(6, 5))\r\n", "ax1 = plt.subplot(2, 1, 1)\r\n", "plt.errorbar(\r\n", "    coef_indices,\r\n", "    coefs,\r\n", "    coef_error,\r\n", "    fmt=\"o\",\r\n", "    label=\"Learned values\\nand 95% confidence interval\",\r\n", ")\r\n", "plt.scatter(coef_indices, true_coefs, color=\"C1\", label=\"True values\", zorder=3)\r\n", "plt.xticks(coef_indices, treatment_names)\r\n", "plt.setp(ax1.get_xticklabels(), visible=False)\r\n", "plt.title(\"Coefficients\")\r\n", "plt.legend(loc=(1.05, 0.65))\r\n", "plt.grid()\r\n", "\r\n", "# Plot intercepts\r\n", "plt.subplot(2, 1, 2)\r\n", "plt.errorbar(coef_indices, intercepts, intercept_error, fmt=\"o\")\r\n", "plt.scatter(coef_indices, true_intercepts, color=\"C1\", zorder=3)\r\n", "plt.xticks(coef_indices, treatment_names)\r\n", "plt.title(\"Intercepts\")\r\n", "plt.grid()\r\n", "plt.show()"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x360 with 2 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Make Policy Decisions with EconML\n", "\n", "Investments such as tech support and discounts come with an associated cost. Thus, we would like to know what incentives to give to each customer to maximize the profit from their increased engagement. This is the **treatment policy**.\n", "\n", "In this scenario, we define a cost function as follows:\n", "* The cost of `tech support` scales with the number of PCs a customer has. You can imagine that if the software product needs tech support to be installed on each machine, there is a cost (\\\\$100 here) per machine.\n", "* The cost of `discount` is a fixed \\\\$7,000. Think of this as giving the customer the first \\\\$7,000 worth of product for free.\n", "* The cost of `tech support` and `discount` is the sum of the cost of each of these. Note that this might not be the case in every business application: it is possible that managing multiple incentive programs can add overhead. "], "metadata": {}}, {"cell_type": "code", "execution_count": 12, "source": ["# Define cost function\r\n", "def cost_fn(multi_data):\r\n", "    t1_cost = multi_data[[\"PC Count\"]].values * 100\r\n", "    t2_cost = np.ones((multi_data.shape[0], 1)) * 7000\r\n", "    return np.hstack([t1_cost, t2_cost, t1_cost + t2_cost])"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["We use the model's `const_marginal_effect` method to find the counterfactual treatment effect for each possible treatment. We then subtract the treatment cost and choose the treatment which the highest return. That is the recommended policy."], "metadata": {}}, {"cell_type": "code", "execution_count": 13, "source": ["# Get roi for each customer and possible treatment\r\n", "potential_roi = model.const_marginal_effect(X=X.values) - cost_fn(multi_data)\r\n", "# Add a column of 0s for no treatment\r\n", "potential_roi = np.hstack([np.zeros((X.shape[0], 1)), potential_roi])"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 14, "source": ["all_treatments = np.array([\"None\"] + treatment_names)\r\n", "recommended_T = np.argmax(potential_roi, axis=1)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 15, "source": ["ax1 = sns.scatterplot(\r\n", "    x=X.iloc[:, 0].values.flatten(),\r\n", "    y=multi_data[\"PC Count\"].values,\r\n", "    hue=all_treatments[recommended_T],\r\n", "    hue_order=all_treatments,\r\n", "    cmap=\"Dark2\",\r\n", "    s=40,\r\n", ")\r\n", "plt.legend(title=\"Investment Policy\")\r\n", "plt.setp(\r\n", "    ax1,\r\n", "    xlabel=\"Customer Size\",\r\n", "    ylabel=\"PC Count\",\r\n", "    title=\"Optimal Investment Policy by Customer\",\r\n", ")\r\n", "plt.show()"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAZYAAAEWCAYAAABFSLFOAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAADpqElEQVR4nOydd3gUVdfAf3d7Nr0nJEBoAQKE0JtUpaigUkQQFWxgF7DrK2L5LK++Koi9UEVABEXARlOkt9B7TQKk9832+/2xmyVLAgRIqPN7njzZOXPnzt3Z3Tlzzzn3HCGlREFBQUFBoapQXe4BKCgoKChcWyiKRUFBQUGhSlEUi4KCgoJClaIoFgUFBQWFKkVRLAoKCgoKVYqiWBQUFBQUqhRFsVyDCCFqCSGKhBDqauh7vBBiRlX3q3BmhBBxQggphNC4t38TQgyv4nMcEULcVJV9Kly/KIrlCkAIMUIIsV0IYRJCnBRCfC6ECDqP471uClLKY1JKPymlo1oGfOZxdBNCpF7Kc15h518hhHjoLPtLFUSR+++IEOLF8z2PlPJmKeXUixtt9SGECBBCfCyEOOZ+nwfd22EX2a+i/K4SFMVymRFCPAO8BzwHBALtgdrAX0II3eUcm0K1ESSl9AOGAuOEEH0u94CqCvd3dinQBOgDBAAdgGyg7WUcWpUhXCj3zrMhpVT+LtMfrh9dETD4NLkfkAk84N4eD8wFZgOFwGaguXvfdMAJlLj7eh6IAySgcbdZAbwFrHa3+RUIBb4HCoANQFyZ808AUtz7NgGdy+wbD8w4w/vpBqSW2V4BvAmsco/7TyDMve834InTjt8KDHC/bgT8BeQAe8teI+AWYJe7zzTgWcDXfQ2c7vdYBNRwj/dHYIa7/XYgHngJyHC/z15l+g4EvgVOuPt+C1C7940A/gU+AHKBw8DN7n3/BzgAs/vckyq4Pl6fi1u2wT1+FfAf4Kh7XNOAwIqOc1/Xh8r08TCw2/3+dgEtcT2o/HTa+ScCE87w2R1xX5Nd7vc2GTC49+0A+pVpqwWygBYV9PMQkA74neV7L4H6ZbanAG+5X4cBC4E892e/0n1tyn3P3e1vA3a6268AGp/2np4DtgHF7s81Etd3rxBYAgSXad8e128kD9d3sdtp3+X/w/VdLik7fuWvgs/4cg/gev7D9URnL3ujKbNvKvCD+/V4wAYMcv+on8V1U9O69x8BbipzbEU3ogNAPVw3zl3APuAmQIPrJja5zPH34FI8GuAZ4GSZm8x4zk+xHMR1I/dxb7/r3ncfsKpM2wT3D1qPS0mkAPe7x9AC140swd32BG5lBwQDLSs6f5nxmoHeZd7rYeAV97V8GDhcpv184Ev3GCKA9cAo974R7s/hYUANPAocB0SZ9/tQRdfm9M8FEEAnwATcCDzg/ozq4nqwmAdMP8vn+ZD79Z24FGAbd5/1cc14o3HdTIPc7TS4FFarM4ztCC4FUhMIwXUDLb3ZPw/MLtP2dmD7GfqZBUw9x/f+bIrlHeAL92ejBTqXub5H8P6ex7vfY0932+fd11BXpv1aXMokxv3+N+P6PhmAZcBr7rYxuGZVt+BSZD3d2+FlrvkxXDMxDe7fnvJX8Z8ynbu8hAFZUkp7BftOuPeXsklKOVdKaQM+xPXDaH8e55ospTwopczH9cR2UEq5xH3uH3H92ACQUs6QUmZLKe1Syv/hutk3PL+35nXefVLKEmAOkOSWzweShBC13dvDgHlSSgvQFzgipZzsHsMW4CdcN1Fw3dwThBABUspcKeXmc4xhpZTyjzLvNRyXgrPhuhHGCSGChBCRuG4so6WUxVLKDOAjYEiZvo5KKb+WLv/VVFw38MjzvCZZuJ7GvwFelFIudb//D6WUh6SURbhmD0NKHfZn4SHgv1LKDdLFASnlUSnlCeAfTl2zPri+a5vO0tckKWWKlDIH19P5ULd8BnCLECLAvX0vrhlERYTi+u5eKDZc17S2lNImpVwp3Xf2CrgLWCSl/Mv9WX6A6wGmY5k2n0gp06WUabhmP+uklFuklGZc38HS7/09wGIp5WIppVNK+RewEdf3oZQpUsqd7u+k7SLe4zWPolguL1lA2BluHtHu/aWklL6QUjqBVFymnsqSXuZ1SQXbfqUbQohnhRC7hRD5Qog8XLOcC3W8nizz2lR6HillIbCIUzftobhMc+B64m4nhMgr/cN1441y7x+I6wd/VAjxtxCiwznGcPp7zZKnAhtK3P/93OfVAifKnPdLXDOXcu9HSmkqc+z5ECalDJZSNpZSTnTLauAyg5VyFNeT8bmUVk1cs8KKmIrrhon7/5mUQSkpZV4fdY8JKeVxXDOYge6gkps59VmdTjau7+6F8j6uWcefQohD5whu8Lpm7t9FCq7ZRymV/d7XBu487Tt3A97vpez1UTgLimK5vKwBLMCAskIhhB+uH+/SMuKaZfargFhcZhhwmRaqBCFEZ1wmhcG47M9BQD4uM0tV8wMw1K0YDMBytzwF+FtKGVTmz09K+SiA++n8dlw3/J9xzYTg4q9DCq7PI6zMeQOklE0qefzFnP84rptbKbVwmUnTK27uIQWXibMifgYShRBNcc0Cz6QMSqlZ5nUtTn2/4JSSuhNY454BVMQSoLcQwvcs5zEBxjLbpQ8MSCkLpZTPSCnr4vKfjBVC3Fi6+7R+vK6ZEEK438OZxnY2UnCZHst+53yllO+WaaOkgq8kimK5jLjNUq8Dnwgh+gghtEKIOFw3ylS8nzBbCSEGuGc3o3HdANe696Xjss1XBf64bmiZgEYIMQ5XkEF1sBjXjeENXDZ8p1u+EIgXQtzrviZaIUQbIURjIYROCDFMCBHoNkcU4HLqgus6hAohAi9kMG7z0Z/A/9whsyohRD0hRNdKdnExn8MPwBghRB33g8XbuK5JRWbSsnwDPCuEaOWOVqpfal50m3vmAjOB9VLKY+fo63EhRKwQIgSXD2p2mX0/4woKeBqXn+pMTMd1k/5JCNHIfQ1DhRAvCyFKzUrJwN1CCLU7Is5zfYUQfd3vQeB6oHHg/fmWvb5zgFuFEDcKIbS4/IEWXA7482UG0E8I0ds9LoM7fD32Avq67lEUy2VGSvlf4GVc9uECYB2uH+aNbn9DKb/gsinn4rJxDyhj530H+I97Cv/sRQ7pD+B3XM79o7gc39ViAnC/v3m4gghmlpEXAr1wmcmO4zI/vYfL1wOu939ECFEAPILLTIaUcg+uG/Qh97U4H1NhKfcBOk5FR82l8qadCcAgIUSuEGLiOVt78x2um/I/uIILzMCT5zpISvkjLn/ITFyRTj/jcr6XMhVoxrnNYLj7+BM4hMu89laZ85Tg8nPVwfWZnWk8Flyf5x5cUX0FuAIgwnB9t8GlnPrhCtYY5h5zKQ1wzXqKcM3oP5NSls5kvb7nUsq9uGZRn+AyG/fDFb1mrcR7PX3cKbiCEl7G9VCVgiuiTLlHXgCl0RYKVzBCiPG4omjuOVdbBYWyCCFq4brJR0kpCy6yr3FAvPI9VDgX54o4UVBQuEpx++LGArOqQKmEAA/imi0qKJwVZZqnoHAN4naeF+Baj/HaRfb1MC7T0G9Syn+qYHgK1ziKKUxBQUFBoUpRZiwKCgoKClXKVe1jCQsLk3FxcZd7GAoKCgpXFZs2bcqSUoZXV/9XtWKJi4tj48aNl3sYCgoKClcVQoij52514SimMAUFBQWFKkVRLAoKCgoKVYqiWBQUFBQUqpSr2seioHC1YLPZSE1NxWw2X+6hKFxHGAwGYmNj0Wq1l/S8imJRULgEpKam4u/vT1xcHK78igoK1YuUkuzsbFJTU6lTp84lPfd1awrLM1nZnprHp8sP8O/+LLKLLOc+SEHhAjGbzYSGhipKReGSIYQgNDT0ssySr8sZS7HFzuRVR5iwdL9H1r1hOP8bnESIr+4yjkzhWkZRKgqXmsv1nbsuZyyFZhufrTjgJVu+N5MsZdaioKCgcNFcl4rF5pDYHOVzpCnmMIXrCT+/862ofGH8/PPP7Nq167KeZ/z48cTExJCUlETTpk1ZsGDBWfuKi4sjK8tVGbxjx45VPtZrnWpXLO5qbFuEEAvd23WEEOuEEAeEELOFEDq3XO/ePuDeH1ddY/LRqqkb5lteFn5pfmgKCtcTV4JiARgzZgzJycn8+OOPPPDAAzidzjO2Lcvq1RdSkPL65lLMWJ4GdpfZfg/4SEpZH1eFvgfd8geBXLf8I3e7aiHMX8+UB9rSJi4YgPoRfswa2Z5g46UNyVNQuBJYsWIF3bp1Y9CgQTRq1Ihhw4YhpeT333/nzjvv9GrXt29fAP788086dOhAy5YtufPOOykqKgLgxRdfJCEhgcTERJ599llWr17NggULeO6550hKSuLgwYN069aNMWPG0Lp1axo3bsyGDRsYMGAADRo04D//+Y/nfDNmzKBt27YkJSUxatQoHA4H4JppvfLKKzRv3pz27duTnp5e4XnOROPGjdFoNGRlZfHDDz/QrFkzmjZtygsvvFBh+7Izu/fee49mzZrRvHlzXnzxRQ4ePEjLli09+/fv3++1fd0ipay2PyAWWAr0wFXHXOAqIapx7+8A/OF+/QfQwf1a424nztZ/q1at5MWQU2yR6fklMrPQfFH9KCici127dl3uIZTD19dXSinl8uXLZUBAgExJSZEOh0O2b99erly5UtpsNlmzZk1ZVFQkpZTykUcekdOnT5eZmZmyc+fOHvm7774rX3/9dZmVlSXj4+Ol0+mUUkqZm5srpZRy+PDh8scff/Sct2vXrvL555+XUkr58ccfy+joaHn8+HFpNptlTEyMzMrKkrt27ZJ9+/aVVqtVSinlo48+KqdOnSqllBKQCxYskFJK+dxzz8k333yzwvOU5bXXXpPvv/++lFLKtWvXyujoaJmamipr1qwpMzIypM1mk927d5fz58+XUkpZu3ZtmZmZ6XWdFi9eLDt06CCLi4ullFJmZ2dLKaXs1q2b3LJli5RSypdeeklOnDjx/D+MaqSi7x6wUVbjvb+6ZywfA88DpXPOUCBPSml3b6cCMe7XMbhrq7v357vbeyGEGCmE2CiE2JiZmXlRgws26ogIMBDmpz93YwWFa5i2bdsSGxuLSqUiKSmJI0eOoNFo6NOnD7/++it2u51FixZx++23s3btWnbt2kWnTp1ISkpi6tSpHD16lMDAQAwGAw8++CDz5s3DaDSe8Xy33XYbAM2aNaNJkyZER0ej1+upW7cuKSkpLF26lE2bNtGmTRuSkpJYunQphw4dAkCn03lmTq1ateLIkSOVeo8fffQRSUlJPPvss8yePZuNGzfSrVs3wsPD0Wg0DBs2jH/+OXMdsyVLlnD//fd73ldISAgADz30EJMnT8bhcDB79mzuvvvuSo3nWqbawo2FEH2BDCnlJiFEt6rqV0r5FfAVQOvWrZUqZQoKVYBef+rhSq1WY7e7nv2GDBnCpEmTCAkJoXXr1vj7+yOlpGfPnvzwww/l+lm/fj1Lly5l7ty5TJo0iWXLlp31fCqVyuvcKpUKu92OlJLhw4fzzjvvlDtWq9V6wmjLjvVcjBkzhmeffdaz/csvv1TquHMxcOBAXn/9dXr06EGrVq0IDS33PHzdUZ0zlk7AbUKII8AsXOawCUCQEKJUocUCae7XaUBNAPf+QCC7GsenoKBwDrp27crmzZv5+uuvGTJkCADt27dn1apVHDjgCtkvLi5m3759FBUVkZ+fzy233MJHH33E1q1bAfD396ewsPC8znvjjTcyd+5cMjIyAMjJyeHo0bNnej/f87Rt25a///6brKwsHA4HP/zwA127dj1j+549ezJ58mRMJpNnTOBKm9K7d28effRR7r///kqf/1qm2hSLlPIlKWWslDIOGAIsk1IOA5YDg9zNhgOljw0L3Nu49y9z2wIVFBQuE2q1mr59+/Lbb795zE/h4eFMmTKFoUOHkpiYSIcOHdizZw+FhYX07duXxMREbrjhBj788EPANet5//33adGixVmd6mVJSEjgrbfeolevXiQmJtKzZ09OnDhx1mPO9zzR0dG8++67dO/enebNm9OqVStuv/32M7bv06cPt912G61btyYpKYkPPvjAs2/YsGGoVCp69epVqfd3rXNJat67TWHPSin7CiHq4prBhABbgHuklBYhhAGYDrQAcoAhUspDZ+u3devWUin0pXA1sHv3bho3bny5h6FQTXzwwQfk5+fz5ptvXu6hlKOi754QYpOUsnV1nfOSpHSRUq4AVrhfHwLaVtDGDNx5ulxBQUHhSqZ///4cPHjwjP6k65HrMleYgoKCQlUxf/78yz2EK47rMqWLgoKCgkL1oSgWBQUFBYUqRVEsCgoKCgpViqJYFBQUFBSqFEWxKChcJwgheOaZZzzbH3zwAePHj798A1K4ZlEUy1WIw+kgqySLPTl7SClIIc+cd7mHpHAVoNfrmTdvnqfOiIJCdaEolquQowVHGfDLAO789U5umX8L49eMJ8ecc7mHpVCF/LwljU7vLqPOi4vo9O4yft6Sdu6DzoFGo2HkyJF89NFH5fYdOXKEHj16kJiYyI033sixY8cAGDFiBE899RQdO3akbt26zJ0713PM+++/T5s2bUhMTOS111676PEpXDsoiuUqI8+cx7jV48i15HpkS48tZVdW9RdSUrg0/LwljZfmbSctrwQJpOWV8NK87VWiXB5//HG+//578vPzveRPPvkkw4cPZ9u2bQwbNoynnnrKs+/EiRP8+++/LFy4kBdffBFw1WPZv38/69evJzk5mU2bNp01M7DC9YWiWK4ybE4bu7LLK5GN6Upqm2uF9//YS4nN4SUrsTl4/4+9F913QEAA9913HxMnTvSSr1mzxpPu/d577+Xff//17LvjjjtQqVQkJCSQnp4OuBTLn3/+SYsWLWjZsiV79uxh//79Fz0+hWsDZeX9VYZOrSMpIokNJzd4yTvU6HCZRqRQ1RzPKzkv+fkyevRoWrZsWelMvGXT2pfmFpRS8tJLLzFq1KgqGZPCtYUyY7nKCNQH8nqH16nhWwMAgWBQg0HEB8df5pEpVBU1gnzOS36+hISEMHjwYL799luPrGPHjsyaNQuA77//ns6dO5+1j969e/Pdd995ShKnpaV5UtwrKCgzlquQWP9Yvr/1e0w2Ezq1DqPGSIA+4HIPS6GKeK53Q16at93LHOajVfNc74ZVdo5nnnmGSZMmebY/+eQT7r//ft5//33Cw8OZPHnyWY/v1asXu3fvpkMH10zZz8+PGTNmEBERUWVjVLh6uSRp86sLJW2+wtXC+abN/3lLGu//sZfjeSXUCPLhud4NuaNFzLkPVFA4jWs2bb6CgsL5cUeLGEWRKFy1VJuPRQhhEEKsF0JsFULsFEK87pZPEUIcFkIku/+S3HIhhJgohDgghNgmhGhZXWNTUFBQUKg+qnPGYgF6SCmLhBBa4F8hxG/ufc9JKeee1v5moIH7rx3wufu/goKCgsJVRLUpFne9+iL3ptb9dzaHzu3ANPdxa4UQQUKIaCnl2QtdX8HYHU6yiqxsPJqDj1ZNs9hAIvwNl3tYCgoKCtVKtfpYhBBqYBNQH/hUSrlOCPEo8H9CiHHAUuBFKaUFiAFSyhye6padOK3PkcBIgFq1alXn8C+ao9kmbpv0L8VWV3RPdKCBnx/vRGSAolwUFBSuXap1HYuU0iGlTAJigbZCiKbAS0AjoA0QArxwnn1+JaVsLaVsHR4eXtVDrjIKzTbeXrzbo1QATuSb+X7dURxO52UcmYKCgkL1ckkWSEop84DlQB8p5QnpwgJMBtq6m6UBNcscFuuWXZVY7E6OZBeXk+89WYjNcfWGeCtcnWRnZ5OUlERSUhJRUVHExMR4tq1W6zmPX7FiBX379j1nu7Vr19KuXTuSkpJo3LjxZU3Lv2LFClavXn3Zzn89U22mMCFEOGCTUuYJIXyAnsB7pX4TIYQA7gB2uA9ZADwhhJiFy2mffzX7VwINWvo0jeLT5Qe95P1bxGDQqi/TqK48nNJJdkk2a46vweww0yW2C6GGULRq7eUe2jVFaGgoycnJAIwfPx4/Pz+effbZKj/P8OHDmTNnDs2bN8fhcLB378XnN7sQ7HY7K1aswM/Pj44dO16WMVzPVKePJRqY6vazqIA5UsqFQohlbqUjgGTgEXf7xcAtwAHABFQukdEVilaj4v5OdTiWbWLR9hNo1Sru7xRH2zohl3toVxQZpgwG/zrYk63ZoDbwQ98fqB9U/zKP7DKzbQ4sfQPyUyEwFm4cB4mDq/QUmzZtYuzYsRQVFREWFsaUKVOIjo7mwIEDPPLII2RmZqJWq/nxxx8BKCoqYtCgQezYsYNWrVoxY8YMXM+Hp8jIyCA6OhoAtVpNQkICUF6ZNW3alIULFwLQp08fWrVqxebNm2nSpAnTpk3DaDQSFxfH4MGD+e233/Dx8WHmzJnUr1+fI0eO8MADD5CVleXJElCrVi1GjBiBwWBgy5YtxMTEsHr1atRqNTNmzOCTTz45Z5oahaqj2kxhUsptUsoWUspEKWVTKeUbbnkPKWUzt+weKWWRWy6llI9LKeu591frkvqCEht7TxbyybL9LN5+gsxCS5WfI8xPz9sDmrH6xRv55/nuPH1jA0J89ec+8DrB7rQzY/cMrxIAZoeZDzd+SJG16CxHXuNsmwO/PgX5KYB0/f/1KZe8ipBS8uSTTzJ37lw2bdrEAw88wCuvvALAsGHDePzxx9m6dSurV6/2KIotW7bw8ccfs2vXLg4dOsSqVavK9TtmzBgaNmxI//79+fLLLzGbzeccy969e3nsscfYvXs3AQEBfPbZZ559gYGBbN++nSeeeILRo0cDZ0/xn5qayurVq5k3bx6PPPIIY8aMITk5WVEql5jrMgmlzeHkj50n6f3xP/zvz3089v1m7v12HVnVoFz8DVqiAg1EBhjw0SmJDspic9o4VnCsnPxk8UmsjnPb/a9Zlr4BttMyGdtKXPIqwmKxsGPHDnr27ElSUhJvvfUWqampFBYWkpaWRv/+/QEwGAwYjUYA2rZtS2xsLCqViqSkJI4cOVKu33HjxrFx40Z69erFzJkz6dOnzznHUrNmTTp16gTAPffc45Wyf+jQoZ7/a9asAc6e4v/OO+9ErVZMzZeb6/JOl1ts5f8W7/aS7TlZyK4TBXTxv3Ijza41fDQ+9K/fn+Upy73kt9S55fpOqpmfen7yC0BKSZMmTTw361IKCwvPeEzZ9PlqtRq73V5hu3r16vHoo4/y8MMPEx4eTnZ2NhqNBmeZaMiyM5nTzWllt8/0+kz4+vqes41C9XNdzlicEvJMtnLytCqqd3E9U2gtJN2UToYpA5PNdM72LSJb8FSLp/DT+qFT6RjacCgD4weiUV2XzzwuAmPPT34B6PV6MjMzPYrFZrOxc+dO/P39iY2N5eeffwZcMxuT6dyfYymLFi3y1GzZv38/arWaoKAg4uLi2Lx5MwCbN2/m8OHDnmOOHTvmGcfMmTO54YYbPPtmz57t+V+aSbmyKf79/f3PqigVqo/rUrH4aNV0jQ/zkqlVghvqh53hiOolu8jC8bwS0gvMWO1X7xqXnJIc3ljzBr3m9qLPT334NPlTcs25Zz0mSB/EfQn3seCOBfw+8HfGtBpDsCH4Eo34CuXGcaA9rfaK1sclryJUKhVz587lhRdeoHnz5iQlJXlCc6dPn87EiRNJTEykY8eOnDx5stL9Tp8+nYYNG5KUlMS9997L999/j1qtZuDAgeTk5NCkSRMmTZpEfPyp+kENGzbk008/pXHjxuTm5vLoo4969uXm5pKYmMiECRP46KOPAFeK/8mTJ5OYmMj06dOZMGFChWPp168f8+fPJykpiZUrV17IZVK4QK7btPnpBWbG/bKDJbsziAny4a07mtI6LhjjJfaDpOSYeGTGJnYeLyDAoOG1fgn0ahKFv+HqCre1OW18ufVLvtz2pZf8/S7v06fOue3sFZFvyafEXoJKqPDV+uKrvXrNHOebNv9SRIVdCRw5coS+ffuyY8eOcvvi4uLYuHEjYWGX54HvWkFJm38JiQww8MGdzSmxOVAhCPXTVcqGW5XkFFt48oct7DxeAECB2c4zP25jee2Qq06xFFgKWHJsSTn5osOL6BrbFZ/Tn8DPQVZJFuNWjWNl2ko0Kg1DGg5hZOLI62c2kzj4mlQkCtcH16UprBR/g5YIfwNh/vpLrlTAtTo/OSWvnHztwexLPpaLRa/WUzugdjl5g6AG573Y0Ww38832b1iZ5jJflIYlb07fXCVjVbhyiIuLq3C2Aq7ZjDJbuTq5rhXL5UatEoT7l1/XUi/C7zKM5uLw0/kxpuUY/LSnxh5pjGRIoyHn7YgvshXxd8rf5eS/Hfnt+g5DVlC4SrhuTWFXAqFGHe8OaMbD0zbidLu6OtUPpV741elLqOlfk1/u+IWtmVsxqA00Dm1MmM/5P3Hq1XrqBNYhtcg7vDYhNOH6jhZTULhKUH6lp2GxO1AJgVZd/ZM5tVpF+7qh/PN8d7YcyyM22IdaIUZC/a6M1fkOpwOr04pBbaiUqVCtUhNhjKBn7Z4XdV5/nT/PtXmOLRlbKLK5VuDX8q/FbfVuQyWUSXZlcUonAnFZzLwK1zeKYnFTaLZxKKuYb1YewlenYWSXutQI8jmvhJFFFhtFZgcWuwOjTkNYJQICfPUafPUaYoONF/sWqpSskiwWHlzI5ozNdIntQo9aPQgxXLo8Z7X8a/HLHb+wO3s3Rq2ROoF1Lmj2cz1id9ox2UzkWfLQq/WEGELQqDSKglG4ZCiKxc3WlHzu+XadZ3ve5jR+H92ZuuGV83fkmax8++9hPl9xELtTEhvsw/QH21En7Ooza+WU5PDokkfZk7MHgOUpy1mZupLXO71OkD7okoyhdPYTYYy4JOe7VnBKJznmHDJNmQAUUkieJY+6gXUx6Aw0a9YMm82GRqPhvvvuY8yYMahUKjZu3Mi0adOYOHHiZRv722+/zcsvv3zZzq9QdSh2BVwpXj78a5+XzOpw8vXKw9gcZ1+wWGS2YbY5OJFv5pNlB7C7nSWpuSU8PWsLOUVXn7M5y5zlUSqlLEtZRsnp+asUrjgcTgfZJd5RhXanHZPdhI+PD8nJyezcuZO//vqL3377jddffx2A1q1bX1alAi7FonBtoCgWwCklFrujnNxkteN0VryANM9kZdmedJ6YuYVX5m8H4KbG3k/X21LzsV2F1SLtzopzQDlk+WukUD0sOrSIXnN7kTg1kV5ze7Ho0KJKH1vRoufTZREREXz11VdMmjQJKaVXIa+///7bUwSsRYsWnrQo7733Hs2aNaN58+a8+OKLACQnJ9O+fXsSExPp378/ubmuTAvdunWjdPFyVlYWcXFxAEyZMoUBAwbQp08fGjRowPPPPw/Aiy++SElJCUlJSQwbNuw8rpTClYiiWIAQXx0P3lCnnPyhG+qgr8DH4nA4Wbz9JA9M2ciKfZn8tDmN/p+t4vHu9THqTrWvF+6LWnX12bUjjBFE+UZ5yZqGNcWoubL8QNcqiw4tYvzq8ZwoPoFEcqL4BONXj6+UclEJFYGGwHKyirIW1K1bF4fDQUZGhpf8gw8+4NNPPyU5OZmVK1fi4+PDb7/9xi+//MK6devYunWrRyHcd999vPfee2zbto1mzZp5ZkBnIzk5mdmzZ7N9+3Zmz55NSkoK7777rmdG9f3335+zD4UrG0Wx4Mqa2qNRBB8Obk6TGgG0rh3MzIfaEXcG/0iOycZnKw54ycw2J3/tSqdzA1d2ZKNOzf8GJxF2hUR4nQ9hPmFM6zONvnX7UiewDkMaDmFi94mE+ChFyi4FEzZPwOzwrmNidpiZsLninFhlUavURBojCTeGo9fo8df5UzewLmpV5YNQOnXqxNixY5k4cSJ5eXloNBqWLFnC/fff70mhHxISQn5+Pnl5eXTt2hVwVY/8559/ztn/jTfeSGBgIAaDgYSEBI4ePVrpsSlcHVRnaWID8A+gd59nrpTyNSFEHWAWEApsAu6VUlqFEHpgGtAKyAbuklIeqa7xnU6QUUf/FjF0jQ9HpRIEG3VnbCuR2M9Qt/6N2xIY2aUONYONBBmvrrQsZYn2i+bV9q9SYi/BV+uLQWO43EO6bjhZXHHSxzPJT0ej0hDuE06IIQSBOKNSOXToEGq1moiICHbvPlVG4sUXX+TWW29l8eLFdOrUiT/++OO830PZNPmnF/uqbPp9hauX6pyxWIAeUsrmQBLQRwjRHngP+EhKWR/IBR50t38QyHXLP3K3u6QIIQj1059VqQCEGHU81NnbdKZVC+5qU5PIQB9qhRrJLraSmltCTnHVFw+7VBi1RkJ9QhWlcok53Qx5LnlFCCHQqDRnVCqZmZk88sgjPPHEE+XCkA8ePEizZs144YUXaNOmDXv27KFnz55MnjzZk0I/JyeHwMBAgoODPZmDp0+f7pm9xMXFsWnTJgDmzp1bqTFrtVpstvLlLBSuPqptxiJd3sLS+rJa958EegB3u+VTgfHA58Dt7tcAc4FJQgghr8D0yxq1ioEtYwnx1TFj7TGCfbU816shkQEGUnJMDP5yDSfyXU9p7eqE8OmwllelSex0skqyyC7JRqPSEKwPVkxj1cTTLZ9m/OrxXuYwg9rA0y2fvqh+S53jpeHG9957L2PHji3X7uOPP2b58uWoVCqaNGnCzTffjF6vJzk5mdatW6PT6bjlllt4++23mTp1Ko888ggmk4m6desyefJkAJ599lkGDx7MV199xa233lqp8Y0cOZLExERatmyp+Fmucqo1bb4QQo3L3FUf+BR4H1jrnpUghKgJ/CalbCqE2AH0kVKmuvcdBNpJKbNO63MkMBKgVq1arS6nfVZKSZ7JhlYt8DNoKTTbeO7Hbfy+09tk8d7ARO5qU/MyjfIURdYirA4rAfqA806NcrL4JCN+H0FaURoACSEJfHrTp8qixUpyvmnzFx1axITNEzhZfJIo3yiebvk0t9at3A1aQaEs11zafCmlA0gSQgQB84FGVdDnV8BX4KrHcrH9XQxCCIJ9T5nNzDYHW1PzyrVbdSCLgS1j0Jxnmhibw0lusZWjOSaCjVpCfPWE+J7dTFcRVoeVYwXH+HDTh5wsPsktdW5hYPzASqegL7GVMGnLJI9SAdiVs4tfD/7KfQn3nZdjWKFy3Fr3VkWRKFy1XJKV91LKPCHEcqADECSE0Egp7UAsUHq3SgNqAqlCCA0QiMuJf9Xgo9PQJi6YBVtPeMl7NIo4b6UCsD+9iLu+XEOhxeXc7BYfzv8GNz/vXGI55hyGLhrqMa1M2DKBjJIMxrQag4/m3HVSTHYTO7LLpzbflL6JuxrehVGlhCErKCicotqc90KIcPdMBSGED9AT2A0sBwa5mw0HfnG/XuDexr1/2ZXoXzkbfnoNL93cmPpl0t73bhJJ5wYuc1GxxU5GoZki87kdlNlFFp6bu9WjVABW7Mtke1r+eY/rn9R/yoWv/rTvJ4qsRWc4whtfrS9tItuUk3eO6aw49hUUFMpRnTOWaGCq28+iAuZIKRcKIXYBs4QQbwFbgG/d7b8FpgshDgA5wJBqHFu1ER3kw6yH21NktaNVqTDq1QQbdZzIK+Hd3/ew8UguLWoF8dLNjYkJPvNswSEle04WlpNvOppLt4bnlz9Lry4/w9GpK18x06AxMDJxJNuytrErexfgUio943oq2YYVFBTKUZ1RYduAFhXIDwFtK5CbgTurazyXkjB/PWGcuplnFVkY9s06DmUVA5CWV0JySh7zH+tUYaEvAJ1aRdu4ENYc8rYGdmsYft7j6VijI8H6YHItuR7Zg00fxF/rX+k+wo3hfH7T55hsJlRChVFrvGQJKRUUFK4ulOzGl4Ais92jVEpJzS0h12Q9o2IJMur476BEhn+3nkNZxahVggdvqEPdsPOvLhnqE8qcfnOYsXsGKQUp3FH/DlpEtkCvOT9fTYgh5JKmzldQULg6ue7tGCVWBxkFZrKLqm8ho0ZdsclJdw6Hfs0QI7NHdeDv57qx6oUePNmjvlcUWmVRCRVRvlGMbjmad7u8S/da3ZXZxnVGdna2J7FkVFQUMTExnm2r9dwZuMsmqTwba9eupV27diQlJdG4cWPGjx9fBaO/MFasWMHq1avPuP/XX38lISGBpk2b8sorr5yx3ZQpUwgPD6dFixY0aNCA3r17e/U7btw4lixZUqVjPx+Sk5NZvHjxZTt/RVzXM5asQgsfL9nHou0niAo0ML5fE5rFBmLUVe1l8dVp6NEogmV7TiX761A3hACfc6d8cc1oqmZxpUalUUr7XqeEhoaSnJwMwPjx4/Hz8+PZZ5+t8vMMHz6cOXPm0Lx5cxwOB3v37q3yc1QGu93OihUr8PPzo2PHjhW2GT16NEuWLKFOnTocPnz4rP3dddddTJo0CYDly5czYMAAli9fTuPGjXnjjTeqfPznQ3JyMhs3buSWW265rOMoy3U7Yymy2Pi/xbuYse4YuSYbu08Ucvc368gqrPqZS7CvjvcHJfK/O5vTLzGa9wY2Y9LdLS9oTYrC9UH+r7+yv8eN7G6cwP4eN5L/669Vfo5NmzbRtWtXWrVqRe/evTlxwhUmf+DAAW666SaaN29Oy5YtOXjwIABFRUUMGjSIRo0aMWzYsArT82dkZBAdHQ248oAlJCQALmX2wQcfeNo1bdqUI0eOcOTIEU9/jRs3ZtCgQZ60MXFxcTz//PM0a9aMtm3bcuCAK/HrkSNH6NGjB4mJidx4440cO3YMgBEjRvDII4/Qrl07Bg8ezBdffMFHH31EUlKSJ+1MWXQ6HampqQDUqVM+u/mZ6N69OyNHjuSrr77ynLc0bc2LL75IQkICiYmJHsWdnp5O//79ad68Oc2bN/fMdj788EOaNm1K06ZN+fjjjz3vrWnTpp5zffDBB55ZX7du3XjhhRdo27Yt8fHxrFy5EqvVyrhx45g9ezZJSUnMnj270u+jOrluFYvJ4uDX09abOJySv3anV8v5Qv30DGwVy4d3JXFXm1pXTF17hSuP/F9/5cSr47AfPw5SYj9+nBOvjqtS5SKl5Mknn2Tu3Lls2rSJBx54wGMOGjZsGI8//jhbt25l9erVHkWxZcsWPv74Y3bt2sWhQ4dYtWpVuX7HjBlDw4YN6d+/P19++WW5BJQVsXfvXh577DF2795NQEAAn332mWdfYGAg27dv54knnmD06NEAPPnkkwwfPpxt27YxbNgwnnrqKU/71NRUVq9ezbx583jkkUcYM2YMycnJdO7c2eucTqeThIQEHnjgAY4cOXK+l4+WLVuyZ493Mbzs7Gzmz5/Pzp072bZtG//5z38AeOqpp+jatStbt25l8+bNNGnShE2bNjF58mTWrVvH2rVr+frrr9myZcs5z2u321m/fj0ff/wxr7/+OjqdjjfeeIO77rqL5ORk7rrrrvN+L9XBdatYEBBYgSkq1Nf7hi+lJKPAzKJtx/lpUyon883YHBde8Ep7AQslFa5cHE4HGaYMFh5cyG+HfyPTlIlTXlxxt4yPPkaedkOWZjMZH318Uf2WxWKxsGPHDnr27ElSUhJvvfUWqampFBYWkpaWRv/+/QEwGAyeVPlt27YlNjYWlUpFUlJShTfkcePGsXHjRnr16sXMmTPp06fPOcdSs2ZNOnXqBMA999zDv//+69k3dOhQz/81a9YAsGbNGu6+25Vu8N577/Vqf+edd6JWnzsTxCeffELz5s35/PPP6devH5mZmWzYsIFBgwad81iouJhaaSmABx98kHnz5nmu27Jly3j00UcB1ywuMDCQf//9l/79++Pr64ufnx8DBgyocFZ1OgMGDACgVatWF6QQLxXXrcE9xKjj+T4NeeGn7R5ZTJAPdcJ82XOygKgAPUFGPekFZvp9sopMt3PfqFOz4IlO1I+ofKiuwrXL8eLj3PXrXRTaXGuOAvWBzOk7hxp+NS64T/uJE+clvxCklDRp0sRzsy6ltFpkRVQ23X29evV49NFHefjhhwkPDyc7O9srjT54p9I/fT1V2e0zvT4Tvr4V11A6nT/++IPnn3+ebt268eqrr3LrrbfStm1bhgyp3PK5LVu2lMu/pdFoWL9+PUuXLmXu3LlMmjSJZcuWVaq/sn2c6TrBqc/gSi83cN0+PmvUKvo0jeLnxztyT7taPNurIZPubsEzP25l6FdrOZxlIqPAzJTVRzxKBcBkdfDO4j0cyiwit/jqq2evUHWYbCYmbZnkUSoA+ZZ8vt3+LVbHhX83NG7TU2XlF4JeryczM9OjWGw2Gzt37sTf35/Y2Fh+/vlnwDWzKfV5VIZFixZ5nub379+PWq0mKCiIuLg4Nm/eDMDmzZu9nOXHjh3zjGPmzJnccMMNnn2lPoPZs2fToUMHADp27MisWbMA+P7778uZuUrx9/c/o6Js0aIFM2bMwOl0MnjwYBo0aMDMmTMrlYn577//5quvvuLhhx/2khcVFZGfn88tt9zCRx99xNatWwFXYbPPP/8cAIfDQX5+Pp07d+bnn3/GZDJRXFzM/Pnz6dy5M5GRkWRkZJCdnY3FYmHhwoXnHM/Z3ufl4rpVLACBPjpCfPUUmO2sO5zNoC/WcCCjiFyTjYISG78kH+dgZnG541JyTaw+mM2nyw9QWIn0LArXJlaHldTC1HLyowVHsTguPAgkYsxohME7VY4wGIgYM/qC+zwdlUrF3LlzeeGFF2jevDlJSUkep/L06dOZOHEiiYmJdOzYkZMnK1dgrPTYhg0bkpSUxL333sv333+PWq1m4MCB5OTk0KRJEyZNmkR8fLznmIYNG/Lpp5/SuHFjcnNzPWYjgNzcXBITE5kwYQIfffQR4DJjTZ48mcTERKZPn86ECRVX1uzXrx/z58+v0Hn/yiuvIKWkadOmtGrVisjISEaNGsXdd9/tNWMopdQ5Hh8fz9tvv81PP/1UbsZSWFhI3759SUxM5IYbbuDDDz8EYMKECSxfvpxmzZrRqlUrdu3aRcuWLRkxYgRt27alXbt2PPTQQ7Ro0QKtVsu4ceNo27YtPXv2pFGjc+ft7d69O7t27bqinPfVmja/umndurXcuHHjRfVxPK+ETu8t4/TLMPeRDryzeDcPdq7LY99v9tr3aNd6pOWVsHDbcVa90IPooHMnclS49nA4HczaM4t3N7zrJX+r01vcVu82L9PN+abNz//1VzI++hj7iRNooqOJGDOawH79qmzsVwpHjhyhb9++7NhRPslpXFwcGzduJCxMKc1wMVyOtPnX9YwFwFev4e62tbxkvZtEsftEIdvS8mlVK5gne9THR6tGqxYMbBlD5/gwfttxAqcEs/3iHLUKVy9qlZpb6t7C0EZD0aq06NV6RjQZQZfYLpXOw3YmAvv1o8GypTTevYsGy5Zek0pF4drlup+xAOQUW9ifUcTyPRkkRAegUat49setPNy5LiO71EWtEuQUW0nNNbFibyZTVh/BZHUQFWDg1yc7Ee6vZPi9njHZTBTZihAI/HR+FZYiON8Zi4JCVXHNFfq6Wgjx1dOujp7GUQHsO1nImsPZzBrZnlohRnz1rksU7q8n12Rlye50TFYHzWIC+fiupHLhyQrXH0atEaP23DVppJQXPZNRUDgfLtfEQVEsZQjw0dK6Tgit65xKtOhwOMkutmJzOInwNzB7ZHusDolGJZRFjgqVxmAwkJ2dTWhoqKJcFC4JUkqys7MxGC69RUVRLGfBbHOw5VguT89KJqPQQq0QI1/c05L4SP8LqgipcP0SGxtLamoqmZmZl3soCtcRBoOB2NjYS37ealMsQoiawDQgEpDAV1LKCUKI8cDDQOkv7GUp5WL3MS8BDwIO4Ckp5R/VNb7TsTkc5BbbsNqd6LUqQn315JfYGDF5Axa3g/5Yjonh321g0dM3EKH4VRTOA61We175qBQUrmaqc8ZiB56RUm4WQvgDm4QQf7n3fSSl/KBsYyFEAq6qkU2AGsASIUS8lPLC86dUkhKrnbWHchg7J5lck42YIB++Hd6aYqvdo1RKySyyYLI44ApceO9wOhAIVCplNqWgoHD5qLY7kJTyhJRys/t1Ia569zFnOeR2YJaU0iKlPAwcoIJKk9VBfomNkdM3kmtyLXZMyyvhgakbKnTMa9UCg/bcuYguJSabiYN5B/m/df/Hexve41jBMSz26qsvo6CgoHA2LsmjrRAiDleZ4nVu0RNCiG1CiO+EEMFuWQyQUuawVM6uiKqMo9kmbA7v6InjeWa0asE97bzXuDzXuxH+hivLNXUw/yADFgzgx30/MnPPTO745Q6OFx+/3MNSUFC4TjmnYhFCdKqM7CzH+wE/AaOllAXA50A9IAk4Afyvsn25+xsphNgohNhYVY7QqMDy/hK9RoVGpeKZ3g35Y3RnJtyVxPJnu3JXm5qeEOSLwe5wklFo5kh2MekFZiy2C7P4FVoL+WTzJ14ZdW1OG99s/0aZtSgoKFwWKnOH/ARoWQlZOYQQWlxK5Xsp5TwAKWV6mf1fA6VZ1tKAmmUOj3XLvJBSfgV8Ba4FkpUY/zkJ9NFyf8c4Jq8+4pGN65tAgI8WH52aYKOOhlEBVXEqwKVUtqbm8fC0TeQUWzHq1Lw7IJGbGkdgPE+l5XA6KLaVz2dWYC3AUf3uKQUFBYVynPEuJoToAHQEwoUQY8vsCgDO6WQQrmD9b4HdUsoPy8ijpZSl+b/7A6VJghYAM4UQH+Jy3jcA1p/He7lggow6nr6pAcPa1+ZgZhEJ0QEEGV1K5UJwOiUmqx2DTo2mAkd6jsnqUSrgypg8Zk4y/z7f/bwVS6A+kLsb3822ldu85Pc2vrdSi/YUFBQUqpqz3cV0gJ+7TdkYqAKgMtVwOgH3AtuFEMlu2cvAUCFEEq4Q5CPAKAAp5U4hxBxgF66IsscvRURYKUFGHUFGHfUj/C6qn+wiC79uPc6yPRm0qh3M0Ha1yoUmmywOj1IpxeGUHM0xnXdCSyEEN8TcwOsdX2fKziloVVpGJY6iUci5s6IqKCgoVAfnzBUmhKgtpTx6icZzXlRVrrCy2B1OcoqtFFrs+OrU+Oo1+BvKV5qsiNxiK8/8uJVlezI8svhIP2Y+3J6wMqv00wvMdP9gBSart95c+Xx3aoZUPMsoKLGhVavOOItySie55lwEgmBDsLK6W0FB4YxcCbnC9EKIr4C4su2llD2qa1CXC6dTsutEAfd9t548kw21SvDUjQ0Y3qE2QUZdufZ2p5Misx2jToNOo8JkdXgpFYB96UUUlNhwSklBiR2jTo2PVsU7A5oxZnYyTrdef6JH/QpLJeeZrKw7nMP0NUcJ9dPx9I0NiA32QafxVjAqoSLUJ7Tc8VaHlRJ7Cb5aXzSqKyuaTUFB4dqkMneaH4EvgG9wrYi/ZskutvDY95vJc69ncTglH/21j1ubRZdTLFlFFmZvSGH5ngya1wxyZ0Eu32eDCD8sdid3TfjXU4ny7ra1GNMznlUv9OBIdjExwUYCDVoCTlMsDoeThVuP859fdnpkv+84ydKxXYk9w8ymLBmmDKbunMr2rO10junMgAYDKlQ+CgoKClVJZRSLXUr5ebWP5ArA6pCk5paUk+9My/fyveSarDwzJ5m/92UBsPFoLn/tSmfOqPa0qh3MpqO5nraPd6/P83O3eZU3nrn+GANbxdCqdshZfSo5Jhuf/33IS2axO5mzKZWxPePPcJSLrJIshv8+3FPhcEvGFjalb+Ldzu8SZAg667EKCgoKF0NlFkj+KoR4TAgRLYQIKf2r9pFdBnRqQXSZNS0aleDWZtE0rxlIscXukZdYHR6lUsqxHBMZhRa+vLcVI7vUJSE6gKFta9K2Tgjb0/LLnWvdoZxKjEjirMAH5qigdOrppBenlyubu+r4Kkrs5RXnlUqeOY+UwhR2Z+8mqyQLh/OanjArKFwzVGbGMtz9/7kyMgnUrfrhXF5CfPV8PqwlIyavx+qQfH1fazYeyeGJH7ZQJ9SXZ3o3pEaAAYfzzAEPYX56nukVz6gudTHqNJisdprUCGDn8QKvdm3rnFs3B/vqeLhLXd74dZdHplOruKt1rbMc5UJy9RZwA8gx5/DGmjdYemwpAMH6YCb3mUy9oHqXeWQKCgrn4pyKRUp5XaRkNVnsFJht2JySX564AY1K8OFf+/hps2uN5o60Av7el8n8xzqx43g+nRuEsXL/qVlLrRAj0YEus5Zeo0bv53Ku++jUfDQ4iSFfr/WEGA9uHUvdcN9zjkmjUtE/KYZQXx0z1h4lxFfHMz0bEhHgijAz280U24oxao3lqhZG+UYR6x/rNWvpVKNThdUNr0R2Z+/2KBWAXEsu41aNY9KNkwg2BJ/lSAUFhcvNORWLEOK+iuRSymlVP5zLg9XuZOWBLB7/fjN292zk99Gd+SXZO99WgdlOcmoefjo1z/dpRLOYE6w7nENSzSAe7lyXcH/XDb+0OFhKbgn+Bg2BRi2LnuyEzQkqAX56TYVRZhUR7Kvj9qQYusaHo1WrPOlkMkwZfJH8BZsyNpEUnsRjSY8R6RvpOS7MJ4wpvacwZecUdmTtoHNsZwY2GHjV+Fc2pG8oJ9uVswu7015BawUFhSuJypjC2pR5bQBuBDbjqrVyTZBrsvLsnK0epQKQb7KhEgJOMyk5nZJfth7nhvphtKsbQs+ESBJqBKAvE/57OLuYgZ+vIb/EFV3Wtk4Iz/aKZ+/JQvom1qi0UilL2WOyS7IZ9dcoDuQdAOBQ/iE2Z2xmSp8pXlFfkb6RjG45GrPDfNWFG3eK7sS327/1kiWFJ6FTnf+1U1BQuLSc03kvpXyyzN/DuHKEXdzy9CsMs81BocX7SXjpngzuPi2zcbifnjphvizefpKX5+9wKx+8lEpusdW1z61UANYfzmFfehHzNqfx0+ZU7JVwvp+NYluxR6kA1PCtQaG1kHxL+SABvUZPoD7wjEoluySbnVk72XBiA5mmzCvGQV4/uD6DGgxC4FroWcO3Bq93fJ1AQ+BlHpmCgsK5uJBH2GLgmvK7+OjUhPrqyC6TZmXq6iP8/Vx3EmMD+SX5OLVDjQxoGct/ft7uaTNl9REmDmnh1ZfN4WTPCW9HPcDuEwXUDDEyZfURbk+qQfhFVKBUCdfzQMuIloxuNZoTRSfw0fic03+SVZLFscJjOJwO6gTWQS3UjPprFHtz9wKuvGMzbp5BXGDcBY+tqgg2BDOm1RhGNR+FxWHBV+tLqEFZg6NQHiklOeYcJJJgfTBq1ZVVL+l6pDI+ll85ZQ9SA42BOdU5qEtNiFHHV/e14sGpG8kz2dBrVLx5R1P89BpuT6pB5wZh/LUrnWFfr6W4TBoWg1bNjuP5hPrp8NG5LqVBp6ZtnRCW7PZegd8mLoRPlx/AR6u+6HQrvlpf+sT1YWijoTy57EnPTKVuYF2+7f0tYT5h5Y7JMGV4rWsJNYTybe9vyTCdGme+JZ/3NrzHf7v8F3/d5S+RGaAPIEBfdVmlFa49CiwFrDmxhi+2foHNaePehHvpHdebIH3Q5R7adU1lZixlSwjbgaNSytQzNb4a0ahVJMYG8efoLhRbHRh1agJ8NJitTuasPsaCrcd5845mXmWKhYD7O8Xx85Y02sSFeBRLgEHLG7c3JSVnA3vTC1EJGNSqJmqVYH9GEZ8Pa0mo78X5CYINwTzf5nleX/O6l/nrUP4hFh5ayPCE4V7Ky+6088OeH7wixLLN2Xyz/Rv61evHtF2n3GX7c/djtpuvCMWioHAu9ubu5dm/n/Vsv7X2LXw0PvSr20/Jl3cZqUy48d9CiEhOOfH3V++QLg9atYqIAAM2h5Nck5VCs52dafm8sXA3AP/uz+TnxzsxdfURbA7JwFYxrNibSZ1wv3JJKmsE+fD9Q+0w2eyohGDXiQJ+236Cnx7tSIMIvyr5wgsEB/MOlpNvz9yOzWlDpz6lvMx2M7uyd5VreyDvALfVu81L1rFGR/y015QLTeEapcRWwoxdM8rJf9jzA51jOith6ZeRylSQHIyrLsqdwGBgnRCiMmnzrwpyii1kFJgpMtvJLbby3b+H6ffJvwz4bDXHckx8MrQFdcJ8ef+PvRRb7DSPDcTfoOGFudvYn1HIiA5x6DTlL2OYv55aIb7EBhvp3jCCdwcm0qp2cLl8YBeKn86PG2JuKCfvHdfbS6kAGLVGbqp1U7m23WK74avx9fhsmoc354kWT+CjvTrWuihc36hV6gpNpf5a/6sqAvJapDJX/xWgjZQyA0AIEQ4sAeZW58CqG5vDwb70Il6et51QXx1jesazPS2fd37bA+BK5RIbxL6MIsb2jCfEV8drC3byXO+GdG8UySNd62HUqQmuhFlLW1F2yjJkFlrYeTyfghI7beuEEOKrLZe9+HQMGgOjmo/iUP4h1p9cj1qoGdBgAG2i2pRrqxIqbqp9E5vSN7H48GIkks4xnRnSaAh6tZ5OMZ1wSAc+Gp9qf8qzO+3kml251AL1geWUoMK1h8PpINeSi0QSqKu6z1yn1vFg0wdZfGgxVqcr8EYlVDzZ4knFlHuZqUw9lu1SymZltlXA1rKyy8XF1GNJLzDT7f0VDG1bk8TYIKx2J9PXHmV7Wj5NagTwXO+GjJq+yeNXqRfuy38HJRLub6BWJTILV5aMAjMDv1hNSo4rh5ePVs38xzrSKLpyTutccy5muxmVUOGr9cVPd2YzVoGlAJPd5DqPxodA/aUN3c235PPb4d/4attX2Jw27m50N0MaDVFMFtcwBZYClh5byqfJn1JiL2Fw/GDua3JflX3mFoeFDFMGs/bMwuKwMLTRUKJ9o5XqqefgSqjH8rsQ4g/gB/f2XcBv5zpICFET1yLKSFxRZV9JKSe4E1jOxlXf5QgwWEqZ6y5lPAG4BTABI6SUm8/v7VSeXccL0GoEvZtEMeTrtYy5KZ5go8tMNbRtLT76a5+Xs/5gZjF7ThSSGFu1N+Ofk9M8SgWgxOZg/K87+eKeVpVaSFn6A80153Io/xC7snfRMqIlkb6R5RTH5Y6y2p65nf9b93+e7c+2fkawIZjBDQd7zHEK1xb7cvcxbvU4z/Y3O77BT+fH8CbDq8RcpVfrqelfk2daPwMSVBWUAle49FRmgeRzwJdAovvvKynl85Xo2w48I6VMANoDjwshEoAXgaVSygbAUvc2wM246tw3AEYC1ZqqP9hXR6taIazYm4mU8POWNB7vXp/3BjalR6MIrI7yixh3nyzAZHWQWWjmXDO9ymB3Otl1vLCc/Gi2CYfTycn8EuZsTGHe5lTSC8w4z5D8Ms+cx383/Jdhi4fxf+v+j4G/DmTm7pkU24oveoxVhclmYva+2eXk8/fPr3Bhp8LVj9VhZfbe8p/5Lwd+qfLPXCVUilK5gjjjI4MQoj4QKaVcJaWcB8xzy28QQtSTUpYPSSqDlPIEcML9ulAIsRuIAW4HurmbTQVWAC+45dOk6469VggRJISIdvdT5cQG+xARoCfUzzUr0KgF0UEGTFYHczel8EKfRmQXW3l53nbPzKVHowhu/3QVAsG7A5rRvGYQBu0pX4jNXdbY5nCy+VguRq2GpFpBnrLE+SU2LDYHQghCfXVoVCoGtozh5+Q0r7ENahVLfomdfp/861k3E2TUsujJzsQEl3esF1gLWHhooZfsq21fMaDBAHy15052eSnQqDREGCPKyUN9QhVH6zWKWqiJ9o0uJw/xCVE+82ucs6n4j4HyS8gh372v0ggh4oAWwDpcyqpUWZzEZSoDl9JJKXNYqlt2el8jhRAbhRAbMzMzz2cYXoT56XmuV0NuahxJTJAP7w9qzmfLD3L/lA18+Nd+RkzewJqDWTzTqyG+OjVjbmrA8TwzR7JMHM4q5p5v15FbZqV+nsnK9rR8Cs02ftqcRkaBhVA/HW8s2ElWkYUTeSU8MXMzbd9eSv/PVrHqYBYmi52msYGMvqkBPlo1KgF9mkRxb/vavP/nXq/FmHkmG1/+fRCrvfxMqtQZXha7tGNxWMrJLxc6tY77m9yPUXPK9q1VaXm65dOKo/UaRa1SM7TRUAJ0p8yvGqHhmVbPXHL/nsKl5WyPDZFSyu2nC6WU292KolIIIfyAn4DRUsqCsms4pJRSCHFeNiUp5VfAV+By3p/PsacT5q8nzF/P/Mc7YrY5mbUhxWv/3E1p/PN8A25NjObTZQf4eOmpJTw2h2Rrap6nAmRqbglSSm6Z8K/HjBbup+fr4a3ZmZbP/C1pnjT7qbkljJi8gX+e60ZMsJFRXesxtG0tpASjTo3d6SQ1p3xBriPZxVjtjnLhzdF+0fhofLyKeEUaI71u4lcCUb5R/HLHL/x++HcsDgu31r2VcJ9w8i35WBwWBIIQQ8g5U3LkW/I5UXyCFSkriA+Op3l4c6Xk8hVKhDGCebfN48+jf1JoLaRf3X6EGctnhlC4tjibYgk6y75KLXQQQmhxKZXv3eY0gPRSE5cQIhoozSmSBtQsc3isW1btRPgb2Jde3tcBUGi2E+6v5+99GfRKiCS/xMb6wzk4JUQGuPJ9OZ2SE/klfL3ysJdvJrPIwm/bT9C+bggn8s1e/TqckvVHcugfbMRHq8anjEnN4XBye1KNcpUnB7WqiZ+h/DqYIH0Q3/T6hhdXvkhKYQrxwfG83+V9QnyurEKfGpWGKN8oRjQd4ZGdLD7JSytfYmP6RsJ8wni1/au0i2qHr65iE57FbmHhwYW8u+Fdj6xZWDM+6fHJZVEu2SXZbMnYwu6c3XSL7UatgFrK03gZ1Co1kb6R3Jtw7+UeisIl5GyKZaMQ4mEp5ddlhUKIh4BN5+rYHeX1LbBbSvlhmV0LcFWlfNf9/5cy8ieEELOAdkB+dflXSrHYHeSZbDilxF+vIcxPR1bRKfNWiK+OEF8dWpXg83tasWDrcVr46Xm+dyOmrT3iCTtWqQShvnpOnqY8AE4WmGkcHcDRbG9HuhCQGBPEttQ8Fm8/QcMofzrUDSMyQI9arWJAyxhSckqYteEYapXggU5xdG5Q8ZOeTq2jWVgzpt08DYfTgValveKUSkXkmfN4ceWLbEp3fZ2ySrIYvXw0iwYsOqNiybfmM3HLRC/Z9qztHC08eskVS3ZJNk8ve5qtWVsBl1/r6ZZPM6zRMGWRqcJ1zdkUy2hgvhBiGKcUSWtAB/SvRN+dgHuB7UKIZLfsZVwKZY4Q4kHgKK7V/ACLcYUaH8AVbnx/pd/FBZBnsvLzljT+9+c+Ci12HuwUx/QH2/HMnK3sOlFA42h/PhycRLivjmV7Mxk5/ZQujQny4adHOxLqdsoD1Ao1ckuzKL74+5DXee5sFYuvXs3om+J5cd4py+LYnvGs2JfBm+6UMQCNovz56r5WRAUYCPHV80Kfhjze3VWKN8BH6xUocDpCiAqTT17JWBwWj1IpRSJZf2I9Nf1rVniMUzo9a3HKklGcUUHr6uV40XGPUinly61fclu92xTFonBdc0bFIqVMBzoKIboDTd3iRVLKZZXpWEr5L3CmpFg3VtBeAo9Xpu+qICWnhPFlasl/u+oIarWKKfe3RiLQqAShfnoyCy28ucg7z1ZaXgn/7MtkcJtTN78wPz3DO8SRa7Lx85Y0fHRqXuzTiDphvhSYHXRtGM4/z3Xjtx0niY/yp2GkPz0//Nur3z0nC9mWko+xroYwfzVGvQajvuqjZ3LMOWzN2Mqq46voVKMTzSOaE2K4uBmOUzrJMedgdVhdM6bTfCW55lx2Zu3k79S/aRPVhtaRrVEJFcH6YHIt3sEHtfxrnd69B4PaQFJ4EsmZyR6ZRqUhKSLposZ/IWSbs8vJzA7zFVPTRkHhclGZJJTLgeWXYCyXlEXby1vZpq05woM31PH4TsD1BJ1VaC3XNi3P27lusth5/4+9hPjp+G5EG7RqgY9Ww/1TNrAvvYhgo5Z3BjTjnva18NVrScs1eUV9lZJZZCHfbCXMX19uX1WQZ87jzTVvsuTYEgBm751Nz9o9Gdd+3AWXLbY77ezN2cvYFWM5XnycSGMk73d5nyZhTdCpdRRYCvho00fMPzAfgFl7Z9EhugPvd3mfl9u9zHP/POfpq3lYc+oG1T3juYIMQXzQ9QNeX/M6q46vorZ/bcZ1GHdZ0qQ3DmmMTqXzpBMBl7/HoLnwWjsKCtcC1+2KovjI8qlPaoUYcZRZhOhwOgkwaLgtqYZXOyGgX/NTMqdTYnU4OZFv5puVhxn2zTrSCyyMmZPMvvQiAHJNNh77fjO5JhtWu5PDWcV0qOftE9CqBc1jg7wqUlY1JrvJo1RK+evoXxWalypLrjmXUUtGcbz4OADppnRGLRlFkbWI7JJs8i353Fr3VvrW7es5Zs2JNRTbikmKSGJx/8XM7jubaX2mMaHHhHP6SiJ9I3m387ssvXMpk/tMpnVU68tyMw8yBDG5z2QaBDVAIzR0ie3Ch90+VFLUKFz3XLerlNrXDaVhpD973dFgWrXg2V4NMVltZBepWH0wm6W7M+jcIIyxPePx06uZtT6FcH89r/ZNICpAj8XmIL3QwtTVh6kR5MNdbWqy5pDLPBIRoOdARpHXOZ0Stqfm066uholL9/N//Zvx4V/7WL43g7hQX57p1ZD9GYXUC6++RY1me/kAA3CZcC6UIltRuZXUsX6x5FhyGLt8LIcLDuOn9ePJFk8S7hPO5J2TGd1yNEuOLWFS8iRK7CU0DG5YKaVSypVQAEyv1pMYnsjXvb7GKZ3o1forYlwKCpebSq28P03eCTh5rpX3VzoaleA/fRuTXWQlu9hCy1rBTFtzlGd7N+SVn7fz+450wJXLq2O9UCYMSWJUl3oI4fKnCCE4lOnKjhwf5c/qg9nc1rwGL9/SiG9WHkZKCDBoKDDbvc5bO9SITi0I8NFy9zfr+GRoEs/3aURusRWDVkWr2kEEViJH2IUSoA8g0hhJuindI4vyjfJaxHa+GDVG1EKNQ54y7T3U7CGe/ftZDhccBlzK55317zDt5mn8fvh3moY15aE/H/K035u7l5dXvsyE7hMqbZJzOp04pAOtumpKEVwoyhoaBQVvLmTlfQHnufL+SsSo07AjrYCPl+zjl+Tj3PXlWoJ8tEgpPUqllNUHsyky24kIMBDub0AIgdnmINdko2/zGmQXWWkWE0hMkA8GjZpfnuhE/Qhf3ryjKWVrevVuEkVUoAE/g5aXb2mMxeZgyFfrGPDZKr74+yBh/npCfCvnW7E4LNid9nM3PI1QQyiT+0ymY42O+Gv96VijI9/1/u686smb7Wac8tR6HV+tL482f9SrTd2guhUWItudvZthjYdVWHhsc8ZmL3/FmXA4HZwoPsHELRP5z6r/sCl9k5JvTEHhCqLaV95fiUgpUavgnva16N0kku1p+STGBBFk1JBfUvHN2nxaKpVii52Z647y02b3Gs7tsGjbCd66oykGjatOS49GEfzzXHe2HMujbrgvNYIMHsVRO8TIkrFdSU7Jw6hT0zAqgPBKOOzzLHnszt7N3H1zifWPZWijoUQYI1AJFXanHbvTflZ/gxCCmv41eb/L+1gclvMy3+SYc1iZupLlKctpEdGCvnX7EuoTip/Oj6GNhtIrrhe7s3fTMKQhflq/CiO+EkITOF50vMLV9bF+sZXKcpxVksWgBYMosLqeexYfXswbHd/gtnq3nXPVvoKCQvVTrSvvr0SyiyzsOlFAoI8WtRAE++ro0iCMvSeLeO+PI4zsUpe6Yb4cyjq1oDE60OBJVlmK1eFk/hbvxAD73T6V0uJf/gYt/gYtNSuo36J2l0Lu1SSq0mO3OW0sPLiQ9za855HN2z+Pn2/7GYvTwvRd0zlRfILB8YNpEtbkrCvAz9cXkG/J5621b/HX0b8AWHpsKQsPLeTLm74kxCfEk5K/TmAdAOwOO+M7jmfMijGe2U2Pmj1IKUzh5X9fZmL3ibSLase6k+sAV8jw651er9TM6c+jf3qUSimfJn9K59jOV91aHgWFa5FqW3l/JZJfYmXh1uP4GbQ8Mn0TxVYHtzSNonujCJ6buw2A/emFfDi4OV//e5iNR3JIjA3kP7cmEO7nPZtwOl3O+NNxnCG1fZWM35zPF9u+8JIV2YootBVy96K7KbS5AhGWHlvKK+1eYVD8IE8W2RJ7CQJxwdFTJpvJo1RK2ZOzhyxzVoWr/DVqDe2j2/P7gN/Zlb0Lf50/J4pP8ObaNwH4z6r/MLXPVKxOKxmmDBqHNCZQH0jZXHJnoshaVE5WYi9xVf1RUFC47FTnyvsrjmKLg2axQQz9eq0nFX7PJlF8vGSfp83BzGIemraR9wYkMqRNTTYfzWX+5lRGdq2HUXfqcqlU0K5OCOsO53hkwUYt9SPOXMHxfCg029CpVehPW21vsXtnLG4T2YYVKSs8SqWUL7Z+wU21b8KgNnAo/xCTd0xGo9LwcOLDxPrFnrPCXnZJNiabCbVKja/W94z1Z6yOM/tEjFojRq2RaL9oDuYd5KnlT3kSZdYJqEOwIZhQn1ASQhPOOpbTubXurXy17Svs8pTZ8s74O5UsyQoKVwjVtvL+SkRKSaHZ5lUZUqt2OeLLklVk5d+DWWQWWli47QQ6tYqh7Wpj1GnILbaSZ7LyybIDvNCnEbM3prD6YBYNI/35z60JntorF0quycrag9n8sP4Y0UE+PNatHjWCfNCqVfjp/BgYP5Dvd3/vaW/QGCp0eFsdVtRCzZ6cPdz/x6nsOH8e/ZMf+/1IfHD8Gcdwsvgkjyx5xON8v7HWjbzS7hWahDZhZ/ZOT7sIYwRRvpUz5dX2r82COxZwKO8QgfpAIn0jL3i1f7gxnKk3T+V/G/9HjjmH2+rdxqD4Qeg11bOoVEFB4fw4W7ixAXgEqA9sB76VUp5/GNIVhI9OQ5i/HrVKeExWS3ZlMLh1TT5ZdsDTTq0S3Nw0moenbQRclR6lBKvdyYp9meQUW5i3JY0/d6UzoGUMT/VogI9OTaS/HpXK25Rjdzgpstjx1WnQas7umLbanfy4MZW3F5/KH7Zw63GWjO1KdJAPBo2Bh5s8Ttcavfnp4PfkmfN4rPlj+Op8+Xrb115rUe5qeBcAn2/1LsTplE6+3vY1b3V6C5vThhDCqxhYsa2Yjzd/7BXRtfTYUnrW7smnN37KpC2T2JC+gUYhjXi65dOVjibTqF2Fvioq9nW++Gh8SAxPZEL3CTikg0B9oFI4SkHhCuJsv8apgA1YiatscGNc5rGrlhBfHXaHg//c0piDWcXEBvmw60Q+tzaLJtio5cdNqQT56BjZpS4/bU4lv8QGuFbZ++rV5JVY2X08n7Q81w28yGJn2pqjgKsiZbs6IfiUye2VWWhh5rqjrNyfRcvawTx0Qx0iAs7s48gzWfnib+8QXZPNwZ+7TjK4dU2O5pj4+K99FJjtjOj0DC1rBaLSmCm0FvJD3x/ILslmyo4pdKjRgX71+iGEqDAk+aZaN7EpfRNTdk1Br9LzaNKjxAXEYdQaKbGVsDl9c7lj/k79m7SiNE/BrpTCFCZtmcRLbV+64FQwF8vlOq+CgsLZOZtiSZBSNgMQQnwLrL80Q6peIgJ86NUkks9XHOTPXSfp0SgCrUbFlmN53N22Nr56NZGBegpKbDSK8ufmZtHc064W/gYtJTYHh7KKaV83hN93nvRy1CfVDHLdyB1ONGoVOcVWnpi52eOD2Xg0l6W7M5g9qv2ZzWXCNTsq5f5OcdzevAZOCcfzzPSd+C929zlXH8zmf3cmcsD+AzP2TAegUUgjJvWYRJhPmCfs9oGmD7B52SlF0SKiBWqVmlFLRnlk/6T94zGPGTQGmoY25USxdy611pGt+fPIn54orlJGtxxN0FkDCBUUFK43zqZYbKUvpJT2ykTrXA1kFpoZ9MUaT+Gtzcfy2HQ0j84Nwnj1lx0ARAUYmDWqPQaNCl+dBn8fLQUlNjQqwfN9GlFotvPzYx1ZuT+LD//aR4S/nse61WfsnGTaxIUwtG0tSqwO1h3OoXG0P30Ta6BRCf7clU5WkeWMiiXQR8sDnerw8ZL9PNApjsgAA4O+WMPtSTUwaNUepVLKF38f4pGbkwjUL+CFNi9Qw68GaUVpqISKMJ8whBC0iGjBJz0+4dvt36JRaXil/Su8tuo1r36c0smUHVMY33E8fjo/nmnzDNuytnlW57eKbMUNMTfw1tq3yo1ZXuOhWHnmPAqsBRTbignzCatUhUsFheudsymW5kKI0sUCAvBxbwtcWe6vyqRIR7JM5ao5Lt+bwRM96nu2TxaYOZFXwuGsYmoE+eCjVZNdbEGvUfPEzC2UuJ39D3SKY8Vz3Si22PlzVzqHM4tZfziH7an5/OfWBEZ1qUvL2sFMWX0Em93JXW1qYjxLTRW9Rs3wDi6F0iwmkNs/XXXW8OXSPR90/YAvt37JxnSXTyjSGMmUPlOI9Y8lQB9At5rdSIpIQoXKK+2Kd1+nzhPjF8OsvrPILsn2LKCUUhLjF0NqUaqnXbuodldc+eOqJMecw+trXmfZMVe8SrA+mMl9JhPmE0aALqBSodGVwWQzUeIoIUAbcNnT0ygoVAVniwq7Jh/LShd2t4kL5uam0Ugki7ef9GpTL9yXYquDl+fv4PneDdmWls/zvRsy9Ou1HqUC8N2qIwxsFcufO07Su2kU7eqEAIJjOcUYdCpuaBDGvd+esiBuPJrLV/e1IibIh+xiK2l5JfgbtJ5KleBaXHlX65psTc3zKJVlezL48t7WzN6Q4jVreeCGmhwt/gWTDPMoFXBlF35v/Xu83fltTwhu2bTyDyc+zJPLnvRsq4SK+5vcj059ahFomE9YucWGU/pM4bsd37EjawedYjpxV8O7KvRzOJwO8q35aITmqk7KuDNrp0epAORacnlt9WsMbTQUKSWdYztfVBliKSXHi4/zyZZP2J+7ny6xXbin8T1K7jGFq55qC6URQnwH9AUypJRN3bLxwMNAprvZy1LKxe59LwEPAg7gKSnlH9UxrtohRv7vjqZoNSpmrjuGAEZ0isNPr6ZV7WCa1AhgQIsYnp6dDMAPG44xtmdDii0O0gss5fpLPpZHv+Y1GPDFagrc6WC6NAijS3w4s9Yf87Tz02sYf1sCwUYdG47moteomLB0Pyv2ZtI1PpwPBzf3VKRUqQTRgQY0KoHdKck12Zi14RjTHmjLlDVHKDTbGdAihjZ1/FmS5l9h3q2d2Tsx280Vru1oGdGSL2/6kik7p6BX63mk+SPE+see89pF+kYytvVYSmwl+On8KozEyjXnsvjwYubtn0ewPpgxrcZQL6jeVVmjpKyyLmV39m6C9EE8suQR3uj4BrfXv71SaWgqIrskm2GLhnkKhu3L3cfenL280/mdi1JYCgqXm+qM0ZwCTAKmnSb/SEr5QVmBECIBGAI0AWoAS4QQ8VKewW5zEYT7G4gO8uGBKRs8si2zkpl8fxte6NOIHzelMPTrdZTYHGhUgtggIyCx2B3EBvuQmutd4Kt5zSBe/WWHR6kA/LM/i4wCC2rVqRvO/wY354d1x1ixz6VTfXVqvrinFSoB6w7lsPlYHj0TIrE7neQVW9GqBf8dlMhL87ZjsTv5bftJejaOpE+TKFJzS/h+3TF2Hg9idK87WX18FQsPLfQaV8uIlmc0UwXoA+gY05Fm4c1QCRW+Wl9yzbmU2EsI0geVq/zolE6CDcGohAq9Wo9eXbGPyOawMWvPLD7b+hkAXWK7YHfaSc5IJtQnlDCfsGqrVeKUTrJLskkpTMFH40OkMbLCjADnQ8caHflux3desuYRzTmQ5wpNn7ZrGl1iu1zwDONg/sFyVShXpq3EZDcpikXhqqbaFIuU8p/zSFZ5OzBLSmkBDgshDgBtgTVVPa78EhvfrDxUTj5l1RFe65fAguTjWOxO7mlfm4EtY0gvMBMZ4MoV9vFdSTw8bSO5JhtqlWBUl7po1IJNR/PK9ff3vkzu7xTHou0niAs1YrY5PEoF4NbEGgT76rinXW2e6N4Ai81BodnGzHXHmLMxhWCjjrcHNHP7cBwYdWp+3pLGf//YC7gKlT3cpS5BBh9aRLSgR80eLEtxmW3qBNRhbOux+OrOXtfFX+dPkbWINcfXMHHLRErsJdzd6G561e6FWqVma+ZWJm2ZhMVhYVjjYdxU+6azVmrMt+Qza+8sABJCEri70d08/NfDntX2nWp04u0b3va64eeac8kwZZBrzqVuUF2C9EEU2YpIL06nwFpA3cC6BBuCKbYVk2fJI7UwlTqBdQjQBeCnO5XlILUwlXsW3+NJetk4pDGf3fTZReUOiw+OZ2CDgfy0/ycAavjW4MkWT/LyypcB0Kq0FzxbASr0p6iECtX1W39P4Rrhcqwqe0IIcR+wEXhGSpkLxABry7RJdcvKIYQYCYwEqFXrzLXRz4RaUGGFRr1GhZSSeY91xGx1oNeqmbbmCL+UKpp2tRjWrhYzH2pPgdmGVq3ij50n+XdfFq3jgll90PvJs1lMIFEBemY81I7kY7nsTz+V36pnQiSd6oey+0QB0YE+HMwsollMICfzzbzz2x53q2JunrCSJWO7EOanY/TsZG5sFMm8RzvilJKTBWZ2puVhNJh5ddWrdK/VnQeaPYDD6SDHnMOh/EPU8POufFkRxwqPMfKvkZ7tN9e+ia/Wl1r+tXh0yalU+K+veR2VUHFH/TvOfDMVePw0QxsP5cNNH3qUCsCq46vYnbObTjGdAMgpyeGFlS+w9oTrozeoDXzb+1vm7p3L/IOuMsa+Wl9+7Psjs/fNZurOqe7TCN664S161e6FQWOg0FrIfzf81yuT8u6c3Sw5uoQhjYac8xqciWBDMGNbjeWR5o9gsptIKUjh7XVvewIYHm3+6EXNwGr51yLWP5bUwlMBEX3r9D1nuh0FhSudS61YPgfexBXQ9CbwP+CB8+lASvkV8BVA69atzzvW1c+g5ekb67NiXwal6a9UAu5pX5v8Ejv5ZhsTl+wns8hCn6ZRfDu8DQ9N28CMdccY0SmOIB8dr/26g/WHc2ldO5hJd7fghgZhfPfvIdrWDUUtBCabncgAPREBPui1KsKMOjKKLExa7jKh3NOuFgVmO0t2Z/DHTlfggEYl+PyelvRNjGLhNpfM4ZT8uDGVER3jSE7JY8XeTFQC6oT5UWyx071ROC3qRLMje0e59SXDGg/jhpgbznotbE6bV3qYUvLMrtT8AuEVLfb97u/pFtsNIQQO6cBf6++VRiVYH8wjiY8wfs14Yvxi2J+7v1zf27O2exTLrpxdHqUCriqWr61+jRFNRngUi81hI9+a71Eq4Ipge2vtW7SPbo9BY8DisLA3d2+5c23O2OyViPNCKM3abLFbUKGiUUgjYvxiuC/hPhoEN7jgfsEVIDGtzzTm7J3Dzuyd9Kzdk241uyk5zxSuei6pYnHnHwNACPE1UOoYSANqlmka65ZVC9FBBqY/0I45G1MQAoa2rcXSPen0T4rhzi83eqKxvll5mBKrgyFtajFl9RGOZpuo39ifL+5pTYnVjkoIpqw+QoMIf1rXCeXzFQexOSQPdIoj2KhDrRKE+Bo4ml3C0WwTD3SKY9qao/gbNDgkHqUCYHdKXp63g4+HJHkUC0CAjxajTk3HeqGUWJ08dWN99qYXEuijpVGUPz4aaBvVluUpy73eY/ea3c95HQQCP+0pc1IN3xqM6zAOiSTIEMT0m6fz2dbPWH18NeCqFJljyeGVla+Qbc6mb92+3NfkPk/OL7VKzU21byLGL4Z8Sz6tIluVc4B3qtHJ83pH1o5yYzqYd9BrphWoDySlMKVcuxJ7iafMsq/Glw7RHZh/YL5Xm5tq3VRlqV70Gj1xgXG82v5VHNKBj6ZqKkeEG8MZ1XwUFocFo8ZYZSHMCgqXk0uqWIQQ0VLK0iXd/YHSO8sCYKYQ4kNczvsGVONKf71GzfwtKdzTvhYBBi3b0vLpl1iDjEJLuXUj87ek8cU9rZi65gjxkf7kFFuwOyRSQkqeiR1p+SREB/DMrK2eY8b/ugtfg4ZOdUPxNWioGWzkhZ+2cVPjSGaP6kBEgJ6tqemnD4vMIgta9akbS4BBQ7/EGtgcTl7vl8CJAgtDvlqLyeqKaWgY6c/0h9rwcruXSStKY1/uPtRCzd2N7qZhcMNzXgeNSsO9Cffy0/6fsDltvNvlXV5b/RqH813lhI0aI5/0+ITUwlSOFR7j8aTHGbdqHLtyXFFo3+74FrPDzOiWoz1RX4H6QNrXaI/FbqFRSCMeW/oYh/MPo1FpuL/J/dT0P/X8cEPMDXya/KnXmNpEtWFPzh7Pdo45h7qBdcuVPg41hHqCE3y0PjzR4gkO5h1kW9Y2VELF7fVup01Um3Neg8pSYishz5LHtsxt1PCvQYxfzAUn0TwdjUqj5DpTuKaoznDjH4BuQJgQIhV4DegmhEjCZQo7AowCkFLuFELMAXYBduDx6ogIKyXIqOOZXo14Z/Fuft12KnXJm7c3YXDrmszZeOoJ2d+gwWJ38E7/ZphtDp6YuZkj2SZ6NIrgqRvr07lBOD8nHy93jplrj2G1OdGqBbc0i+b7h9ozf0sqi7al8eANdencoLxTOT7SjzA/Pbcn1SDS38BdbWsCTo7nWzmQXsS0tUc9SgVgb3ohi7YfY0CrKL7u+TUl9hI0Kg2+Wl8vx/bZiDBG8PPtP7MqbRXJGckepQJgspv4ctuXjG01lhp+NcgwZbA961RRUYEgyhhFWlEau7J3ER8cT6QxkiBDEHqNnlj/WCb3nkyJvQStSoufzs+T8NLutBPmE8bMW2Yyffd0fj/8O3WD6vKf9v9h4aGFaIQGu7TTMLghBrWB97u+z7hV4yiyFRHmE8YnPT7x8m9EGCP45MZPMNvNnki3qjIpOZwONqZv5IllT3iKlrWPbs97Xd6rMuWioHAtIc5UZ+NqoHXr1nLjxvJrDSrDgYwibvrwby+ZUadm6gNtufOLU8Fo7wxoRstawfgb1IyYvIGhbWtRN8yXA5lFWO0O2tUNY9rqo/yc7G256xofztv9m7E9LY/G0QGE+ukxqFUczi7m5fnb6d8iBqeEtxfvxmR1EBdq5LsRbagb7ofV7mRfeiGHMotIjA2i7yf/8sGdzXlp3jZyTTav89zVJoZ6DdZzZ/ydF+VINtvMvLH2DX499KuXPMIYwQ+3/kCEMYLZe2bz1rpTaV3uTbgXP40fn287lUF5cPxgnmr51FnDZfPMefxy8Bem7pyKEIIRTUbQs3ZPBIL/bfwfsf6xdI7tjJSSlMIUMk2Z3JNwD4XWQiwOCwaNgWB98FlTqxRYCsg2Z5OckUx8cDw1/Gpc8PXJNGUyZNEQMkwZXvIpvafQKqrVBfV5uZAOB47cXFd5bn9/VIarb32RwsUjhNgkpWxdXf1ft/Pv43kl5WQmq4MQo47HutUjo9DCkDY1XSldNCq2peXz1h3N+ODPvWxNyaNlrWCe6RVPpL+OR7vV5bcdJzx1Xnz1al7t25hDWUWkF1jwN5RQaLYRHejDnV+uIc9kY8ORXO5sHcv0B9oS4KPFR6cm0McVfmpzOPnfn3t5Z0AzCkrsfDK0BcVWO50bhLNgq/fsqHNDI1/tX0i/ev2834vNRI45h/Un1xPjF0P9oPpnXG/hcDrYlrWN9jXal1MsXWK6eJ78u9fqzudbP/esvehVu5dXrReAOfvmMKLpiLMqlpVpK/lg46mlTP/d8F+ijFH0jOvJoPhBPPTnQ3y93VW41KgxMv/2+Rg0hkovsiyxlfDzgZ95f+P7HtmtdW7lxbYvXlBGZKd0llMqAIfyD11VisVRWEjxv/+S8cH/cBQUENi/P2GPjEITosy6FKqW61axNIj086xsLyU22IeMQjOHsooJ9HGlWqkR5EN2kYWaIUbu+motmYWu1fdrDmXz1A9bmPNIBwwaNX+O6cK0NUex2J3c37E2Hy3Zz8IyZranbqzPkDY1ubd9bTrUCyW32LWafuAXa/jxkQ6cyDdzLNtEn2ZR+Os13N+pDn/sOMmk5Qcx2xzc17E2z/SKJ6PQzNpDOeg1Ku7pUAOH9hghhhCvRYsOp4NN6Zu8TDcJoQl8duNnFSqXHHMOL658kWdbP8vdje7mx30/YnPaaB/dnieSnvA4qsN9wpnTbw6/HPiFtKI0/HX+Fablzy7J9vKllOVk8Unm7ptbTj5n3xza12hPk9Am/HTbT8zeO5tAXSCD4gcR7hNemY/UQ4GtgAmbJ3jJFh1exKjmoy5IsejUOhLDEtmWtc1LXpU+nEuBNSWFtDFjPdu506YhdDrCn3wClV4pkqZQdVyXiiWryIJOLfjy3lY8P3cb2cVW6ob58u7AZgQYNKw5mE1+iY0gHy2jezbgncW7GdGpjkeplHI834zF5qRuuC+ZhRYyCswYtGqyi61eSgVg0baT3Ne+NtlFVoZ/t57IAANjesYTH+mPViWQWjVv/7abhJgA6oT54nRKXvv1VKqWT5cfJNio44FOdXitXxNUajMbM/+mTkhDbpY380/qP3Ss0ZFQQyhFtiLeXf+uR6n0qt2LfvX6UWwrxqAxeBX2AtcTeWZJJq/8+wr3JNzD172+RiAI0geRb8vn910u/0d8cDwRxggeTnwYKSXZ5mzCfMLIKsny9KVX68+4fsbpdLIvZ1+FJqlQQygalQabw+ZKmdL8EUINoRcUJWV1WCusqplhyqBOYJ3z7i/YEMx/u/6XMcvHsDtnN/5af55v+/xVl9Mrb255hV6w4BdCh9+HKvz8lLeCwtm4rhSLzeHgcKYJs93BjrR8An20zHusI/klNo7lmHj9110k1AigX2I0M9Ydo0aQDxabk95NozCcISuxTqOiyGLHqFMTZNTyx650usaf+pEadWr+OygRgeDHTanclBBJQo0AXluwk2d/3MrPj3VCrxFMWLoPk9WBlJIis50fNpQPsf1xYyofD0ki2KghOWczraKb8cDvD3jq3ftr/Zl+y3Q2p2/m/274P+bsnUOD4AZoVBrGrx5PgbWAvnX7uio/lrkp6tV6Wka0ZHPGZqbsnMKUnVO4tc6ttI1uy2urT6XYbxHegv92/S//pv6LWqWmc2xnvrjpC8auGMuxwmNEGiNdea50FZvBbNLG8pTlDGs8jJWpKz03f4PawKjmoyiyFvHa6tf4N+1fYvxjGN9hPInhiecd2uuj8aGGbw2OF58yG2pVWuIC486rn7LE+MXwRc8vsNgtaFQaAvWBXkk7LwR7Xh7SakWo1ahDQs5biTrtdhy5uSBB5WtE7Xv2TAva6PIKXx0SCiplpb9C1XJdKZacYhsldgcPTd3omX3o1Cq+Gd6aCUv2sz+jiAAfLZ3qhXJb82j6Na+B2eZg8qoj3Nw0mt5NIvlj56kw4duaR3Mgo5AaQT78sy+L5jWDuatNLZxSoteosNidvNrXlSbmz12njnvwhjqM6lKXz1YcZPXBLPJLbCzfm0FSzSDC/PQs3Z1OuF/5m1a4v56oAAPBvjq6+XbjrbVveZQKQKGtkG+2f0OgPpC317/NxO4TMWqMjPhjhKfN/APz8dX68nTLpz0+iyBDEO92fpfn/nmOrZlbCdIHMTJxJPf+dq/X+bdkbmFH1g6+3P4lJ4tPEqALYN5t85h28zSsDisaleas9Ur0aj1dYrvww54f+K73dyxLWYZAcGvdWwnUBTL277FsSt8EuFK0jPprFIsHLMbH7/wUS4ghhM9v+pyxf4/lYN5BIo2RvH3D2wTpgs6rH+m+cUspUfn4EOJfdb4Ia1oaJ158CdOGDWhr1SL6/97Cp1mzSjvTHfn5FPzxB5kffoSjqIiAW24h8vnn0ISdOYVN4O23kTN1Ko4s9wxTpSLyxRfQhF5dMy+FK5/rSrHkl9j4bfsJL5OW1eHk0+UHGNy6Jv+3eDcPdqpDnTBfdBoVi7Ydp3vDCBxOyTu/7eaju5Lo1SSK7an5dG8YTsMof1JzTdwy8V/P+peoAAPzH+vI1Afa8vL87dQN8+WlXd5rVqasPsKcUR34bMVBAgxaFm8/SdOYAD64M5H//bmP5XsymP5gWxZsPU6B2eXD0KoFL93SiGB3en2rw+pVG6WUE8UnqBNYB7vTzudbP+fR5o+Wa/Pb4d94oOkDXs7waL9oPunxCVaHFZVw1W0psBaUO/ZE8QmC9cGcLD5JgbWAr7Z9xQttX6j003uLiBbszN7Jk8uepFVkK1pEtCDUEIrVafUolVIc0sHm9M2VSk1TFpVQUTeoLt/2+hab04ZaqAkxhJBvzWdTxia2ZmylQ40OxAXEndHn4igspGjlStLffgdHdjZ+XbsS9cbraCMizmssFWHPzib1iSex7N4NgO3YMY498CD1//oTVVRUpfqwHj3KyXGnZpMFCxagCvAn8tlnz6icNOHh1Jn3E4V/LcGelUXgbf3QVMH7UVA4netKsfjrNRzPM5eTH88voUejCPz0KqKDDNwycaUnwivAZy8zHmzHsK/XMWr6JlrVCqZnQgS1Qn1ZuieDWetTvBZVniwwM3uja/Hl9w+2IyXXVO58DqfE7nRSJ8yXHo0jaBITyN6TheSZbPy56yRmm5N3ftvDtAfa8s/+LKSUDGgZS7j/KQerv86ffnX7lbsZd6vZjbXHXWlSThafrPDGGWmMLJfBeHf2bjamb6RDdAfqB9dHg4bm4c3Zmnlq4adaqEkMS+TjTR97ZClFKVgd1korliBDEA81e4i7Gt6FRGLUGPHT+ZFpysRf64/ZYaZtVFu0ai3rT6wnyrdyN9qKKGvuyzXn8tqq11iRugKAz7Z+xvCE4TzS/JEK1/zYMzI4PvYZz3bRihWcHP86Nd57F7X/xa2PkRaLR6l4sNkw79yFtpKKJW/+z+VkhYt/I2zUqDMqFiEE2ogIQobdfb5DVlA4L64r46qPTs0tzcr/cHslRPHD+mN0axTJB3/s9SgVgIISO3M3pXJXm5oMa1eLF25uRF6JjSW70+lUL4wQ3/IZak/mm5m+5hg9/vc3EQEGfHXepqHYYB+CjTrmjOpAdKAPcaFGusaHUyPIh+hAl9ln3eEc7vxyDesP59A6LoSaIUYvP48Qgh61ejCiyQgMagMGtYG7G91NXECcJwVLz9o98dP4EWmM9BynFmrGth7rWdiXZ8nj3fXvMnPPTA7kHeDxZY8zcfNENELD+13fp3WkK9Q9yjeKD7t9yC8Hf/FyjN9R745KL8YsxaAxEG4MJ8IY4Tk2UB/IWze8xdSbp5IYnki9wHp82/tb6gScv7O9IvIseR6lUsqM3TMothVX2L5w+fJysqIVK5BWK7b0dLKnTSdz0qdYjx7DWVI+dP2saDQIn/LmPU1k5WcPujpx5WTa6CiE+pqsz6dwlXFdzViCjDpa1ArmlVsb88my/ZitTvo1j+bGxhHcP3kDvZtGVVjMKy2vhNE3NuB4vpkhX62hdILy+YqDzBrZnrWH/vVSRr2aRDHulx28cHMjfk0+zmfDWvLmot0cyCgiqWYQHw5uTp0wX4+zNsjoetqXUvL6bU0YMXk9Tgk2hyQtr4T4yIpv3MGGYB5Leoz7Eu7DKZ0czj/MS/++hFqo6R3Xm7sb380XyV/wQdcP2J61nQJrAR1rdCTG91Ti6BJbCcMaD2N5ynKc0smDTR9k2q5pFNmLiPaN5uPuH2NxWFAJFVqhZVP6JgxqAyqh4r6E++hYo2OVfDY6tY744Hju/PVOimyuTNBTd03l+1u+Jzkzmc0Zm+kV14u4gLgLqlWSb8kvJ3NIB1ZH+egxAF3t2uVk2lq1cFosHBkwEEdeHgBZn39O7RkzMLZIqvRY1AEBhD/9NBnvvuuRGdu2RVuj8ia/gJtvJmfKVOzH3QEKGg2RL7+srElRuCK4Llfep+eXsC+jCI1Kxd/7Mpi+5ijFVgfvDGhKQYm9TOp6F+8PSqRzgzBGTt/EtlTvG9SYng0I9NHx9qLd+Bs0jO0Zj8nm4O3Fu5n3aEf6f7aa5rGBjOhUhxqBBg5mFtEtPhw/g5YAn/KznWKLncwiC0t3ZxAdaKBNXIiXCexs5JpzPRmF15xYw+q01bzX5T3eWvsWZoeZuoF1GRg/kEbBjTxZiQ/lHWLIoiGe9PY6lY4ven5BtG90hVUlzXYzhVZXwMCFREblmHNIzkhmzfE1dIrpRGJ4IiGGEGxOGx9s+ICZe2Z6te9QowOtI1vzyZZPAHim1TMMbTTUK6tyZcg0ZXLbz7d5lBZATf+aTLt5WoU1W+xZ2Rx74H4s+9wZmoWg9pzZFC1bTvbnn3u19WmRROxnn6EJrvzKfkd+PraTJyn+91/0jRphaNTovJ3o9sxMitevx5Gdg1+P7mhCQ1FVMBNSUDgdZeV9NaDVqFiyK52pa456yZvUCMRPr+FkgZk5G1LQalTc3zEOg1ZNfomNrDJO/6YxATSODkCvVtErIZIeDcMpsthRqwT9P1uNWgjMNtcsZmtqPmPcpY4Bfni4PT+s38Nr/Zp4yhGX4qvX4KvXcG/7WhSU2DDZHKTlmvDRqXE64Z/9maw/nEOfplEkxgYR4nvqxh5sCKZeUD2ySrLoV7cfg+MHoxZq3uj0BuCaFZS9iZrtZr7Z/o1XzRSr08qMXTN4tf2rFV67sivg88x5JGck89vh32gS1oTuNbufdW1HnjmP19e87qkjP2vvLHrX7s2rHV5Fp9ZxsvhkuWOyS7K9cn59vvVzbq17K+Ga81t3EawPZnLvyYxbPY49OXtoGdmSNzu9ecZCYJqwUGpNnkzJ9u3YUlLx69oFdVgYuTNmlGtrz8pGOs4vtZ06MBB1YCCGhudOFnomNOHhBN566wUfr6BQXVyXiiXEV89j3etjdUjmb0klxKjj+T6N0KlVFJlt9E2MZkTHOCSwPSUPg1ZFkI+W25JqMGX1ESYOaUGOycrGI7kEGXWk5ZUwavomEmMCefmWxjx9YwPe+W0PNoeTCH89GWUUUmSAHqNOzd3tapNZaMEpJeH+BhxOp6eUcUGJjYXbjvP24j0UWewkRAfw+T0tGTt7K5uOuYpZzdqQwj3tavH8zY0IMJya+QghmLJzCv+m/cunN37KhM0TWH9yPVqVluFNhjM8YbjHoW9z2sqVxgVoEtoEu7STVpSGWqgRuEx2GpUGq8PqUi5qAzN2z+DLbV8CMHf/XGbunsnXvb4+o3IpshV5lEopfxz9g7GtxxKoD2Rww8GeKpil9I7rzZrjp3K3mewmz8JPcOW+qoxfQaPW0Ci0EV/c9AUO6UCr1p61GiaAJjQU/27dvGTBdw2h4JcFXrLAO+5AHXT2vhTOTWU/S4Urn+tSsQDY7E76JUbxbM94LA4HDieYbQ7C/A0cSC/EKUEAarWKEKOOe79dz7cj2tAmLoRfko97cnbN3ZRKq9rBvH5bE578YQsxwT70bBzJ70935lBWEdMfbMe4X3aQnJJHUs0gXuuXwI60fF5bsIsSm4PG0f58endLZq0/RrCvngEtY7DanLw8/1StkpQcE8fzSjxKpZSZ64/xeI/6HsXicDpYfHgxCw8tZESTEfyw5wfWn3RVH7A5bXyz/RvaR7enXXQ7wBVZNjh+sMfZD/BY88fw1fly96K7aRfdjhi/GLrV7Mb/rf0/hjQaQnJGMvty9/FB1w+YvGOy13j25+3naMHRChWLUzo99VNOp9TP0TSsKf9p/x++2vYVFoeFIQ2HEB8c75Vav0N0B3w0Ptizsyn6+2+K167Dr1tXfNu3r5R/oWxZ5AtBX78e0e+8Q+bEiTiLiwm6cxDBQ4eg0ly3P6WLxp6bi3nXLgoW/Io+oTGBt9yCRskEcFVzXf4acoutFFrsLNh6AoM2gztbx/LpsgMs3nGSlrWCeeP2Jtzz7TqKLXbublebu1rHotWoeHDqBr6+rzW/bvNOBLnpaC5je8Zj1KlZuT+L4R3iWLE3g4gAPa/+vJ2RXeoRGaBna2o+JquD5386lXp+94lCXl+wk7f6N8PhlBRb7Og1KgIMGs8aFoNWTV6Jd1ZjAKcEu+OUj6zIVsSSo0sAaBbWjP9b93/ljll8aLFHsQC0jmrNS21f4rsd3+Gj8aFrza58ue1LPur2EQsPLSTdlI7ZbmZMqzGM/Gsk026exvDfhpNrya0wbcrpTnK7006mKZNZe2fRs3ZPIo2RpJtOreup4VvDKzJsYIOB3FjrRiQSX40vB/MOEh8cT0phCl1iu/Bc6+cwFttJHT2akg0u/1rBggX49+lD1Ovj0QSev2P/fFAHBBB4Wz/8brgBiUTtH4DKoOTZulCcJhM506eT/Znbb/XLL+TO/IG472ecdbGnwpXNdadYCkw2dp7I595v13tKE/+w/hjfP9SOdYdz2Hwsly//PsjQNrX4/O+DTF19hA51Q7mpcSQTlu7H5i7ydTomqx2dRkX9CF9Sc0uoE+7Ll38fYuPRXLanbeGHke1ZczCbrCLvqDONSvBY9/q8/utOluzO4N72tbmrTU3mP9aJQ1lFTFx6gO1p+UT6Gwj00ZJfRsEkRAdgLBPK7KPxoUFQAzalbyLdlE5cQBw55hyv8zUNa+q1XWqC6lGrBw6ng9XHVzOiyQge/PNBLA7XWBccWMCsvrN4ssWTmGwmIo2RZJVk0SaqDRtObvA6f0Joglf/WSVZDFgwgCJbEWtPrOX9ru/zefLn7MjaQWJ4Iq+0f8XLz6FRaby2m4U348ueX+JwOjBqjfhqfbGkH/YolVIKf/+dyOefg2pWLABCrUYTrtz0qgJHURE5337nJbMdPYpl/35FsVzFVGehr++AvkCGlLKpWxYCzAbicBX6GiylzBWuuNsJwC2ACRghpdxcHePKMVn4dNlBL+VgsTuZtSGFm5tGMWPdMVYfzObNO6I9+//el0FcqC8BPhoEkoaR/uxNP5VKJdRXh59ei8MheaxbfdYdymFw61gaRvmz8WguJTYHL/60jXcHNiPPXU8lyKjlmZ4NaVk7CLVK0DU+grZ1QrDYnQz8fDUWu5PYYB8+GdqCjUdz0WkEPzzcjjcX7WbPiQI61Avl1VsTvJz/OrWOB5s9yJJjSzheeJw3O71JviUff50/RwuOMmffHLrEdil3TRzSwYKDC5i7by7/6/o/FhxcwJhWY2gS2sRVvljnx6b0TWzL2oZOpeOdzu/gr/NndMvRfL/7e9YcX0PtgNo83uJxNML7K/XT/p8oshXRvWZ37m58N1JKXmz7In5aPwwaAwH6gHN+ZqcX05Lmik1q0n4q07J0OLDn5CBLShB6vav2iNF4znMpVD3S6URaLAiDoXw+NKcTaS0/83UUlM/6oHD1UJ0zlinAJGBaGdmLwFIp5btCiBfd2y8AN+MqR9wAaAd87v5fpdgdTix2J0WW8qneiyx2goyuUM0GkX40iPBj9sj2SMBqdxAZ4EPvJpHo1Cq+G96ad3/fw5pD2TSJDuCVvgkczzOx4MlO6NQqnNLJ7I0pPNqtHkt2p2PUaehUP4w/d6YzrF0tbmgQxlM9GvDJsv28+ovLl9IrIZKxPePpM2GlZ0ypuSW88NM2Xrm1MWsO5XB78xp8MawlFrsTo06Nn9u3YnfaPTXTI42RzO03F7PdzA97fqB7re58s/0bzA4zI5uNxGIvv06nwFLAt9u/xWQ3IRD0b9CfT5M/5d31rnUWrSNb83TLp/nvhv+y7NgyEsMSeafzOwz/fTiD4gfxaodXyTBl8O76d/mo20eEcsrHkm/Jp2ftnvSq3YtnVjxDgbUAP60f79zwNm2DmsM5rEj2vDxkcTFoNCAl0mpDHRSEJiICe8apGim6+vVRGY1IhwOn2Yw9N5dj9w3Hfvw4Qqcj4vnnCLztNtQBLkVmz8nBaTIhVCpURqPifK8m7NnZ5P+6ENO6tfh27kxA795eYdUqHx/8unalaMUKj0z4+OCTlHTpB6tQZVSbYpFS/iOEiDtNfDuucsUAU4EVuBTL7cA06VpUs1YIESSEiJZSnqAKyTFZmbh0P8Pa1eLFedu99vVvEcN/f9+Dv17DK7ckMGZ2MjuOF+Cn1zBxSBJmm4NVB3JYuT+bhGh/XrylMXaHAykFJTYHUQE+HM8z83NyGguSj9OnaRQlVjsLn+zE/7d33tFRVV0b/53pJb330DtILwoIFgTFAqKIgBQBC6CIvWBDLBQbooACItJBUEBFpSpI7yAdQhLS+2QmU+/3x5kMCcH2iRpf5lkrK5Nz7z1nz8zN2Xe3ZxdYnazal8H5IhtZxXZeuLkh3x/J4scTF+jms0vsbD+Tf7HIHM+yYNCombDmF74+mMEng1oTFXSBsiPHmsPiY4s5kneEm2rcROeEzujVevZn7+eOundw96q7cXqklbT27Fqm3zAds86M3W3HrbgxqA2VCgVXn15NXEAcm9M2+9bYlbWLtWfXclONm/jq1FccyD2Aw+3A7rIz+9AFN4ZGpcGkrWwV9K3flyJ7EQ/+8KAvrdnitPDE5idZdf1C9BoD6l+xJFx5eaQ/+SSOsynET5lMxrPP4Th7FkPjxiTOnEHO+1MpO3QIY5vWRD/1FAhB/ufzKd2yBWOzZsRPmkj6mMdw5eSQ9doEAjp1Qh0UhCs3l7TRo7Ht3QeAuWtX4ia8dtmLC90WC0KnQ6X7ayzI/1W48vNJHTmSsn2SFsiyYSOW9RuImzTRV/OjDg4m9rXx5Hz4IZZ169ElJxH9/POo/YWe/2n80zGW6ArKIhMo5xqJByryxKd5xy6rYrE53Kw5mMkTNzXg2R4NWLIrDb1GxcNda5MYauT5mxvSIDaI51cc5NB5aYpb7C5O5likwtgvxdlwLJuvD2Xyym2NeWDebt66sxnHsorpXDeSX84X89Wojmw9mYPTrTB1/SnZwKtJDHe1TiCzqIwAvZ5Nx3MqyZZeaKNmRFXa85ggA8VlUjHsPVdIQakDo1aNWiVJIidsn8C6c+sA2ZmxV51ejGw+kgJ7ARsPb/QplXJ8tP8jhjQewpiNYwCoE1KHqddN5eNuH/P0j08ToAvgp/SfqsixO2s3t9a+la9OyVRbi9PCkCZDmH5guu+cx1s9ToC2MktAjCkGl8dVqVYGwO62k1+SRYQ6+JKKRXG5KFi6FOvWn4l66kly3nsfx9mzAJQdPkzqAw+SNOsTVMHB0lKxO0gfMwbrDpkFV7p5M5bNm4l65mnOP/6E/P4PHUITE0PerNk+pQJQumEDxV9/Q+i9/RCXgULeVVCAdfsOCr/4Am1CAhHD7kcTHf2fSqVVXC5cBQV4SktRGQyoAgJQB/w56h53UZFPqZSj9Kef8FitUKGYVBMRQfSTTxLx0EMIne5vT8Dw4+/Hvxa8VxRFEUL86bJ/IcQIYARAUlLSn7rWoFVj1qlJzbfyS0YxD1xbC5fbw/xt56gXHcB1DaI4nVPKhmOVN/1WyWFVqvHP5JZi0KqZP6wds346zbBOtSiyOfloQCsGfLKdt/s2Z+CsHeSVSktg4/Echl5TA48CCgpN44PZlXIhfTinxI5eo+K2q+J8qcw6tYoXb23E3K0XCjmzi+3sScnHEHKMNWeX0y25G10TuzJuyzgUFI7mH+Vo/lGOFRzzBd8rwu62k2O78P5OFp7knd3v0DqmNTNvnMmXJ7+kQVgDfs74udJ19ULrkVYi2ZTjzHHoVDq0ai1zu88ltSSVWsG1iDBFVGkiZtaZCTWEolFpKnWbVAs1IcZwLpkJAXhKSyn9USo4ff0G5LzzbqXjrqwsir76iqixY1EUBWdurk+plKPswAG0UdHSjeZyoa9TB4/FQunWrVwMy6ZNBN9x+5/ePKvIbbdTsHARue+/7xsrXrOGWl99hfZPcIH927CfOsW5+4dJin21mogRIwgdPOhPbfoVY16V4PFUGVIZjX7WgP8h/NMklFlCiFgA7+9yJ3k6ULGXbYJ3rAoURZmpKEprRVFaR/7JXPdgo5Y372zGd4czublpLM+vOMhzKw7x8+k87m2XxAOf7yZQr0ajqhxgVJRLZ4I53R4UReGJmxpg1qkJNWo5mlFMgEHD4fNFPqVSjgU7ztGtcTRLd6XRq2U8SWEXntSTwkxEBugZck0NVo/uyJzBrVn+cAe+PZTJTyelyyw53ESZy8MzXxwiyVyfHRk7ePnnl8m15dKjZg8AagXXYnPaZvZk7eGuendVkfmuunexIbUyweKe7D2E6ENYcWIFneI70atur0rkj7HmWO5rdB/7cvbRvUZ3Jl07icP5h7G77Tzw/QN8tP8j5h6Z6yukdBUW4khLw37mDK7cXEzoearZGN9xgWBMwwcxa0y/GlAXRiPGq5rJz/ncOfQNGlQ5x9RS9pt3pqbiyi+ochxA8bgRQhB8111ooqNRmUwYmjSpcp6xVcs/3AsFpJvLbbFUHS8uJn/WrEpjnqIiSn/cXOXc6gpXbh7pj4290LfF7Sb3o49wZWWhuN24i4rw2Ks+tFwMTWgo2ose/vT16/uTKK4A/NMWy1fAIOBN7+8vK4yPEkIsQgbtiy53fAWkxdK+VhjNE0P4fFsKSx/oQGqBjeQwIwatmoXD2+NSYM0jHXnpy8NsO5OPSoAQcH/HGoSb9VidblbtP4/V7ibIqOH2D7agVgleurUxNzSMosDmQKsWlepLyuH2KKiEwOpw89jifSwY3o7zhTaEEKgEfLLlDN0aRRMVoGPetrM83q0BAXoNtSMDaBofzKCrk3li6QE8igzs31LrFnrX7Y3D7eCG5BtICEygzFnGdYnXMaDRAEocJXzc7WM+3PchZa4y+jXoR52QOozfPr6SXA3DGpJaksqurF30rtub/LJ8Prj+AzJKMzCoDUSbo1mXso67693NuZJzjF4/mvyyfOb1mMenhz7F4XYwpPEQDGoDrvx8Msa9iGWddM9pExJImjOba/aUsfraz0gvPU+8OQ7l242o8n5B3b37Jb8rlU5H2ODBWNZvoGDBAqKff460Rx7F480WMnfuhKFpU9xFRWS8/Aqhd9+Fvl497MeP++bQ1ayBNjGRWt9+g8psRuMN0EeOHoV1288406VlqK9Xl9A+fRB/oMjRXVKC/ehRcmfMBJWKiIcfQl+njs/SEXBJehePs2odUnWF4nLiOH260pgqIACh1ZL/6VxK1q1D36ghEcOG/SbNvyYiguTP5pLz3vvY9u/H1KYNEQ8/5G8sdgXgbyOhFEIsRAbqI4As4CVgJbAESAJSkOnG+d504w+A7sh04yGKovwuu+T/h4QyvcDK5O+Os2JvOioBEQF6nr25AdtO57F4p3T1hJl1fDa0LQWlDmpFmlGpBPvOFTL357ME6DWM6FybcLOWPecKCDPreejzPXSpH8lLtzX28YmZdGp6fbi1UgbaXa0SiAjQ89GmU4SatHw56hoOphXz/MqDvjRkIWDZg1dj0qpJyS+lRWIIG47lcCLbwvI9aRRanQgBax9rSWrZEZ778TmsLisqoWJYk2HcVe8uThefZs6hOaiEiv4N+qNWqXF5XLg8LoL0QUzbO43d2bKPS4Qxgre7vM1Tm5+iS0IX3IqbpceXYtKYmHztZOqH1MehOOjxRY8qn+XUrlMx68wEagOJNkcTagil6OuvK/UxAQi44XoMjRuT++FHaEJDcBUUgtNJ3NtTCL755t/8vly5ubhychB6PSqTCWdmpixKDAhAHRyMu6SYU91uQhMWRvyUKRSuXIltz24MTZoQ2rcv5196meinn8LUokUli8SVm4szK8tbkxL5hze70h07OHffoEpjNRYvwnjVVQB4ysrImTqVgoWLUKyyF48wGqn99ddoY///vWX+Sbhycznb716cqRfCnlFPPoFt/wFKvvvON6aJjqbmsqW/WyXvtlpRrFZUZrPf3VVN8HeTUF5x7MZp+Vbu/WQ75/LlP31CqJEXezZixLzKDbMaxQYx6a6mRAUa+OZQJi9+edh3TK0SLBrengc/383DXWvTLD4Yo07DcysOciCtCCFg/G2NaVUjjPfXneBcvgzeX5UQwgPzduNRFCbfdRUNYgN5ZvlBdqdUduP0bBZLvegAfj6dz1M31eN8YRmPLd6Pw+1BJWDU9Un0aK7loXUPVuH6Wn7bcgZ8PaBSsHza9dOYvHMyH934ETanDaPGiNVlJceWg0qoeG/Pe5Q6ShnfcTwjvhuB1SU/m0hjJAtuWYBaqOn1Va8qVfVreq0hUBeISWNCr9GjKAq5H32ESqdH8bgp/vob7EePog4PJ/7ddzk38EKrY1VgILXWrL5kR0a3xeJNKw6uEkx35eWR9/l8yvbsQVevHhFDh3L+qaew7tyJOiKC5E/nULp9B44zpyn68is8JSWoQ0OpsWQxusTEKmv9GbiLi0l7dAzWnyvHnwK6diVu8iTUZjOu/HzchYW4i4rAo1D8w/eE9rkLbVIiKm1VNuvqCEVRsB04QOrQoXhK5b1Qc+UKzvTqXSUmljTvM8xt2vwbYvrxF+BnN77MsLs9NE8M8SmWGuFmDqRX7dVxJKOYEKOOY5klfL6tMguy26Pwxd40pvZrQUqelTe+OUZ4gJbnejTkRHYJL311mBe+PMz3j3Xmlmax5FvshJl1BBo0fDKoNdFBen74JYvWyaEM6pBM88QQvtiTRoH1grukTlQgfVol4vZ4iA8x8f3YzuSU2AkP0KNTQ6mSfkkCycO5h4kxxXCm+IxvbPnx5VyXdB15tjwEgn5r+hFjjuHxVo9j1Bp5pu0zhOnDeHjdwxg0BpweJxqVhutiOmG2uNAFBjCx00Qe3fAoZe4yVELFyOYjCTGEEKS7UODoKS5GX7s2+XM/Q2g1RDz8EGWHDmE/eQpdUiJRTz5J8bffok1OIuqRR6tYCR67Hce5c+S8/Q6unBxC7ryTwO43+VJTXfn55M78mKBuN2Jq2gR1cDC2QweJefllUgYNQmUyYdu3n6zxlV197oICXDk5qIzGv6maW262Mj36Kaze5ABNVCTJ8+ZdsrdLdYYQAkOjRtT65htc2dmyxucyZMv5ceXgilIsuRY7H244xZBranAgrZD8UgctkkK4uWksNoebZbvTfJQpjeOCyLHYpesJUWUulRC4FYVZW84w7paGspDS7aFzvUgWDG/HgE92sPNsPnO3pviq9NUqwTW1w3nk+rrc0DCGmT+e5udTeTSICWL24DY8+8VBjmWVMOSaGqw5kEmAXs3SXWl0qR9FZrGNGZtOY7G7UAnBN483J8IYQa4tt5JctUNqk2m9iH5eQP2w+oQbwtmYuoEydxlni88yesNo9Go9Zq2Zld0X8+FVr6EttmEOjUKt1mD58UdyJo/E0LgxzR9/lDW915BnyyPUEEqANqBK50jr7t2kPzrmwt/bd5A4cwYhfe8hbezjaENDCbq5B4rHgyrAXCX91pWby9k7+/gqsTMPHcKVm0v4iOGo9HoUp5OAjteQOuIBPKWy82PgTTcR9czT1FzxBe6CAoS2as2IOiwMT2kpiuvS1PYehwNPSYms0P+NrDB1UBARDz3IuYsslogHHkRtNlO0YaNPqQC4snPInPA68ZMn+Qoz/ytQabWooqJ8FqW7qIiAG2/EcpErTF+jxr8koR/VGVfUY4jT5cHmdPHE0gNM6NWU1Y90JNfiYNSCvRRYHcwd0obakQFEBOgY17MRPx7PJa/UzsAOlZ84NSrBwA7JfLrlDJP7NGPcl4cZPGcn983ewf1zdxEbbOTOlvE0jQ8mvfCCS8rtUagfE4TD5eHp5QeYs+UsRzNLWLkvnZHz9/BG76bMHtSGrw9moNMIIgMN1IkKoHZUAJPWHqe4zCWJJz0KH/2QwZRr38GsNRNhjCBEH8KIpiNQC3UlN5hAMLzRUK511iK4xEPnSMkOrFFpiDZFoygKnaI7oN7zC4avf8KYnkfeM8+TNeZx9MGhhA0eTNGKFaT3uYewUkHD8IbEmGOqKBVXYSF5n1TOhgIoXLYM+6lTlO3eTckPP1C6ZQumFi0oO3yYshMncRVccAMWf/1NFXqP/HnzfPQeistF1htv+pQKQMnatThTUxEaDYZ69dCEhxE5dix4lZbKbCLm5Zcp/v57hLbqc5QrL4+8j6aTMmgw5595BvuZs7/ZW8XQsCHJ8+Zh7tgRc+fOJC9cgK5ObYBLpjGXHTqE51coaP5LUAcHE/vyS0Q9+STGFi0I6d+fGosX+VmI/bgkriiLxe1R6Nc2iVX7z5MQamTEZ7s5mimtiVM5FvakFDB3aFvsTg/PrzzI4fPFfPNoJ3anFPBO3+as2JOGSa9h9HV1KLI6aV8rnG8OZZJZdGHjOJltYdGOVEZ0rgUI6kYFsDe1EIDmiSHc2y6J0jJnlbjK+aIybE43s346zaM31GPNgQxGzt9D88RgSiskALRNDuGtHrEElJ4jlBi23rIMd/4ZVPoAstRq1mbsYM5Nc5h9aDYalYb7k/sStHwjrlr1UYeEEAb8eN1KSlxWSk8cRZsQT6A5nJyxTxI5YgRpI0ehTUoiZtwLKG43qrAwkhctIvOll3BmZKCJjMSZnw9lZdhPn0YTHY0mLAyh0aCNjaFMp6usHDRaX6aWoXFjwocNJ3X4CJ9yMHfqRNybb6IJD0NcIgYhvDUoACqdntB+9yD0eiwbNmDZuAkUBduBgxi86cjqoCCC7+xNYNcuuHJzEVotBUuXET50SBXXmys3l8wJEyj55lsAHCdPYt2+41djPwDqwEBMbVoT36A+CFHJwgns2oWiL76odL6xZcv/mfRaTVgYYYMHEXJnb4TRiErvZ3X249K4ohSL0+2h2OakZ7M4nG7Fp1TKcTbPSmZRGU63h1CTDqvDzbiVh3jl9sbYHG5evLURxTYXm49n075mBF0bRHE4vZhFI9qz5mAGs36ScY0D6YXc2Sqel1cdYlr/lpTaXahUgvOFVl768hAv3tr4kvKZtBoe6FybDzecYsMxWeKTkm/lvqtroBIQF2JkWo8QIpdcD7oA6D0T5t6MyipjLXGxzRnQbyEWnZFHm40kcO9prA+PJ/yll8iZOhXb3r2ADJwnTPuAokkfYj1zBu3oUUQ88ABFK1Yi9HriJ07k/LPP4DhzFgBNTAxJc2YjNFpcJSXYDx8mbdRoFLsdhCB20kRMTZsSdMsthA4YgOXHHylevQZ3QQHhw+7HsnEj4cOHEdy7N+efeKKSxVH644+UHTpEwLWdCep+E7lTp1Y6HvHQg5IvzOPBU1xM8Q8/4M7LJ6TXHQTdcgvnn3wKU+tWlVxN2vBw3DodqsBAPFYb0U88jvqitsHuoiJceXmUrP2u0rinpATbvv1ou934m/eSOjCw0t8epxN9kybET32f3PenYj9xAl2NGsQ89+xfLrqsThBqtZ9XzY/fxRWlWEx6NY3jgjl8vgibQ0ugXkNUkJ60Aht2l6wGDjJq0apVPHdLAx7qUhuXR0GFYP62FNrUDKfA6iDUpGfl/vPM3XoWkCnCL/ZsxN2tE1myK5WOdSII0GkYe0N9jmYUUzPSzLFMC063h5HX1eGX80Xc2DCa73+50JekXnQAMQEqooxqn1IBaWWt3HueN3s1wWkrInzzOLDmQ/uRsHkSWCsE8DP2oTn2DZq4G4lWBZA/7WM0oaHYz5z2KRWQm2fOe+8T2u8esl5/g9wPplHzqy8RBj2BN95I8bff+JQKgCszk7zpMzC1b4epbVsyX35FKhUgfMQIHCdOkPHEkwDoatcm5pWXCe7ZE6HR4Dh/nqDu3SnZuBFXZiZlxy7UmZTDum8fAdd2RhMRQc0VK8idOQNXdg6hA/qjS0gg6823sJ88ibldW6Iee4z0Rx4le9Jkop58gpjx4y9ZS6EODEQdGIgrPx/cbhRFqRQpcxcV4S4uRqjVKBdVgv+ZQkmQSQX5n82jePVqNNHRxE54DXVEBCqdzk/97scViSsqxhJo0OJwe4gPMeJRYPaQNtzfsRbLH7qa5Q9dzUcDWqIScCyzGLcHZv90hkGzd2BxuDDrtUxae4xvD2XSKjnUp1RAZmC+9e1R+rRK4Np6EdzUOIbOkzeiFlA3OpDBc3by4Oe7Gb1wLw9/vof4UBNP9ajPK7c2pEv9SJ7uXp8FQ1sQs/BGVKVZVSr/P916liZRGvo0DkKVc0QOhiZD9i9V3qNI3YZy8AgGtJiuvhptQjz24yeqnGc/fhxtfLzvDXiKigjt1w9tUiL2E1XPLzt+HE9xCa6cHJzpXlIEIQjs2pW8jz8BIOD664l5/nly33ufc0PvJ2/WLFmUqNGQ+867lB07hql11QzHgM6d5HQaDSqzCUPjxkSNfQxD3bqk3Nuf0s2bcZ0/T9GKlWRPnkLEyIcBKFr5JYYG9S/pQnNbrZTu3EXqAw9y9p5+5E2fIZWMF460dCwbNxHSp0+l6zQxMRgaNawy36/BbbWS89575E2fjjMtDdvu3ZztPwAUxa9U/LhicUUpFpNOQ6hRS2SQnkU7z3HX9J95bsVBek79ib3nCjhyvphfMorJKCrjqWUHGNapFuNvb8ynW84y88fTpBfaOJ1TyrGLXGgAZU4PoSYtj3erz6yfTlM70oxRp2bprlRS8qxEBOh45roEZvZOxF6SR7QjjYH5H/BxV4UH65cSkfIt9HyHMMd5+rdNqDR3UpiJSHcO+r2zocY1cjDjACRfU0UOpV5P7CdOkjpwEMG33QYqFaY2VTdzU9u2lB2V/GdCq0UdGoowGgnu1QvT1VdXOd/cti1lvxzBceoUhkaymZcwGLz1Gh7QaIgYPpzUhx7CunMnrsxMChcvIfcD2VZY6PUUzJtH9PPPoatZQ06qVhM66L4LCs4ri75hI5wZmVj37MFdWFhJDtuuXejr1pXnmkyow8IuWdzoysri3KBBlB08iDM9ndxp08ibPcdHRaKvWZP8zz/H1KEDMS+9RMB11xE2ZAg1Fi7wKQRXXh7W3bspWb8eZ1YWyiWq5z2lpRR9saLyoNOJZd36Kuf64ceVgitKsdhdbtQqgc3hYeGO1ErHJq09Rqe6kYxZvJ/kcDM5JXbunvEzHWpH8OW+C62I80rtxIcaq1gV4WYdGUVlDJy1g1HX1mR++3Ti9HYOpheTFGZi1aBaDC94m1ZretBh92MEahVUDXuiPbgQMaMzfDEc5vRAl7mHp7vE8sEdydzUOIYnusSzbFgLIk8ugb3zoN2DEN+y8msAoUJpMRC7K4rcadNwpaWR9sCDBFx3PaZWrQgdOMCXKaVv0ICIBx+gYOEiVIGBxL4+gbxPPuFsr96cGzCQgI4dCbjxQozB1L49gTd1o/ibb8mbPYe4SRPRJiai2GyogoMQej2GBg2w7d/vc5GVo2T9eoQQaGvVImL0aBCCuClTqLVmNbW+XInKaKLku+98mVjuggI8+XnkvPvOpau0K6Qohw8ahKe4GMu2bbi9Ve7lcJxLJeHDaSTO+oTEj2cS+9pr2I8f91HCqIICiXt9AhnPPkPRV1+hb9iQkL53o/EG7V25uZwbPISU/gNIe3gkp3rcjP0impNyiEsE51XB/630Yj/8uJy4omIsJWUuHE43J7OrWhx2lySUBFi9/zzT+rdg2e50Su0uzHo1Dqv0w3sUWLwzlbf7NueFFQcpLnMRGahnyl1XsWjHOYpsThweFbGW0yi2dHrUMxPbIoDYNYMg09sD5sxmxKc3w/D1oLqISn3TREwNb6On7StuiLChLUpBPXMT3DgeHtgCeODW90GjB60J+swFjxNUajweHWevkxQpAV27ENTzVlR6HYrHQ+h9gwgbMABFUfCUluIps5P40Yeow8LInjiJkrVrAfmkn9J/AMmLFhL9zNO4CwooO3TIF6zX16mN7fARop96CnVICCqjkfj33yNv+gxUAVVp/4U3XhE3aSJlBw/hTE3FXVRE4bLlmNu3w2O1UrDwe4J69EATHk7R6tWY2rbFfuw4QqVGX69uJVdecK9euLKySPzkY6zbt6M4nWS//z5Jn3yMuqYkznRmZlK8ahWh/e6h6KtVlKxdiyYqkojRoymvG1ebzQTecAOmNm1QyspQGY2ow8J8tTUl69ZVcgkqViuZ418j4YOpPs4xAHVICJGjRpE1YcKFsYgIAi5h9fnhx5WCK0qxKB74JbOEpDAzWrXAWYEoMjJQT5lTPjUHGbUs3pGKR4FAg4ZR19Vh/OoL8Yz1R7MZe0Md1jzaCYvNQbAOjOd/pmHrEIa0rEeupYzo1g9gmNaMG+5agT4w/IJSKYetADIPQfMBcHAplHmr/90OGbS5qj+G9J1wLB2ufRrqdQNLNnw3DjL3y+s7jII2w+DMJgirhQirhyYyUvYV0enIeXsKHquN4N69MbdpQ+qoUeB2EzdpIrrkZMp++QXzNddQ8sMPlUTzWCwoZWXo6tTBFRAAQpAwbRqggBCk3n+/j+rD3LEjkU88TtyUyeDxoE1IwJmW5psrfMhgrIcOY27VEuvWrZR8/z2ayEgiRo6k7PhxQvrciTP1nM8S0YSF4UxNxdCoERmvvkr85ElYt22Xwfurr0ZXuxZFq9dg2bABV1YWNebPlxT6q1cTNXo0rqIi0sc+jrFFCwqXLqVopeQ5dRcWkv7oGGp9vQa8tRcqvR7Vr6QV2w4crDLmOHMGLnKHqbRagm/tib5ePQqXLkVXswYhffqgrsbxFXdJCe78fKy7d6OvUwdtQsJlb3Lmx5WNK0qxgMLaw5kMbJ/MW3c2Y/zqIxRYncSHGHmjd1OmbTiJSaemd8t47pu1gxK7i4gAHYPbxdIsphWL9mRSM9zIXU2CiFzYHeu9K4lzn0Jt9YCwwE+TCQ+I42jrVylzmTE4SglbcgfKg1tAqKRmqwh9AJzaAInt4IQ37dUcCQ6LpMndNRsiG8DZzZB8tcwAq90FOo0FjwtUGvigtXwNqKKbkDz3M2yHT5P+yCO+ZfJnzUKl1xN4ww2UfPstWa+/Qdxbb1K4ZAmmtm1JmDoVVWAAnqIiilatwpmWhqekBPupUzhSU8l+8y1cBflEPfUU5rZtiZs8BcvGDRiaNEFlMpM382MMDRpQtGIF8ZMnY/npR5ypaZKBuHFjnGlp5Lz7ri8W4Sgp4fyTT1Lzy5XY9u4l/IEHfbIGXH896WMeI/qF58kY9yIpAwYSeMP1hI8YgcpsJnXYcJzp6ahDQogd/yqFXywHlwtNWBhlR49h27ePqMfHojKZOdu3b+XP2+Oh5Lvv0I8Y4Rty5eVJJgC9vlLKctAtN1O0fHmly82dOuFITUVxuVGHhfrqONQhIZjbtcXYqiVCra7a170awWO3U/LtWjLGjfONBVx3HbGvjf/XlIuroEDWKmk0PvoeP/7buKIUS0SgntY1wpj7cwrDOtbks6Ft0WvUGHQqvj2YSfPEEJ7t0YB3fjhBibcosVd9HWGf30iY20HL5E6ocwuhoC90eQaTpxSx5R048T2E1YLub6LaN58a+hL05S16nVZE4VloeR/s/vSCMAltoKwYTGEgvO6wkCS44yPQGAEh3V3ntkKb4bD1Azi0VJ4nBIzeBwvu8ikVALIOoXblU/Ld2irvvfibbwi//35Kvv0Wd34+KoMRYTAgtFpKt22j6MuVmFq1Iuj2OzA2bUrhii8IueMOsia8jjM9naQ5s7GfOkXKoMGyJuWOOyg7dBhVaCjBPW/BVVCA48wZzg4YQEDHjmgiI8mf9zmRo0ehr1WL4tVrKgvk8VC8ejVlvxwl88WXCOl3D1FjxqCJjCT+nbexbNpEzMsvo4mIQGU0yOQCrZbk+Z/jLinBlZ1D4eJFlHz3PeqICIwtW3LmzjvBG6upuXIF6pAQXNnZlZYtD8x7nE7sx46R8cI47KdOEXxrTyJGjUZoNQi1GkOTJoSPGEH+nDkoTiemdm0JvacvqSMeQCkrI3n+fIxNK/d1Uf0B2v2L4crLo+zoURypqQRcfTXq8HDU5qouxcsFd1ERWW++UWnMsn49ruzR/7hiUTweHCnnyHj+eWwHDmBs2pTYCa+hq1HjsnTy9OPfwxWlWIQQXFMngj0pBQz4ZDsd60bQJCGY1onyKalFUij3frIdq0NuTi2TQgnL3AI5MntKne8N3uYeg/7LECsegJQtciznqNzoR2zCUJiGyhwEsc0hYx+sfR76LYTa10slVLsLhNeBVWPgjmmQ3AGuHgWuMuni0prAECjdZKHJ0rJZM/bCG0m6GlxWKLhANOlDUaovAF0R6tBQ3BYZWzI0aYIjLY3YV1+lcNFiHGfOkPTxx1h37UITEoxiLUUdEID96FESP/kYoVJTduQInsIiEmdMp/jrr0l55VXf3IE3dSPy0TFoExNxpqZi2bgRgKhnn0FXrx72EydRh4biysq6SKYwPCVSpsKFiwgfNgx1cDDa6GhC775b1p5UePp35eVRsn49xubNQcj3FDF6FCF33knB4iVo4+NxnjsHQN6s2UQ+NoaMZ5/zXa+JicHcUaY2uwsKSBl4H4rNJgP3ffpw/oknsO3di6FhQ2LfeJ3wEcMJ7X8v7pISrNu2kf7II77gf+Zrr5E4/SPweLAfP4H9xAnMV3dAExX1h3nBXHl5pI4YQdlhmUKeJQQJH0wloEuXv62NseJ0+tyYFeFMP+9jL/in4M7L49ygQT7lb9u7l3ODBlPji+Vo/VQx/2lcUYrF41GIEoU83jmaEZ1qoKAiq7iMmT+e5kB6EVP7tcCs1/gUS2SgngBbBlz/EiS2AVuhtDoy9ktLoVyplMPthMyDqGp1AZcD+n4OZzZC+l6wW6RCiG8BJ9fDmc3QZ7ZMG7blwb750P0tKM6AvFOQdUgqm5TtEHiRogiIhrzTULcbHK1sCXgimxFyVwsKly7zbdqo1UQ+9hgqnZbIJ5/A1Lo1lo0b0UZHYd27l6hHHyVl8BACr+uK0Gg49/qFJ9rkBfPJHP8a9l9kjMnUvl0VTrCStd8RNmgQcZMnYd25C+f58wRefz14PHiKiiiYN4+Ihx4k8+VXfNdoYmMxXtWM7LfeQh0SgqFJkyqcWhWViqfMTt4nswjqfhMp9/RDHRqKqV072dzr9GlUej1RY8aAWkXGc89RvGoVkY+NodY3X6PY7SgOB5rERLReV4v9+AkUm+RUi3xkNOeffMpXn1N25AgpA++j1urVaKOjyZn6AUXLllWSzZmWhlJWxvmnn6nUEjn65ZcI6dXrD9GdlB0+7FMqACgKma+8So1mzf62jVVlMKCrXRvHqVMXBtXqP1W7c7ngLi6pYlG6cnJwFxb6Fct/HFeUYnFZctGsGk3kye+JrNERrhtHDay0jNtFSeuOHC/KYdHwdmw+kUNmgZV+rWNQGe6BvZ/LoLklC657Aa7qB4pb0qo4LmpPGxgLZ36EpHaQthNOrINmd4GtCDQ6mHsrlHjZh9e9CkO+gaOr4I4ZgAeiG8PJddCgp0xBi2suXV8ag7RotEZQ68AYDB0fkwrrzCbQB6Fc9yKuQgdZr79J0qxPsGzchMdaStAtt+DMyCD1yacIuasPSpkdoZIV58G33ELep5+iWK0E9+pF+mMXLCNdnTo4zp3zKRUA3J4qAWwAV3Y2mW+8Qdxbb6E4HGSMG4fr/HkSpk7Fce4crtxcEmd9gmXjRjSRUQTd3IPUBx8iYtRITC1byh4qp0+jCQ5BE1G1LsVdUoztwH4Crr2WhI8+QmjUuItLsO3bV6kVcMC11xL9/POU7t6Np6SEjOeeo+zwEfT16hE3cSKawECERlPJ7aMODLxQ9OmFp7gYV1Ym2qhIwgYPIvj221BKrRSuWEHJ2rUE3nADnrIyrDt2YGzVirAB/VFHROA4m4LbYvlDisWRklJlzJWTU6XnicfhwJ2bS8m69Qi93sdS8P+xajTh4SR+OI20R8dgP3oUTWQkMa+NR/0netlfLqiMl2Y4+F/hVruS8a8oFiHEWaAEcAMuRVFaCyHCgMVADeAssrvkpRuZ/z+h9jhQnfJmQF11D/w4BfWJ71ADBiD8mjG4E4YzJO1luZmXjQC7kBt6y/uk+2rFg3Dfl6A2wDWPwoYLaabENIPw2nIsujF8MQxaD4Xze6WrLLzuBaUCMgPsuxeg22uQdRhs+bB6zIXjUQ3hnoWw/jW4+zM4vhaa94NT60Glg6wj0Ph2lJteRzGEoWgDKV24BOvOnaT0H4CpQwdUBj2pDz5E3MSJKHY7BZ/PRxsbR8jdd6G43ehq1qJwqYzdqHR6n6sHQBMWiut85Q7RbksJmrg4XOcv1PYIgwF9nTrET5qEZf168j+dC0DgDTegTUwgbtIkrDu2kz15MrrkGhivao7bYsHcsSPqwEDODRnqm8vQqBEJH32INjq60rpCrSZ8xAisu3eRP3sOisNBjaVLSB87ttJ5lk2bCB8xHHOHDpzt2xdXdg4gmQZSBg2i9upVsmNkTDTG1q2x7dqF0Ghlv5GLqV0CA3Hm5JDzzrtYNmxAHRJCxMMPEXDD9QR06IB17z4Ce/Qg+JabyXprIs7UVIzNm2Nq0wa8RZvu4mKfJaYOCUGlu0Drb+7UiYthvrpDFaXkOJvC2bvu8tUIqUNCqPnFcrRxcVWu/yPQJSeTNHuWnM+rZP8u19tvQZjNBN1+G8VffuUbC+rZE9XfGGPy45/Bvxkh66ooSvMKXcyeAdYpilIXWOf9+7JCJRQwhcsYRkjyhUys8uM/T0WLGxJayfNyj8EPL0FcC4ioK62Duz6FX76CvZ9B/e4w4AuZ9nvr+3DPfNg8BdqOgLM/gscNje6ArVPlfMVpVYUqTpeZYFoDbHxdjgXGwE0T4JYpskblhldkQL/lffDpLbDhdZjdDTIP4ap5K/YCNUXfbCZ32nSMLZoT9fRTKE4npZs3U/Ld96hMpkoV7EWrV1OyfgOnbriR4jVrCLpZth22nzqJsUVz33llR37B1LaNVLJe5E6bRvzbb6P3+uO1iYkkfDAVV3ExBfPn+5RK+LBhmDt15Nz9wzjbuzeWTZuJnziJ8AdGoImJxrZ7D2EDB5Iz7cNKH0fZkSOUHTzoo8ovhyYsDMXhIHfqB7K3itOJp8RySetJcbnw2O0+pVIOT1ERrtxc33wJ771LwrQPcBUXEdKvX6VzA2+8EZXZTNabb2FZtw48Htz5+WS9NgFD3bpoIiIwNmlM+OBBpI993NfG17ZvH2kPP4wrLw9Xbi7nn3+Bk126crrnrRQuXYar6EJTOU1EBHFvT0HtVUKmDh2InTChkvXgLikhe8qUSoWn7sJC8j6di+cS7/2PQhMWhjY2Fm1k5L+iVAA0wcFEP/00SXPmEP7ACJJmzyL6uWcr1Qn58d9EdXKF3Q508b6eC2wEnr6cCwihga4vSIvCdgljyOMGp00qkKtHwQ+vQucn4eunZKA8sj7cMR3i28A3T8LZn6TV0moQaM2woA+U5sqaE53XnBcqafGkbIGuz8OBJZXXbNxbZoXpzFCaI5VM389h/QQZ9DeGSplrd4WvnwTXhQ1GKTyHK6+A888973NX5c+eTeRjjxF8550yXVYIIkePorBCjEAbE4M7Oxs8HnLee48aixcRcs895M/9jLg33yD77bexbt+BKiAA1Gri355C1utv4MrNRR0SgjookIhRIzHUq4ersFCyCBcVETV2LB6LBdvBQwR06ULKgAG+Na0//0zWW28R+8brqOLjsR06hOJ04Kmw0ZbDlZePx26n4nbnKSuj+KtVlc8rKECblOQL2IPsL6+rWVNaH0Kgr1eP8PuHoomNxVNcjKqCC0wTHi5jQYCxaVOCb7mZ0q1bMbVoib5hAxSPB8v331eRr+S77zA0aIA6NJSyo0er9JBxnD6Nx2old/p03/We4mKyxo/H2LQJmmbNAFAHBBDUrRvmq68GBKgEmosC/4rDgSvzosZtgDM1VVLM/IPtjt1FRdL6EgJ1UDAqw1+nzdeEhaHp0B5zh/aXQUI/qgv+LcWiAN8JIRRghqIoM4FoRVHK/S6ZQPSlLhRCjABGACQlJf3JVd0QFCfjGhp91RhJWC3IPw11rpepwO0fgPl3gd0bBM85Bgv7wvAN0PU52Po+LLhbznPfV9DxcRmMP7UBOjwM8a2h8BzENJUFktZ8GaPZ+gE4rdDsbmhzPxSdkwH7OjfKNOQfp8DpDXJNWwF8/TiM2CQVVAW4a/bAkXJRDATImzmTGksWo42OIujmmyn6apWvT7swGgkbOoTzT3sNQrebtJGjSPp8HuHD7peUK5MnX9gs1Wo0iYkkfTYXj8WCdedOzg0ejCs7h+gXx2Fq1Yo0b21J0J29iR0/HqHXU3SREgAo3bIFFEX2TOnendIdOzG1a4d1+/YLJ6nVGFs0R7HbcRcWYTv6C7Y9ewm65WZ0F3UrzJ02jfgpU8iaMAHbvn3oatUi9o3XUYeGothsRD7xBKZmzch89VXsJ06giYkh7s030AQHV2Ew1oSGogkNxdSypW/MlZuHNi4Ox9mzlc7VeSv8VQZDJZ6zcgi9HqHRUPLDuirHilatwuhVLADu/HxKfliHIz2d4NtuRQhRiZJfHRJC8G23kT1pUqV5Qu66C/U/GIsot75KN29GZTISMXIkIb17+yn0/bgk/i3F0lFRlHQhRBTwvRDiaMWDiqIoXqVTBV4lNBOgdevWlzznVyGETN3dPEVmet33JXw1GrKPyCLFG1+FNY9LpWErhqDoC0qlHJZsKEqD71+Cnm/DT+9AUDwcXiEtl8R2MtCeewzu/BgKU6H3x/DNU1JBdBgFw36Q1pHdIpWVPghqXisJJsuKYdObVWU/ulpW2VfIRBOeUtwFVS0vT2kpQm/AduAA6qhoQu7piyY6GtxuzB2vIXvSpEoxEk14OI4TJ0gbOYqY8eNxZZwnb/YcFJsNc8eORIwaScazzxE5ejRlR4/5XEyuzCzyPv4Yd2EBCR9Ow51fQMGiRQTdeisBXa7F0LABxavX+KwlTWSkz60m1GoK5n9OzLPPkfPhh5T++CPauDiiX3wRd0kJ6sBACpYswZWRQcC111Kybj3BvXtR+MUXuPNkqwBHSgoqs4mEjz5EcToRavUFQkqtluBbbialf3+c6ee98maSOnwEtX/4/pLU+B6rFWdWFoXLlqMOCiS4Tx+ix71A6vARvviLrmZNTG3b4szIkEo3PBzztddSummTb56IR0aDToc2Lg57URGqgADMHTsCoG94IfvKlZPDmb734MqQz1P5s2YR/87bBHbr5nNPCbWa4F534MrJpmDRYoRWS/iIERhbtqh6j/xNcFutZE952/cePaVWsidOwti8BaZ/UI7fgis/H9uBA1g2bcLcoQOmVq0uSU7qxz+Df0WxKIqS7v2dLYRYAbQFsoQQsYqiZAghYoHs35zk/7WwB5YOhaHfws/vQYuBcPVoCEmUFPQrH5IWiykcdn0KN79VdQ6VWqb7uu2wfBj0Xy5fn1wHjlLYPl0eb9wLjn0LtbpIN9ltH8jrU7fLdXq8BZYM6d4qyZCWym3TZEZQSLKUoyLC68jkgPYPw565oNGj1moxd+gg3SEV/O2mtm1QPG5iXpuAOyebUz1uxtioEUKrxXBVM+wnK6SaarVEPj6WnHfeQZecjDrATGaFuEfpTz+hDgsjoGsX0p9+mhrzZN8RPB4Cb+pG6rDhRI59nMLlX1B28CAJ779P5osvYtu7D5XZRMTIUdRcsxpnSgoiIMCXjaUKCcF89TWkPfkkidM/Qhk9GmdaKvlz52Jq3QrdnX2w7txJ6datFCxcSNQzz1C05mtqLFmC/ewZFKsNbVQU9lOnCbi2c6WgeDk8drtPqfhuAacTx9mzVZIDAMqOHSOl/wCfEsn7+BNqrfqK2mu/xbJ5M9qYWAyNG5H5yitY1m9AFRBA1PPPETv+VRwpKdiPHMHUvgPamGjUwcHEjHuB/AULCBswQHapFGBu1QqP3Y5Kr8eyebNPqZQja+IkTG3aVKLc14SFETlmDOH33y8/u+DgS77fvwue0lJK1ldlay5evbpaKBZXURFZb77pc5UWLlyE+dprpXX6P1rJf3GNV3XDPx68F0KYhRCB5a+BbsAh4CtgkPe0QcCXl31xax7UuhZQ5KZ/cJlUKJ/2lBt8/mmZSpyxHzo8BOf3yYB5RbR7UGZn9XxHzqfWQuoOGX+ZeS3s/ETGcGbdCPV7yJiNEJJ+RQiZLtxqqFQ+XwyXSgWkYtn8FvzwsiScrEhOGd0YanSCgrNSuTz0MwzfCJkHUZ9YTNKsj9HVrg1areyJ8sorCK0W257d5E77EF18PPo6dVCZzWQ89zzx779H3JTJRD3zDLVWfUXJxo2UHTqM8aqrKN1StW976Y8/yqJEpxPHuXMYr2pG3KSJkja/sBBj48ZY1q0jdOBAcj74ANvefUD5k+1EXBkZ5HwwDeFRfD1RVFotYQMHEPXoIxTOX8DZu+4i/fEnCOp+E/qaNcl57z1MrVqRPP9zdDVqkDdjBoFdrsVTVIjl+x+wHdgPQlC4bJmk7r8IzpwclLIyxCU24IoFpK68PBzp6TgzMijdtr1SZpjHYiH/s3lo4+II698fY5vWpD/+BJb1G3zHM599Do/FgrlNG8IGDcJQv54v+G5o1IiIESM4N/A+8ufOJf/TuZzueasv0O/Mrvrs5M7P95GhVoTKYJDZbJGRlZSKq6AQZ0YGzqws3BU6b15OCLUaXWJClXF9g/p/eq6Lm6pdDihWa5X4W+mmTXiKq5LN/tfhLi7GduAAGeNeJHfmx7KdwyXul38b/4bFEg2s8GpbDbBAUZRvhRA7gSVCiPuBFODuy76yMQya3CndTWE1Ycu7Mvtq4EqZEpzYVsZgVDo4sAg2vQUPbZU1JVmHZU1J7nFY+5xM/w2Kk4WStbtKJVERtgLY97lMCU7fDXfOktlolmwZ3M85JpVOOQzBEJQI9btBwTkZU0ndIdcISZLnrhoNxefh6kekJZSxD/XezzAntSf59cfAGI6iuMiYOAlj48YE9+6FOiwMT34Blk2bMDRsSMQDD1CwYCGRj0ouMdv+/bjS0gm+/Xb0DeqjuNxVPjZdcrKvkE1fvx5xb7+N4nIh1GpC7u2HxyETCozNmpLzzjtVrrdu2442MpK00aOpsXQJTo8HhApNaAimNm3IeX8qACF39cGVkUnmiy/5rtUmJRH31ltkvz0FV14+aQ8+6KvzKFy4iKTZs2S9z0Ww/3IUy6aNhD/wALlTp/rGg3r29LUpdqSlUbBsOe7MDCzbdxA+cCARj41BeBRM7duBouAuLELxeGSnSZsN2+7dVdYq3bIVfa1aVcYVj4fcaR9W6uOiOJ3kTJtG7IQJBHXvTu77U0FRCLn7LoJuvVXWcPzBjcKVnU36409g3bkTtFrCBgwgfMTwy/6UrgkLI+bFl0gZMMAXe9PVrEngddf9oesVj0dS8CxbhjPjPKF9+6KrUeMPMxT87vwXJU+Uw2OryjDwX4biclGybj0Zzz7rGyuYN69aMhX84xaLoiinFUW5yvvTWFGUCd7xPEVRrlcUpa6iKDcoipL/e3P9aegDJalj1iFJ5Kg1woGl3kC5gNCa8NN7sPU9aHCLjIUIIeMwx7+Rrq9vnpbxEQQMWCEViFonq/IvhrVAxnQsWbCon+QFC46HogwwhFw4L6yWtG48Dtj4hnSR5Z6Ag0sg/4wsgESB0FrQaohMODBHQrfxcu1z29Cs6Ivmh0fwePREDB+GUKnImTIFd3Y2Qqul+JtvyP3wQ9LGjiVi2P3yaXv8a2iio4l68glUJhP2k6cI6tHdF5wGGYiOGDWSwiVLCL77bhS7HU9JCZ6SEopXryagU2e0sbFoYmNxZWejS66aUKGrUQNnZiaekhKZ9uzxULhsGdbduxE6HYYmTYh+/jnChgzBfG1nop560lfL4PQWV8a98QY5775badP1WCyUrFuH0GpwWyoXqhZ/+y0FCxYiNGqSPptL1JNPkDBjOpGPj0UTEoIzNw9XVjae3FyE3kDClMmUHT5E8M2y70pK/wGk3NufgvnzL2SuqVRoLtEGWV+3zqXvN5erSqMyAHe+JF3UREWR+PFMol94AW1iIqkjHuDsnX04N3jIJYsnK81htZL97ntSqQA4neTPmUPZwUO/ed3/F/r69aj93VriJk4k8ZOPSZ732R/ukOnKyeFMr17kfvABRcu/4OzdfSnZuNHXg+evQmU2o70okUcTGfk/F2NxFRSQ887blcdycijduOlXrvj3cGUxval0cH6PVC4Hv4CHt0GHkTJzS3HLdN+UH2HHTJjZRab2ntsmN/7UHRf6y+sCICAKZneXcRWVBprfW3W9RrfBae+Xbi+RBZA754BWDyXnodVgeaznu7B0sKyZObgMVj4sYzHxrWH9eBnYV2mg++vSqgpJlnEdcxSM3IHSaybuPotw3jQTj0ePMz0dU4f2hA4ciLugEMvPPxM+QlpUrvPnKTt6lMJFi7Dt2IFKq+VMr94ULFxI0fLlnBs8hMSZM6S7bOJEaq1ehcdmI+qppwkfOpTsyZM5c9vtnO1zF/bjJxBA6c5dJH86B9vBQ0Q98USlFFhD06ZoYmOwHzsGKhVCCKx79hJ4XVfSHhuL4nYT/dSTWDZt5nT3HqT0uxfb3n0kfDBVFi0iU3UdKSmVijfL4crLo2DZMnJnzJAsueXrNmoIikLOO++SNmo0xWu/o3DJ0gvXpaeTMnAgli1bMF7VDBBEPfUUpTt2ULxqlc8lVrplC3lzPsVjt6MJCyP2tfGVmo0Z27TxdbQE8NhsOLOyJQuyx0PovVXvi9D+96IOCkJtNmO+5hrM7duRM+VtH8WM4/Rp0h8bW6mV8sVwFxZS+uOPVcaLv17zt7ibVHo92pgYgm+7VZKM/om2AMXffVclyST3vfcqfV9/BZqICJJmz8J8bWdUgYGYOnQgad5nvvqg/yW4L+He+6375N9Cdapj+fvhLJWbccFZWcS4dapUIiCzuvZ+Dre+B5/dJlN793wmrYxu42HbR3B6I0TUgy7PwsY3oawAlg2G0XuhXg+piA4tk7UnHR+TSsXiJV4UKpktFl4TEHLdhrfBgz/J4sfbvO6aPZ/BgcWwew4MXAHbpknrxuOGwysh7yQ0ul1aLAhY2B/X3Suwbv2Z0p1LiXhgBEUrVlK6VcZKArp2IWzwEIRO62sT7C4upuz4CQK73Uj+3M8qFd8509LIeO55Yl4bj+J04i4qouT7H9BEROAptWDd4X1C9rITm9q1lV0fHQ6ZrqxSUXvtt9j27kMdHITicHD+KVmOFD50KCXr1+M4m4Jm0H1oo6LwWK0Uf/kVpT/9JOdVFEq+/x5D40YEdOpE6bZt6Bs2IH/ePIL79CH3vfcqfaVBPXrIHjFD7yfw2i5oWrcCZIFj4eIl2E+cwFNcjP3kSWLGjUMdFIS7pIScqe+jCggg4Z13yHzjDcr27yds6BAcZ85WuW0sGzcSPnQImvBwTC1bUuf777Dt2482Pg5tYqIvIcFdUkLR6tVkvzURpawMTWwsyZ/PI/rll8j/ZBYoCmHDh2Fq1843txAC6yXca2VHjlTpxlkOR2oqpT//jK5WTUkBUwHGZldVO2bgS1ptJZY/7PL7I9AlJBA/aZJMjNDp/hWKmr8b6oAAgnv1onDhwgqDaoK6d//3hPoVXFmKRXHLYLvHBWX5cvOuiIIz0rIIry3rShwW6cqaf5d0QQ1bJ11iXz8hYy0giScLz0lFoQ+QRJIBUfL4TxXM1mvGyNTj4gwZ68k7JalZohvDZ7dLa0hnhm4TpMtu1yypjOrfLN1xn90qFSLIyv/W90ONjnjunEXOxIkUfbWakIEDKFy6zKdUACwbNmJs2ozgO3tLQkaXC1VAAG6rFVf6edyFlRmHQWYBlW7ZStarr6KNjyNu0iQ8VhtZr71W5dzSzT8S/uADKLYybKlpGBs1BKcLU7u2uDKzUFxOIkeORFe3DrZ9+8l55x0iHxsjXWd5eQi1BsumjVXn3b6DwG7diBj5MHkzZlB27DgR9w9DKbNRtGo16oAAwoYOxbpjJ+aO1wCy66PJq1jUYWEkzpyBIyUFV0EBxmbNEAYDarMZZ14entJSQu7sTf6CBZTt3w+A83wG+jq1sWzYUEkWfb16vk6YKpMJlcl0SToVd34+WRVYn10ZGaQOG07ygvkE3XCDlCs0tEqlu75evSpzaWJicKalUXbiBMZmzXzV6K7CQs4/+xyunBxix79K2qHDeLxBe33dugR2u7HKXP82gm+9lbyPpldKjAi5807UgZe3fbM6KAj175/2n4XKaCRy9ChUJhPFq1ahiYok6pln0ERWv6ZyV5Zi0RikG2vLuzKry30JSgyXDfp8CovuhaZ3w3fPyzjK7jkyQ+yHV6oUKqIPgCZ9YMkAafUIFVw3Tgb+Mw9BSILMPguOh6QOIBTJbBwULzPJyl1sjlJZ6zL0OyhKlcSV170oY0LlSqUcez+Dpn0QReew/XIMAF1cHMVrvq7ylkp37MDU8RpS7paNrwK6diXmpRfJeOklQu++29eWuBwh9/RF36gRxlatsO3eTeYrr5Lw4TT0depUKRbU16+PJjoaxWZDZTbhtlhknYtahTY2Fo/FQtHq1diPHcNTWoqudm0COnVGKSigxpLFoFajb9CAskOHK81raNgQfe1aaOLiMDZvIXurFBXiTD9P1ONjUaw2ir5YTtmRX3xV24ZmTX3Xq7RaPDod2ho10NaogdBo0IaH4y6xYD92jJB7+iEEZL/zru8ay7p1JH8+j5Lvf/C9T3VYGFFjH0PodLgtFtQBAVXvGS9sh6rGNxxnzqDYbL/J66VNSCTguq6+bDO0WmLGjSN/0WJK1qwh6umnCR3QH5U3rdy2axcAeTNmkPjJxzjOnkUTFY2+Xt1LuqhceXl4rFaEWo3KZPrHixq1UVEkzZ1L9sSJuPPzCLr9dsIGDLgslftXGjRhYUQ+MpqwwYNk3VY17fx5ZSkWe7EkfnTaIG2XpJ2vyBemD5LxixUPwoBlUJQuU5BVamlJOKzQ/U1Y89gFM779Q9LyaHQHjNopg/iOUi8TsVmyENuKIKy2zPxKagcL7oGwOtDlqQtKpRwet6SF6TEJds+Wwfw2w6q+F49LWjLHv8VQvx6OEydwpqejr1+PsgobnDY5mcjHxiA0GmosWoTt8CFy3n6HknXriH3zTTxFRcRPfZ/cDz9CsdsJ6d0blcFA+sMjiXvrTbKnvE3ZoUO48/IIGzoE6549uL0+XV2tWgTf1Qf70WOcf/JJ3AUFCK2WyMcek/1f8vIwtmpF3KSJOE6fkUWMWg1pI0fizMggeeECSn/eRvigQZRu/tGXeaZNSCD03nsRKoHl++8p3bIFXe1aaMLD0SYnc/7xJ+SbU6mIefklCpcuxdiiOYa6dXEVFiJUKsqOHiN/7lxUBj0hfftiP3ES8zVXI/R6UofeT83Vq/AUFaOvW9dXS6I4naQ/9TSJ0z7AXVKCYrejr10bBciZMkUmN9x6KwGdO10y80pfu3aVMXV4+K/SrrgKCrBu207RmjWEDRlC5OjRODMz0depg+J2E/HgA6gDAsidPp2gnregiowElUqyTp88SemWrZRu/RltUhLRzz5L7gfTCOjaBWPzFmhCpCvImZVF6oMP+dgZArrdSOzLL/+jG5LKZMLcpjWJM2eAy/WH6nA8DgfuwkI8paVSGQYG+lmPvVDp9fJeqMa4shSLokhrpdVg6aYavgG21ZJ1KeG1ZVxkwwTIPiwD+eF14J4F8pjQwJEvoUlvaYlkH4WgWBnUD0mGzRNh3wK5TmgNuHsezO0pN3+3Q7IaX/MYtH9QWiA3T5a0LlqT/F0REXVl8oAtX14fWV8qpbIK9Rp1u8vK/RqdUAXuR2U2o69fH3P79pRu/VnyYHXrRmC3G8l4+RVs27YB0lqJf/cd8ud9jrldO7JefwNzp04kTH2fsl9+oWjll9IV5PGQ+eqrRI5+hOwpU3BmZpI3azYJ772HKzcHodejb9AA55kzZIwb5wvOKk4n2RMnkrxgPufuH0bko4+gb9CAoq+/xvrTT76YgDY5GZXegKekhLx5n5P4yce48/MRGg1Cr0cRkpqmcNFiQBZqlny7lhqLFxF0UzfsJ09ibNIEZ04O2thYFG9rWwDr7t2kPfSw76MqXvsdyXPmYD92DKHVYe54DeeGDCF54UKiHh/LuYMHffKrTCZUgYFooqMleWd+Pmd73+mTu/Snn4gYOZLQQfdVoV/RREcT3KsXRStkC2a0WuLeeP2SSshTZqdg/gJyP5CFs5YffkCblETCB1M5dUtPcDoJ7tWL8OHDKK1AeaMJDyfujddJuW+QDPYrCrqEeHC7KFy8mMLFi4l+cZxslOZ0kvP+1EqUP5bvvsfStSshvXr9zj/L5ccfTYP2OJ1eMs+ReCwWhFZL9LhxBN3c4zctRj+qD0R1LK75o2jdurWyy+sW+EMoSoc53eGuuZBxUCqAoBjZN6X4vHRjlWTIbK6rH5VuM7dD/rjscpO3W6AwRbrMEtrKWpemfWQqckXU6irrW75/8cJYuwclaeWnt8C9S2DPPNkh8psnvSnMSOXT7G74qMOF62p2lq61n96RsZu63eU5uz5B6TAKtwjFlVeA7eBBFJcbc/t22E+coHDxYlSBQYTe24/ChYso/lq6yWJeeQVQMLZoyZnbbgPA0LQJcZMn487JAY0Gx9mzZL81kfj3ZbA8d+bHCJXAfPU1GK9qhlCpJEmlx8PpnrdW+ahjxr9K/qdz8RQXkzTvMzJffgWrV7kBJM6cQdbrb1RyrQXfcTsh992HKz0dXY2anOnVS/ZCr4CEGdNxW23kTp6MMzsbTWQkitWKsVUrjE2bEtijO+efeJKygwcrXRfcuxemDh0wNm0GLqf8rOwOXCUlmJs3x5WfhzosDF1CArbDRyhcuoSgHj1Qmc2kj37EN09gjx6E3tuPgoWL8BQVEjpgIKaWLXzBYldhIe6CAlyZmehq1kQdHIzKaKzy+ThzcjjdvXuVbo5RzzxNydq1viLThI8+RKjVmNq29dHQeJxO3PkFOM6eQRgMOM+mkPnaa77GbpqoKGouXwZCcPbe/pVIOkF2/IybOPEP9Yz5NSiKgjsvD3dxCSqD/rK62JzZ2Zy57fbKQX+Vijo//IA2LvayrHGlQwixuwKz/GXHlWWxqDVwzyLZ9jeprXQnKYpkKd6/UAbdB62CY1/LzT6+tXRDaQxgFJD1C+ycKWMore+XLrDub8COT6quZcmStTBCA7+slEWSTe+G0jzZ197lgIBIyPlFxlQKU6TlY46QqcU1r/XWryC7TR5dIznMnDbYOw/m3AROG+LAElTDfkRxuyndtBltjRpYd+4kc9wFhWb54QeSPpuL5aef8BQXY92xncgnnvDxhakCA4l54QXSR43GfuIEAKY2bUj4cBqayEiE0UjcKy/jKihAExZG+tixvo0v6TOZ1lnO31UOXXIyrqwsaYGo1YQPGexTLOqQEIROT+BN3VCZTFg2/4ht926KVn5JxMMj0TRoIJX4JdJmPZZSAjp1pGB2GM70dFznz6OJiiR82P1YNmzEcfastF4uguJ0oYuJ4fwzz1B2+DCmFi2IefVVSn/eiio8DEN8HCXr1pHzzrvoa9cm8uGHyZvzKeZrO8v0YrcbVWAgYfcN5Nx9g3xFj6VbthL7+gSC77gDoVKhCQlBExKCvkIt0K/hUsWoitMl71MvrLt2Ef7AA5W4zVRaLaroKLTRUeTO/JictyvXNiguFwoy2Gto3LiKYjG1aXNJRoI/A2daGimDBvvuocDu3Yl5cdxlcbEpNlvVTDKPB8e5FL9i+Y/gylIslhyZPrzqEdl7HiA4QVbeB8XLHvU/vCRrV0D+PvmDjKuAZDYux4nv4d7F4HRAUnuZFlyO1vdLrrC1z8vMsvYPy54uh1ZCQkvJJ9Z9IsS3kllq3zwtu0yG1IRvnpGxoDbDJY/ZuldkZljtrqAPhpSfpcwhybJ5mKsMcWQ5eV9mUfL998S+PoG8jysrOsXppHjNGgI6d6Z49WoMjRqDy4XidGLq0AF9ndoULF7sUyoA1p07se3fT8BNN+E8c4a0Bx7E3LEjuuQkn1JRh4RgP3WS2DdeJ33UaF8FdOh992E/ehSPxUL4Qw+R9+lcArtcS/zU9ylcsoTQAQNRBQViP3ESd3Exwbf2JOTO3mQ8/wIehx2VTk/x2m8JvPHGSokF6pAQDA3qU7hypawnUcCZcR6VwUDWhAmE3NmH4u++I6RPH7LGj7/wAQhB6ID+aKOjiX3pRTxWK67sbEq3/ERgjx4IIPPVVyn5Vq5l27MHV2EB0U8+hbu4iOQF8yk7eBDbwUMUf/11pUp6gLyPPyagc+c/VduhDgggpG9fCj777IKYJhPmtm3Ief9935ipTVsKli0jrM9dqIMCq8wTdHMP8mbMQHG7UbwNxcIGDpRNxbRaop54HNvevT7qfUOzZgR17/6XeKbcRUVkvvxyJSLTkm+/JfiO2wns0uX/PW85hNGIKiAAz0VFr9rExL88tx//DK4sxRKSKDnCypUKSKbitc/LvivGkAtKpRw5R2V8I/8MXP8iHPpCxkgAdn8K178se6/UvVEG4m96S/49s/MF99bZn2SdSvN+Ujmp9ZB/Srri1DqofYMM9s/sLM/v8qzMJHM5oO8CSSdjipDFnUdWyLqXG1+RFtP2GSguhyTYBPl0fSn3pkcBlQpjy5YYmjTGY7ORO3MmgV27YGzZ8gKNfgVYt2/H1LYtBZ9/TsSYR9HXqUPe9BkAGJo0IeYFSbLoPJ9BzRVf4C4qQh0eTtmhQ+TP+5zol14k8NpryZs7F/vJUwT2vIXounXxlJZytu89PuJM265dRD3xBOEPPYQzNRVHSgp502eQ8OE0dMlJlP60RSYhjBoJQiAQnLn9DiIffRR0WnImTUYdHo6hZQs0MdEIrY6YV1+haMUKhMFIxIjhaKKjOTf0fhynJbmnsWULYl56CcfJU+iSEilZeyGJQ1+3LmEDBnC2b19f7CXw5h6EDxtG0YqVVT4n5SJKGcXjwe1t9KUymVAFBaEJDUVRFFy5ubjz8lAFBBB+/1C0sTEUr1mDNi6eiNGjyP3oI9/nEnznnehr1URlNOA8n47iiKiivFR6PckLFuApLpbUKfl5mNu3lxlkgC4+nppLl+DKyUXotKjDwv6yVeFxOLDt219lvPTHny6LYlGHhBA38S3SHx0jlbgQRI4Zc9koYP4MKn5nwmBAHRRUbTOxqhOuLMWi0srOjhcjfZe0ZH7tKU6opRIRQJdnZMB+q/ep0hAEG+ZC+1EyyG/Ll0SUnovcHNs+lJlgN70uXWmnNsC826USaD5A0st43JK7rDAVPpF1D2iN0n1XcBaWDrow3+n10tI6vhalUR9Kn5XHPGVlhA4YULnmRKMh9N5+uMvKcJ45w/lnniVx5gysW3/GuvVnEmZMx9SiBY5TFViPAVPrNmhiYogYOVJmfGk0RD39FAWLFhF+333Yjh7F1LoVntJSMl54AW1SMuHDh6GOiCD25Zco+vobbAcPYWzUmIJFCzG1aU3hsuUIraZK58eCxYtJ+vRT3DYr9pOn8FgsnLt/GEHdbiT49ttltpdeT+5H0311K3mzZlFz5QpKvl1L/JQp5M2ejW3fPkwtWxDSrx+K243QatEmJJI3fTqO06dRh4cT8/zzqMNCcRcXy436orqS0IEDyZ40qVK1eMnX3xDSpw+h99xDwaJFleQPHzLYxz8G4Eg5R8qAAT73oPnaa4l7fQIeq42UAQPwWK0kffIx5599Dn2tWgTfehuoVN7U5rGE3XsvqsBAVEYj6WPH+lKxtUlJJH82F62XVsaVm0vqQw9VOX5xkLycvPJyQaXTYbjqKqwV6qUAzNdcc9nmN3foQO0fvseVnY0mIgJVYOC/Erh3pqWR0n+AL2PR1KE98ZMn/8/RxVxuVK8S3b8bQiWbeF2M+Fby6d9RKvupVERkA9AZIbqRjHUsHiCD6SHJsujx22fh2mekgpneEbbPQPYxu8TaBWeky00I+Hmqt3oeUFzS4tAHSvbi7dMvXOe0wYrhsnVxRSgK7J0P/ZaguFV4vG4ofVISKpOJuEkTMbVpQ0DXriR9PJO8hQtx5+VRsHQZESNH4sq6wKybPXES4SOGo6tApGhs2RLT1R3wFBVxfuzjnLtvECn3DcKVk4OhXn2su/egUqkoWrGSku++J6TvPTI111KK8Hg406s39sOHcOVkc/7ppyXNSco5Ly3LJRS4SoXHWkr2m28SeF1XVGYTOJ1YNm7Efvo05nbtZLdGtxvHyZO+a4ReT8LU90kdMYLCRYuwHz1KwYKFZDz7LOZrrkEdEoLbWkrZgYMgBPHvvE3+Z59xbvAQzg28j3ODB6PYbAT3udMniq5GcpW6GgDrtm1SKcycQcB112Fq04aYV15GExWF2xs4dxUWkfnyS5ViTqWbNmHds4f8zz/HlZVF1OOyzYDj9GlK1q3DsmULqAT248dRBQWR8cIL5H70EYVfrKgkh/PcOXI//BBPWRmK203hipVVjud8MA1HWhq2Awdw5eT8LfQu6uBgYl9+Sfb48SLguuvQ162D23p5iB9VRiPa6GiMTZuijY39V5SKu7iYzAkTfEoFwPrzNvl9+fGbuLIUiy0fEtvLmEU5QpKh8xMy/VioZBpwpyekgmn3ANz+gay8r3+zZBoG2Dcf7lko4y/H1sixNWOhrFBW5jfrKwPwFdH6fjj+rVQIhhDot0jWqgxdK11eze6SLMa5J6gCSzaoqzamQqWGA4tQbZtMzXkzCOx2I8JgIHfGdLQJCZg7XoO+Xj0yxr1I0fwFZL02gbhXXwWXqxLPlMdmw3HmDFFPP0WtVatIXriA0Hv64iktpXDJUl/sJbjnLdj27KVw6VLUQYGkj5X+e9vevWQ88wza+DhUwUEUrVoFikLgDTdSuNTbEtnjQahVWDZvJujmHoiLajtC+92DKiAAV2YWWW+8SeLHHxPSvz/J8+Yh1Goyx4+n5OtviHzoQV+MJ3zIENTBwbiLiqoUbpYdOgxuN+mPjUWoVBivugpzhw7Y9h/Atm+f7zxXdjZZkyYT+eijRD31JMaWLVGcTgxXXVXl4za1bkPJ+nVkjHsRfd26mDteQ9FXqzj/5FMoDmnBKE7HpZXS9u0o9jLiJk3C0KwZtv37QaUibvIkzO3bUbhsOXkffoTj5EmSPv2UwO7dKTtcdR7bvn14SkvxlJVdIKCs+L4P7MeyaRNn7+7L6dvvwHFR4P5yQRMXR9KsT0j6dA7JixYS0KkTZ+7ohTMt/W9Z79+Ax26/JKmn9eefqyVVfXXCleUKczvguxdlv5OOj8m/HRZYPVY291JrZeGk2wH1bpK0K3NvlVbDgUVQ9yZZga/WwbHVkmvs3iXyATzD63O25ku32cAVMlvMYZEElaU5ENNEKoNvX5RKBmRK891z4cyP0GumXPtiBMVLi6giVGqU1vcjFvdHZcnCUJpOzPMzceaXEjZoMNadu8ipUFUOsk+6q6AAQ8MGZFfIJArrfy+Fy5ejr1cPTXg4KQMGgttN8vzPKTtyxHdewHXXk/nqq4T2vZvC5curiFm0/At0yckIb1aT4nFLtxeye6M6JARVUBD5cz8jcfYsCubL2EBInz5oYmMp3bqVgM6dyJ89h7KjR4l/9x0yx4/3KRLHqVNYd+8m/r33CO7VC3OnjrLQ7tdcmB4PxqZNcGZkEHz77ZRu31aljTMgN3BFIWzQIEJ690aYTMS98Ya3BXM2CEFI37vRN6iPp8xG3kfTyZsxw3e9uWNHXxW5Sq/H2LJlFYJIc6fOOM6eRR0eRuGihRhbtECXnIQzLa3S95QyYCC1vvqKgC5dcWVlV5nH1K4dKnMAQqfF3KkjpZs3VzpubN4c+3H5IODOzydr/GvEvz3lsnNneQoLSXt0jFRcbrcvgy//83nEvvgiQvPf31pURiOm1q0qxd9A1oJV5yZb1QFXlsWi0spYiuKSRI+f3QEL+oI1V6YN2y1yYz+zGda9Ki2T8p4pah14nFIxtBgAu+ZA66HwyyoZt0lqf2GdA4tll8jOT0ir5Nw2GStpfKdMOy5XKiAVz3fjoOGtMg5jyZZJAuUWjzEUbn0f8k7ImEq97tC4tyzuNIXjGbwWV7/vKGvxAsXrt4DbjalNa8ztL3LpIYPS6uAg0GgI6NIFc8eOxE2ZjK5mTYJ63EzwHXegDgr2BSctP/+MqX2F9+V2IbRa2YvlEtXkQquldOdOgm+/DbRair/+htD+/X3HM1+bQMK776CNjaVgvrSKYl4bj65uHYrXrMa6datMtwXcubmo9HqfUimHKysLBATf2tPHn6UOCqrU8hfA1K4taDREPvaY3OTUKoLvuEOmD18Ec9s2qMxmhFotlZ9Oh65GMjWXL6PWN19TZ/06osaORRsVhalVK4LvuMN3rTYxkZiXX/IFltVBQcS89GKlDKag22/H2LQJQTf3IP/TuRStXkPwHbcTfNttFC7/orIwHg/5C+aj0mkJ7nkLpqsv1DMZGjUifPhwVAY9QqUi+OabqxwP6X0nxasvNL0q++WXX+1X8pcgVPIecDorpYWrdPpfV/T/MagDAoh++hl0NWv4xgK7d8fU+m8r//ifwX//seJPQcgCRbddxjeGfiP5wpxW2Pw2dHpMZl11HCNp7Muh1slq/Z2z5YZemiNjLlf1k50iA2Oh13T4YoSsRyknjzSGyr9jr5JZX/rAC5ZNRWQfkRZJl6fBWSbdc416STZmtQ4OfwkbJ0BUQzmvyw65JxD7F+G89h3OjRrn60oI8qk29s03iBj5MLnTZ4DbjSYqkrjJkyjd+jPW3bsI6tkTXc2aOE6fRhUYSOmBLWji48h5+x1i33qT82MfJ2/6DGp99SXW7duxbttG8dffED54MPkLFhDzwvNYNm2+sKkIQfiDD6AODcWyeTPJ8z6jYP4ChEZD4qxZFMyfj8poQHG7Cbt/KK6cHHLffQ/LTz/JJ161muT580l/7DFAkjD+2lP2xYV96vBwEqZOpXDxYsqOHMbYvDlBt94KajW5U6di3bETbXwcSbNnE9CpE4E33eRLY9bXqycpaC6iCxEq1SWD3prwcKKffYbIRx9BcThQmc1VMrV0CQnUWLgAT6kVodPK4sHgYNxWKyqDHsVqJX3MYyR8OO2SRYoqowmEQBMeTvyUKXisVvB4UJlMlYLGFx9X7HbOjXigUtGlqW0bxCUKNP8qNOFhRI4aRdqoUb4xodMRdt/AKiSb/2Vo42JJnjdPcq1pNAizGc3/IHPy5Ua1q7wXQnQH3gPUwCeKorz5a+f+6cr70lzYNh1aDpT9TlY9KpWKMVRW4xtDJQOyOUpu9nvmSsbh1kOlWyyiLqyfIJmOTaGyMdiMTpKkMqapTBM2hkqrqDQPAmPgxFr5uuCM7PoYnADzLqLTSGoPvb21J2VFMN2bXRPXUiqSBrfAF8Nk2rEQsgtmkzth6we4en+GMzWLtIcewpWTg9BqiXjkERS3G+NVzdAlJ+MpKpIBX60WXWwsZYcPYzt4CHVQIEE33IArPx/bocOSzNHtQWjUCLVG8mWhIIQKlVHGeIRej/34cRwpKRjqN6Bg4QIUj0Jo/3vRJSejjYzEY7ejeDzYT5/G8s23lJ06haF2bTx2O0KvI/S++xBqNa70dPJmz0HotIT1H4AjPV0WwDldeEpLUUdFkjt9BpbvKqQC16tH0pzZVbJyPE6nbEBmsyGMRtRGI4pLzqPY7ajMZtTh4QghcBUVoZSWorjdVTbrvxtlR45w5q67pWXZoQPBN99MxrhxvuPCYKDW6lXoEqq2Av49uPLzyZ40maKVK0FR0NerS+LMmb4ssssNd3EJ9hPHyf90LqrAQMKHD0cbF/uXKvr9+Gfwd1feVyvFIoRQA8eBG4E0YCfQT1GUI5c6/08rFpCupoNLZTC87vXy6V9rklaG0yqVxPl9ckM/tFTWufyy2kvzMloG2tN2SioWlUamHn/3/IX5TeHwwGZJEWMvlRldpTnSFdbzPUi+GrZ/JLPHPC7ZROzeJTKg77BIC2XFCFn7Uo6md8uqe0eJlDdlq3S39f4YwmqjCI1ksLXZUOn1KIpUBsJoQBMSguJ24ykrQ2UwINRquQmXlkr6/Px8ufk6HNhPn8HQpAnqIJnaKdRq3FYrQqv11UWUw221ojgc0s0iBCqDoRJvFsiNLuu1CT4qGUOzZiR8MBVthZ7znrIyEAJPaSnpY8b4+r0EXNeVWG/KdMm6dVjWr8fYsiUhvXr9qULE6gZ3aSnO1FTyZs8Bj1tm6OXmUfD556hDQwgfOhRNbGyVz/sPz19cjMdqRXE6/zGl6bHZQKXyK5T/EK40xdIBeFlRlJu8fz8LoCjKG5c6//+lWEC6b5w2GS/xuKCsGGx5clN3uyA4TloelmypbFRa6cbSB0qrx+2U3Q3VekCB83uldRNaUzYQC4yVcRuPW8Zl3A55bs5xOLdFWkCKRyoprUGupTNfkM+SA0dWysZitbtKJbfnc1ngmdhGyigEmKPBFPLn3/+lPhK75EL7PdbZPwt3UZHc6DweVEbjbxaXuQoKfF0Uhcnki6EoiuJTmv8rbhaPzYbi8aD2tmD2lJWBWv3/Vih++PFncKUplj5Ad0VRhnn/Hgi0UxRlVIVzRgAjAJKSklql/E5v8D8Mj0fGXjSG/1/w0VUmLZiL04wrwu2U6caaP7B5K4qcs1wej0v+qLRSWWkukX7shx9++PEH8Hcrlv9cVpiiKDMVRWmtKErryMvZk0Clku6q/29Gi8bw20oFZDrzH1EqIOWoKI9K411D7VcqfvjhR7VGdVMs6UBFprkE75gffvjhhx//EVQ3xbITqCuEqCmE0AH3AF/9yzL54YcffvjxJ1Ct6lgURXEJIUYBa5HpxrMVRanKa+GHH3744Ue1RbVSLACKonwNfP1vy+GHH3744cf/D9XNFeaHH3744cd/HNUq3fjPQgiRA/zRfOMIIPdvFOdyoLrL6Jfvr6G6ywfVX0a/fH8N5fIlK4pyGdNqK+M/rVj+DIQQu/7OvO3Lgeouo1++v4bqLh9Ufxn98v01/FPy+V1hfvjhhx9+XFb4FYsffvjhhx+XFVeSYpn5bwvwB1DdZfTL99dQ3eWD6i+jX76/hn9EvismxuKHH3744cc/gyvJYvHDDz/88OMfgF+x+OGHH374cXmhKMr//A/QHTgGnASe+Rvmnw1kA4cqjIUB3wMnvL9DveMCeN8rywGgZYVrBnnPPwEMqjDeCjjoveZ9LrgwL7nGJeRLBDYAR4DDwKPVSUbAAOwA9nvle8U7XhPY7p1zMaDzjuu9f5/0Hq9RYa5nvePHgJt+7x74tTV+5XNUA3uB1dVUvrPe72AfsKs6fcfe80KAZcBR4BegQ3WRD6jv/dzKf4qBMdVFPu95jyH/Pw4BC5H/N9XqHvRdc7k32er2g9wMTgG1AB1y82p0mdfoDLSksmKZWP7lAM8Ab3lf3wx8470x2wPbK9xcp72/Q72vy2/iHd5zhffaHr+1xiXkiy2/8YFAZJfORtVFRu81Ad7XWu9N3B5YAtzjHZ8OPOR9/TAw3fv6HmCx93Uj7/er9/4znPJ+/796D/zaGr/yOY4FFnBBsVQ3+c4CEReNVYvv2HtsLjDM+1qHVDTVRr6L9oxMILm6yAfEA2cAY4X7YvCv3R/8S/egT97LucFWxx/kU9HaCn8/Czz7N6xTg8qK5RgQ630dCxzzvp6BbLdc6TygHzCjwvgM71gscLTCuO+8X1vjD8j6JbL9c7WTETABe4B2yAphzcXfI5KktIP3tcZ7nrj4uy0/79fuAe81l1zjEnIlAOuA64DVv3XtvyGf9/hZqiqWavEdA8HIjVFUR/kukqkbsKU6yYdULKlIhaVB3oM3/dr9wb90D5b/XAkxlvIvpBxp3rG/G9GKomR4X2cC0b8jz2+Np11i/LfW+FUIIWoALZBWQbWRUQihFkLsQ7oUv0c+PRUqiuK6xJw+ObzHi4Dw/4fc4b+xxsV4F3gK8Hj//q1r/w35ABTgOyHEbm+nVag+33FNIAeYI4TYK4T4RAhhrkbyVcQ9SFfTb137j8qnKEo6MBk4B2Qg76ndVL97EPAH7/8RKFLVK//2GkKIAGA5MEZRlOI/e/1fxW+toSiKW1GU5kjLoC3Q4O+U5c9ACNETyFYUZfe/LcvvoKOiKC2BHsBIIUTnigf/5e9Yg3QXf6QoSgugFOn2qS7yAeDtA3UbsPTPXns58GtrCCFCgduRCjoOMCNjItUSV4Ji+be6UmYJIWIBvL+zf0ee3xpPuMT4b61RBUIILVKpzFcU5YvqKCOAoiiFyESDDkCIEKK8tUPFOX1yeI8HA3n/D7nzfmONirgGuE0IcRZYhHSHvVeN5AN8T7UoipINrEAq6OryHacBaYqibPf+vQypaKqLfOXoAexRFCXrd679p+W7ATijKEqOoihO4AvkfVmt7sFyXAmK5d/qSvkVMjsE7+8vK4zfJyTaA0VeM3gt0E0IEep9OumG9GVmAMVCiPZCCAHcd9Fcl1qjErzXzQJ+URTl7eomoxAiUggR4n1tRMZ/fkEqmD6/Il/5nH2A9d4nva+Ae4QQeiFETaAuMmB6yXvAe82vreGDoijPKoqSoChKDe+16xVF6V9d5PN+bmYhRGD5a+R3c4hq8h0ripIJpAoh6nuHrkdmKVYL+SqgHxfcYL917T8t3zmgvRDC5L2+/POrNvdgJfxeEOZ/4QeZwXEc6bd//m+YfyHS7+lEPpndj/RNrkOmEP4AhHnPFcA0rywHgdYV5hmKTOk7CQypMN4auUmcAj7gQpriJde4hHwdkeb1AS6kU95cXWQEmiHTeA9453jRO17Le9OfRLom9N5xg/fvk97jtSrM9bxXhmN4s25+6x74tTV+47vuwoWssGojn/e8/VxI2X7+tz7/f/o79p7XHNjl/Z5XIrOmqpN8ZuQTenCFseok3yvIVO1DwDxkZle1uQcr/vgpXfzwww8//LisuBJcYX744YcffvyD8CsWP/zwww8/Liv8isUPP/zww4/LCr9i8cMPP/zw47LCr1j88MMPP/y4rPArFj/+pyCEiBFCLBJCnBKS2uRrIUS9/8c8dwghGv0dMv4JGdoLIbYLIfYJIX4RQrzsHb9NCPHM71zuhx//Gvzpxn78z8BbOLYVmKsoynTv2FVAkKIoP/7JuT5F1qssu+yC/vqaakVR3BX+PgbcrSjKfiGEGqivKMqRf0oeP/z4/8Jvsfjxv4SugLNcqQAoirJfUZQfhRBdhBCry8eFEB8IIQZ7X78phDgihDgghJgshLgayRc1yWst1BZCNBdCbPOes8JbVY0QYqMQ4h0hxC6vVdFGCPGFEOKEEOK1CusNEELs8M43w6soEEJYhBBThBD7kTQ2FRGFLLxFkVxqR7zXDBZCfOB9va/Cj00Ica23Cn+2d729QojbL/sn7YcfvwG/YvHjfwlNkIyvfxhCiHCgF9BYUZRmwGuKomxFUl88qShKc0VRTgGfAU97zzkIvFRhGoeiKK2RvSq+BEZ6ZRkshAgXQjQE+gLXKJJo0w30915rRvbyuEpRlJ8uEu8d4JhXkT0ghDBcLL9XvubAOGRV+1ZkZfV6RVHaIpXtJC/Nix9+/CPwKxY/rnQUAWXALCFEb8B68QlCiGAgRFGUTd6hucjmbuUo5547CBxWFCVDURQ7sslTIpLXqRWwU8jWANcjaTJAKpnllxJMUZRXkTQg3wH3At9e6jwhRF1gEtJt5kTyUz3jXWsjkt4j6bc+BD/8uJzQ/P4pfvjxn8FhLpDlXQwXlR+kDCB7VQgh2iI3+z7AKCR78Z+B3fvbU+F1+d8aJK/UXEVRnr3EtWUV4yoXw2stfSSE+BjI8VpYPgjZCmEJMFy50NNDAHcqinLsT74PP/y4LPBbLH78L2E9oBcXmlwhhGgmhOgEpACNvKyuIUhFUr4xByuK8jWyp/hV3ktLkG2cURSlCCjwzgMwECi3Xv4I1gF9hBBR3jXDhBDJv3eREOIWb0ICSBZaN1B40WmzgTkXJSesBUaXXyuEaPEnZPXDj78Mv8Xix/8MFEVRhBC9gHeFEE8jXVxnkY3NUoUQS5DMsGeQbMoglceX3viFQPa1B9l35WMhxCNIS2YQMF0IYUK6uIb8CbmOCCFeQHZ3VCFZsEcild1vYSDwjhDCirS4+iuK4i7XNV7l1AeoJ4QY6r1mGDAe2fHygHe9M0DPPyqvH378VfjTjf3www8//Lis8LvC/PDDDz/8uKzwKxY//PDDDz8uK/yKxQ8//PDDj8sKv2Lxww8//PDjssKvWPzwww8//Lis8CsWP/zwww8/Liv8isUPP/zww4/Liv8DQo2G23gnuJIAAAAASUVORK5CYII="}, "metadata": {"needs_background": "light"}}], "metadata": {"scrolled": true}}, {"cell_type": "markdown", "source": ["We compare different policies: the optimal policy we learned, the current policy, and the policy under which each customer is given all incentives. We note that the optimal policy has a much higher ROI than the alternatives. "], "metadata": {}}, {"cell_type": "code", "execution_count": 16, "source": ["roi_current = potential_roi[np.arange(X.shape[0]), T].sum()\r\n", "roi_optimal = potential_roi[np.arange(X.shape[0]), recommended_T].sum()\r\n", "roi_bothT = potential_roi[:, -1].sum()\r\n", "all_rois = np.array([roi_optimal, roi_current, roi_bothT])\r\n", "Y_baseline = (Y - model.effect(X=X.values, T1=T)).sum()"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 17, "source": ["pd.DataFrame(\r\n", "    {\r\n", "        \"Policy\": [\"Optimal\", \"Current\", \"All Investments\"],\r\n", "        \"ROI ($)\": all_rois,\r\n", "        \"ROI (% of baseline Y)\": np.round(all_rois / Y_baseline * 100, 1),\r\n", "    }\r\n", ")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            Policy       ROI ($)  ROI (% of baseline Y)\n", "0          Optimal  9.735966e+06                   64.0\n", "1          Current  2.535076e+06                   16.7\n", "2  All Investments  9.683787e+05                    6.4"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Policy</th>\n", "      <th>ROI ($)</th>\n", "      <th>ROI (% of baseline Y)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Optimal</td>\n", "      <td>9.735966e+06</td>\n", "      <td>64.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Current</td>\n", "      <td>2.535076e+06</td>\n", "      <td>16.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>All Investments</td>\n", "      <td>9.683787e+05</td>\n", "      <td>6.4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 17}], "metadata": {}}, {"cell_type": "markdown", "source": ["### Performance of policies based on ground truth"], "metadata": {}}, {"cell_type": "code", "execution_count": 18, "source": ["true_roi = np.zeros((X.shape[0], 4))\r\n", "true_roi[:, 1:3] = TE_fn(X.iloc[:, [0]])\r\n", "true_roi[:, 3] = np.sum(true_roi[:, 1:3], axis=1)\r\n", "true_roi[:, 1:] -= cost_fn(multi_data)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 19, "source": ["roi_current = true_roi[np.arange(X.shape[0]), T].sum()\r\n", "roi_optimal = true_roi[np.arange(X.shape[0]), recommended_T].sum()\r\n", "roi_bothT = true_roi[:, -1].sum()\r\n", "all_rois = np.array([roi_optimal, roi_current, roi_bothT])\r\n", "Y_baseline = (Y - model.effect(X=X.values, T1=T)).sum()"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 20, "source": ["pd.DataFrame(\r\n", "    {\r\n", "        \"Policy\": [\"Optimal\", \"Current\", \"All Investments\"],\r\n", "        \"ROI ($)\": all_rois,\r\n", "        \"ROI (% of baseline Y)\": np.round(all_rois / Y_baseline * 100, 1),\r\n", "    }\r\n", ")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            Policy     ROI ($)  ROI (% of baseline Y)\n", "0          Optimal  8920500.30                   58.6\n", "1          Current  1829938.41                   12.0\n", "2  All Investments   373176.80                    2.5"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Policy</th>\n", "      <th>ROI ($)</th>\n", "      <th>ROI (% of baseline Y)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Optimal</td>\n", "      <td>8920500.30</td>\n", "      <td>58.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Current</td>\n", "      <td>1829938.41</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>All Investments</td>\n", "      <td>373176.80</td>\n", "      <td>2.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 20}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Policy Decisions Using Doubly Robust Policy Learning"], "metadata": {}}, {"cell_type": "code", "execution_count": 21, "source": ["from econml.policy import DRPolicyForest\r\n", "\r\n", "X = multi_data[['Size', 'PC Count']]\r\n", "est = DRPolicyForest(random_state=1,\r\n", "                     min_propensity=1e-3,\r\n", "                     min_impurity_decrease=0.1,\r\n", "                     min_samples_leaf=40,\r\n", "                     max_samples=.6,\r\n", "                     honest=True,\r\n", "                     max_depth=4)\r\n", "costs = np.hstack([np.zeros((X.shape[0], 1)), cost_fn(multi_data)])\r\n", "est.fit(Y - costs[np.arange(X.shape[0]), T], T, X=X)\r\n", "recommended_T = est.predict(X.values)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 22, "source": ["ax1 = sns.scatterplot(\r\n", "    x=X.iloc[:, 0].values.flatten(),\r\n", "    y=multi_data[\"PC Count\"].values,\r\n", "    hue=all_treatments[recommended_T],\r\n", "    hue_order=all_treatments,\r\n", "    cmap=\"Dark2\",\r\n", "    s=40,\r\n", ")\r\n", "plt.legend(title=\"Investment Policy\")\r\n", "plt.setp(\r\n", "    ax1,\r\n", "    xlabel=\"Customer Size\",\r\n", "    ylabel=\"PC Count\",\r\n", "    title=\"Optimal Investment Policy by Customer\",\r\n", ")\r\n", "plt.show()"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {}}, {"cell_type": "code", "execution_count": 23, "source": ["plt.figure(figsize=(15, 5))\r\n", "est.plot(0, max_depth=2, treatment_names=['None'] + treatment_names)"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1080x360 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {}}, {"cell_type": "markdown", "source": ["### Performance based on ground truth"], "metadata": {}}, {"cell_type": "code", "execution_count": 24, "source": ["roi_current = true_roi[np.arange(X.shape[0]), T].sum()\r\n", "roi_optimal = true_roi[np.arange(X.shape[0]), recommended_T].sum()\r\n", "roi_bothT = true_roi[:, -1].sum()\r\n", "all_rois = np.array([roi_optimal, roi_current, roi_bothT])\r\n", "Y_baseline = (Y - model.effect(X=X.values[:, [0]], T1=T)).sum()"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 25, "source": ["pd.DataFrame(\r\n", "    {\r\n", "        \"Policy\": [\"Optimal\", \"Current\", \"All Investments\"],\r\n", "        \"ROI ($)\": all_rois,\r\n", "        \"ROI (% of baseline Y)\": np.round(all_rois / Y_baseline * 100, 1),\r\n", "    }\r\n", ")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["            Policy     ROI ($)  ROI (% of baseline Y)\n", "0          Optimal  8735944.79                   57.4\n", "1          Current  1829938.41                   12.0\n", "2  All Investments   373176.80                    2.5"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Policy</th>\n", "      <th>ROI ($)</th>\n", "      <th>ROI (% of baseline Y)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Optimal</td>\n", "      <td>8735944.79</td>\n", "      <td>57.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Current</td>\n", "      <td>1829938.41</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>All Investments</td>\n", "      <td>373176.80</td>\n", "      <td>2.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 25}], "metadata": {}}, {"cell_type": "markdown", "source": ["# Conclusions\n", "\n", "In this notebook, we have demonstrated the power of using EconML to:\n", "\n", "* Learn the effects of multiple concurrent interventions\n", "* Interpret the resulting individual-level treatment effects\n", "* Build investment policies around the learned effects\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). "], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": [], "outputs": [], "metadata": {}}], "metadata": {"kernelspec": {"name": "python3", "display_name": "Python 3.6.6 64-bit"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}, "interpreter": {"hash": "2e5c6628eef985e7fd2fa2aad22c988c5b8aa1d2648cf9c51c543a2a2637c546"}}, "nbformat": 4, "nbformat_minor": 4}