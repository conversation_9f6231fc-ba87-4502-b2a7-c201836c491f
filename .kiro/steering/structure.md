# Project Structure

## Root Directory Organization

### Core Notebooks
- **dowhy*.ipynb**: Main DoWhy framework exploration notebooks
- **Untitled*.ipynb**: Experimental and scratch notebooks
- **bow.ipynb**: Specific analysis implementations

### Documentation & Guides
- **DoWhy_GCM_Deep_Analysis.md**: Comprehensive GCM analysis (English)
- **DoWhy_GCM_深度解析.md**: GCM deep analysis (Chinese)
- **DoWhy因果推理方法综述.md**: DoWhy methods overview (Chinese)
- **causal_effects_concepts.md**: Core causal concepts documentation
- **chains_forks_colliders_deep_dive.md**: Detailed structural analysis
- **causal_structures.md**: Causal structure fundamentals

### Implementation Scripts
- **dowhy_complete_workflow.py**: Complete DoWhy analysis pipeline
- **causal_graph_examples.py**: Graph construction and manipulation examples

### Framework Directories
- **dowhy/**: DoWhy library source and extensions
- **EconML/**: EconML framework integration
- **causalML/**: CausalML framework exploration
- **causal-learn/**: Causal discovery algorithms
- **causaltune/**: Hyperparameter tuning for causal models
- **pywhy-llm/**: LLM integration for causal reasoning

### Data & Resources
- **data/**: Datasets for analysis examples
- **book/**: Reference materials and academic papers
- **images/**: Generated plots and diagrams
- **img/**: Static images and illustrations

### Configuration
- **setup_gui_env.sh**: GUI environment setup script
- **.env_gui**: Environment variables for interactive tools
- **netlify.toml**: Web deployment configuration

## Directory Conventions

### Notebook Organization
- Version-controlled notebooks with clear naming
- Checkpoint files in `.ipynb_checkpoints/`
- Bilingual content support (English/Chinese)

### Documentation Structure
- Comprehensive markdown files for each major concept
- Step-by-step tutorials and guides
- Academic reference materials in `book/`

### Code Organization
- Modular Python scripts for reusable workflows
- Framework-specific directories for organized exploration
- Example implementations with clear documentation

### Data Management
- Structured datasets in `data/` directory
- Generated visualizations in `images/` and `img/`
- Academic papers and references in `book/`

## File Naming Patterns

### Notebooks
- `dowhy[version].ipynb`: Version-specific DoWhy explorations
- `[concept]_guide.ipynb`: Educational tutorial notebooks
- `Untitled*.ipynb`: Experimental work-in-progress

### Documentation
- `[Topic]_[Language].md`: Bilingual documentation
- `[concept]_deep_dive.md`: Comprehensive analysis documents
- `[framework]_guide.md`: Framework-specific guides

### Scripts
- `[framework]_workflow.py`: Complete analysis pipelines
- `[concept]_examples.py`: Focused example implementations

## Development Workflow

1. **Exploration**: Start with Untitled notebooks for experimentation
2. **Documentation**: Create comprehensive markdown guides
3. **Implementation**: Develop reusable Python scripts
4. **Integration**: Organize into framework-specific directories
5. **Validation**: Test workflows and update documentation

## Best Practices

- Maintain bilingual documentation for accessibility
- Use clear, descriptive file names
- Keep notebooks focused on specific concepts
- Organize related files in appropriate directories
- Version control all significant changes