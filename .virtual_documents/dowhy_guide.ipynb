














import networkx as nx
import matplotlib.pyplot as plt

plt.figure(figsize=(6, 2)) 

G = nx.DiGraph()

G.add_node('X')
G.add_node('Y')
G.add_node('Z')
G.add_node('Ux')
G.add_node('Uy')
G.add_node('Uz')

G.add_edge('X', 'Y')
G.add_edge('Y', 'Z')
G.add_edge('Ux', 'X')
G.add_edge('Uy', 'Y')
G.add_edge('Uz', 'Z')

X_pos = (0, 0)  
Y_pos = (2, 0)
Z_pos = (4, 0)
Ux_pos = (0, 1)
Uy_pos = (2, 1)
Uz_pos = (4, 1)

pos = {'X': X_pos, 'Y': Y_pos, 'Z': Z_pos, 
       'Ux': Ux_pos, 'Uy': Uy_pos, 'Uz': Uz_pos}

nx.draw(G, pos, with_labels=True)
plt.show()








import networkx as nx
import matplotlib.pyplot as plt

plt.figure(figsize=(6, 2))

G = nx.DiGraph() 

G.add_node('X')
G.add_node('Y')
G.add_node('Z')  
G.add_node('Ux')
G.add_node('Uy')
G.add_node('Uz')

G.add_edge('X', 'Y')
G.add_edge('X', 'Z')

G.add_edge('Ux', 'X')
G.add_edge('Uy', 'Y')
G.add_edge('Uz', 'Z')

X_pos = (1, 1) 
Y_pos = (0.5, 0)
Z_pos = (1.5, 0)  

Ux_pos = (1, 2)
Uy_pos = (0.5, 1)
Uz_pos = (1.5, 1)

pos = {'X': X_pos, 'Y': Y_pos, 'Z': Z_pos,  
       'Ux': Ux_pos, 'Uy': Uy_pos, 'Uz': Uz_pos}

nx.draw(G, pos, with_labels=True)
plt.show()








import networkx as nx
import matplotlib.pyplot as plt

plt.figure(figsize=(6, 2))

G = nx.DiGraph()

G.add_node('X')  
G.add_node('Y')
G.add_node('Z')
G.add_node('Ux')
G.add_node('Uy')
G.add_node('Uz')

G.add_edge('X', 'Z')
G.add_edge('Y', 'Z') 

G.add_edge('Ux', 'X')
G.add_edge('Uy', 'Y') 
G.add_edge('Uz', 'Z')

X_pos = (0.5, 1)  
Y_pos = (1.5, 1)
Z_pos = (1, 0) 

Ux_pos = (0.5, 2)
Uy_pos = (1.5, 2)
Uz_pos = (1, 1)

pos = {'X': X_pos, 'Y': Y_pos, 'Z': Z_pos,  
       'Ux': Ux_pos, 'Uy': Uy_pos, 'Uz': Uz_pos}

nx.draw(G, pos, with_labels=True)
plt.show()











import networkx as nx
import matplotlib.pyplot as plt

G = nx.DiGraph()

# nodes
G.add_nodes_from(['Z', 'W', 'X', 'Y', 'U', 'Uz', 'Uw', 'Ux', 'Uy', 'Uu']) 

# edges 
G.add_edges_from([('Z', 'W'), ('X', 'W'), ('X', 'Y'), ('W', 'U')])
G.add_edges_from([('Uz', 'Z'), ('Uw', 'W'), ('Ux', 'X'), ('Uy', 'Y'), ('Uu', 'U')])

# positions
pos = {'Z': (0, 3), 'X': (2, 3), 'Y': (3, 3),  
       'W': (1, 2),
       'U': (1, 1), 
       'Uz': (0, 4), 'Ux': (2, 4), 'Uy': (3, 4),
       'Uw': (1, 3),
       'Uu': (0, 2)}

nx.draw(G, pos, with_labels=True, node_color='pink', edge_color='grey')
plt.show()





import networkx as nx
import matplotlib.pyplot as plt

G = nx.DiGraph()

# 新增节点 T
G.add_node('T')  

# 原有节点
G.add_nodes_from(['Z', 'W', 'X', 'Y', 'U', 'Uz', 'Uw', 'Ux', 'Uy', 'Uu'])

# 原有边 
G.add_edges_from([('Z', 'W'), ('X', 'W'), ('X', 'Y'), ('W', 'U')])
G.add_edges_from([('Uz', 'Z'), ('Uw', 'W'), ('Ux', 'X'), ('Uy', 'Y'), ('Uu', 'U')])

# 添加T的边
G.add_edge('T', 'Z')
G.add_edge('T', 'Y')

# 位置
pos = {'T': (1, 4),  
       'Z': (0, 3), 'X': (2, 3), 'Y': (3, 3),
       'W': (1, 2),
       'U': (1, 1),
       'Uz': (0, 4), 'Ux': (1, 3.5), 'Uy': (3, 4),  
       'Uw': (1, 3),
       'Uu': (0, 2)}

nx.draw(G, pos, with_labels=True)
plt.show()














固定X=x的情况，用do(X=x）来表示
- P(Y=y|do(X=x)) 表示通过干预使得X=x，这时Y=y的概率。
- P(Y=y|do(X=x),Z=z)表示对于给定的z，干do(X=x)得到的分布中Y=y的概率。
             
do运算符(do-operator)是Bayes网络中用于预测的一个重要运算符。

do运算符表示在网络中进行干预,强制设置某些节点状态,然后推断网络中其他节点值的变化。
具体而言,do(X=x)表示:
- 强制将变量X的值设置为x
- 断开X与其父节点之间的边,使X不再受父节点影响
- 基于该操作,推断网络中其他变量值的分布变化
do运算符区别于观察变量x的状态,观察不会截断原有关系。 
do运算符常用于因果推断,判断操作某因素时网络效应。也可用于计划决策分析。
例如,在医疗网络中,do(治疗A=1)可以推断进行治疗A后病情的分布变化。
总之,do运算符通过干预模拟操作网络,是Bayes网预测的重要工具。








import networkx as nx
import matplotlib.pyplot as plt

plt.figure(figsize=(6, 2))

G = nx.DiGraph()

G.add_node('X')  
G.add_node('Y')
G.add_node('Z')
G.add_node('Ux')
G.add_node('Uy')
G.add_node('Uz')

G.add_edge('Z', 'X')
G.add_edge('Z', 'Y') 
G.add_edge('X', 'Y')

G.add_edge('Ux', 'X')
G.add_edge('Uy', 'Y') 
G.add_edge('Uz', 'Z')

Z_pos = (1, 1)  
Y_pos = (2, 0)
X_pos = (0, 0) 

Ux_pos = (0, 0.5)
Uy_pos = (2, 0.5)
Uz_pos = (1, 1.5)

pos = {'X': X_pos, 'Y': Y_pos, 'Z': Z_pos,  
       'Ux': Ux_pos, 'Uy': Uy_pos, 'Uz': Uz_pos}

nx.draw(G, pos, with_labels=True)
plt.show()





import networkx as nx
import matplotlib.pyplot as plt

plt.figure(figsize=(6, 2))

G = nx.DiGraph()

G.add_node('X')  
G.add_node('Y')
G.add_node('Z')
# G.add_node('Ux')
G.add_node('Uy')
G.add_node('Uz')

# G.add_edge('Z', 'X')
G.add_edge('Z', 'Y') 
G.add_edge('X', 'Y')

# G.add_edge('Ux', 'X')
G.add_edge('Uy', 'Y') 
G.add_edge('Uz', 'Z')

Z_pos = (1, 1)  
Y_pos = (2, 0)
X_pos = (0, 0) 

Ux_pos = (0, 0.5)
Uy_pos = (2, 0.5)
Uz_pos = (1, 1.5)

pos = {'X': X_pos, 'Y': Y_pos, 'Z': Z_pos,  
       'Ux': Ux_pos, 'Uy': Uy_pos, 'Uz': Uz_pos}

nx.draw(G, pos, with_labels=True)
plt.show()











#产生数据
import pandas as pd

# 创建一个用于联合概率分布的DataFrame
data = pd.read_csv("/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/recovery_dataset.csv")


# 定义因果模型
model = CausalModel(
    data=data,
    treatment='Drug',
    outcome='Recovery',
    common_causes=['gender']
)

# 显示因果图（可选）
model.view_model()


print("Table 1.1 Results of a study into a new drug, with gender being taken into account")

print("{:<10} {:^20} {:^20}".format('', 'Drug', 'No drug'))

print("{:<10} {:>3} out of {:>3} recovered ({:>2}%) {:>20} {:>3} out of {:>3} recovered ({:>2}%)".
      format('Men', 81, 87, 93, '', 234, 270, 87))

print("{:<10} {:>3} out of {:>3} recovered ({:>2}%) {:>20} {:>3} out of {:>3} recovered ({:>2}%)". 
      format('Women', 192, 263, 73, '', 55, 80, 69))
      
print("{:<10} {:>3} out of {:>3} recovered ({:>2}%) {:>20} {:>3} out of {:>3} recovered ({:>2}%)".
      format('Combined data', 273, 350, 78, '', 289, 350, 83))








# 识别因果效应的标识
# identified_estimand = model.identify_effect(proceed_when_unidentifiable=True, method_name="backdoor.linear_regression")
identified_estimand = model.identify_effect()

print(identified_estimand)


estimate = model.estimate_effect(identified_estimand,
       method_name="backdoor.linear_regression",
       test_significance=True
)
print(estimate)








import matplotlib.pyplot as plt
import networkx as nx

plt.figure(figsize=(6, 2))
# Create a directed graph object
G = nx.DiGraph()

# Add edges (solid lines)
G.add_edges_from([("X", "Y"), ("W", "Y")])

# Add dashed edges (Z is involved)
dashed_edges = [("Z", "X"), ("Z", "W")]

# Add missing nodes and edges to the graph
G.add_node("Z")
G.add_edges_from(dashed_edges)

# Redraw the graph with the missing node and edges
pos = nx.spring_layout(G, seed=42)  # positions for all nodes


nx.draw(G, pos, with_labels=True, node_color="lightblue", font_weight="bold", node_size=700, font_size=18)
nx.draw_networkx_edges(G, pos, edgelist=dashed_edges, edge_color="grey", style="dashed")

plt.title("Causal Graph")
plt.show()






# Create a new directed graph object for the new causal structure
G_new = nx.DiGraph()
plt.figure(figsize=(6, 4))

# Add edges based on the new causal structure
G_new.add_edges_from([("X", "Y"), ("X", "W"), ("W", "Z"), ("Z", "T"), ("T", "Y"), ("W", "U")])

# Draw the new graph
pos_new = nx.spring_layout(G_new, seed=42)  # positions for all nodes
nx.draw(G_new, pos_new, with_labels=True, node_color="lightblue", font_weight="bold", node_size=700, font_size=18)

plt.title("New Causal Graph")
plt.show()












# Create a new directed graph object for the new causal structure
G_final = nx.DiGraph()

# Add edges based on the new causal structure
G_final.add_edges_from([("U", "Y"), ("U", "X"),("X", "Y")])

# Draw the new graph
pos_final = nx.spring_layout(G_final, seed=42)  # positions for all nodes
nx.draw(G_final, pos_final, with_labels=True, node_color="lightblue", font_weight="bold", node_size=700, font_size=18)

plt.title("Final Causal Graph")
plt.show()






# Create a new directed graph object for the new causal structure
G_final = nx.DiGraph()

# Add edges based on the new causal structure
G_final.add_edges_from([("U", "Y"), ("U", "X"), ("X", "Z"), ("Z", "Y")])

# Draw the new graph
pos_final = nx.spring_layout(G_final, seed=42)  # positions for all nodes
nx.draw(G_final, pos_final, with_labels=True, node_color="lightblue", font_weight="bold", node_size=700, font_size=18)

plt.title("Final Causal Graph")
plt.show()






























import networkx as nx
import matplotlib.pyplot as plt

# 创建一个有向图
G = nx.DiGraph()

# 添加节点和边
G.add_node("Gender (X)")
G.add_node("Qualification (Z)")
G.add_node("Hiring (Y)")
G.add_node("Income (I)")

G.add_edge("Gender (X)", "Qualification (Z)")  # "Gender (X)"指向"Qualification (Z)"
G.add_edge("Gender (X)", "Hiring (Y)")  # "Gender (X)"指向"Hiring (Y)"
G.add_edge("Qualification (Z)", "Hiring (Y)")
G.add_edge("Income (I)", "Qualification (Z)")
G.add_edge("Income (I)", "Hiring (Y)")

# 设置节点的布局为菱形
layout = {
    "Gender (X)": (0, 0),
    "Qualification (Z)": (1, 1),
    "Income (I)": (2, 0),
    "Hiring (Y)": (1, -1)
}

# 获取节点位置
pos = layout

# 绘制节点
nx.draw(G, pos, with_labels=True, node_size=500, node_color='lightblue')

# 调整图的大小为2x2
plt.figure(figsize=(1, 1))

























import networkx as nx
import matplotlib.pyplot as plt

# 创建一个有向图
G = nx.DiGraph()

# 添加节点
G.add_node("X")
G.add_node("Z")
G.add_node("W")
G.add_node("Y")
G.add_node("U_X")
G.add_node("U_Z")
G.add_node("U_W")
G.add_node("U_Y")

# 添加有向边和标签
G.add_edge("U_X", "X", label="1")
G.add_edge("U_Z", "Z", label="1")
G.add_edge("U_W", "W", label="1")
G.add_edge("U_Y", "Y", label="1")

G.add_edge("X", "Z", label="a")
G.add_edge("X", "W", label="b")
G.add_edge("Z", "Y", label="d")
G.add_edge("Z", "W", label="c")
G.add_edge("W", "Y", label="e")

# 按照钟表方式排列节点
pos = {
    "X": (0, 2),
    "Z": (1, 1),
    "W": (-1, 1),
    "Y": (0, 0),
    "U_X": (2, 2),
    "U_Z": (2, 1),
    "U_W": (-2, 1),
    "U_Y": (0, -1),
}

# 绘制节点
node_labels = {node: node for node in G.nodes()}
nx.draw_networkx_labels(G, pos, labels=node_labels, font_size=10)

# 绘制边和标签
edge_labels = {(edge[0], edge[1]): G.edges[edge]["label"] for edge in G.edges()}
nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)

# 绘制图形
nx.draw(G, pos, node_size=500, node_color="lightblue", with_labels=True)  
plt.axis("off")
plt.show()



















































pip install dowhy











import networkx as nx
from dowhy import gcm
from dowhy.utils import plot, bar_plot
df = pd.read_csv("/home/<USER>/OneDrive/Productive/Causality/dowhy/docs/source/example_notebooks/rca_microservice_architecture_latencies.csv")
df.head()
causal_graph = nx.DiGraph([('www', 'Website'),
                           ('Auth Service', 'www'),
                           ('API', 'www'),
                           ('Customer DB', 'Auth Service'),
                           ('Customer DB', 'API'),
                           ('Product Service', 'API'),
                           ('Auth Service', 'API'),
                           ('Order Service', 'API'),
                           ('Shipping Cost Service', 'Product Service'),
                           ('Caching Service', 'Product Service'),
                           ('Product DB', 'Caching Service'),
                           ('Customer DB', 'Product Service'),
                           ('Order DB', 'Order Service')])

from dowhy import CausalModel
import networkx as nx
causal_model = CausalModel(
   data=df, # some pandas dataframe
   treatment="Caching Service",
   outcome="Website",
   graph="\n".join(nx.generate_gml(causal_graph))
)

plot(causal_graph, figure_size=[5, 5])





import numpy as np

from dowhy import CausalModel
import dowhy.datasets
# 引入 dowhy 数据集生成函数，用于生成一个线性的因果模型的数据
data = dowhy.datasets.linear_dataset(
    beta=10,  # 设置treatments（treatment）对结果（outcome）的影响大小为 10
    num_common_causes=5,  # 设置有 5 个common cause（也叫做混杂变量或者控制变量）
    num_instruments=2,  # 设置有 2 个工具变量（instrumental variables）
    num_effect_modifiers=1,  # 设置有 1 个效应修饰变量（effect modifier）
    num_samples=5000,  # 设置样本数量为 5000
    treatment_is_binary=True,  # 设置treatments（treatment）是一个二进制变量（0 或 1）
    stddev_treatment_noise=10,  # 设置treatments（treatment）的噪声的标准差为 10
    num_discrete_common_causes=1  # 设置其中 1 个common cause（common cause）是离散变量
)

# 将生成的数据存储在 DataFrame 中
df = data["df"]


from dowhy import CausalModel

model= CausalModel(
        data=df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        common_causes=data["common_causes_names"],
        effect_modifiers=data["effect_modifier_names"])
model.view_model()





# !pip install cdt dowhy


import dowhy
from dowhy import CausalModel

import numpy as np
import pandas as pd
import graphviz
import networkx as nx

np.set_printoptions(precision=3, suppress=True)
np.random.seed(0)


import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt

plt.figure(figsize=(6, 2))

G = nx.DiGraph()

G.add_node('X')  
G.add_node('Y')
G.add_node('Z')
G.add_node('Ux')
G.add_node('Uy')
G.add_node('Uz')

G.add_edge('Z', 'X')
G.add_edge('Z', 'Y') 
G.add_edge('X', 'Y')

G.add_edge('Ux', 'X')
G.add_edge('Uy', 'Y') 
G.add_edge('Uz', 'Z')

Z_pos = (1, 1)  
Y_pos = (2, 0)
X_pos = (0, 0) 

Ux_pos = (0, 0.5)
Uy_pos = (2, 0.5)
Uz_pos = (1, 1.5)

pos = {'X': X_pos, 'Y': Y_pos, 'Z': Z_pos,  
       'Ux': Ux_pos, 'Uy': Uy_pos, 'Uz': Uz_pos}

nx.draw(G, pos, with_labels=True)
plt.show()
# 读取数据集

data_mpg = pd.read_csv('http://archive.ics.uci.edu/ml/machine-learning-databases/auto-mpg/auto-mpg.data-original',
                   delim_whitespace=True, header=None,
                   names = ['mpg', 'cylinders', 'displacement',
                            'horsepower', 'weight', 'acceleration',
                            'model year', 'origin', 'car name'])
data_mpg.dropna(inplace=True)
data_mpg.drop(['model year', 'origin', 'car name'], axis=1, inplace=True)


def make_graph(adjacency_matrix, labels=None):
    idx = np.abs(adjacency_matrix) > 0.01
    dirs = np.where(idx)
    d = graphviz.Digraph(engine='dot')
    names = labels if labels else [f'x{i}' for i in range(len(adjacency_matrix))]
    for name in names:
        d.node(name)
    for to, from_, coef in zip(dirs[0], dirs[1], adjacency_matrix[idx]):
        d.edge(names[from_], names[to], label=str(coef))
    return d

def str_to_dot(string):
    '''
    Converts input string from graphviz library to valid DOT graph format.
    '''
    graph = string.strip().replace('\n', ';').replace('\t','')
    graph = graph[:9] + graph[10:-2] + graph[-1] # Removing unnecessary characters from string
    return graph


from cdt.causality.graph import LiNGAM, PC, GES

graphs = {}
labels = [f'{col}' for i, col in enumerate(data_mpg.columns)]
functions = {
    'LiNGAM' : LiNGAM,
    # 'PC' : PC,
    # 'GES' : GES,
}

for method, lib in functions.items():
    obj = lib()
    output = obj.predict(data_mpg)
    adj_matrix = nx.to_numpy_array(output)
    adj_matrix = np.asarray(adj_matrix)
    graph_dot = make_graph(adj_matrix, labels)
    graphs[method] = graph_dot

# Visualize graphs
for method, graph in graphs.items():
    print("Method : %s"%(method))
    display(graph)


for method, graph in graphs.items():
        if method != "LiNGAM":
            continue
        print('\n*****************************************************************************\n')
        print("Causal Discovery Method : %s"%(method))

        # Obtain valid dot format
        graph_dot = str_to_dot(graph.source)

        # Define Causal Model
        model=CausalModel(
                data = data_mpg,
                treatment='mpg',
                outcome='weight',
                graph=graph_dot)

        # Identification
        identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
        print(identified_estimand)

        # Estimation
        estimate = model.estimate_effect(identified_estimand,
                                        method_name="backdoor.linear_regression",
                                        control_value=0,
                                        treatment_value=1,
                                        confidence_intervals=True,
                                        test_significance=True)
        print("Causal Estimate is " + str(estimate.value))





from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/pcm.png"))


from IPython.display import display, Image
display(Image(url="https://www.pywhy.org/dowhy/v0.10.1/_images/scm.png"))









































# model is an instance of CausalModel
identified_estimand = model.identify_effect()
print(identified_estimand)





# model is an instance of CausalModel
identified_estimand = causal_model.identify_effect()
print(identified_estimand)




















estimate = causal_model.estimate_effect(identified_estimand,
       method_name="backdoor.linear_regression",
       test_significance=True
)
print(estimate)





causal_estimate_dmatch = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.distance_matching",
                                             target_units="att",
                                             method_params={'distance_metric':"minkowski", 'p':2})
print(causal_estimate_dmatch)








# Propensity-Based Matching
causal_estimate_match = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.propensity_score_matching",
                                             target_units="atc")
print(causal_estimate_match)
print("Causal Estimate is " + str(causal_estimate_match.value))


# Propensity-based Stratification
causal_estimate_strat = model.estimate_effect(identified_estimand,
                                             method_name="backdoor.propensity_score_stratification",
                                             target_units="att")
print(causal_estimate_strat)
print("Causal Estimate is " + str(causal_estimate_strat.value))


# Inverse Propensity Weighting
causal_estimate_ipw = model.estimate_effect(identified_estimand,
                                           method_name="backdoor.propensity_score_weighting",
                                           target_units = "ate",
                                           method_params={"weighting_scheme":"ips_weight"})
print(causal_estimate_ipw)
print("Causal Estimate is " + str(causal_estimate_ipw.value))























causal_estimate_iv = model.estimate_effect(identified_estimand,
       method_name="iv.instrumental_variable", method_params = {'iv_instrument_name': 'Z0'})
print(causal_estimate_iv)
print("Causal Estimate is " + str(causal_estimate_iv.value))





causal_estimate_regdist = model.estimate_effect(identified_estimand,
       method_name="iv.regression_discontinuity",
       method_params={'rd_variable_name':'Z1',
                      'rd_threshold_value':0.5,
                      'rd_bandwidth': 0.15})
print(causal_estimate_regdist)
print("Causal Estimate is " + str(causal_estimate_regdist.value))








linear_estimate = model.estimate_effect(identified_estimand,
                                       method_name="backdoor.linear_regression",
                                      control_value=0,
                                      treatment_value=1)
print(linear_estimate)





from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor
dml_estimate = model.estimate_effect(identified_estimand, method_name="backdoor.econml.dml.DML",
                                    control_value = 0,
                                    treatment_value = 1,
                                target_units = lambda df: df["X0"]>1,  # condition used for CATE
                                confidence_intervals=False,
                               method_params={"init_params":{'model_y':GradientBoostingRegressor(),
                                                             'model_t': GradientBoostingRegressor(),
                                                             "model_final":LassoCV(fit_intercept=False),
                                                             'featurizer':PolynomialFeatures(degree=1, include_bias=False)},
                                              "fit_params":{}})





# Lets generate some data with an obvious impact of a treatment.


import networkx as nx, numpy as np, pandas as pd
import dowhy.gcm as gcm
X0 = np.random.normal(0, 0.2, 1000)
T = (X0 > 0).astype(float)
X1 = np.random.normal(0, 0.2, 1000) + 1.5 * T
Y = X1 + np.random.normal(0, 0.1, 1000)
data = pd.DataFrame(dict(T=T, X0=X0, X1=X1, Y=Y))





causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X0', 'T'), ('T', 'X1'), ('X1', 'Y')]))
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)





gcm.average_causal_effect(causal_model,
                         'Y',
                         interventions_alternative={'T': lambda x: 1},
                         interventions_reference={'T': lambda x: 0},
                         num_samples_to_draw=1000)





causal_model.graph.add_edge('X0', 'Y')
causal_model.graph.add_edge('T', 'Y')
gcm.auto.assign_causal_mechanisms(causal_model, data, override_models=True)
gcm.fit(causal_model, data)
gcm.average_causal_effect(causal_model,
                         'Y',
                         interventions_alternative={'T': lambda x: 1},
                         interventions_reference={'T': lambda x: 0},
                         num_samples_to_draw=1000)





gcm.average_causal_effect(causal_model,
                         'Y',
                         interventions_alternative={'T': lambda x: 1},
                         interventions_reference={'T': lambda x: 0},
                         observed_data=data)

















# 导入所需的库：NumPy用于数值计算，Pandas用于数据处理，NetworkX用于图论模型，dowhy用于因果推断
import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

# 生成1000个从-5到5均匀分布的随机数作为变量X的值
X = np.random.uniform(low=-5, high=5, size=1000)

# 生成变量Y，它是变量X的0.5倍加上一个均值为0、标准差为1的正态分布噪声
Y = 0.5 * X + np.random.normal(loc=0, scale=1, size=1000)

# 生成变量Z，它是变量Y的2倍加上一个均值为0、标准差为1的正态分布噪声
Z = 2 * Y + np.random.normal(loc=0, scale=1, size=1000)

# 生成变量W，它是变量Z的3倍加上一个均值为0、标准差为1的正态分布噪声
W = 3 * Z + np.random.normal(loc=0, scale=1, size=1000)

# 将所有生成的变量存储在一个Pandas DataFrame中
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))






# 使用gcm库中的InvertibleStructuralCausalModel类创建一个因果模型。
# 这里，nx.DiGraph表示一个有向图，其中('X', 'Y'), ('Y', 'Z'), ('Z', 'W')定义了因果关系：X -> Y -> Z -> W。
causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z'), ('Z', 'W')]))

# 使用auto模块的assign_causal_mechanisms函数自动为模型中的每一条因果关系分配一个因果机制。
# 这里，数据DataFrame用于帮助算法确定最合适的因果机制。
gcm.auto.assign_causal_mechanisms(causal_model, data)

# 使用fit函数拟合模型，这一步将使用数据来估计模型中的各个参数。
gcm.fit(causal_model, data)








# 从X的正常分布（均匀分布，范围在-5到5）中随机抽取一个样本
X = np.random.uniform(low=-5, high=5)

# 计算Y的值。这里，我们故意将Y的噪声设置为5，这是一个异常高的值。
# Y通常是X的0.5倍加上一个正态分布的噪声，但在这里，噪声被设置为5。
Y = 0.5 * X + 5

# 计算Z的值，它是Y的2倍（这里没有额外的噪声）
Z = 2 * Y

# 计算W的值，它是Z的3倍（这里没有额外的噪声）
W = 3 * Z

# 创建一个只包含这一个异常样本的Pandas DataFrame
# 这个数据框只包含一个样本，其中包含X、Y、Z和W的值。
anomalous_data = pd.DataFrame(data=dict(X=[X], Y=[Y], Z=[Z], W=[W]))





anomalous_data.iloc[0]['W'],data['W'].mean()





# 使用gcm库的attribute_anomalies函数来计算异常归因分数。
# 这里，'causal_model'是先前创建和拟合的因果模型，
# 'W'是我们关心的目标节点，
# 'anomalous_data'包含了异常样本。
attribution_scores = gcm.attribute_anomalies(causal_model, 'W', anomaly_samples=anomalous_data)
attribution_scores





import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()












import networkx as nx, numpy as np, pandas as pd
from dowhy import gcm
from scipy.stats import halfnorm


X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = np.maximum(X, Y) + np.random.normal(loc=0, scale=0.5, size=1000)
W = Z + halfnorm.rvs(size=1000, loc=0.1, scale=0.2)
data_old = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))


X = halfnorm.rvs(size=1000, loc=0.5, scale=0.2)
Y = halfnorm.rvs(size=1000, loc=1.0, scale=0.2)
Z = X + Y + np.random.normal(loc=0, scale=0.5, size=1000)
W = Z + halfnorm.rvs(size=1000, loc=0.1, scale=0.2)
data_new = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z, W=W))





causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X', 'Z'), ('Y', 'Z'), ('Z', 'W')]))  # (X, Y) -> Z -> W
gcm.auto.assign_causal_mechanisms(causal_model, data_old)


import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()





attributions = gcm.distribution_change(causal_model, data_old, data_new, 'W')
attributions














To see how the method works, let us generate some data following the example above:


import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm

X = abs(np.random.normal(loc=0, scale=5, size=1000))
Y = X + abs(np.random.normal(loc=0, scale=1, size=1000))
Z = Y + abs(np.random.normal(loc=0, scale=1, size=1000))
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





causal_model = gcm.StructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)


import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()





contributions = gcm.intrinsic_causal_influence(causal_model, 'Z')
contributions














import numpy as np, pandas as pd, networkx as nx
from dowhy import gcm


X = np.random.normal(loc=0, scale=1, size=1000)
Z = np.random.normal(loc=0, scale=1, size=1000)
Y = X + 3 * Z + np.random.normal(loc=0, scale=1, size=1000)
data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Z', 'Y')]))  # X -> Y <- Z
gcm.auto.assign_causal_mechanisms(causal_model, data)
gcm.fit(causal_model, data)


import matplotlib.pyplot as plt
import networkx as nx

# 获取因果模型的有向图
graph = causal_model.graph  # 或者直接使用你创建的 nx.DiGraph 对象

# 设置节点的位置，以水平方式展示
pos = {'X': (1, 1), 'Y': (2, 1), 'Z': (3, 1), 'W': (4, 1)}

# 使用networkx进行绘图
nx.draw(graph, pos, with_labels=True, node_color='skyblue', arrows=True)

# 显示图形
plt.show()





parent_relevance, noise_relevance = gcm.parent_relevance(causal_model, target_node="Y")
parent_relevance, noise_relevance





from sklearn.linear_model import LinearRegression
from dowhy.gcm.util.general import variance_of_deviations


mdl = LinearRegression()
mdl.fit(data[['X', 'Z']].to_numpy(), Y)
relevance = gcm.feature_relevance_distribution(mdl.predict, data[['X', 'Z']].to_numpy(), subset_scoring_func=variance_of_deviations)
relevance





single_observation = np.array([[2, 1]])





from dowhy.gcm.util.general import means_difference


relevance = gcm.feature_relevance_sample(mdl.predict, data[['X', 'Z']].to_numpy(), baseline_samples=single_observation, subset_scoring_func=means_difference)
relevance











# 生成数据
import numpy as np, pandas as pd

X = np.random.normal(loc=0, scale=1, size=1000)
Y = 2*X + np.random.normal(loc=0, scale=1, size=1000)
Z = 3*Y + np.random.normal(loc=0, scale=1, size=1000)
training_data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))


# 建立模型
import networkx as nx
from dowhy import gcm

causal_model = gcm.ProbabilisticCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
causal_model.set_causal_mechanism('X', gcm.EmpiricalDistribution())
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
causal_model.set_causal_mechanism('Z', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))


gcm.fit(causal_model, training_data)





samples = gcm.interventional_samples(causal_model,
                                     {'X': lambda x: 1},
                                     num_samples_to_draw=1000)
samples.head()





samples = gcm.interventional_samples(causal_model,
                                     {'X': lambda x: x + 0.5},
                                     num_samples_to_draw=1000)
samples.head()





# 生成数据

import networkx as nx, numpy as np, pandas as pd
from dowhy import gcm

X = np.random.uniform(low=0, high=10, size=1000)
Y = -2*X + np.random.normal(loc=0, scale=5, size=1000)
Z = 3*Y + 80 + np.random.normal(loc=0, scale=5, size=1000)
training_data = pd.DataFrame(data=dict(X=X, Y=Y, Z=Z))





causal_model = gcm.InvertibleStructuralCausalModel(nx.DiGraph([('X', 'Y'), ('Y', 'Z')])) # X -> Y -> Z
causal_model.set_causal_mechanism('X', gcm.EmpiricalDistribution())
causal_model.set_causal_mechanism('Y', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))
causal_model.set_causal_mechanism('Z', gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))


gcm.fit(causal_model, training_data)





gcm.counterfactual_samples(
    causal_model,
    {'X': lambda x: 5},
    observed_data=pd.DataFrame(data=dict(X=[0], Y=[5], Z=[110])))





gcm.counterfactual_samples(
    causal_model,
    {'X': lambda x: 5},
    noise_data=pd.DataFrame(data=dict(X=[0], Y=[5], Z=[15])))






































res_placebo=model.refute_estimate(identified_estimand, estimate,
       method_name="placebo_treatment_refuter", show_progress_bar=True, placebo_type="permute")
print(res_placebo)








# Testing for zero causal effect
ref = model.refute_estimate(identified_estimand,
                          causal_estimate,
                          method_name="dummy_outcome_refuter"
                          )
print(ref[0])


# Testing for non-zero causal effect
coefficients = np.array([1,2])
bias = 3
def linear_gen(df):
    y_new = np.dot(df[['W0','W1']].values,coefficients) + 3
    return y_new


ref = model.refute_estimate(identified_estimand,
                          causal_estimate,
                          method_name="dummy_outcome_refuter",
                          outcome_function=linear_gen
                          )
print(ref[0])








res_random=model.refute_estimate(identified_estimand, estimate, method_name="random_common_cause", show_progress_bar=True)
print(res_random)








res_subset=model.refute_estimate(identified_estimand, estimate,
       method_name="data_subset_refuter", show_progress_bar=True, subset_fraction=0.9)
print(res_subset)














# 导入数据
import numpy as np

from dowhy import CausalModel
import dowhy.datasets

# 引入 dowhy 数据集生成函数，用于生成一个线性的因果模型的数据
data = dowhy.datasets.linear_dataset(
    beta=10,  # 设置treatments（treatment）对结果（outcome）的影响大小为 10
    num_common_causes=5,  # 设置有 5 个common cause（也叫做混杂变量或者控制变量）
    num_instruments=2,  # 设置有 2 个工具变量（instrumental variables）
    num_effect_modifiers=1,  # 设置有 1 个效应修饰变量（effect modifier）
    num_samples=5000,  # 设置样本数量为 5000
    treatment_is_binary=True,  # 设置treatments（treatment）是一个二进制变量（0 或 1）
    stddev_treatment_noise=10,  # 设置treatments（treatment）的噪声的标准差为 10
    num_discrete_common_causes=1  # 设置其中 1 个common cause（common cause）是离散变量
)

# 将生成的数据存储在 DataFrame 中
df = data["df"]
data


# 建模
# 创建一个没有因果图（graph）的因果模型
# 使用 DoWhy 库的 CausalModel 类来实例化一个因果模型对象

# data=df: 指定数据集 df，这是一个包含所有相关变量（处理变量、结果变量、共同原因等）的 DataFrame。
# treatment=data["treatment_name"]: 指定处理变量（treatment variable）的名称。
# outcome=data["outcome_name"]: 指定结果变量（outcome variable）的名称。
# common_causes=data["common_causes_names"]: 指定所有可能的共同原因（common causes，也叫做混杂变量或控制变量）的名称。
# effect_modifiers=data["effect_modifier_names"]: 指定影响修正变量（effect modifiers）的名称，这些变量可能会改变处理对结果的影响。

model= CausalModel(
        data=df,
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        common_causes=data["common_causes_names"],
        effect_modifiers=data["effect_modifier_names"])
model.view_model()


# 这一行代码使用 identify_effect 方法来识别因果估计量（estimand）。
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)

print(identified_estimand)


# 使用 `estimate_effect` 方法来估算 `identified_estimand` 定义的因果效应。
# 这里使用 "backdoor.propensity_score_stratification" 作为估计方法，它是一种倾向得分分层方法。
estimate = model.estimate_effect(identified_estimand, method_name="backdoor.propensity_score_stratification")

# 打印出因果效应的估计结果，这将包括用于估计的表达式和相关假设。
print(estimate)



res_unobserved=model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                    confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear",
                                   effect_strength_on_treatment=0.01, effect_strength_on_outcome=0.02)
print(res_unobserved)





res_unobserved_range=model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                    confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear",
                                   effect_strength_on_treatment=np.array([0.001, 0.005, 0.01, 0.02]), effect_strength_on_outcome=0.01)
print(res_unobserved_range)





res_unobserved_range=model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                          confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear",
                                          effect_strength_on_treatment=[0.001, 0.005, 0.01, 0.02],
                                          effect_strength_on_outcome=[0.001, 0.005, 0.01,0.02])
print(res_unobserved_range)





res_unobserved_auto = model.refute_estimate(identified_estimand, estimate, method_name="add_unobserved_common_cause",
                                          confounders_effect_on_treatment="binary_flip", confounders_effect_on_outcome="linear")
print(res_unobserved_auto)








# Step 1: Load required packages

import os, sys
sys.path.append(os.path.abspath("../../../"))
import dowhy
from dowhy import CausalModel
import pandas as pd
import numpy as np
import dowhy.datasets

# Config dict to set the logging level
import logging.config
DEFAULT_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        '': {
            'level': 'ERROR',
        },
    }
}

logging.config.dictConfig(DEFAULT_LOGGING)
# Disabling warnings output
import warnings
from sklearn.exceptions import DataConversionWarning
#warnings.filterwarnings(action='ignore', category=DataConversionWarning)


# Step 2: Load the dataset
np.random.seed(100)
data = dowhy.datasets.linear_dataset( beta = 10,
                                      num_common_causes = 7,
                                      num_samples = 500,
                                      num_treatments = 1,
                                     stddev_treatment_noise =10,
                                     stddev_outcome_noise = 5
                                    )


# Step 3: Create Causal Model

data["df"] = data["df"].drop("W4", axis = 1)
graph_str = 'graph[directed 1node[ id "y" label "y"]node[ id "W0" label "W0"] node[ id "W1" label "W1"] node[ id "W2" label "W2"] node[ id "W3" label "W3"]  node[ id "W5" label "W5"] node[ id "W6" label "W6"]node[ id "v0" label "v0"]edge[source "v0" target "y"]edge[ source "W0" target "v0"] edge[ source "W1" target "v0"] edge[ source "W2" target "v0"] edge[ source "W3" target "v0"] edge[ source "W5" target "v0"] edge[ source "W6" target "v0"]edge[ source "W0" target "y"] edge[ source "W1" target "y"] edge[ source "W2" target "y"] edge[ source "W3" target "y"] edge[ source "W5" target "y"] edge[ source "W6" target "y"]]'
model = CausalModel(
            data=data["df"],
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=graph_str,
            test_significance=None,
        )
model.view_model()
data['df'].head()


#Step 4: Identification

identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)


# Step 5: Estimation

estimate = model.estimate_effect(identified_estimand,method_name="backdoor.linear_regression")
print(estimate)






refute = model.refute_estimate(identified_estimand, estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "linear-partial-R2",
                               benchmark_common_causes = ["W3"],
                               effect_fraction_on_treatment = [ 1,2,3]
                              )





refute.stats





refute.benchmarking_results








refute.plot(plot_type = 't-value')





print(refute)











refute = model.refute_estimate(identified_estimand, estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "e-value",
                              )





refute.stats


refute.benchmarking_results


print(refute)





np.random.seed(100)
data = dowhy.datasets.linear_dataset( beta = 10,
                                      num_common_causes = 7,
                                      num_samples = 500,
                                      num_treatments = 1,
                                     stddev_treatment_noise=10,
                                     stddev_outcome_noise = 1
                                    )


model = CausalModel(
            data=data["df"],
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=data["gml_graph"],
            test_significance=None,
        )
model.view_model()

data['df'].head()


identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)


estimate = model.estimate_effect(identified_estimand,method_name="backdoor.linear_regression")
print(estimate)


refute = model.refute_estimate(identified_estimand, estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "linear-partial-R2",
                               benchmark_common_causes = ["W3"],
                               effect_fraction_on_treatment = [ 1,2,3])


refute.plot(plot_type = 't-value')


print(refute)


refute.stats


refute.benchmarking_results














%load_ext autoreload
%autoreload 2


# Required libraries
import re
import numpy as np
import dowhy
from dowhy import CausalModel
import dowhy.datasets
from dowhy.utils.regression import create_polynomial_function





np.random.seed(101) 
data = dowhy.datasets.partially_linear_dataset(beta = 10,
                                               num_common_causes = 7,
                                               num_unobserved_common_causes=1,
                                               strength_unobserved_confounding=10,
                                               num_samples = 1000,
                                               num_treatments = 1,
                                               stddev_treatment_noise = 10,
                                               stddev_outcome_noise = 5
                                                )
display(data)





data["ate"]





# Observed data 
dropped_cols=["W0"]
user_data = data["df"].drop(dropped_cols, axis = 1)
# assumed graph
user_graph = data["gml_graph"]
for col in dropped_cols:
    user_graph = user_graph.replace('node[ id "{0}" label "{0}"]'.format(col), '')
    user_graph = re.sub('edge\[ source "{}" target "[vy][0]*"\]'.format(col), "", user_graph)
user_data





model = CausalModel(
            data=user_data,
            treatment=data["treatment_name"],
            outcome=data["outcome_name"],
            graph=user_graph,
            test_significance=None,
        )
model.view_model()
from IPython.display import Image, display
display(Image(filename="causal_model.png"))


# Identify effect
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
print(identified_estimand)


# Estimate effect
import econml
from sklearn.ensemble import GradientBoostingRegressor
linear_dml_estimate = model.estimate_effect(identified_estimand, 
                                    method_name="backdoor.econml.dml.dml.LinearDML",
                                    method_params={
                                        'init_params': {'model_y':GradientBoostingRegressor(),
                                                        'model_t': GradientBoostingRegressor(),
                                                        'linear_first_stages': False
                                                       },
                                        'fit_params': {'cache_values': True,}
                                     })
print(linear_dml_estimate)





refute = model.refute_estimate(identified_estimand, linear_dml_estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "non-parametric-partial-R2",
                               partial_r2_confounder_treatment = np.arange(0, 0.8, 0.1),
                               partial_r2_confounder_outcome = np.arange(0, 0.8, 0.1)
                              )
print(refute)





refute.RV











refute_bm = model.refute_estimate(identified_estimand, linear_dml_estimate ,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "non-parametric-partial-R2",
                               benchmark_common_causes = ["W1"],
                               effect_fraction_on_treatment = 0.2,
                               effect_fraction_on_outcome = 0.2
                              )








refute_bm.plot(plot_type = "upper_confidence_bound")
refute_bm.plot(plot_type = "bias")





refute_bm.results





# Estimate effect using a non-parametric estimator
from sklearn.ensemble import GradientBoostingRegressor
estimate_npar = model.estimate_effect(identified_estimand, 
                                    method_name="backdoor.econml.dml.KernelDML",
                                    method_params={
                                        'init_params': {'model_y':GradientBoostingRegressor(),
                                                        'model_t': GradientBoostingRegressor(),                                                       },
                                        'fit_params': {},
                                     })
print(estimate_npar)





refute_npar = model.refute_estimate(identified_estimand, estimate_npar,
                               method_name = "add_unobserved_common_cause",
                               simulation_method = "non-parametric-partial-R2",
                               benchmark_common_causes = ["W1"],
                               effect_fraction_on_treatment = 0.2,
                               effect_fraction_on_outcome = 0.2,
                               plugin_reisz=True
                              )
print(refute_npar)



