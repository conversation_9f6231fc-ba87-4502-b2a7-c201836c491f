# Technology Stack

## Primary Languages
- **Python**: Main programming language for all implementations
- **Jupyter Notebooks**: Primary development and documentation environment (.ipynb files)
- **Markdown**: Documentation and educational content

## Core Causal Inference Libraries

### Primary Frameworks
- **DoWhy**: Microsoft's causal inference library (primary focus)
- **EconML**: Microsoft's econometric machine learning library
- **CausalML**: Uber's causal machine learning library
- **causal-learn**: CMU's causal discovery library

### Supporting Libraries
- **NetworkX**: Graph manipulation and visualization
- **NumPy/Pandas**: Data manipulation and analysis
- **Matplotlib/Seaborn**: Data visualization
- **Scikit-learn**: Machine learning utilities
- **PyTorch**: Deep learning (for advanced causal models)

## Development Environment

### Jupyter Setup
- Notebooks are the primary development interface
- Mat<PERSON>lotlib backend configured for inline plotting
- Chinese language support for educational content

### Environment Configuration
- GUI environment setup scripts for interactive tools
- Display configuration for macOS (Cocoa backend)
- Interactive mode enabled for feedback tools

## Common Commands

### Environment Setup
```bash
# Setup GUI environment for interactive tools
source setup_gui_env.sh
# or
source .env_gui
```

### Jupyter Notebook
```bash
# Start Jupyter server
jupyter notebook

# Convert notebooks to other formats
jupyter nbconvert --to html notebook.ipynb
jupyter nbconvert --to pdf notebook.ipynb
```

### Python Execution
```bash
# Run causal analysis scripts
python dowhy_complete_workflow.py
python causal_graph_examples.py
```

## File Organization Patterns

- **Notebooks**: Exploratory analysis and educational content
- **Python scripts**: Reusable workflow implementations
- **Markdown files**: Comprehensive documentation and guides
- **Data files**: CSV datasets for analysis examples
- **Image files**: Generated plots and diagrams

## Development Practices

- Bilingual documentation (English/Chinese)
- Extensive inline documentation and comments
- Modular code structure for reusability
- Version control with detailed commit messages
- Educational focus with step-by-step explanations