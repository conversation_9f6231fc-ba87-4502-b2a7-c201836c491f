# RIPER-5 快速参考卡片

> **一页纸掌握所有核心功能** 📋

---

## 🎚️ 任务复杂度快速判断

| 复杂度 | 文件数 | 时间 | 流程 |
|--------|--------|------|------|
| **简单** | 1-2个 | <30分钟 | 理解→实施→验证 |
| **中等** | 3-8个 | 30分钟-2小时 | 分析→设计→实施→验证 |
| **复杂** | 9+个 | >2小时 | 研究→创新→规划→执行→审查 |

---

## 🎮 用户控制指令

### 基础控制
```bash
@fast      # 快速模式 - 跳过详细分析
@detail    # 详细模式 - 完整流程分析
@skip      # 跳过当前阶段
@back      # 返回上一阶段
```

### 流程控制
```bash
@stop      # 暂停流程
@reset     # 重新开始
@help      # 显示帮助
@lang en   # 切换英文模式
```

### 思考模式控制
```bash
@auto-think              # 自动选择思考模式
@think sequential        # 逐步思考分析
@think mental [模型]     # 心智模型 (first_principles/pareto_principle等)
@think debug [方法]      # 调试方法 (binary_search/divide_conquer等)
@think collab           # 协作推理 (多视角分析)
@think decide           # 决策框架 (结构化决策)
@think meta             # 元认知监控 (质量评估)
@think science          # 科学方法 (假设验证)
@think argue            # 结构化论证 (观点辩论)
@think visual           # 视觉推理 (图形化思考)
@think-combo [模式1,模式2] # 组合思考模式
@think-status           # 查看思考状态
```

---

## 🔄 三大核心模式

### 🔍 [模式: 理解]
- ✅ 分析代码和需求
- ✅ 评估任务复杂度
- ✅ 提出澄清问题
- ❌ 不提供解决方案

### 💡 [模式: 设计]  
- ✅ 创新方案设计
- ✅ 多方案对比分析
- ✅ 推荐最优方案
- ❌ 不写具体代码

### ⚡ [模式: 实施]
- ✅ 执行具体代码
- ✅ OODA循环优化
- ✅ 实时问题修复
- ✅ 进度记录更新

---

## 📝 项目记忆格式

```markdown
# 项目记忆

## 📋 项目概览
- 目标: [一句话描述]
- 复杂度: Simple/Medium/Complex
- 时间: 开始-预计完成

## 🎯 关键决策
### [时间] 决策点
- 问题: ...
- 选择: ...
- 理由: ...

## 📊 当前状态
- 阶段: [当前模式]
- 进度: X/Y
- 下一步: ...
```

---

## 🛠️ 快速模板

### 🐛 Bug修复
1. 问题复现
2. 根因分析  
3. 最小修改
4. 测试验证

### ✨ 功能添加
1. 需求澄清
2. 设计方案
3. 接口定义
4. 实现开发
5. 测试覆盖

### 🔧 代码重构
1. 现状评估
2. 重构目标
3. 风险评估
4. 分步实施

---

## ✅ 质量检查清单

### 基础检查
- [ ] 错误处理完整
- [ ] 类型注解清晰
- [ ] 代码风格规范
- [ ] 命名清晰易懂

### 测试检查
- [ ] 单元测试覆盖
- [ ] 边界条件测试
- [ ] 集成测试完整
- [ ] 性能测试验证

### 文档检查
- [ ] API文档更新
- [ ] README同步
- [ ] 变更日志记录
- [ ] 配置说明完整

---

## 🚀 使用技巧

### 高效开始
1. 让AI自动评估复杂度
2. 简单任务用 `@fast`
3. 复杂任务用 `@detail`
4. 不确定时先用理解模式

### 流程控制
- 需要跳过分析：`@skip`
- 发现理解有误：`@back`
- 需要暂停思考：`@stop`
- 完全重新开始：`@reset`

### 质量保证
- **强制确认**: 使用 `interactive_feedback` 工具确认关键决策
- 重要变更要记录
- 测试覆盖要充分
- 文档更新要同步

### Interactive Feedback 必调场景
- 🔍 需求不明确时
- 🎯 关键决策点
- ✅ 任务完成前
- ⚠️ 发现问题时

---

## ⚡ 常见场景速查

| 场景 | 推荐指令 | 预期流程 |
|------|----------|----------|
| 修复小bug | `@fast` | 直接实施 |
| 添加新功能 | 默认流程 | 理解→设计→实施 |
| 架构重构 | `@detail` | 完整5阶段 |
| 代码审查 | `@detail` | 重点在理解和设计 |
| 性能优化 | 默认流程 | 分析→方案→实施 |

---

## 🎯 成功指标

- **响应速度**: ≤30秒
- **理解准确**: >95%
- **用户满意**: >90%
- **代码质量**: 通过所有检查

---

**💡 记住**: 协议的目标是在保持高质量的同时提高效率，灵活使用控制指令是关键！

**© 2025 RIPER-5 快速参考** | 随时可用的效率助手 