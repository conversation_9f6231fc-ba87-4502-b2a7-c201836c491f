# This workflows will upload a Python Package using Twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

name: Publish Package

on:
  release:
    types: [created]
  workflow_dispatch:

jobs:
  deploy:
    name: Upload release to PyPI
    runs-on: ubuntu-latest
    environment: release
    permissions:
      id-token: write
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python 3.9
      uses: actions/setup-python@v5
      with:
        python-version: '3.9'
    
    - name: Install Poetry
      uses: abatilo/actions-poetry@v4.0.0
      with:
        poetry-version: '1.5.1'

    - name: Install Poetry Dynamic Versioning Plugin
      run: pip install poetry-dynamic-versioning

    - name: Install dependencies
      run: poetry install
        
    - name: Build and publish
      run: |
        poetry-dynamic-versioning
        poetry build
    - name: Publish package distributions to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
