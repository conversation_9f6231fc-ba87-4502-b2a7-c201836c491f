




















#pip install dowhy econml


# Required libraries
import dowhy
from dowhy import CausalModel
import dowhy.datasets

# Avoiding unnecessary log messges and warnings
import logging
logging.getLogger("dowhy").setLevel(logging.WARNING)  # 设置日志级别，避免过多的日志信息
import warnings
from sklearn.exceptions import DataConversionWarning
warnings.filterwarnings(action='ignore', category=DataConversionWarning)  # 过滤掉数据类型转换相关的警告信息

# Load some sample data
data = dowhy.datasets.linear_dataset(
    beta=10,  # 因果效应的大小
    num_common_causes=5,  # 具有因果影响的特征数量
    num_instruments=2,  # 工具变量的数量
    num_samples=10000,  # 样本数量
    treatment_is_binary=True,  # 处理变量是否为二进制
    stddev_treatment_noise=10  # 处理变量的噪声标准差
)






# 创建一个因果模型（CausalModel）。
model = CausalModel(
    data=data["df"],  # 数据
    treatment=data["treatment_name"],  # 处理变量
    outcome=data["outcome_name"],  # 结果变量
    common_causes=data["common_causes_names"],  # 共同原因变量
    instruments=data["instrument_names"]  # 工具变量
)

# 使用`model.view_model()`函数以"dot"布局可视化了因果模型的结构。
model.view_model(layout="dot")





# 创建一个因果模型（CausalModel）并给定因果图。
model = CausalModel(
    data=data["df"],  # 数据
    treatment=data["treatment_name"][0],  # 处理变量
    outcome=data["outcome_name"][0],  # 结果变量
    graph=data["gml_graph"]  # 给定的因果图
)

# 使用`model.view_model()`函数以"dot"布局可视化了因果模型的结构。
model.view_model()





# 第二步：识别因果效应并返回目标估计量
# 使用CausalModel对象来识别因果效应。将'proceed_when_unidentifiable'设置为True，以便在识别不可确定时继续执行。
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)

# 打印已识别的估计量。
print(identified_estimand)





# III. 使用倾向得分分层方法来估计目标估计量。
propensity_strat_estimate = model.estimate_effect(identified_estimand,
                                 method_name="backdoor.dowhy.propensity_score_stratification")

print(propensity_strat_estimate)



import econml
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

# 使用经济学机器学习中的 Double-ML 方法来估计目标估计量
dml_estimate = model.estimate_effect(identified_estimand,
                                    method_name="backdoor.econml.dml.DML",
                                    method_params={
                                        'init_params': {'model_y':GradientBoostingRegressor(),
                                                        'model_t': GradientBoostingRegressor(),
                                                        'model_final':LassoCV(fit_intercept=False), },
                                        'fit_params': {}
                                     })
print(dml_estimate)






# IV. Refute the obtained estimate using multiple robustness checks.
refute_results = model.refute_estimate(identified_estimand, propensity_strat_estimate,
                                       method_name="placebo_treatment_refuter")
print(refute_results)








import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import math
import dowhy.datasets, dowhy.plotter





# 通过随机选择，rvar 变量被设置为 1 或 0。这决定了是否有因果关系。
rvar = 1 if np.random.uniform() > 0.2 else 0
is_linear = False # A non-linear dataset. Change to True to see results for a linear dataset.

# 使用 dowhy 库中的 xy_dataset 函数生成一个数据集。
# effect 参数设置为 rvar，表示是否有因果效应（1 表示有，0 表示无）。
# num_common_causes 设置为 1，表示有一个共同原因（即一个混杂变量）。
# sd_error 设置为 0.2，表示生成数据的标准差。
non_linear_data_dict = dowhy.datasets.xy_dataset(10000, effect=rvar,
                                      num_common_causes=2,
                                      is_linear=is_linear,
                                      sd_error=0.2)

# 从生成的数据字典中获取 DataFrame。
non_linear_df = non_linear_data_dict['df']
print(non_linear_df.head())

# 使用 dowhy 库的 plotter 模块中的 plot_treatment_outcome 函数
# 来绘制治疗变量和结果变量之间的关系。
# 第一个参数是 DataFrame 中代表治疗的列。
# 第二个参数是 DataFrame 中代表结果（或响应）的列。
# 第三个参数是 DataFrame 中代表时间的列（如果有的话）。
dowhy.plotter.plot_treatment_outcome(non_linear_df[non_linear_data_dict["treatment_name"]],
                                     non_linear_df[non_linear_data_dict["outcome_name"]],
                                     non_linear_df[non_linear_data_dict["time_val"]])





# 通过随机选择，rvar 变量被设置为 1 或 0。这决定了是否有因果关系。
rvar = 1 if np.random.uniform() > 0.2 else 0
is_linear = True # A non-linear dataset. Change to True to see results for a linear dataset.

# 使用 dowhy 库中的 xy_dataset 函数生成一个数据集。
# effect 参数设置为 rvar，表示是否有因果效应（1 表示有，0 表示无）。
# num_common_causes 设置为 1，表示有一个共同原因（即一个混杂变量）。
# sd_error 设置为 0.2，表示生成数据的标准差。
linear_data_dict = dowhy.datasets.xy_dataset(10000, effect=rvar,
                                      num_common_causes=2,
                                      is_linear=is_linear,
                                      sd_error=0.2)

# 从生成的数据字典中获取 DataFrame。
linear_df = linear_data_dict['df']
print(linear_df.head())

# 使用 dowhy 库的 plotter 模块中的 plot_treatment_outcome 函数
# 来绘制治疗变量和结果变量之间的关系。
# 第一个参数是 DataFrame 中代表治疗的列。
# 第二个参数是 DataFrame 中代表结果（或响应）的列。
# 第三个参数是 DataFrame 中代表时间的列（如果有的话）。
dowhy.plotter.plot_treatment_outcome(linear_df[linear_data_dict["treatment_name"]],
                                     linear_df[linear_data_dict["outcome_name"]],
                                     linear_df[linear_data_dict["time_val"]])








# 创建一个因果模型对象
# 数据集是 df，处理（或干预）变量是在 data_dict["treatment_name"] 中定义的，
# 结果变量是在 data_dict["outcome_name"] 中定义的，
# 共同原因（或混杂变量）是在 data_dict["common_causes_names"] 中定义的，
# 工具变量（如果有的话）是在 data_dict["instrument_names"] 中定义的。
model_linear= CausalModel(
        data=linear_df,  # 数据集
        treatment=linear_data_dict["treatment_name"],  # 处理变量
        outcome=linear_data_dict["outcome_name"],  # 结果变量
        common_causes=linear_data_dict["common_causes_names"],  # 共同原因
        instruments=linear_data_dict["instrument_names"])  # 工具变量（如果有的话）

# 可视化因果模型
# 使用 "dot" 布局来显示因果图
model_linear.view_model()






# 创建一个因果模型对象
# 数据集是 df，处理（或干预）变量是在 data_dict["treatment_name"] 中定义的，
# 结果变量是在 data_dict["outcome_name"] 中定义的，
# 共同原因（或混杂变量）是在 data_dict["common_causes_names"] 中定义的，
# 工具变量（如果有的话）是在 data_dict["instrument_names"] 中定义的。
model_non_linear= CausalModel(
        data=non_linear_df,  # 数据集
        treatment=non_linear_data_dict["treatment_name"],  # 处理变量
        outcome=non_linear_data_dict["outcome_name"],  # 结果变量
        common_causes=non_linear_data_dict["common_causes_names"],  # 共同原因
        instruments=non_linear_data_dict["instrument_names"])  # 工具变量（如果有的话）

# 可视化因果模型
# 使用 "dot" 布局来显示因果图
model_non_linear.view_model()









# 使用 'identify_effect' 方法来识别用于估计因果效应的估算量（Estimand）。
# 参数 'proceed_when_unidentifiable=True' 表示即使因果效应不可识别，也继续执行。
non_linear_identified_estimand = model_non_linear.identify_effect(proceed_when_unidentifiable=True)

# 打印出识别的估算量及其相关属性和假设。
# 这将帮助您了解可以用哪些方法来进行因果效应的估计，以及这些方法需要满足哪些假设。
print(non_linear_identified_estimand)






# 使用 'identify_effect' 方法来识别用于估计因果效应的估算量（Estimand）。
# 参数 'proceed_when_unidentifiable=True' 表示即使因果效应不可识别，也继续执行。
linear_identified_estimand = model_linear.identify_effect(proceed_when_unidentifiable=True)

# 打印出识别的估算量及其相关属性和假设。
# 这将帮助您了解可以用哪些方法来进行因果效应的估计，以及这些方法需要满足哪些假设。
print(linear_identified_estimand)









# 使用 'estimate_effect' 方法和线性回归（"backdoor.linear_regression"）来估计因果效应。
# 这里的 'identified_estimand' 是之前识别步骤的输出。
linear_estimate = model_linear.estimate_effect(linear_identified_estimand,
        method_name="backdoor.linear_regression")
print(linear_estimate)

# 使用 DoWhy 的 'plot_causal_effect' 方法来绘制因果效应。
# 这将绘制一个散点图，显示处理变量与结果之间的关系，以及线性回归的斜率（即因果效应）。
dowhy.plotter.plot_causal_effect(linear_estimate,
                                 linear_df[linear_data_dict["treatment_name"]],
                                 linear_df[linear_data_dict["outcome_name"]])







# 使用 'estimate_effect' 方法和线性回归（"backdoor.linear_regression"）来估计因果效应。
# 这里的 'identified_estimand' 是之前识别步骤的输出。
non_linear_estimate = model_non_linear.estimate_effect(non_linear_identified_estimand,
        method_name="backdoor.linear_regression")
print(non_linear_estimate)

# 使用 DoWhy 的 'plot_causal_effect' 方法来绘制因果效应。
# 这将绘制一个散点图，显示处理变量与结果之间的关系，以及线性回归的斜率（即因果效应）。
dowhy.plotter.plot_causal_effect(non_linear_estimate,
                                 non_linear_df[non_linear_data_dict["treatment_name"]],
                                 non_linear_df[non_linear_data_dict["outcome_name"]])










# 从 scikit-learn 中导入所需的模块
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

# 使用 econml 包中的 DML 方法来估计因果效应。
# control_value 设置为 0，treatment_value 设置为 1。
# target_units 指定我们对数据子集中 "X0" > 1 的情况感兴趣。
# 我们对结果（'model_y'）和处理（'model_t'）模型都使用 GradientBoostingRegressor。
# 对于最终模型（'model_final'）使用 LassoCV，并使用 PolynomialFeatures 进行特征工程。
dml_estimate_linear = model_linear.estimate_effect(linear_identified_estimand,
                                     method_name="backdoor.econml.dml.DML",
                                     control_value = 0,
                                     treatment_value = 1,
                                     confidence_intervals=False,
                                     method_params={"init_params":{
                                        'model_y':GradientBoostingRegressor(),
                                        'model_t': GradientBoostingRegressor(),
                                        "model_final":LassoCV(fit_intercept=False),
                                        'featurizer':PolynomialFeatures(degree=2, include_bias=True)},
                                                    "fit_params":{}})

# 打印估计的效应
print(dml_estimate_linear)





# 从 scikit-learn 中导入所需的模块
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

# 使用 econml 包中的 DML 方法来估计因果效应。
# control_value 设置为 0，treatment_value 设置为 1。
# target_units 指定我们对数据子集中 "X0" > 1 的情况感兴趣。
# 我们对结果（'model_y'）和处理（'model_t'）模型都使用 GradientBoostingRegressor。
# 对于最终模型（'model_final'）使用 LassoCV，并使用 PolynomialFeatures 进行特征工程。
dml_estimate_non_linear = model_non_linear.estimate_effect(non_linear_identified_estimand,
                                     method_name="backdoor.econml.dml.DML",
                                     control_value = 0,
                                     treatment_value = 1,
                                     confidence_intervals=False,
                                     method_params={"init_params":{
                                        'model_y':GradientBoostingRegressor(),
                                        'model_t': GradientBoostingRegressor(),
                                        "model_final":LassoCV(fit_intercept=False),
                                        'featurizer':PolynomialFeatures(degree=2, include_bias=True)},
                                                    "fit_params":{}})

# 打印估计的效应
print(dml_estimate_non_linear)



print("线性算法非线性估计方法 " + str(non_linear_estimate.value)),
print("DML算法非线性数据估计方法 " + str(dml_estimate_non_linear.value))



print("线性算法线性估计方法 " + str(linear_estimate.value)),
print("DML算法线性数据估计方法 " + str(dml_estimate_linear.value))









res_random=model_non_linear.refute_estimate(non_linear_identified_estimand,
                                            dml_estimate_non_linear,
                                            method_name="random_common_cause")
print(res_random)


res_placebo=model_non_linear.refute_estimate(non_linear_identified_estimand,
                                  dml_estimate_non_linear,
                                  method_name="placebo_treatment_refuter",
                                  placebo_type="permute",
                                  num_simulations=20)
print(res_placebo)












