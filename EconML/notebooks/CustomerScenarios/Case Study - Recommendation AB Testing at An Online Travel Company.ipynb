{"cells": [{"cell_type": "markdown", "source": ["<img src=\"https://www.microsoft.com/en-us/research/uploads/prod/2020/05/Recomendation.png\" width=\"400\">\n", "\n", "# Recommendation A/B Testing: Experimentation with Imperfect Compliance\n", "\n", "An online business would like to test a new feature or offering of their website and learn its effect on downstream revenue. Furthermore, they would like to know which kind of users respond best to the new version. We call the user-specfic effect a **heterogeneous treatment effect**. \n", "\n", "Ideally, the business would run an A/B tests between the old and new versions of the website. However, a direct A/B test might not work because the business cannot force the customers to take the new offering. Measuring the effect in this way will be misleading since not every customer exposed to the new offering will take it.\n", "\n", "The business also cannot look directly at existing data as it will be biased: the users who use the latest website features are most likely the ones who are very engaged on the website and hence spend more on the company's products to begin with. Estimating the effect this way would be overly optimistic."], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "markdown", "source": ["In this customer scenario walkthough, we show how tools from the [EconML](https://aka.ms/econml) library can still use a direct A/B test and mitigate these shortcomings.\n", "\n", "### Summary\n", "\n", "1. [Background](#Background)\n", "2. [Data](#Data)\n", "3. [Get Causal Effects with EconML](#Get-Causal-Effects-with-EconML)\n", "4. [Understand Treatment Effects with EconML](#Understand-Treatment-Effects-with-EconML)\n", "5. [Make Policy Decisions with EconML](#Make-Policy-Decisions-with-EconML)\n", "6. [Conclusions](#Conclusions)"], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "markdown", "source": ["# Background\n", "\n", "<img src=\"https://cdn.pixabay.com/photo/2013/07/13/12/18/boeing-159589_640.png\" width=\"450\">\n", "\n", "In this scenario, a travel website would like to know whether joining a membership program compels users to spend more time engaging with the website and purchasing more products. \n", "\n", "A direct A/B test is infeasible because the website cannot force users to become members. Likewise, the travel company can’t look directly at existing data, comparing members and non-members, because the customers who chose to become members are likely already more engaged than other users. \n", "\n", "**Solution:** The company had run an earlier experiment to test the value of a new, faster sign-up process. EconML's IV estimators can exploit this experimental nudge towards membership as an instrument that generates random variation in the likelihood of membership. This is known as an **intent-to-treat** setting: the intention is to give a random group of user the \"treatment\" (access to the easier sign-up process), but not not all users will actually take it. \n", "\n", "EconML's `IntentToTreatDRIV` estimator model takes advantage of the fact that not every customer who was offered the easier sign-up became a member to learn the effect of membership rather than the effect of receiving the quick sign-up."], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 1, "source": ["# Some imports to get us started\r\n", "# Utilities\r\n", "import os\r\n", "import urllib.request\r\n", "import numpy as np\r\n", "import pandas as pd\r\n", "\r\n", "# Generic ML imports\r\n", "import lightgbm as lgb\r\n", "from sklearn.preprocessing import PolynomialFeatures\r\n", "\r\n", "# EconML imports\r\n", "from econml.iv.dr import LinearIntentToTreatDRIV\r\n", "from econml.cate_interpreter import SingleTreeCateInterpreter, \\\r\n", "                                    SingleTreePolicyInterpreter\r\n", "\r\n", "import matplotlib.pyplot as plt\r\n", "%matplotlib inline"], "outputs": [], "metadata": {"slideshow": {"slide_type": "skip"}}}, {"cell_type": "markdown", "source": ["# Data\n", "\n", "The data* is comprised of:\n", " * Features collected in the 28 days prior to the experiment (denoted by the suffix `_pre`)\n", " * Experiment variables (whether the use was exposed to the easier signup -> the instrument, and whether the user became a member -> the treatment)\n", " * Variables collected in the 28 days after the experiment (denoted by the suffix `_post`).\n", "\n", "Feature Name | Type | Details \n", ":--- |:--- |:--- \n", "**days_visited_exp_pre** | X | #days a user visits the attractions pages \n", "**days_visited_free_pre** | X | #days a user visits the website through free channels (e.g. domain direct) \n", "**days_visited_fs_pre** | X | #days a user visits the flights pages \n", "**days_visited_hs_pre** | X | #days a user visits the hotels pages \n", "**days_visited_rs_pre** | X | #days a user visits the restaurants pages \n", "**days_visited_vrs_pre** | X |#days a user visits the vacation rental pages \n", "**locale_en_US** | X | whether the user access the website from the US \n", "**os_type** | X | user's operating system (windows, osx, other) \n", "**revenue_pre** | X | how much the user spent on the website in the pre-period \n", "**easier_signup** | Z | whether the user was exposed to the easier signup process \n", "**became_member** | T | whether the user became a member \n", "**days_visited_post** | Y | #days a user visits the website in the 28 days after the experiment \n", "\n", "\n", "**To protect the privacy of the travel company's users, the data used in this scenario is synthetically generated and the feature distributions don't correspond to real distributions. However, the feature names have preserved their names and meaning.*"], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 2, "source": ["# Import the sample AB data\n", "file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/RecommendationAB/ab_sample.csv\"   \n", "ab_data = pd.read_csv(file_url)"], "outputs": [], "metadata": {"slideshow": {"slide_type": "skip"}}}, {"cell_type": "code", "execution_count": 3, "source": ["# Data sample\n", "ab_data.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   days_visited_exp_pre  days_visited_free_pre  days_visited_fs_pre  \\\n", "0                     1                      9                    7   \n", "1                    10                     25                   27   \n", "2                    18                     14                    8   \n", "3                    17                      0                   23   \n", "4                    24                      9                   22   \n", "\n", "   days_visited_hs_pre  days_visited_rs_pre  days_visited_vrs_pre  \\\n", "0                   25                    6                     3   \n", "1                   10                   27                    27   \n", "2                    4                    5                     2   \n", "3                    2                    3                     1   \n", "4                    2                    3                    18   \n", "\n", "   locale_en_US  revenue_pre  os_type_osx  os_type_windows  easier_signup  \\\n", "0             1         0.01            0                1              0   \n", "1             0         2.26            0                0              0   \n", "2             1         0.03            0                1              0   \n", "3             1       418.77            0                1              0   \n", "4             1         1.54            0                0              0   \n", "\n", "   became_member  days_visited_post  \n", "0              0                  1  \n", "1              0                 15  \n", "2              0                 17  \n", "3              0                  6  \n", "4              0                 12  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>days_visited_exp_pre</th>\n", "      <th>days_visited_free_pre</th>\n", "      <th>days_visited_fs_pre</th>\n", "      <th>days_visited_hs_pre</th>\n", "      <th>days_visited_rs_pre</th>\n", "      <th>days_visited_vrs_pre</th>\n", "      <th>locale_en_US</th>\n", "      <th>revenue_pre</th>\n", "      <th>os_type_osx</th>\n", "      <th>os_type_windows</th>\n", "      <th>easier_signup</th>\n", "      <th>became_member</th>\n", "      <th>days_visited_post</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>25</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0.01</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10</td>\n", "      <td>25</td>\n", "      <td>27</td>\n", "      <td>10</td>\n", "      <td>27</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>2.26</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>18</td>\n", "      <td>14</td>\n", "      <td>8</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0.03</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17</td>\n", "      <td>0</td>\n", "      <td>23</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>418.77</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>24</td>\n", "      <td>9</td>\n", "      <td>22</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>18</td>\n", "      <td>1</td>\n", "      <td>1.54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 3}], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 4, "source": ["# Define estimator inputs\n", "Z = ab_data['easier_signup'] # nudge, or instrument\n", "T = ab_data['became_member'] # intervention, or treatment\n", "Y = ab_data['days_visited_post'] # outcome of interest\n", "X_data = ab_data.drop(columns=['easier_signup', 'became_member', 'days_visited_post']) # features"], "outputs": [], "metadata": {"slideshow": {"slide_type": "fragment"}}}, {"cell_type": "markdown", "source": ["The data was generated using the following undelying treatment effect function:\n", "\n", "$$\n", "\\text{treatment_effect} = 0.2 + 0.3 \\cdot \\text{days_visited_free_pre} - 0.2 \\cdot \\text{days_visited_hs_pre} + \\text{os_type_osx}\n", "$$\n", "\n", "The interpretation of this is that users who visited the website before the experiment and/or who use an iPhone tend to benefit from the membership program, whereas users who visited the hotels pages tend to be harmed by membership. **This is the relationship we seek to learn from the data.**"], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 5, "source": ["# Define underlying treatment effect function \n", "TE_fn = lambda X: (0.2 + 0.3 * X['days_visited_free_pre'] - 0.2 * X['days_visited_hs_pre'] + X['os_type_osx']).values\n", "true_TE = TE_fn(X_data)\n", "\n", "# Define the true coefficients to compare with\n", "true_coefs = np.zeros(X_data.shape[1])\n", "true_coefs[[1, 3, -2]] = [0.3, -0.2, 1]"], "outputs": [], "metadata": {"slideshow": {"slide_type": "skip"}}}, {"cell_type": "markdown", "source": ["# Get Causal Effects with EconML\n", "\n", "To learn a linear projection of the treatment effect, we use the `LinearIntentToTreatDRIV` EconML estimator. For a more flexible treatment effect function, use the `IntentToTreatDRIV` estimator instead. \n", "\n", "The model requires to define some nuissance models (i.e. models we don't really care about but that matter for the analysis): the model for how the outcome $Y$ depends on the features $X$ (`model_Y_X`) and the model for how the treatment $T$ depends on the instrument $Z$ and features $X$ (`model_T_XZ`). Since we don't have any priors on these models, we use generic boosted tree estimators to learn them. "], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 6, "source": ["# Define nuissance estimators\r\n", "lgb_T_XZ_params = {\r\n", "    'objective' : 'binary',\r\n", "    'metric' : 'auc',\r\n", "    'learning_rate': 0.1,\r\n", "    'num_leaves' : 30,\r\n", "    'max_depth' : 5\r\n", "}\r\n", "\r\n", "lgb_Y_X_params = {\r\n", "    'metric' : 'rmse',\r\n", "    'learning_rate': 0.1,\r\n", "    'num_leaves' : 30,\r\n", "    'max_depth' : 5\r\n", "}\r\n", "model_T_XZ = lgb.LGBMClassifier(**lgb_T_XZ_params)\r\n", "model_Y_X = lgb.LGBMRegressor(**lgb_Y_X_params)\r\n", "flexible_model_effect = lgb.LGBMRegressor(**lgb_Y_X_params)"], "outputs": [], "metadata": {"slideshow": {"slide_type": "fragment"}}}, {"cell_type": "code", "execution_count": 7, "source": ["# Train EconML model\r\n", "model = LinearIntentToTreatDRIV(\r\n", "    model_y_xw = model_Y_X,\r\n", "    model_t_xwz = model_T_XZ,\r\n", "    flexible_model_effect = flexible_model_effect,\r\n", "    featurizer = PolynomialFeatures(degree=1, include_bias=False)\r\n", ")\r\n", "model.fit(Y, T, Z=Z, X=X_data, inference=\"statsmodels\")"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<econml.iv.dr._dr.LinearIntentToTreatDRIV at 0x260fd1a6fc8>"]}, "metadata": {}, "execution_count": 7}], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 8, "source": ["# Compare learned coefficients with true model coefficients\r\n", "coef_indices = np.arange(model.coef_.shape[0])\r\n", "# Calculate error bars\r\n", "coef_error = np.asarray(model.coef__interval()) # 95% confidence interval for coefficients\r\n", "coef_error[0, :] = model.coef_ - coef_error[0, :]\r\n", "coef_error[1, :] = coef_error[1, :] - model.coef_"], "outputs": [], "metadata": {"slideshow": {"slide_type": "skip"}}}, {"cell_type": "code", "execution_count": 9, "source": ["plt.errorbar(coef_indices, model.coef_, coef_error, fmt=\"o\", label=\"Learned coefficients\\nand 95% confidence interval\")\r\n", "plt.scatter(coef_indices, true_coefs, color='C1', label=\"True coefficients\")\r\n", "plt.xticks(coef_indices, X_data.columns, rotation='vertical')\r\n", "plt.legend()\r\n", "plt.show()"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {"slideshow": {"slide_type": "fragment"}}}, {"cell_type": "markdown", "source": ["We notice that the coefficients estimates are pretty close to the true coefficients for the linear treatment effect function. \n", "\n", "We can also use the `model.summary` function to get point estimates, p-values and confidence intervals. From the table below, we notice that only the **days_visited_free_pre**, **days_visited_hs_pre** and **os_type_osx** features are statistically significant (the confidence interval doesn't contain $0$, p-value < 0.05) for the treatment effect. "], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 10, "source": ["model.summary()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                            Coefficient Results                             \n", "============================================================================\n", "                      point_estimate stderr  zstat  pvalue ci_lower ci_upper\n", "----------------------------------------------------------------------------\n", "days_visited_exp_pre             0.0  0.007   0.055  0.956   -0.011    0.012\n", "days_visited_free_pre          0.284  0.007  38.206    0.0    0.272    0.296\n", "days_visited_fs_pre           -0.011  0.007  -1.582  0.114   -0.022      0.0\n", "days_visited_hs_pre            -0.19  0.007 -27.999    0.0   -0.201   -0.178\n", "days_visited_rs_pre            0.001  0.007   0.092  0.927    -0.01    0.012\n", "days_visited_vrs_pre             0.0  0.007   0.028  0.978   -0.011    0.011\n", "locale_en_US                  -0.013  0.113  -0.111  0.911   -0.199    0.174\n", "revenue_pre                     -0.0    0.0  -1.231  0.218     -0.0      0.0\n", "os_type_osx                    0.961  0.139   6.922    0.0    0.732    1.189\n", "os_type_windows                0.041  0.138   0.298  0.766   -0.186    0.269\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept          0.537   0.27 1.989  0.047    0.093    0.981\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""], "text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "            <td></td>            <th>point_estimate</th> <th>stderr</th>  <th>zstat</th>  <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>days_visited_exp_pre</th>        <td>0.0</td>       <td>0.007</td>  <td>0.055</td>   <td>0.956</td>  <td>-0.011</td>    <td>0.012</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_free_pre</th>      <td>0.284</td>      <td>0.007</td> <td>38.206</td>    <td>0.0</td>    <td>0.272</td>    <td>0.296</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_fs_pre</th>       <td>-0.011</td>      <td>0.007</td> <td>-1.582</td>   <td>0.114</td>  <td>-0.022</td>     <td>0.0</td>  \n", "</tr>\n", "<tr>\n", "  <th>days_visited_hs_pre</th>        <td>-0.19</td>      <td>0.007</td> <td>-27.999</td>   <td>0.0</td>   <td>-0.201</td>   <td>-0.178</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_rs_pre</th>        <td>0.001</td>      <td>0.007</td>  <td>0.092</td>   <td>0.927</td>   <td>-0.01</td>    <td>0.012</td> \n", "</tr>\n", "<tr>\n", "  <th>days_visited_vrs_pre</th>        <td>0.0</td>       <td>0.007</td>  <td>0.028</td>   <td>0.978</td>  <td>-0.011</td>    <td>0.011</td> \n", "</tr>\n", "<tr>\n", "  <th>locale_en_US</th>              <td>-0.013</td>      <td>0.113</td> <td>-0.111</td>   <td>0.911</td>  <td>-0.199</td>    <td>0.174</td> \n", "</tr>\n", "<tr>\n", "  <th>revenue_pre</th>                <td>-0.0</td>        <td>0.0</td>  <td>-1.231</td>   <td>0.218</td>   <td>-0.0</td>      <td>0.0</td>  \n", "</tr>\n", "<tr>\n", "  <th>os_type_osx</th>                <td>0.961</td>      <td>0.139</td>  <td>6.922</td>    <td>0.0</td>    <td>0.732</td>    <td>1.189</td> \n", "</tr>\n", "<tr>\n", "  <th>os_type_windows</th>            <td>0.041</td>      <td>0.138</td>  <td>0.298</td>   <td>0.766</td>  <td>-0.186</td>    <td>0.269</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>0.537</td>      <td>0.27</td>  <td>1.989</td>  <td>0.047</td>   <td>0.093</td>    <td>0.981</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"]}, "metadata": {}, "execution_count": 10}], "metadata": {"slideshow": {"slide_type": "fragment"}}}, {"cell_type": "code", "execution_count": 11, "source": ["test_customers = X_data.iloc[:1000]\r\n", "true_customer_TE = TE_fn(test_customers)\r\n", "model_customer_TE = model.effect(test_customers)"], "outputs": [], "metadata": {"slideshow": {"slide_type": "skip"}}}, {"cell_type": "code", "execution_count": 12, "source": ["# How close are the predicted treatment effect to the true treatment effects for 1000 users?\n", "plt.scatter(true_customer_TE, model.effect(test_customers), label=\"Predicted vs True treatment effect\")\n", "plt.xlabel(\"True treatment effect\")\n", "plt.ylabel(\"Predicted treatment effect\")\n", "plt.legend()\n", "plt.show()"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {"slideshow": {"slide_type": "skip"}}}, {"cell_type": "markdown", "source": ["# Understand Treatment Effects with EconML\n", "\n", "EconML includes interpretability tools to better understand treatment effects. Treatment effects can be complex, but oftentimes we are interested in simple rules that can differentiate between users who respond positively, users who remain neutral and users who respond negatively to the proposed changes.\n", "\n", "The EconML `SingleTreeCateInterpreter` provides interperetability by training a single decision tree on the treatment effects outputted by the any of the EconML estimators. In the figure below we can see in dark red users who respond negatively to the membership program and in dark green users who respond positively."], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 13, "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\n", "intrp.interpret(model, test_customers)\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=X_data.columns, fontsize=12)"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1800x360 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {"slideshow": {"slide_type": "fragment"}}}, {"cell_type": "markdown", "source": ["# Make Policy Decisions with EconML\n", "\n", "Interventions usually have a cost: incetivizing a user to become a member can be costly (e.g by offering a discount). Thus, we would like to know what customers to target to maximize the profit from their increased engagement. This is the **treatment policy**. \n", "\n", "The EconML library includes policy interpretability tools such as `SingleTreePolicyInterpreter` that take in a treatment cost and the treatment effects to learn simple rules about which customers to target profitably. "], "metadata": {"slideshow": {"slide_type": "slide"}}}, {"cell_type": "code", "execution_count": 14, "source": ["intrp = SingleTreePolicyInterpreter(risk_level=0.05, max_depth=2, min_samples_leaf=10)\n", "intrp.interpret(model, test_customers, sample_treatment_costs=0.2)\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=X_data.columns, fontsize=12)"], "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1800x360 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {"slideshow": {"slide_type": "fragment"}}}, {"cell_type": "markdown", "source": ["# Conclusions\n", "\n", "In this notebook, we have demonstrated the power of using EconML to:\n", "\n", "* Get valid causal insights in seemingly impossible scenarios\n", "* Intepret the resulting individual-level treatment effects\n", "* Build policies around the learned effects\n", "\n", "To learn more about what EconML can do for you, visit our [website](https://aka.ms/econml), our [GitHub page](https://github.com/py-why/EconML) or our [documentation](https://econml.azurewebsites.net/). "], "metadata": {"slideshow": {"slide_type": "slide"}}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}