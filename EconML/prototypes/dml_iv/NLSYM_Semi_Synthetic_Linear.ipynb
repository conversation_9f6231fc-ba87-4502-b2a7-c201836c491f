{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression, Lasso, LogisticRegression\n", "from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.preprocessing import PolynomialFeatures, StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "import scipy.special"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# NLSYM DATA"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\preprocessing\\data.py:645: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.partial_fit(X, y)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\base.py:464: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.fit(X, **fit_params).transform(X)\n"]}], "source": ["# Preprocess data\n", "df = pd.read_csv(\"data/card.csv\")\n", "data_filter = df['educ'].values >= 6\n", "T = df['educ'].values[data_filter]\n", "Z = df['nearc4'].values[data_filter]\n", "y = df['lwage'].values[data_filter]\n", "\n", "# Impute missing values with mean, add dummy columns\n", "# I excluded the columns 'weights' as we don't know what it is\n", "X_df = df[['exper', 'expersq']].copy()\n", "X_df['fatheduc'] = df['fatheduc'].fillna(value=df['fatheduc'].mean())\n", "X_df['fatheduc_nan'] = df['fatheduc'].isnull()*1\n", "X_df['motheduc'] = df['motheduc'].fillna(value=df['motheduc'].mean())\n", "X_df['motheduc_nan'] = df['motheduc'].isnull()*1\n", "X_df[['momdad14', 'sinmom14', 'reg661', 'reg662',\n", "        'reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']] = df[['momdad14', 'sinmom14', \n", "        'reg661', 'reg662','reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']]\n", "X_df[['black', 'smsa', 'south', 'smsa66']] = df[['black', 'smsa', 'south', 'smsa66']]\n", "columns_to_scale = ['fatheduc', 'motheduc', 'exper', 'expersq']\n", "scaler = StandardScaler()\n", "X_raw = X_df.values[data_filter]\n", "X_df[columns_to_scale] = scaler.fit_transform(X_df[columns_to_scale])\n", "X = X_df.values[data_filter]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['exper', 'expersq', 'fatheduc', 'fatheduc_nan', 'motheduc',\n", "       'motheduc_nan', 'momdad14', 'sinmom14', 'reg661', 'reg662', 'reg663',\n", "       'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66',\n", "       'black', 'smsa', 'south', 'smsa66'],\n", "      dtype='object')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X_df.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Semi-Synthetic Data with Binary Instrument and Continuous Treatment\n", "\n", "Data generating process uning real covariates $X$ and instrument $Z$, but synthetic $y$ and $T$ based on the \"intent to treat\" instrument setting with non-compliance. The instrument corresponds to a fully randomized recommendation of treatment. Then each sample complies with the recommendation to some degree. This probability also depends on an unobserved confounder that has a direct effect on the outcome. Moreover, compliance also depends on the observed feature $X$.\n", "\n", "\\begin{align}\n", "X \\tag{ real features}\\\\\n", "Z \\tag{real instrument}\\\\\n", "\\nu \\sim \\; & \\text{U}[0, 1] \\tag{unobserved confounder}\\\\\n", "C = \\; & c\\cdot X[i], \\; c \\;(const)\\sim \\text{U}[.2, .3] \\tag{compliance degree}\\\\\n", "T = \\; & C\\cdot Z + g(X) + \\nu  \\tag{treatment}\\\\\n", "y \\sim \\; & \\text{Normal}(\\mu=\\theta(X) \\cdot (T + \\nu) + f(X),\\; \\sigma=.1) \\tag{outcome}\n", "\\end{align}\n", "\n", "Moreover:\n", "\\begin{align}\n", "\\theta(X) = \\; & \\alpha + \\beta \\cdot X[i] \\tag{CATE}\\\\\n", "f(X) = \\; & X[i] \\tag{Nuissance function}\\\\\n", "g(X) = \\; & X[i] \\tag{Nuissance function}\n", "\\end{align}\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAloAAACcCAYAAACndQVqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nmZmVxImWmZmZWUmcaJmZmZmV5P8D94MIeaiUGzMAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 720x144 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def dgp_bin_Z_cont_T(X, Z, hetero_col, true_fn, random_seed=None):\n", "    np.random.seed(random_seed)\n", "    n, d = X.shape\n", "    nu = np.random.uniform(-1, 1, size=(n,))\n", "    c = np.random.uniform(0.2, 0.3)\n", "    C = c * X[:, hetero_col] # Compliers when recomended\n", "    T = C * Z + X[:, hetero_col] + nu # Treatment with compliance\n", "    y = true_fn(X) * (T + nu)  + 0.05*X[:, hetero_col] + np.random.normal(0, .1, size=(n,))\n", "    return y, T\n", "\n", "hetero_col = 4 # Mother's education\n", "hetero_col_2 = 7\n", "true_fn = lambda X: 0.1 + 0.05*X[:, hetero_col] - 0.1*X[:, hetero_col_2]\n", "\n", "np.random.seed(1237)\n", "y, T = dgp_bin_Z_cont_T(X_raw, Z, hetero_col, true_fn)\n", "\n", "plt.figure(figsize=(10, 2))\n", "plt.subplot(1, 2, 1)\n", "plt.hist(T[Z==0])\n", "plt.title(\"T[Z=0]: Total: {}, Mean(T): {:.2f}\".format(T[Z==0].shape[0], np.mean(T[Z==0])))\n", "plt.subplot(1, 2, 2)\n", "plt.hist(T[Z==1])\n", "plt.title(\"T[Z=1]: Total: {}, Mean(T): {:.2f}\".format(T[Z==1].shape[0], np.mean(T[Z==1])))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSIS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some hyperparameters"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.006686726847208292\n"]}], "source": ["random_seed = 12345 # random seed for each experiment\n", "N_SPLITS = 10 # number of splits for cross-fitting\n", "COV_CLIP = 20/X.shape[0] # covariance clipping in driv\n", "print(COV_CLIP)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some generic non-parametric regressors and classifiers"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from utilities import RegWrapper\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from xgboost import XGBClassifier, XGBRegressor\n", "from xgb_utilities import XGBWrapper\n", "\n", "# Linear models for regression and classification \n", "model = lambda: Pipeline([('poly', PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)),\n", "                          ('lasso', LassoCV(cv=3, n_jobs=-1))])\n", "model_clf = lambda: RegWrapper(Pipeline([('poly', PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)),\n", "                                    ('logistic', LogisticRegressionCV(cv=3, solver='liblinear',\n", "                                                                      scoring='neg_log_loss', n_jobs=-1))]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Some utility functions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["def nuisance_diagnostic(cate, nuisance_model, property_name, property_fn, \n", "                        index_names=None, statistic=np.std, threshold=None):\n", "    std = statistic([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]], axis=0)\n", "    if hasattr(std, '__len__'):\n", "        if threshold is None:\n", "            coefs = np.argmax(std).flatten()\n", "        else:\n", "            coefs = np.argwhere(std >= threshold).flatten()\n", "        if index_names is None:\n", "            index_names = np.arange(std.shape[0])\n", "        for high_var in coefs:\n", "            plt.title(\"{}: {}[{}] Across Folds\".format(nuisance_model, property_name, index_names[high_var]))\n", "            plt.plot([property_fn(ns)[high_var] for ns in cate.fitted_nuisances[nuisance_model]])\n", "            plt.xlabel('fold')\n", "            plt.ylabel('property')\n", "            plt.show()\n", "    else:\n", "        plt.title(\"{}: {} Across Folds\".format(nuisance_model, property_name))    \n", "        plt.plot([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]])\n", "        plt.xlabel('fold')\n", "        plt.ylabel('property')\n", "        plt.show()\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE via DMLATEIV"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\numba\\errors.py:105: UserWarning: Insufficiently recent colorama version found. Numba requires colorama >= 0.3.9\n", "  warnings.warn(msg)\n"]}], "source": ["from dml_ate_iv import DMLATEIV\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We need to specify models to be used for each of these residualizations\n", "model_Y_X = lambda: model() # model for E[Y | X]\n", "model_T_X = lambda: model() # model for E[T | X]. We use a regressor since T is continuous\n", "model_Z_X = lambda: model_clf() # model for E[Z | X]. We use a classifier since Z is binary\n", "\n", "dmlate = DMLATEIV(model_Y_X(), model_T_X(), model_Z_X(),\n", "                  n_splits=N_SPLITS, # n_splits determines the number of splits to be used for cross-fitting.\n", "                  binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                  binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "                 )"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<dml_ate_iv.DMLATEIV at 0x22503e76358>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# We fit DMLATEIV with these models\n", "dmlate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["# We call effect() to get the ATE\n", "ta_effect = dmlate.effect()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.651\n", "True ATE: 0.609\n", "CATE MSE: 0.03\n"]}], "source": ["# Comparison with true ATE\n", "print(\"ATE Estimate: {:.3f}\".format(ta_effect))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))\n", "# CATE MSE\n", "print(\"CATE MSE: {:.2f}\".format(np.mean((true_fn(X_raw) - ta_effect)**2)))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate Interval: (0.607, 0.696)\n", "True ATE: 0.609\n"]}], "source": ["# We can call normal_effect_interval to get confidence intervals based\n", "# based on the asympotic normal approximation\n", "ta_effect = dmlate.normal_effect_interval(lower=2.5, upper=97.5)\n", "# Comparison with true ATE\n", "print(\"ATE Estimate Interval: ({:.3f}, {:.3f})\".format(ta_effect[0], ta_effect[1]))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 12 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done   8 tasks      | elapsed:  1.4min\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Coverage: 0.52\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:  8.4min finished\n"]}], "source": ["def get_dmlateiv_coverage(true_effect, iteration):\n", "    y, T = dgp_bin_Z_cont_T(X_raw, Z, hetero_col, true_fn, random_seed=iteration)\n", "    dmlate = DMLATEIV(model_Y_X(), model_T_X(), model_Z_X(),\n", "                  n_splits=N_SPLITS, # n_splits determines the number of splits to be used for cross-fitting.\n", "                  binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                  binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "                 )\n", "    dmlate.fit(y, T, X, Z)\n", "    left, right = dmlate.normal_effect_interval(lower=2.5, upper=97.5)\n", "    if true_effect >= left and true_effect <= right:\n", "        return 1\n", "    return 0\n", "\n", "from joblib import Parallel, delayed\n", "n_experiments=100\n", "true_ate = np.mean(true_fn(X_raw))\n", "if True:\n", "    contains_truth = np.array(Parallel(n_jobs=-1, verbose=3)(\n", "            delayed(get_dmlateiv_coverage)(true_ate, it) for it in range(n_experiments)))\n", "    print(\"Coverage: {}\".format(contains_truth.mean()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE and CATE via DMLIV"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from utilities import SelectiveLasso, SeparateModel\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from econml.utilities import hstack\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DMLIV we also need a model for E[T | X, Z]. To allow for heterogeneity in the compliance, i.e.\n", "# T = beta(X)*Z + gamma(X)\n", "# we train a separate model for Z=1 and Z=0. The model for Z=1 learns the\n", "# quantity beta(X) + gamma(X) and the model for Z=0 learns gamma(X).\n", "model_T_XZ = lambda: SeparateModel(model(), model())\n", "\n", "# We now specify the features to be used for heterogeneity. We will fit a CATE model of the form\n", "#      theta(X) = <theta, phi(X)>\n", "# for some set of features phi(X). The featurizer needs to support fit_transform, that takes\n", "# X and returns phi(X). We need to include a bias if we also want a constant term.\n", "dmliv_featurizer = lambda: PolynomialFeatures(degree=1, include_bias=True)\n", "\n", "# Then we need to specify a model to be used for fitting the parameters theta in the linear form.\n", "# This model will minimize the square loss:\n", "#        (Y - E[Y|X] - <theta, phi(X)> * (E[T|X,Z] - E[T|X]))**2\n", "#dmliv_model_effect = lambda: LinearRegression(fit_intercept=False)\n", "\n", "\n", "# Potentially with some regularization on theta. Here we use an ell_1 penalty on theta\n", "# If we also have a prior that there is no effect heterogeneity we can use a selective lasso\n", "# that does not penalize the constant term in the CATE model\n", "dmliv_model_effect = lambda: SelectiveLasso(np.arange(1, X.shape[1]+1), LassoCV(cv=5, fit_intercept=False))\n", "\n", "\n", "# We initialize DMLIV with all these models and call fit\n", "cate = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(), \n", "             dmliv_model_effect(), dmliv_featurizer(),\n", "             n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "             binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "             binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "            )"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<dml_iv.DMLIV at 0x225047062e8>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["cate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYEAAAD8CAYAAACRkhiPAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# To get the CATE at every X we call effect(X)\n", "dml_effect = cate.effect(X)\n", "plt.hist(dml_effect, label='est')\n", "plt.hist(true_fn(X_raw), alpha=.2, label='true')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.62317714  0.          0.          0.         -0.          0.10377389\n", " -0.          0.         -0.         -0.          0.          0.\n", " -0.         -0.         -0.         -0.          0.          0.\n", " -0.          0.          0.         -0.          0.        ]\n"]}], "source": ["# To get the parameter theta we call coef_. The first entry is the intercept of the CATE model\n", "print(cate.coef_)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.624\n", "True ATE: 0.609\n"]}], "source": ["# We can average the CATE to get an ATE\n", "print(\"ATE Estimate: {:.3f}\".format(np.mean(dml_effect)))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXcAAAEICAYAAACktLTqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nIjGk5C4iEkNK7iIiMaTkLiISQ/8fglZ3ZDEcpacAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also see how it compares to the true CATE at each target point and calculate MSE\n", "plt.title(\"DMLIV CATE as Function of {}. MSE={:.3f}\".format(X_df.columns[4], np.mean((dml_effect-true_fn(X_raw))**2)))\n", "plt.scatter(X[:, 4], dml_effect, label='est')\n", "plt.scatter(X[:, 4], true_fn(X_raw), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE and Projected CATE via DRIV"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper, StatsModelLinearRegression, ConstantModel\n", "from sklearn.dummy import DummyRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DRIV we need a model for predicting E[T*Z | X]. We use a classifier\n", "model_TZ_X = lambda: model()\n", "\n", "# We also need a model for the final regression that will fit the function theta(X)\n", "# If we want to fit an ATE, we simply fit a constant functin theta(X) = theta\n", "# We can do this with a pipeline where the preprocessing step only creates a bias column\n", "# and the regression step fits a linear regression with no intercept.\n", "# To get normal confidence intervals easily we can use a statsmodels linear regression\n", "# wrapped in an sklearn interface\n", "const_driv_model_effect = lambda: ConstantModel()\n", "\n", "# As in OrthoDMLIV we need a perliminary estimator of the CATE.\n", "# We use a DMLIV estimator with no cross-fitting (n_splits=1)\n", "dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=False)\n", "\n", "const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                        dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                        model_TZ_X(), # model for E[T * Z | X]\n", "                        const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                        cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                        n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                        binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                        binary_treatment=False, # a flag whether to stratify cross-fitting by treatment\n", "                        opt_reweighted=False # whether to optimally re-weight samples. Valid only for flexible final model\n", "                       )"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n"]}, {"data": {"text/plain": ["<dr_iv.DRIV at 0x225048e3668>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["const_dr_cate.fit(y, T, X, Z, store_final=True)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\statsmodels\\regression\\linear_model.py:1554: RuntimeWarning: invalid value encountered in double_scalars\n", "  return self.ess/self.df_model\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py:877: RuntimeWarning: invalid value encountered in greater\n", "  return (self.a < x) & (x < self.b)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py:877: RuntimeWarning: invalid value encountered in less\n", "  return (self.a < x) & (x < self.b)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py:1831: RuntimeWarning: invalid value encountered in less_equal\n", "  cond2 = cond0 & (x <= self.a)\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>     nan</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>   nan</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>15:27:35</td>     <th>  Log-Likelihood:    </th> <td> -7831.2</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.566e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2990</td>      <th>  BIC:               </th> <td>1.567e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     0</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "    <td></td>       <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th> <td>    0.5458</td> <td>    0.061</td> <td>    8.995</td> <td> 0.000</td> <td>    0.427</td> <td>    0.665</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>4190.040</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   1.964</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>8408463.090</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td>-7.266</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>262.343</td> <th>  Cond. No.          </th>  <td>    1.00</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.000\n", "Model:                            OLS   Adj. R-squared:                  0.000\n", "Method:                 Least Squares   F-statistic:                       nan\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):                nan\n", "Time:                        15:27:35   Log-Likelihood:                -7831.2\n", "No. Observations:                2991   AIC:                         1.566e+04\n", "Df Residuals:                    2990   BIC:                         1.567e+04\n", "Df Model:                           0                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.5458      0.061      8.995      0.000       0.427       0.665\n", "==============================================================================\n", "Omnibus:                     4190.040   <PERSON><PERSON><PERSON>-Watson:                   1.964\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          8408463.090\n", "Skew:                          -7.266   Prob(JB):                         0.00\n", "Kurtosis:                     262.343   Cond. No.                         1.00\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is the pipeline, we then look\n", "# at the reg step of the pipeline which is the statsmodel wrapper and then we look\n", "# at the model attribute of the statsmodel wrapper and print the summary()\n", "const_dr_cate.effect_model.summary()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_driv_coverage(true_effect, iteration):\n", "    y, T = dgp_bin_Z_cont_T(X_raw, Z, hetero_col, true_fn, random_seed=iteration)\n", "    dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=True)\n", "    const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                            dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                            model_TZ_X(), # model for E[T * Z | X]\n", "                            const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                            cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                            n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                            binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                            binary_treatment=False # a flag whether to stratify cross-fitting by treatment\n", "                           )\n", "    const_dr_cate.fit(y, T, X, Z, store_final=True)\n", "    left, right = const_dr_cate.effect_model.est.model.conf_int(alpha=0.05)[0]\n", "    if true_effect >= left and true_effect <= right:\n", "        return 1\n", "    return 0"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 12 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done   8 tasks      | elapsed:  1.3min\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Coverage: 0.98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed: 11.3min finished\n"]}], "source": ["from joblib import Parallel, delayed\n", "n_experiments=100\n", "true_ate = np.mean(true_fn(X_raw))\n", "if True:\n", "    contains_truth = np.array(Parallel(n_jobs=-1, verbose=3)(\n", "            delayed(get_driv_coverage)(true_ate, it) for it in range(n_experiments)))\n", "    print(\"Coverage: {}\".format(contains_truth.mean()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Projecting CATE to a pre-chosen subset of variables in final model"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4 7]\n"]}], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We could also fit a projection on a subset of the features by using the\n", "# subset wrapper from our utilities.\n", "\n", "# Example: including everything for expository purposes, but any array_like of indices would work\n", "subset_names = set(['motheduc', 'sinmom14'])\n", "# list of indices of features X to use in the final model\n", "feature_inds = np.argwhere([(x in subset_names) for x in X_df.columns.values]).flatten() #[0] #np.arange(X.shape[1]) \n", "print(feature_inds)\n", "# Because we are projecting to a low dimensional model space, we can\n", "# do valid inference and we can use statsmodel linear regression to get all\n", "# the hypothesis testing capability\n", "proj_driv_model_effect = lambda: SubsetWrapper(StatsModelLinearRegression(),\n", "                                          feature_inds # list of indices of features X to use in the final model\n", "                                         )"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['exper', 'expersq', 'fatheduc', 'fatheduc_nan', 'motheduc',\n", "       'motheduc_nan', 'momdad14', 'sinmom14', 'reg661', 'reg662', 'reg663',\n", "       'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66',\n", "       'black', 'smsa', 'south', 'smsa66'],\n", "      dtype='object')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["X_df.columns"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"collapsed": true}, "outputs": [], "source": ["proj_dr_cate = const_dr_cate.refit_final(proj_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"collapsed": true}, "outputs": [], "source": ["# To get the CATE at every X we call effect(X[:, feature_inds])\n", "proj_dr_effect = proj_dr_cate.effect(X[:, feature_inds])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.002</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.001</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>   2.637</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>0.0717</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>15:38:55</td>     <th>  Log-Likelihood:    </th> <td> -7828.6</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.566e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2988</td>      <th>  BIC:               </th> <td>1.568e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     2</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>        <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>    <td>    0.5561</td> <td>    0.064</td> <td>    8.684</td> <td> 0.000</td> <td>    0.431</td> <td>    0.682</td>\n", "</tr>\n", "<tr>\n", "  <th>motheduc</th> <td>    0.1310</td> <td>    0.062</td> <td>    2.121</td> <td> 0.034</td> <td>    0.010</td> <td>    0.252</td>\n", "</tr>\n", "<tr>\n", "  <th>sinmom14</th> <td>   -0.1149</td> <td>    0.204</td> <td>   -0.563</td> <td> 0.573</td> <td>   -0.515</td> <td>    0.285</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>4157.081</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   1.966</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>8324885.444</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td>-7.146</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>261.061</td> <th>  Cond. No.          </th>  <td>    3.41</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.002\n", "Model:                            OLS   Adj. R-squared:                  0.001\n", "Method:                 Least Squares   F-statistic:                     2.637\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):             0.0717\n", "Time:                        15:38:55   Log-Likelihood:                -7828.6\n", "No. Observations:                2991   AIC:                         1.566e+04\n", "Df Residuals:                    2988   BIC:                         1.568e+04\n", "Df Model:                           2                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.5561      0.064      8.684      0.000       0.431       0.682\n", "motheduc       0.1310      0.062      2.121      0.034       0.010       0.252\n", "sinmom14      -0.1149      0.204     -0.563      0.573      -0.515       0.285\n", "==============================================================================\n", "Omnibus:                     4157.081   <PERSON><PERSON><PERSON>-Watson:                   1.966\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          8324885.444\n", "Skew:                          -7.146   Prob(JB):                         0.00\n", "Kurtosis:                     261.061   Cond. No.                         3.41\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is\n", "# an instance of SubsetWrapper, we look at the model of the SubsetWrapper which is \n", "# and instance of the pipeline, we then look at the reg step of the pipeline which is the statsmodel wrapper and\n", "# call summary() of the wrapper (most prob there is a better API for this, but we can go with this for now :)\n", "proj_dr_cate.effect_model.summary(alpha=.05, xname=['const']+list(X_df.columns[feature_inds]))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimated Params: 0.5560648374345198, [ 0.13103506 -0.11489838]\n", "True Params: [ 0.61740685  0.14934234 -0.1       ]\n"]}], "source": ["# We can access the coefficient by looking at the coefficient attribute of the final step of the pipeline\n", "print(\"Estimated Params: {}, {}\".format(proj_dr_cate.intercept_, proj_dr_cate.coef_))\n", "# True coefficients of projection\n", "print(\"True Params: {}\".format(\n", "        LinearRegression(fit_intercept=False).fit(PolynomialFeatures(degree=1,\n", "                                                                     include_bias=True).fit_transform(X[:, feature_inds]),\n", "                                                  true_fn(X_raw)).coef_))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coverage of True Projection: 1.00\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfYAAAFpCAYAAABu2woqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nQECUOYMZ+z5A06+bovq86hj7+**************************/Hnx9qdmUtzew7uQ6jNkyBm2r\ntUWt0rVU+e/09fTFwm4LkZGVgX6r+6HZkmbYeHojJDTMts7r/Hnq1ta0KdUMz5pFjVvyLl/rj9jc\nulX5Ofzvf9QQxlIHDtAyT35SUihD0ZqSJv0S/Y4dpn/+7bfqNZGZNavwOnprtG1Lt458lXrlCv33\n60+g27OHrlRt7UxoSq9etHTnDISguva8iZ52psRlSXMA56SU56WU6QDCAfQyfoCU8i8pZULO3b8B\nOE4lP8slOZmS4L7+mlY4d++m2NG5M+WGDB2WhXtl/kCapKL0jac34v3d78PbwxufdPoEZ18/i8Mj\nDz+UdX74lSOY3GYqro6LN/m6N25QVzl3d7pSL14ciLwciWcinkGLyi2wqt8q6NzUyRLWuekwqP4g\nHB99HMt7L0fKgxT0DO+Jc3fOQUAg9IdQFJ+lwKlm1tCfXJOeTkvSU6fSm+fEifZ/4//rL+oSZKmx\nY+lDSX6SkujK2pr9yAoVgPr1talnv39fnTr2+vXp9hHl+yfYVMduLDKSViz0PQlu3qQPOfojZ5W0\nYQOd9e5MdDp6U9OIEkvxlQBcNrp/BUBB61MvAvjN6L4EsFUIIQF8LaXMezXPVJaZSdu1R45QIjWQ\nOwnOt2QadpzfgbE712HD6Q24ee8mVvZdiWcaPIPhTYZjSMMhqOBbocDXuHXvFs7dScTd9PIA/HL9\nLDGRmnhlZ9OVepkywIWEC+jxYw9ULl4ZGwZtgLeH+pndOjcdnmv0HAY3GIxt/27DwJ8H/nfVnpKe\nguKziiP5XRu67Fji0iXgww/pajY8nFq+Xr3qGBnuloiNpSAwa5Z6rxEaSolcaWmGk9HsZfFi23IP\nTNEfYOBodezGevemA3C++orK4FhuM2ao8yHHTEoEdlPpqCbXMIUQ7UGBvbXRt1tJKeOEEOUAbBNC\nnJJS7jXx3BEARgBA1aqms5iZ+bKzDVuTZ87QWebe3sCMvXRSGjpMg+wkce3uNZSZUxOpGanw8/RD\nt9rd0CeoD7rUpKMfSz9iXqnR4WuHER4TiZeTn0ZtGPbJ796lFd779+lKvSJVp6GiX0UMCB6AcS3H\noaxPIZnBDVYBbumAmXvshXF3c0fXWl2Rkp67i1be+6qIjwc++ghYkvP5dtQo+p/l5mZeUNdfxVty\nApqaVud0Bhw40Pox9OUV+f039etH+/T379s/sKtBX76nYeeyQulPApw82XCAEDMIzrsbbV9K/M25\nAsD4X1xlAHF5HySEaAjgWwBdpZS39d+XUsbl3N4QQqwDLe0/FNhzruSXAEBISIgDbX46l7g4QxJc\nSgq9d9SuTUlwweECgGE5VF+W9ubjb6JV1VZoH9gexdyV61qWlkZBPTGR2kFXq0YHuDzIeoDSj5TG\nou7mXQl1bF4ZJbxKKDYvPT9Pv1zB3M/Tr4BHK2DzZsqyTk+npjJTplgeoPV7e0qfoW6t8HBKMKte\n3foxwsKopK92bdM/b92a/rDCBQQY9vBt9fLLwAcfUOlbu3bKjMkUoURg/wdALSFEdQBXAQwC8Izx\nA4QQVQFEAHhOSnnG6Ps+ANyklCk5Xz8F4AMF5sSMJCZSMD92jLrCCUFBtEMHoFjF09h0fh3m/flL\nvhkXH3b4UPE5ZWbSVt+NGxTL6tQBMrMzMXjtYJxPOI+DIw6a/SFi+1B19leT301G8VnFkZKeAj9P\nP3WW4RMT6bjLOnWo3GvwYGpLWbPg0r18BQRQs3tTGeK28va2rKNWfDzt8RR2pKW+Bj2/nAFf3/xL\n4fQePKC/4Hn/uwcNUrfsSI1jSvX5AsnJytexFyumXK15uXKU1JaWBpQoQbXbrrBi4gJsDuxSykwh\nxGsAfgeVu30vpTwuhHgl5+eLQWuk/gAWCvpHrC9rKw9gXc733AGsklJusXVOjFq3HjlC73WXczIg\nAgKoHLhxY+By2nH0X9Mfp27RMlpIRfsVJGRnU/b75ctAz55U2iylxBu/vYGNZzZiftf5Fq0MrDq2\nCl7uXuhbt6/ic1VtTz01lVr0ffIJ1Qvu309Lzt99Z9u416/TldTixbmbCihh7lzLHh8QQPNxKyRH\nt1w5Sspr1Mj0l/ddNgAAIABJREFUzzdvphr9RYuo25wps2fT1eOtW7nrtIOD1VsW9fJSZ3/59Gm6\nfWD68CKbxMYq26nvm28MH8yio5Ub19jbbzvWSXdOQJFNHCnlZgCb83xvsdHXLwF4ycTzzgPI518z\ns1RmJi2xR0dTdVRWFl1gPd4yA6ml/8CeWz9D590QbYqPRDWvaqhWohpee+w19KzTE1VK0JKv2GlY\nMLG5iYoJMhtYs4aS9Dp1MrQNn/PnHCyKWoTxLcfj1eavWjTmkIghNLYK81VcRgaVYI0eTfe7d6eA\npFT70JQUSr5ylDp2c5qheHgATz+d/8/1/y1Hj+b/mA4dqCPa7t25+5ifPUvJJF26mDFZC02ZUvhK\ngqMJDDRk3StB//f21CnaKinsQ5w1PvlE+TFdnIN24WDm0ifB/fwzXbSsWUNXwo0aAVXa/Y7D1Z/F\nkCNlMXBLByyNXoqLSXResq+nL7Y8uwWvNn/1v6BuTOkg2bRCUzzT4Fmc3h+ImBjDwWMAsPbEWkzc\nMRGD6g/C7NDZBQ9UAPcPHDjZSEpaIq9f3xDUGzSgk7KU7IwWl5PesmuXcmPqLV5MqwHmOHqU/ieb\n01M8OZmWhy1dETDWogVd1eUte/vhB9qjV8OHHwLbtik/bqdOdOunck6HUrZvpy0JnU697nPMIg78\nTsgKEhdHV+YxMYYkuPJVkpFZPgqvdO4Ad3egy4ovEBUXhb51+6J3UG+EPhpaaNlYiWKlULWk8lUH\n/t7+eLSUP+KvUI+Vzp0NP2tZpSVefexVfPbUZxZ3fDMO5lkyC+4fuCNzqoOVCR09CowcSWeOBwdT\nXe6AAYbWoc7i0CHqeGeO8HD67y1fvvDHJifTSsann1LNuzU8PSkpzJ717GlptPSvNH2SoBr71bGx\n9GfZMuXGNE7UvH9fuXGZ1TiwOyExqgFwYgAAiVl9R+Ou/zb8k/kdIq/shrwsMaBtPMr7lsey3svg\n7+0Pdzft/zdfT43HiZt30PWJQPTqRftll5IuoaJfRQT4BWB+2Hyrxs171ro5Z6/bjb5MrVgx+iT2\n7bdU0+fuTkui+qYBrkZK2jPv2FH55K+CdOxI+/GXL9uv3O/7723Pi8jrdk7RkCPXsRvz9KRVBjVW\nL5hVeCneyYjpAniQU9r12EK8m1gOc68MQYq8hSltpuDQyEMo51MOAFDet7zFQT3pQQKOXT+i9LRx\nx+dPRGQPRfPOF+DmBsSlxKH1960xcuNIm8bVCV2B9zURFweMGGE4yaxOHVqifPFFQ21ynTq29+w2\nRb98q8apY+aKiqIkj4JOcrOE/qo/MLDgxw0aBPzxB3Wjc2b+/nTryHXsef3yC60C1Kun9UwY+Ird\nuXneBQD8+8a/eLSUcm/kxXTKt7L08ARQ6RDc3Og41LCVYUhIS8BrzV+zadzMqZlw/8AdWTILOqHT\ndhk+ORmYMwf4/HO62nr1Vcpg1OkefpPevp1KF5SmD2pPPKH82OYKD6eEOOMkNluEhVFWaOVCOlFX\nrGjocMRMCwxUp8eBtzcdSMQcAgd2F6BkUFdrj10vIysD/Vf3R8yNGGx6ZhOaBNieOOYQe+r79gF9\n+9Ke6+DB1FKyoKtmNYI6QIHt99/VWQ0oXZraiBamWTNg/Hjza971mdT5lWEVK0YByZz660OHKElx\nxgzK2B46VL1DYAB1auR/y+m4nZRk360M5jJ4Kd7JmHtSmrWSHyQjNV29Hsdjfx+Lbee34Zse36Bz\nzc6FP8GRZWcbDnoIDqajQf/5B1i1Srul8Lg4KqHbvVv5sWfPpv++wjzzDDBzpvnjlitHe+Pr15v+\n+aZNdEW4cGHhYx0+TC15T5yg+zVrGrLMleblBTz1lPLjnj9Pt+npyo8dG6tOxQRzKBzYndDYx+no\nzPNjYhUfWyIL5xOUT+oaspZqzSMvR+Kzpz7D8CbDFX8Nu9q1i0qsunalAO/vT/uM5nZ8a9bMsi5u\n5kpJoQxzrerY//rLcIiJudzd6feYX9tZfYelkycLHys0lG712fHHj1MtqBqmTFHvQ4NaoqOpWoG5\nNA7sTHU+M32Qnk1XHxnZGZiyc4rGM7LBsWO059uhA3VVe+MN68apVKnwZDBrXLtGt/v2KT/23Lm0\nzZCfzEzaVx81yrJxExNp2dySq/z8VKtGV+n6wP7TTwU3v7GFWnXs+rr74iocFdyoEechFAEc2FXk\nNcMLYrqA1wzn6Z9colgpNCivbDPAe5n3CrzvNLZtozfGyEhKkjtzhsrXrOm2tWGDei041XLiBLBn\nT/4/372btiYsPcntLiWBKnb8aWgozTMjQ5nx8pOWRkfpKk2/clFMuQOXWNHCgV0FFy8CHs8MwINt\n7wE7p+PB+RB4TlGuP7Ovpy9KeflD5+YApV1m8Hb3LvC+Q0tMBA4coK/btgWmT6fStfHjbWsg0rix\nOldkWgoPpwNbunbVdh6hoRQUL1xQ/7VWrlR+TH33QOkELZKZQ+KseIXcuAH8eSAV6/acxaEL55F5\ny6ie89+nkPFvJyxdShd89evbdsBS55qdgeadUVr5qjQkld2MY8WvAOil2Jipk1LhM9MH9zLvwdvd\nG6mT1EvOU9TNm1SXW6wYJTR5etK+qhKUPqBFr0ROj4P8jjhVS3o6EBEB9O4NPKLwX0z9UbTmnnjX\nqxdtCajRt9wehg2jXgDlymk9E+akOLDb4O5d4NDhbMQcc8Px87exOOprZPtdQImql4E6+4A/JtAD\nm30NxDfF9evAunXAr79Sf5KGDenWod5/am9BRT/l9+CcJpgbO3WKgvvKlVSXraSNG9W5ItMHA/3p\nOvYSGQkkJCjXlMZYt26UQKdv3FIYZ2rsYkrt2lSyyJiVnPxfgP2lpwO//XEFq3bEIPJYPMp4l8Ow\nJ7qhb9fS8Gkr0b3hc2hVpRV0bjqItnRSmmepW4ia8BHqlQX+jL6Oub/swv2YnoiJ8YaPD52f0KSJ\n+Rdxe2L3YPmRWIxM6wVfX2UzqwNLBqJNNRUaWDgzc/qdW0qtZdZKlShjX41zwitWpE+iprRtSy1y\nrWnlqsvZUsqvY5wQtFpiySfgdeuA996jW7UOgQGc73Q3ViRwYDdDdjYdkTz75y3YcuAcbiTfATxS\nEVj7Hnp1aYQxPQFAoCMm5XpenTJ1Ubt0LWyYMvW/750VmxChexG+utIYUmEmKsqhOHzYG1FRdEFS\nrx4F+TJl8p/PrXu3cCnpIjKylE8Oik2MRWxiLLae24r4t+MVH5+p7OpVoH17YMUKYMgQZcd+//2C\nf16jhnXjli0L7N2bf7nbpk20tP7FF+YfEuPrSysuFy6ot+fv5QW0a6fO2IzZgAN7PjKzM7E28gDW\n7DiHJu7PIu2+G07HZqB0lZt4qX0gXujQDjX883kjynHuzjmU9sp9HvULTV5ASMUQvL/7fXx9ahRK\ner2LN1tPQqjXOBw7JrB3L73HVaxIS/VNmlBvDnsI+DTgv6+vpV5DwKcBHNzV0rq1oRGJkpKS6Nae\nx2du2AAsX05Z7dZ0SnN3L7g7nD6Z7MwZ88ds3ZpyI+bMoRLA4Sr0TVi6NP8VDMY0xIHdyL2Me1j7\nz26s2HYcf0Ql4V6yJ9zcBEL6XUe/sABMrdcdHh49zB4vKzsDB+MPAgjN9f2G5RsiYmAEDsUfwvu7\n38fZlCOYGirQujUQdz0dx4964tgxYMsWqrCqXt2QdKfm9uG11GsF3i9ynnxSvSXz0qUNZV5K0nfC\nU6MJyezZ1IBmw4bc3//hB6qbL1XK9PMKk5BAv4+JE4FZs2yfJ0AJfK1aATt3UumbGoFdjXwCxhRQ\n5AO7zwwf3LsHeN58HC9VmouFu6JQTPcIGtV+FD0HVcfL3R5DuZK+OY8WFo+fnvUg3581DWiKDYM3\n/LekHn0tGl1WdME7rd7BK6+/glvx3jh8mBpunTtH76dBQUDSTR9r/lMLVcGnQq5gXsHHyU/JcmR5\ng6MzOH+esrWNpaRQNqjxyXWWSs1JrPzhB+UCO0DHuO7cyWVjrMhxpHxsu/OZ6YN7fz0P/DkB6Wfa\n4psDP2DWC91w8aex2L/4BUwa0t4oqKvHQ0cZ1+5u7mhYviHGbR2HGl/WwC/xX6JztzS88w61365T\nh7YNky5XQXnfAMXPWY9/O/6/YF7BpwIvw589C7z8snmtTC31xBPqJOXZ24YN1KjFEa9e1UyaY8yB\nFekr9nuZ94DUnDfXZl8jo0QcJg6bo/CrmH+VX79cfWx9biv2XtyLqbumYsyWMVgUtQgxo2JQt64O\ndetSVn5MTD0kJ9dDFRUOfirywdzYtWvAt99S0FI6y7xsWeD+fWXHBAzL4fY6Fzs8nI5TbdlSvdfQ\nH9caFGTZ8xo3pr4DM2YoPyfGHFiRDuze7t74r7lpiTjFO6Lp3DzQvKLl9cRtqrXB7ud3Y9eFXbiQ\neAE6Nx2klPj5xM/oHdQbTZsqXFPN7E+tpXh9OYUax4ma0qIFZYar2YwhLIw6AFqbRcpL8ayIKdKB\nPXVSKsRWqjVXoyNagG8A/IpZ3za0ffX2aI/2AIA9F/fg6Z+fRvWS1RGfEo+0rDSU9y6Pa+OLeIIb\ny03NOvZHH6VT6YxNnmz7uPo69vwaOWRm0jn3FSpY3iioc2fg9m3b5seYkynSe+zG1OiM5uHmAXeh\nTD/3ttXaYtMzm3Ap8RLSstIAANfvXc9VosbYf3Xs+tPNlDRxInXM04uMVObM8HLl6Bz1X34x/fPN\nm6mdrDWHxLRqBSxYYNv8GHMyRT6wP+KhToY5AFxKvoSEtARFxhJCIKxWGLKQlev7Rb4kDaAkt0uX\nlB9XpwP8/AxXlEoKDTW/97klEhPp9tQp5cc2duMGlQMqcdSqTkdbB/n1RtcfRatG3T9jLqjIB/Yx\nLd7A+JYTVBk7KzsD+68eUHTMvCVoRb4kTUrqrV2tGmVnK6llSyA5WZ3uYt7e1B1NaTdv0u3Bg8qP\nPX06lZABwNq1QFYW0L+/7ePeuUNtY609254xlkuRD+zubh4o5q7eucfZMlPR8bgkLY8dOwxfq9Hw\nRS3OeB771auG0r/wcCA4mLom2epeTgprRITpnz/3HN2++67tr8VYEVDkA/vq46sxY+8HWk/DIvFv\nx0NOkxzUAWD+fLpt27bgBvvWOHmSSt1iYpQdFwA6dKBkNGd05Qp1mhs0iK601ebnRysz1hwww1gR\nVOQD+5nbKu5FFkuCZ/BW9cYv6mJjDclce/YAFy8Co0YBD/Lv9meRW7eAn34Crl9XZjxjvr5Acesr\nJvKlP9q0USPlx9bTHzk7cKAy4+n31seMUWY8xoq4Il3upjZd63nw9vTDhO0T8HHox1pPx/X8/HPu\n+ydOAIsXU3Bz9KYkatWxly5Nt0oskedn5EiqX69dW5nxPD251pwxBRX5K3a1TNg+AVkyC4kPEjHn\nzzmYsF2dBL0ibdw44NgxCjKdO9PxnM8/T4eVqJE85gwqVQJ276ase6UFBwNt2lAzmqZNlR+fMaYI\nDuwqiTgRUeB9ZiMpaX83ODj39z//nHqwP/+8MjXWzubqVcriV6OOfexYGvv11ykjnjHmkBQJ7EKI\nLkKI00KIc0KIiSZ+LoQQX+b8/KgQoqm5z1Wbr6cK+5wA+gb3LfA+s4GU1ITlk0/o/v79wO+/09el\nSgFLllDC2+ef2/Y6np5AxYp0rrfSevRQZx/8zh26VSPhD6CtjoMH1antZ4wpwuY9diGEDsACAJ0A\nXAHwjxBig5TyhNHDugKolfOnBYBFAFqY+VxVjWmhTsKOfk894kQE+gb35T12Jf39NyXL5XeiWLdu\nwPffA31t/DDVogVdAatBCHUyyvXtU48cUX7sPn1o3HnzlB+bMaYYJZLnmgM4J6U8DwBCiHAAvQAY\nB+deAJZLKSWAv4UQJYUQAQACzXiuqoSK5Tofh37MAV0N8+dTRvmzz9L92rUNp5rpDR9Ot/rleE9P\n+83PHM54HvuePXSrRFMaxphqlFiKrwTgstH9KznfM+cx5jxXVSuO/uB0dexF2rVrwJo1FLj1ndtK\nlQJKlnz4sSkpQEgI8NFH1r1WTAxd/avRSKZbN3Uz19Wwfj0wdy5tTzDGHJYSgd3UJW/e2pX8HmPO\nc2kAIUYIIaKEEFE39W0zFRBbhg6WWHJwiWJjMhUtWQJkZACjRxu+Z7zHbszPj/axZ860LjgnJNAB\nJGqcDqbTAe4qVJuWLUu3ISHKj/3kk1xrzpgTUOKd5QoA45ZQlQHEmfkYTzOeCwCQUi4BsAQAQkJC\nFCl6XXJwCVA1EqgaiZG/0vdGNBuhxNAsr9RUqjN/zPLz6XPp04eW4c2toZ43jzLEn38e+Ocfy4/9\nVItaS/H6lYs6ddQZnzHm8JS4Yv8HQC0hRHUhhCeAQQDyvmttADA0Jzv+cQBJUsp4M5+rmrUn1hZ4\nnylkxw4gKAho3hy4cMG2sRo0oLIrY/o6dlNKlwa+/pqSvmbNsu21nYGadeyMMadgc2CXUmYCeA3A\n7wBOAlgtpTwuhHhFCPFKzsM2AzgP4ByAbwCMLui5ts7JXP2C+xV4n9koO5tu/f2B+/fp61Qbzr3/\n8EPg0CHLn9ezJzBkCPDrr0CmsofyOBw169gZY05BkU0+KeVmUPA2/t5io68lgFfNfa696Jfd155Y\ni37B/XgZXik3bgCTJlEQXbqUztpetAh4+mnrxzxyBJg6lY47zdv1bP/+wp+/aBHg5WXZvvYjj9CS\ntre3ZXM1R8+e6pwhf+sW3UZH04cZxliRU+R7xY9oNoIDulLS06kUbfp0OopzzBhDhzhbywoXLKBA\nqy9js5SfH90mJtLJZD16FP6ckBDglIqHBKkhIYFuj9tt4Ysx5mCKfGBnCjl8GBg8GDh9mnq2f/FF\n7gSuPn0o2FvTxS0hAVixgq5A9YecGGvc+OE69vxMnkyZ9VFRQMOGls9FKWolz1WvTrdNmqgzPmPM\n4XGveGYbfc/wChWornzTJioRy5uVrdPRFbebFX/lvv+e9uhfNbmbQx8WzG1A8/779CFg+HAqmyvI\n0aN0zrsaB8r07EkfSJRWty4t8Tv66XaMMdVwYGfWSUqi09WeeoqW2wMCqJwsLMz046OjKZs9Pt66\n1+vRI/9AmF8duyllytB++6FDwJw5BT82KQnYu5eW751JlSrqtKtljDkFDuzMMllZwHffUR35F1/Q\n0m9aGv2soGBy7hzVlFvT7GXcOGWXrvv2pT7z06fTsa9a2LBBnY52jLEij/fYmfkuXKA+4YcOAa1a\n0ZJ7s2bqvmZUFGXBF7SE36KF6ZayBfnqKxrT3L15xhhzEnzFzgqn30cvX572yVetosxytYP62bPU\nqU6N08TKlAFWrgQqV1Z+bMYY0xAHdpa/+/eBDz6gDOu0NKrn/uMPyn63xx7uwoXUAnbw4IIfZ8ke\ne15XrwLdu5s+v9zXl0re9KVySlIreY4xVuRxYGcPkxL4+WfKsJ42jdrB3r1r25hCUGa8ue7epeY2\n/ftTxr1aPD3pg8Hw4Q93pWvShBICmzdX7/UZY0xhHNhZbkeP0vL3gAFAiRLArl3A6tW0dG2Lfv0o\ncJp7VOnKlZSV/tprhT+2oF7xhSlbllYGoqKATz+1bgxrcPIcY0wlHNgZuX2b6sQbN6a67UWL6LZd\nO23ms3YtJc098YT6rzVgAK0MTJtGJ9DpHTpEczhwQPnX/OcfYNs25cdljBV5HNgZ1WrXrk2noOl0\nwNChwCuvKHteeFQU8OKLtKdtjk2bgIgI8/bybdlj11uwgPbSJ082fC81lTrqpaTYNrYpISF8Ahtj\nTBUc2Isy/Ulr9erRlXl0NO2rq3HoSWwsdZDT9zIvSHY2Jc1Vq6b8PPJTrhx9mFi61H6vyRhjKuDA\nXhRdugQMHAg8+SSVsvn709J3/fp0dXrvnnZzu3yZmt7s2GH+c2zZY887TokSdJiNtR3yGGNMYxzY\nixJ9+VpQECVv9eplqFHXS0qy7cx0W339NQX3GjW0eX0p6UNCnz4P/24YY8wJcOe5ouLMGerrfvEi\nJYt98onppe6EBLp618KDB3TyWo8eQGCg+c8z5zx2cwkBjBgBPPMMbR20a8fd6RhjToUDu6u7d4/2\nzAMDqVPc0qVA+/b2n4eHBy1zF9Qads0a4OZN80rc1DRoEJX4rV5NeQdBQdrOhzHGLCCklFrPwWIh\nISEyKipK62k4toQEOqL0l1+A48epi5o59FnoWvy9aNGCtgJOnLDseNfHH6de8Vu2KDeXa9eA4GAK\n6vv2WdZchzHGFCaEOCilDDHnsbzH7mqysoBvvqHytfnz6RjVvB3VCvPUU+rMrSBS0hnin31m3Znt\nSqtQgeYTGQmcPq31bBhjzGwO8A7KFHPnDrU/HTGCytb0jWYsOfksMFCdFq5//w08/TRl5JsiBNCp\nE9Ctm+VjK1HHbsro0ZQdX7eu8mMzxphKOLC7Av156KVKUclaeDiwZ491h4z4+alTx37lCu2hJyc/\n/LObN4Hx44G4OOVf11YVKtjnwBvGGFMIB3Zn9uABMGsWULUqlYgJASxbRjXq1gYjLerYv/2W+rQn\nJVn3fKXq2BljzAVwYHdGUgIbN1LHuPfeA1q1Um7suDjqG28vmZm0XdCxo/VL3vfvG1YtGGOsiONy\nN2e0eTOd5x0URHvLSia7padTa1V72bCBVhu+/NL6MY4eVW4+jDHm5DiwO6MbN4CAANpHL1dO69mY\n55FHgMqVHz5YZv582kro3l2beTHGmIvhwO6Mhg+nP86kWze6MjeWkQFUqUKd5mw5Sa5FC8sy/xlj\nzIVxYGe5eXvb7zhRDw9K9mOMMaYYTp5zRr/+SkvX1maRF6RcOXWufvftA7p2peNbASp7i45WZmy1\n6tgZY8wJcWB3RhcvUoJberryY6tVx379OrV8vXuX7v/vf0CTJsDJk7aP/cILwNtv2z4OY4y5AF6K\nZ7nZo449O5uS5h5/XJmubt99Z/sYjDHmIviKneUWGwtcvarua2zbBpw9q/0pbowx5oJsCuxCiNJC\niG1CiLM5tw8dXC2EqCKE2CWEOCmEOC6EGGP0s/eFEFeFENE5f8JsmQ9TyI4d6o4/fz7t5ffvr+7r\nMMZYEWTrFftEADuklLUA7Mi5n1cmgHFSyroAHgfwqhAi2OjnX0gpG+f82WzjfIqGkiXp9DZnOkrU\nz4+W3dPSKJFu5EigWDGtZ8UYYy7H1j32XgDa5Xy9DMBuABOMHyCljAcQn/N1ihDiJIBKAE7Y+NpF\n15Ah9MeZdO5M56wDdMJbdra282GMMRdl6xV7+ZzArQ/gBbZBE0IEAmgCYL/Rt18TQhwVQnxvaimf\n2VnZsnS8qhqysqjPffHi3FCGMcZUUmhgF0JsF0LEmPjTy5IXEkL4AlgLYKyUUn925yIANQA0Bl3V\nf1bA80cIIaKEEFE3b9605KVdz7p1QNu2QGKi8mP7+ABeXsqPu2sXdZfz86OWuIwxxlRRaGCXUoZK\nKeub+LMewHUhRAAA5NyafMcWQniAgvpKKWWE0djXpZRZUspsAN8AaF7APJZIKUOklCFly5a17L9S\nC0eO0NGpq1crP3ZcHLB3L7VkVZqfH/V1V9qtW3R77x6tCjDGGFOFrUvxGwAMy/l6GID1eR8ghBAA\nvgNwUkr5eZ6fBRjd7QMgxsb5OIb9+4GmTenrgQO1nYulUlLoGFSl6a/S33rL+rPiGWOMFcrWwD4b\nQCchxFkAnXLuQwhRUQihz3BvBeA5AB1MlLXNEUIcE0IcBdAewJs2zkc7WVlATM7nksceAz7+2PCz\npUuBefO0mZelYmOBf/9VftxBg4CpU4EZM5QfmzHG2H+ElFLrOVgsJCRERkVFaT0Ng8hI4PXXqenK\nv/8CZcrQ9/VXpgMGAGvWAJ99RlestlqwgJq73Lih/LK2fs5O+PeCMcZclRDioJQyxJzHcktZW1y/\nDkycSH3PK1YEFi8G/P1zP6ZTJ2DlSvp63Di6sh8/3rbXLVsWaNbMtqNOGWOMuSSODNa6fh2oU4eS\nwSZOBCZNAnx9cz/Gy4sOOvHwAFatAtzcgHfeoeA+0VQvHzM9/bR6JWmMMcacGgd2S507B9SsCZQv\nD0yZAvToQV3gTOnYEahVi752dwdWrKBucZmZ9puvpQIDgTZttJ4FY4wxK/EhMOa6dIn2yoOCDEly\n48blH9QB6rl+9qzhvrs78MMPwOTJdD8+3rq5/PQTZd0nJFj3fMYYYy6LA3th0tIokzsoCPj1V2Da\nNKBGDfOfmzf4uuX8ys+fB+rVo/EsTVS7dQs4fFidK3+1zmNnjDFmFxzYC5KRQUlqU6YAYWHAqVP0\ntSUNXL75xvT3q1UD+vQBPviAxnSULHR7nMfOGGNMNbzHbkpcHGW5e3gAr75Ky+2hocq+hk5HQV+n\nA2bOpIS6jz7SvnlLbOzDmf2MMcacBl+xG7t7F3jvPaB6dWBzTn+d0aOVD+p6bm5UIvfKK8Ds2cCy\nZeq8jqUOHtR6BowxxqzEV+wALYP/9BPw9tvA1avA0KGGlrC26t694J+7uQELFwKNGwODB5s3ZqVK\nQLt2tKLAGGOMGeErdoBqwgcPphK2P/+kK+cKFWwf18sLCA4u/HFCACNHAsWKAbdvA59+WvCee+/e\ndFqaGkefSuk4+/2MMcYsxoF9+XLg559pSfzAAaBlS+XG7tCBat4tnc/48cAbb3CAZYwxZjEO7OfO\nGa6YdTplx965k8a3xNixtCUwfz4l7mVnP/yYFSsooe/OHWXmyRhjzGXwHvu2bepdGaelAdeuWfYc\nIYA5c+hDxscfU6364sWG+ncASEqixjdZWcrOlzHGmNPjK/a//1Z3/OXLLX+OEMCsWZShv2MHX5kz\nxhgzGwd2RyUEdbw7eJCOgc3K4it0xhhjheLAXqqU1jPInxCU+S4l8MILwPDhHNwZY4wViAP7a6+p\n2+2tXz/bxxCCTon74Qdg2DCgShU6Vc7T0/axGWOMuRROnlOTl5f5B8YUZvJkSqCbNImu2iMi6LQ4\nxhhjzAh/eShUAAAGdUlEQVRHhooVgZAQdcY2Po9dCe+9R9nyEydSudv06cqNzRhjzCVwYI+LA6Ki\n1Bl7xw46mlVJEyZQcP/tNzp9jtvKMsYYM8J77L/+qm4d+6VLyo/79tv0oYGDOmOMsTw4sB8+rO74\n4eHqjs8YY4wZ4cDOGGOMuRAO7OXLaz0DxhhjTDEc2EeMULeO/Zln1BubMcYYy4MDu06nXqMXLy+g\ncmV1xmaMMcZM4MBepgxQp446Yytdx84YY4wVggP79evAsWPqjL1jBx2vyhhjjNkJB/aICHXr2Dmw\nM8YYsyMO7MePqzv+unXqjs8YY4wZ4cCutkqVtJ4BY4yxIoQDe9Wq6o198SIvxTPGGLMrmwK7EKK0\nEGKbEOJszm2pfB4XK4Q4JoSIFkJEWfp8VQ0bpl4de9WqwCOPqDM2Y4wxZoKtV+wTAeyQUtYCsCPn\nfn7aSykbSymNz0i15Pnq8PYG/P3t/rKMMcaYGmwN7L0ALMv5ehmA3nZ+vu08PQE/P7u/LGOMMaYG\nWwN7eSllPADk3JbL53ESwFYhxEEhxAgrnq+exEQgNtbuL8sYY4ypwb2wBwghtgOoYOJHkyx4nVZS\nyjghRDkA24QQp6SUey14PnI+EIwAgKpKJrz9+KN6deyMMcaYnRUa2KWUofn9TAhxXQgRIKWMF0IE\nALiRzxhxObc3hBDrADQHsBeAWc/Pee4SAEsAICQkRLlIfO6cYkMxxhhjWrN1KX4DgGE5Xw8DsD7v\nA4QQPkIIP/3XAJ4CEGPu8xljjDFmPlsD+2wAnYQQZwF0yrkPIURFIcTmnMeUB/CHEOIIgAMANkkp\ntxT0fLuqWdPuL8kYY4yppdCl+IJIKW8D6Gji+3EAwnK+Pg+gkSXPt6vBg4EZMzSdAmOMMaYU7jxX\nsiQQGKj1LBhjjDFFcGB/6y3g/HmtZ8EYY4wpggM7Y4wx5kI4sDPGGGMuhAM7Y4wx5kI4sDPGGGMu\nhAM7Y4wx5kI4sDPGGGMuhAM7Y4wx5kI4sDPGGGMuhAM7Y4wx5kI4sDPGGGMuhAM7Y4wx5kI4sDPG\nGGMuhAM7Y4wx5kI4sDPGGGMuREgptZ6DxYQQNwFc1HoeCioD4JbWk3AQ/Lsw4N9Fbvz7MODfhUFR\n+V1Uk1KWNeeBThnYXY0QIkpKGaL1PBwB/y4M+HeRG/8+DPh3YcC/i4fxUjxjjDHmQjiwM8YYYy6E\nA7tjWKL1BBwI/y4M+HeRG/8+DPh3YcC/izx4j50xxhhzIXzFzhhjjLkQDuwOQAjxoRDiqBAiWgix\nVQhRUes5aUkI8YkQ4lTO72SdEKKk1nPSihBigBDiuBAiWwhRJDN/hRBdhBCnhRDnhBATtZ6PloQQ\n3wshbgghYrSei9aEEFWEELuEECdz/o2M0XpOjoIDu2P4RErZUErZGMCvAKZqPSGNbQNQX0rZEMAZ\nAO9qPB8txQDoC2Cv1hPRghBCB2ABgK4AggEMFkIEazsrTf0PQBetJ+EgMgGMk1LWBfA4gFeL+N+N\n/3BgdwBSymSjuz4AinTig5Ryq5QyM+fu3wAqazkfLUkpT0opT2s9Dw01B3BOSnleSpkOIBxAL43n\npBkp5V4Ad7SehyOQUsZLKQ/lfJ0C4CSAStrOyjG4az0BRoQQMwEMBZAEoL3G03EkLwD4SetJMM1U\nAnDZ6P4VAC00mgtzUEKIQABNAOzXdiaOgQO7nQghtgOoYOJHk6SU66WUkwBMEkK8C+A1ANPsOkE7\nK+z3kfOYSaDltpX2nJu9mfO7KMKEie8V6RUtlpsQwhfAWgBj86x+Flkc2O1EShlq5kNXAdgEFw/s\nhf0+hBDDAHQH0FG6eE2mBX83iqIrAKoY3a8MIE6juTAHI4TwAAX1lVLKCK3n4yh4j90BCCFqGd3t\nCeCUVnNxBEKILgAmAOgppbyn9XyYpv4BUEsIUV0I4QlgEIANGs+JOQAhhADwHYCTUsrPtZ6PI+EG\nNQ5ACLEWQB0A2aBT616RUl7VdlbaEUKcA1AMwO2cb/0tpXxFwylpRgjRB8BXAMoCSAQQLaXsrO2s\n7EsIEQZgLgAdgO+llDM1npJmhBA/AmgHOtHsOoBpUsrvNJ2URoQQrQHsA3AM9N4JAO9JKTdrNyvH\nwIGdMcYYcyG8FM8YY4y5EA7sjDHGmAvhwM4YY4y5EA7sjDHGmAvhwM4YY4y5EA7sjDHGmAvhwM4Y\nY4y5EA7sjDHGmAv5P8wCrnqffJ17AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also evaluate coverage and create prediction intervals using statsmodels attributes\n", "from statsmodels.sandbox.regression.predstd import wls_prediction_std\n", "res = proj_dr_cate.effect_model.model\n", "predictions = res.get_prediction(PolynomialFeatures(degree=1, include_bias=True).fit_transform(X[:, feature_inds]))\n", "frame = predictions.summary_frame(alpha=0.05)\n", "pred = frame['mean']\n", "iv_l = frame['mean_ci_lower']\n", "iv_u = frame['mean_ci_upper']\n", "\n", "# This is the true CATE functions\n", "theta_true = true_fn(X_raw)\n", "# This is the true projection of the CATE function on the subspace of linear functions of the\n", "# subset of the features used in the projection\n", "true_proj = LinearRegression().fit(X[:, feature_inds], theta_true).predict(X[:, feature_inds])\n", "\n", "# Are we covering the true projection\n", "covered = (true_proj <= iv_u) & (true_proj >= iv_l)\n", "print(\"Coverage of True Projection: {:.2f}\".format(np.mean(covered)))\n", "\n", "fig, ax = plt.subplots(figsize=(8,6))\n", "\n", "order = np.argsort(X[:, feature_inds[0]])\n", "ax.plot(X[order, feature_inds[0]], iv_u[order], 'r--')\n", "ax.plot(X[order, feature_inds[0]], iv_l[order], 'r--')\n", "ax.plot(X[order, feature_inds[0]], pred[order], 'g--.', label=\"pred\")\n", "ax.plot(X[order, feature_inds[0]], theta_true[order], 'b-', label=\"True\", alpha=.3)\n", "ax.plot(X[order, feature_inds[0]], true_proj[order], 'b-', label=\"TrueProj\", alpha=.3)\n", "ax.legend(loc='best')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# CATE via Re-Weighted DRIV: DRIV-RW"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lasso CATE"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper, StatsModelLinearRegression, ConstantModel, WeightWrapper\n", "from sklearn.dummy import DummyRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DRIV we need a model for predicting E[T*Z | X]. We use a classifier\n", "model_TZ_X = lambda: model()\n", "\n", "# We also need a model for the final regression that will fit the function theta(X)\n", "# This model now needs to accept sample weights at fit time.\n", "const_driv_model_effect = lambda: WeightWrapper(Pipeline([('bias', PolynomialFeatures(degree=1, include_bias=True)),\n", "                                                          ('reg', Selective<PERSON><PERSON>o(np.arange(1, X.shape[1]+1),\n", "                                                                                  LassoCV(cv=5, fit_intercept=False)))]))\n", "\n", "# As in OrthoDMLIV we need a perliminary estimator of the CATE.\n", "# We use a DMLIV estimator with no cross-fitting (n_splits=1)\n", "dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=False)\n", "\n", "const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                        dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                        model_TZ_X(), # model for E[T * Z | X]\n", "                        const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                        cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                        n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                        binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                        binary_treatment=False, # a flag whether to stratify cross-fitting by treatment\n", "                        opt_reweighted=True # whether to optimally re-weight samples. Valid only for flexible final model\n", "                       )"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\linear_model\\coordinate_descent.py:492: ConvergenceWarning: Objective did not converge. You might want to increase the number of iterations. Fitting data with very small alpha may cause precision problems.\n", "  ConvergenceWarning)\n"]}, {"data": {"text/plain": ["<dr_iv.DRIV at 0x225048e3278>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["const_dr_cate.fit(y, T, X, Z, store_final=True)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.612\n", "True ATE: 0.609\n"]}], "source": ["# We can average the CATE to get an ATE\n", "dr_effect = const_dr_cate.effect(X)\n", "print(\"ATE Estimate: {:.3f}\".format(np.mean(dr_effect)))\n", "print(\"True ATE: {:.3f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXcAAAEICAYAAACktLTqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also see how it compares to the true CATE at each target point and calculate MSE\n", "plt.title(\"DMLIV CATE as Function of {}: MSE={:.3f}\".format(X_df.columns[4], np.mean((dr_effect-true_fn(X_raw))**2)))\n", "plt.scatter(X[:, 4], dr_effect, label='est')\n", "plt.scatter(X[:, 4], true_fn(X_raw), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random Forest CATE"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "    \n", "np.random.seed(random_seed)\n", "\n", "rf_driv_model_effect = lambda: RandomForestRegressor(n_estimators=5000, max_depth=3, min_impurity_decrease=0.00001,\n", "                                                     min_samples_leaf=100, bootstrap=True)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true}, "outputs": [], "source": ["rf_dr_cate = const_dr_cate.refit_final(rf_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"collapsed": true}, "outputs": [], "source": ["rf_dr_effect = rf_dr_cate.effect(X)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.63\n", "True ATE: 0.61\n"]}], "source": ["print(\"ATE Estimate: {:.2f}\".format(np.mean(rf_dr_effect)))\n", "print(\"True ATE: {:.2f}\".format(np.mean(true_fn(X_raw))))"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXcAAAEICAYAAACktLTqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBo\ndHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzt3Xt8VPWd//HXJyEkAQmRizdAQZa1\nalVcI9jVtm5d67ULra1V2aqt1QeLdt1ta9XaKm1ly66uP3XV9eGt6gpV3LVo1cq6utZ6gRpWEBWx\nQL0kVEViuOZG8vn98Z3AZDJJZpLJXM68n49HHifnM2fOfCboOyff+Z5zzN0REZFoKcl1AyIiknkK\ndxGRCFK4i4hEkMJdRCSCFO4iIhGkcBcRiSCFu4hIBCnci5iZvWNmTWa21cwazewlM5ttZiVx29xr\nZq1mts3MGszsaTP7VNzj55vZC7Hvl5jZT5O8zgwz+8DMhvTQx0lm9nysj41m9lsz+5uEbY43Mzez\nH8TVPhvra5uZbY89vi3ua38ze87MmhPqv07x53N+bJ83JNRnxur3xtUuMLO3Yu/hQzN7wsxGJPkZ\ndn6t7OV1T4jta4eZ/a+ZHdDLtqPM7Fex9/+umZ2T6r7M7DcJPbWa2aq4x6ea2e/MbLOZ1ZnZ1an8\n3CQ/KNzlS+4+AjgAmA9cDtydsM2/uPsewDigPsnjne4FvmFmllD/BrDA3XcmPsHMvgo8DNwPjAf2\nBq4GvpSw6XlAQ2wJgLv/zt33iPV2aKxc3Vlz9/ditUvianu4e+K+e7MO+HrCL6Zzgbfj3sPngX8C\nzo79LA8GFiXs518Sejgi2YuZ2RjgEeDHwCigFniol/5uBVoJP7dZwL+b2aGp7MvdT4nvCXiJ8G/R\naSHwfOy5nwf+LvGXruQvhbsA4O6b3f0x4OvAeWb26STbNBFCa2oPu1lMCILPdhbMbE/gdEJ4dxH7\nJXAD8DN3vyvWQ4e7/9bdL4zbbhjwVeBiYIqZ1fT3ffbDB8Aq4KRYL6OAvwQei9vmaOBld38VwN0b\n3P0+d9/aj9f7CvCGuz/s7s3AXOCI+L+WOpnZcOAM4Mfuvs3dX4j19Y1+7Gsi4d/tP+LKEwm/lNvd\nfR3wArt/iUqeU7hLF+7+e6COuIDuFAuTs4G1PTy3M/zPjSufCbzl7smGIQ4CJgD/2UdbZwDbCEeV\nSxL2PyCx4ajj+tjs/rjXPAt4FGiJe3wZcJKZ/cTMjjWz8gG0dCiw62fl7tsJfz0kC9U/B9rd/e24\n2sq4bdPZ17nA79z9j3G1G4FzzazMzA4CPgP8T9rvSHJC4S7JbCAcgXf6vpk1AluB49h9ZJjMfcDX\nzKwytn5urJbM6NjyT330cx7wkLu3E4YKzjazsj6eE+/mWIh3fv2s8wF3r44d8fbmV8DxZjaS8H66\n/BXi7r8jHCX/BfAEsMnMbjCz0rjNvp/QQ08/kz2AzQm1zcCIfmybzr7OJQyrxXuc8BdTE/AWcLe7\nv9JD35JnFO6SzDjC+Han6929mvBnehPhiDupWFBuBGaY2YGEIYuFPWy+Kbbct6f9mdkE4K+ABbHS\no0AFcFqf72K3v4+FeOfXj9N4budfJE8APwLGuPuLSbb5TWwsfxQwAzgf+HbcJtcn9HBe4j5itgFV\nCbUqwi/WdLdNaV+xv1z2Ie4vqNjw01PATwk/7wmEv07m9NC35BmFu3RhZkcTwr3b0WzsA8pLgZvi\njsyT6RzG+Abw3+7+YQ/brQHeJwy79OQbhP9Of21mHwDrCWGTsaGZFN0PfI+uY9LdxD4zeAZ4Fuj2\nuUUK3gB2fdgaGwqbHKsnehsYYmZT4mpHxG2b6r7OAx5x921xtQMJQz73u/tOd68DHgRO7cd7khxQ\nuAsAZlZlZqcT/gd+wN1XJdvO3Z8mDNtc1Mvu7gf+GriQnodk8HC96e8CPzazb8Z6KDGz48zsjthm\n5wI/IXyI2/l1BnCamY1OuuPB8VvgRODfEh+wMNXzLDPb04JphNklS/vxOr8CPm1mZ5hZBWHm0Gvu\n/lbihrEx9EeAn5rZcDM7lvBXw3+kuq/YL+mv0X1I5u3wsJ0T+zfZh/Bhe49TOCXPuLu+ivQLeIcw\nzLKVMBb7MmFGSmncNvcC1yY87+uEKZHlhOGHF5Ls+zngE6A8hT5OBn5HGEbYGHvuacAxQDMwNslz\n3iBMcexcnwg4MCRJH82xfXd+LY97fBvw2R76SvreYo9dC9wb+/5zwDPAx7Gf5dvADxJ+hq0JPXzc\ny8/jrwlj3E2x/ifGPfZD4Ddx66MIs5S2A+8B56S6r9jjZwPvApakjy8Ar8T+2/gAuBMYluv/bvWV\n2pfF/hFFRCRCNCwjIhJBCncRkQhSuIuIRJDCXUQkgpJepS8bxowZ4xMnTszVy4uIFKTly5d/7O5j\n+9ouZ+E+ceJEamtrc/XyIiIFyczeTWU7DcuIiESQwl1EJIIU7iIiEZSzMfdk2traqKuro7m5Odet\nDIqKigrGjx9PWVk6V6sVEUlfXoV7XV0dI0aMYOLEiXS/U1thc3c2bdpEXV0dkyZNynU7IhJxeTUs\n09zczOjRoyMX7ABmxujRoyP7V4mI5Jc+j9zN7B7CPTA/cvdu16eO3QfzJsJ1nncA57v7//W3oSgG\ne6covzcR6cFdp0Fd3O0Rxh8H335i0F82lSP3ewmXZO3JKcCU2NdFwL8PvC0RkQhIDHYI63elcyOx\n/ukz3N39ebreci3RDOB+D5YC1WbW423TouTee+9lw4YNuW5DRPLVrmAvjfuie+APgkyMuY8j3Cqt\nU12s1o2ZXWRmtWZWu3Hjxgy8dG4p3EUkX2VitkyygeSkdwBx9zuAOwBqamoGfJeQxa/Wc92SNWxo\nbGK/6kouO+kgZh6Z9PdKWh544AFuvvlmWltbmT59OrfddhsXXHABtbW1mBnf+ta3mDBhArW1tcya\nNYvKykpefvllKit7u62oiEj2ZCLc6wh3Ru80nnCPzUG1+NV6rnxkFU1t7QDUNzZx5SPhtp8DCfjV\nq1fz0EMP8eKLL1JWVsacOXO49tprqa+v5/XXXwegsbGR6upqbrnlFq6//npqamoG/oZEJHrGHxcb\ngmnvXh9kmRiWeQw4N3Zj4GOAze7+pwzst1fXLVmzK9g7NbW1c92SNQPa7zPPPMPy5cs5+uijmTp1\nKs888wwNDQ2sX7+e73znOzz11FNUVVUN6DVEpEh8+4nuQZ6l2TKpTIX8JXA8MMbM6oBrgDIAd78d\neJIwDXItYSrkNwer2XgbGpvSqqfK3TnvvPP4+c9/3qU+b948lixZwq233sqiRYu45557BvQ6IlIk\nshDkyfQZ7u5+dh+PO3BxxjpK0X7VldQnCfL9qgc27n3CCScwY8YM/vEf/5G99tqLhoYGtm7dyp57\n7skZZ5zB5MmTOf/88wEYMWIEW7duHdDriYgMhry6/EA6LjvpoC5j7gCVZaVcdtJBA9rvIYccwrXX\nXssXv/hFOjo6KCsr44YbbuDLX/4yHR0dALuO6s8//3xmz56tD1RFJO9YOPDOvpqaGk+8Wcfq1as5\n+OCDU97HYM2WGUzpvkcRyaHFl8KqRdDeDKUVcNiZMPOmnLZkZsvdvc9ZHAV75A5hVky+h7mIFKjF\nl8KK+4BSKKmE9pbYOjkP+FTk1YXDRETyxqpFQCmUD4eyIWFJaaye/xTuIiLJtDdDSXnXWkl5qBcA\nhbuISDKlFdDR0rXW0RLqBUDhLiKSzGFnAu3Qsh3adoYl7bF6/ivoD1RFRAZN54emqxZBe1PezJZJ\nlcI9TmNjIwsXLmTOnDm5bkVE8sHMmwomzBNpWCZOY2Mjt912W7d6e3t7kq1FRPJXYR+572iAhnXQ\nvBkqRsKoyTBsVL93d8UVV7Bu3TqmTp1KWVkZe+yxB/vuuy8rVqzgySef5PTTT991Zcjrr7+ebdu2\nMXfuXNatW8fFF1/Mxo0bGTZsGHfeeSef+tSnMvUuRUTSVrjhvqMB6muhbDhUjoK2prA+rqbfAT9/\n/nxef/11VqxYwXPPPcdpp53G66+/zqRJk3jnnXd6fN5FF13E7bffzpQpU1i2bBlz5szh2Wef7ecb\nE5GMmTsySW1zdntY91tY+SBsqYeqcXDEWTD584P+soUb7g3rQrAPHRbWO5cN6wZ09B5v2rRpTJo0\nqddttm3bxksvvcTXvva1XbWWlpZeniEiWZEs2Dvr2Qr4db+F56+DymqoPgCaPwnrMOgBX7jh3rw5\nHLHHK6uEpt5u95qe4cOH7/p+yJAhuy4cBtDcHE5k6OjooLq6mhUrVmTsdUUkIlY+GIJ9+Jiw3rlc\n+eCgh3vhfqBaMTIMxcRrawr1furtEr577703H330EZs2baKlpYXHH38cgKqqKiZNmsTDDz8MhOvB\nr1y5st89iEiEbKmHij271ir2DPVBVrjhPmoytG2H1h3gHpZt20O9n0aPHs2xxx7Lpz/9aS677LIu\nj5WVlXH11Vczffp0Tj/99C4fmC5YsIC7776bI444gkMPPZRHH3203z2ISIRUjQtDMfGaPwn1QVbQ\nl/zN9GyZbNAlf0WypKcxd8jNmHvFniHYmxrhc5f1e1imKC75y7BReR/mIpIjczfnfrZMZ4CvfBAa\n3w1H7EdfqNkyIiIDku1pj8lM/nxWwjxR3o2552qYKBui/N5EJL/kVbhXVFSwadOmSIagu7Np0yYq\nKgrjcqEiUtjyalhm/Pjx1NXVsXHjxly3MigqKioYP358rtsQKQw3T4OGNbvXRx0Ef//73PVTYPIq\n3MvKyvo8I1REikBisENYv3laWgE/8YonutXemX9ays+fPu9pPtzaumt97xFDWXbViSk/PxM99Fde\nDcuIiADQsIa2Nrp9dQv8XiQL1d7qiRKDHeDDra1Mn/d01noYCIW7iOSdtrb06oMhMdj7qucbhbuI\n5J2e5lREcK7FoFG4i0jeebNj37Tq0p3CXUTyzkz+lTfa9911pO4Ob7Tvy0z+NbeNFZC8mi0jItJp\nJv8KHX1vN1gqSo3m9u7jQBWlloNu0qcjdxGJpJ6mG6Y6DfGtead2C/KKUuOteadmrYeBSOmqkGZ2\nMnATUArc5e7zEx4fCTwA7E/4a+B6d/9Fb/tMdlVIEYmQAV60KyPzw5fdDa/cCTs+hmFjwkW7pl+Q\n3j7yTKpXhewz3M2sFHgbOBGoA14Bznb3N+O2+SEw0t0vN7OxwBpgH3fvcc6Qwl0kwuaOpA0gfupi\nGZRB9i7mtexueO6foHw4lFVB2xZo2Q7H/7CgAz7VcE9lWGYasNbd18fC+kFgRsI2DowwMwP2ABqA\nnWn2LCIR0Qpdgz22ntUZ4q/cGYK9ck8YUhqW5cNDvQikEu7jgPfj1utitXi3AAcDG4BVwKXu3u2j\nEDO7yMxqzaw2qtePERGwHk426qk+KHZ8HI7Y45VVhXoRSCXck300nDiWcxKwAtgPmArcYmZV3Z7k\nfoe717h7zdixY9NuVkQKQ16chDRsTBiKide2JdSLQCrhXgdMiFsfTzhCj/dN4BEP1gJ/BD6FiBSl\n5h6mMPZUHxRHXxjG2Js+gZ3tYdmyPdSLQCrh/gowxcwmmdlQ4CzgsYRt3gNOADCzvYGDgPWZbFRE\nCsfhLKSpnS4nITW1h3rWTL8gfHg6ZBhs/yAsC/zD1HT0eRKTu+80s0uAJYSpkPe4+xtmNjv2+O3A\nz4B7zWwVYRjncncvjoEtEUnqcBbm9CQkIAR5kYR5opTmuQ8GTYUUibZcXcc86lKdCqnLD4jIoFCQ\n55bCXUS6G+DZpRmxowEa1kHzZqgYCaMmw7BR2e2hgOnaMiLSVbJg760+GHY0QH0t7GyFylFhWV8b\n6pIShbuI5J+GdVA2HIYOA7OwLBse6pIShbuI5J/mzVBW2bVWVhnqkhKFu4jkn4qR0NbUtdbWFOqS\nEoW7iOSfUZOhbTu07ghnQLXuCOujJue6s4KhcBeRrnqaFZPN2TLDRsG4GhgyFJoawnJcjWbLpEFT\nIUWku2xPe0xm2CiF+QDoyF1EJIIU7iIiEaRhGZGoyYezSwHefByW3wdbN8CI/eCo8+CQ07PfR5FS\nuItESW9nl6YR8LPufJkX1+0+G/TYyaNYcOFnUu/jzcfhf/8JKqtg5P7Q8klYBwV8lmhYRkS6SAx2\ngBfXNTDrzpdT38ny+0KwDxsDpaVhWVkV6pIVOnIXiZi2JPcpLStL/fmJwd5XPamtG8IRe7zyPWHz\ne6nvQwZER+4iEdLaml590IzYLwzFxGv5JNQlKxTuIhGSF/cuhfDhadMW2PExtLeHZdOWUJes0LCM\nSJ750eJV/HLZ+7S7U2rG2dMncO3Mw1J67uEs5LX2c6goCRdTdA/BfjgLeWdw2+6q80PT5feFoZgR\n+8Exl+jD1CxSuIvkkR8tXsUDS3ePS7e771pPJ+Bzfu9SCEGuMM8ZDcuI5JH4YE+lLtIThbuISARp\nWEYkz7xG8jHzbDFgPB8yveRNxtLIRqpZ1nEIdeydtR5k4HTkLpJHXuMcKktDsENYVpaGerb88bKD\nmVH6ApW0sIFRVNLCjNIX+ONlB2etBxk4HbmL5JGKHg63eqoPindf4PtfmgaV1btrTY3w7gsw+sAs\nNiIDoSN3kTzSecSeaj3RlL2Gp1VPauuHUF7VtVZeFepSMBTuInnEPb16oqe/e3y3IJ+y13Ce/u7x\nqTcxYm9o2dK11rIl1KVgaFhGJI80D4XKJNeGaR4KQ1PcR1pBnswBx8GqReH78qoQ7M2NMOWLA9uv\nZJXCXSQbUrzGetXczWyZO5KK1rjZMkNDPWtGHwiHnRnG2LfUhyP2KV/UeHuBUbiLDLa5I5NfqbGH\na6wnBnmqR+wZNfpAhXmB05i7yCDLmys1SlFJ6cjdzE4GbgJKgbvcfX6SbY4HbgTKgI/d/fMZ7FOk\nICS76Nc1A5wBI9IffYa7mZUCtwInAnXAK2b2mLu/GbdNNXAbcLK7v2dmew1WwyL56keLV/GDpcdx\ndQlYSWy8fCl4SfIgT3UGTNry5R6qklOpDMtMA9a6+3p3bwUeBGYkbHMO8Ii7vwfg7h9ltk2R/PeD\npcclPbs0q9dY7+0eqlJUUgn3ccD7cet1sVq8Pwf2NLPnzGy5mZ2bqQZFCkVvZ5c2te8+UncP69m8\nXowUn1TG3JONDCb+QTkEOAo4AagEXjazpe7+dpcdmV0EXASw//4J91cUKXC9nV2aN9dYl6KRypF7\nHTAhbn08sCHJNk+5+3Z3/xh4HjgicUfufoe717h7zdixY/vbs0heGujZpSKZlEq4vwJMMbNJZjYU\nOAt4LGGbR4HPmtkQMxsGTAdWZ7ZVkfyWN/cvFSGFcHf3ncAlwBJCYC9y9zfMbLaZzY5tsxp4CngN\n+D1huuTrg9e2SP45nIW5H1vvaVaMZssUnZTmubv7k8CTCbXbE9avA67LXGsihScvxtYV5ILOUBUR\niSRdW0YkzpYrRna7xV3V/CwfCd/2Ofho5e71vY6AOc9ntwcpeAV75L741XqOnf8sk654gmPnP8vi\nV+tz3ZIUuC1XjEx6EtKWKwbnBKB35p/WvZgY7BDWb/vcoPQg0VWQR+6LX63new+vpL0jfHJV39jE\n9x4O/0PMPDLx/Kre93PdkjVsaGxiv+pKLjvpoLSeDzDrzpd5cV3DrvVjJ49iwYWfSWsfA/VnVz7B\nzrjpdkMM1v48SXD04PBrnmJLS/uu9aryUl77ycmZbDErps97mg+37r4a194jhrLsqhNTfv5g3uIu\naZAnkxjsfdVFelCQ4X7Vr1btCvZO7R3OVb9alXI4L361nisfWUVTWwi1+sYmrnxkFZD6L4jEYAd4\ncV0Ds+58OeWAT3ahqWtnHpbSc6F7sAPs9FBPJeATgx1gS0s7h1/zVFoBP/GKJ7rVUg60JH2k+wsm\nMdgBPtzayvR5T6cc8AO9xZ1IPinIYZntre1p1ZO5bsmaXcHeqamtneuWrEl5H4nB3lc90Y8Wr+KB\npe/RHps71+7OA0vf40eLV6XcQ2Kw91VPlBjsfdWTSRbsvdUT9fYLJlWJwd5XPZmBnoSUkfuXimRI\nQYZ7JmxobEqrPhgWLnsvrXpUZeIXTCYM9CSkjNy/dK9uJ3b3XhfpQUEOy2RCiUF7kiOykiz+Cd7R\nwxFhT3UZXIezkNfaz+k2W+ZwFvJOivsY8P1L5zyv2TKSEUUb7smCvbe6FIdBOwnp+kNgW9yMrj3G\nwfffTL6tglwyoGiHZUSyJjHYIaxff0hu+pGioHAXGWyJwd5XXSQDinZYRqLnNZKPl2fVzdOgIW7G\n1aiDsvv6IjE6cpdIeI1zkp5d+hrnZK+JxGCH7usiWaJwl0jIxNmlFaXJp0r1VO8m3SDfI72zoUXS\noXCXSMjE2aVvzTu1W5BXlBpvzTt1AJ3FJAZ5b7NlRDJAY+4SCe7JgzzdW9xlJMiTUZBLlhXkkfve\nI4amVU/mb49JfoPunurJ5MPp5j0dmBbb5VDy4hZ3PX14qg9VJQcKMtyXXXVityBP9wqA1848jL89\nZn9KY4d7pWb87TH7p3XRroGebn7s5FFp1ZP5f1+fmlY9USZ+UeaDvLjF3d//vnuQjzoo1EWyrGCH\nZdIJ8p5cO/OwtMI8mYGcbr7gws8M+JLBnVew7O+li5dddeKAL5VbWVZCU1v3Q+TKstSOHYZY8gud\nDUnjzw8j+dmlWf8LRkEuecI83UHJDKmpqfHa2tqcvLZk1uJX6/nuQyu65GoJcMPXp6b8S2ag16QH\nmHTFE8T/12zAH9O47LBIITCz5e5e0+d2CnfJhEzc+ERE+pZquBfssIzkl5lHjhtYmM9Nciu7uVm+\ndynAjUdB49rd69V/Bv+wPPt9iAxQQX6gKhGTLNh7qw+WxGCHsH7jUdntQyQDFO4inRKDva+6SB5T\nuIuIRJDCXUQkghTuIp2q/yy9ukgeU7hL7vU0Kybbs2X+YXn3INdsGSlQmgop+SEX0x6TUZBLROjI\nXUQkglIKdzM72czWmNlaM7uil+2ONrN2M/tq5loUEZF09TksY2alwK3AiUAd8IqZPebubybZ7p+B\nJYPRqOS5fDjDdO5oYGdcYQjM3ZTdHkTyRCpH7tOAte6+3t1bgQeBGUm2+w7wX8BHGexPCkE+nGHa\nLdgJ63NHZ68HkTySSriPA96PW6+L1XYxs3HAl4HbM9eaSDoSg72vuki0pRLuyS6JnXgpyRuBy929\nvdcdmV1kZrVmVrtx48ZUexQRkTSlMhWyDpgQtz4e2JCwTQ3woIW7Go0BTjWzne6+OH4jd78DuAPC\nJX/727SIiPQulXB/BZhiZpOAeuAs4Jz4Ddx9Uuf3ZnYv8HhisIsMriEkH4LRqRxSnPoclnH3ncAl\nhFkwq4FF7v6Gmc02s9mD3aAUgHw4w3TuJroHuWbLSPHSnZhERApIqndi0hmqIiIRpAFJyY8TkO46\nDepe2L0+/jj49hPZ7UEkQnTkXuzy4QSkxGCHsH7XadnrQSRidOQuubcr2Evjiu3dA19EUqYjdxGR\nCFK4i4hEkMJdcm/8cbFv2uO+4usiki6Fe7HLhxOQvv1E9yDXbBmRAdEHqpIft7hTkItklI7cRUQi\nSOEuIhJBGpaJgp/sA960e90q4ZoPstvD45fDygXQ1gRllXDELDj9n7Pbg4jsoiP3QpcY7BDWf7JP\n9np4/HKovRPaW2HI8LCsvTPURSQnFO6FLjHY+6oPhpULoGQIlO8BZbFlyZBQF5GcULjLwLU1QUlF\n11pJRaiLSE4o3GXgyiqho7lrraM51EUkJxTuhc56CNCe6oPhiFnQsRNatkFbbNmxM9RFJCcU7oXu\nmg+6B3m2Z8uc/s9QcyGUDoWd28Oy5kLNlhHJId1mT0SkgOg2eyIiRUzhLiISQTpDNddu/wJ8sHz3\n+j5Hwexns9vDglnwh98QLrVbClNOgVmaoy5SyHTknkuJwQ5h/fYvZK+HBbPgD48Tgr0kLP/weKiL\nSMHSkXsu7Qp2iyt698AfTH/4TViWlu+utbfsrotIQdKRe9HrPGKPFzuCF5GCpXAveqVAR0KtI1YX\nkUKlcM+lfY6KfeNxX/H1LJhySli2t0B7W1jG10WkICncc2n2s92DPNuzZWYtgCmns/sIvjSsa7aM\nSEHTB6q5lu1pj8koyEUiR0fuIiIRpHAXEYmglIZlzOxk4CbCwOxd7j4/4fFZQOc91bYBf+fuKzPZ\naN665S/h4zd2r485FC55Kbs9LL4UVi2C9mYorYDDzoSZN2W3BxHJK30euZtZKXArcApwCHC2mR2S\nsNkfgc+7++HAz4A7Mt1oXkoMdgjrt/xl9npYfCmsuC/ct7SkMixX3BfqIlK0UhmWmQasdff17t4K\nPAjMiN/A3V9y909iq0uB8ZltM08lBntf9cGwahFQCuXDY/cvHR7WVy3KXg8ikndSCfdxwPtx63Wx\nWk8uAJKeu25mF5lZrZnVbty4MfUupWftzVBS3rVWUh7qIlK0Ugl3S1JLeocPM/srQrhfnuxxd7/D\n3WvcvWbs2LGpdyk9K62AjpautY6WUBeRopVKuNcBE+LWxwMbEjcys8OBu4AZ7r4pM+3luTGHplcf\nDIedCbRDy/bY/Uu3h/XDzsxeDyKSd1IJ91eAKWY2ycyGAmcBj8VvYGb7A48A33D3tzPfZp665KXu\nQZ7t2TIzb4Kp54X7lnY0heXU8zRbRqTIpXQPVTM7FbiRMBXyHnefZ2azAdz9djO7CzgDeDf2lJ19\n3eNP91AVEUlfqvdQ1Q2yRUQKSKrhXtzXlvnP2fDGI+AtYOVw6Ffgq7dnt4dn58PyX0DLZigfCUd9\nE75wRXZ7EJHIKd5w/8/Z8PovCR87lIO3xdbJXsA/Ox9evBGGlEP5KNi5PayDAl5EBqR4ry3zxiNA\nCZQNg7KysKQkVs+S5b8IwV5ZBWWlYTmkPNRFRAageMPdW4CyhGJZrJ4lLZthyPCutSHDQ11EZACK\nN9ytHGhLKLbF6llSPjIMxcTbuT3URUQGoHjD/dCvAB3QtgPa2sKSjlg9S476JuxsgaYt0NYeljtb\nQl1EZACK9wPVzg9NczlbpvND0+W/gJaGcMQ+/e/0YaqIDJjmuYuIFJBU57kX77CMiEiEKdxFRCKo\ncMfc8+XWcjrDVETyUGGGe+d2EPdBAAAFvklEQVSt5SiN3VquJbZOdgNeZ5iKSJ4qzGGZfLm1nM4w\nFZE8VZjhni+3ltMZpiKSpwoz3PPl1nI6w1RE8lRhhnu+3FpOZ5iKSJ4qzA9UOz80XbUI2ptyN1tG\nZ5iKSJ7SGaoiIgVEZ6iKiBQxhbuISAQV5ph7ptS/Cqt/DVvqoWocHPwlGHdkrrsSERmw4j1yr38V\nlt4Grdug+oCwXHpbqIuIFLjiDffVv4bKPWHYaCgpDcvKPUNdRKTAFW+4b6mHiuqutYrqUBcRKXDF\nG+5V46C5sWutuTHURUQKXPGG+8FfgqZPYMcm6GgPy6ZPQl1EpMAVb7iPOxKOmQND94DGd8PymDma\nLSMikVDcUyHHHakwF5FIKt4jdxGRCFO4i4hEUErhbmYnm9kaM1trZt0ueWjBzbHHXzOzv8h8qwk2\nrYf/ux9+e11Yblo/6C8pIlIo+gx3MysFbgVOAQ4BzjazQxI2OwWYEvu6CPj3DPfZ1ab14XK/rTvC\n1MXWHWFdAS8iAqR25D4NWOvu6929FXgQmJGwzQzgfg+WAtVmtm+Ge93t3RfCCUeV1VBSEpYV1aEu\nIiIphfs44P249bpYLd1tMLOLzKzWzGo3btyYbq+7bf0Qyqu61sqrQl1ERFIKd0tSS7zDRyrb4O53\nuHuNu9eMHTs2lf6SG7E3tGzpWmvZEuoiIpJSuNcBE+LWxwMb+rFN5hxwXLhUQFMjdHSEZXNjqIuI\nSErh/gowxcwmmdlQ4CzgsYRtHgPOjc2aOQbY7O5/ynCvu40+MNwzdeiwcKGvocPC+ugDB+0lRUQK\nSZ9nqLr7TjO7BFgClAL3uPsbZjY79vjtwJPAqcBaYAfwzcFrOWb0gQpzEZEepHT5AXd/khDg8bXb\n47534OLMtiYiIv2lM1RFRCJI4S4iEkEKdxGRCFK4i4hEkMJdRCSCLEx0ycELm20E3s3Ji6duDPBx\nrpvIgCi8jyi8B4jG+4jCe4DCfR8HuHufp/jnLNwLgZnVuntNrvsYqCi8jyi8B4jG+4jCe4DovI+e\naFhGRCSCFO4iIhGkcO/dHbluIEOi8D6i8B4gGu8jCu8BovM+ktKYu4hIBOnIXUQkghTuIiIRpHDv\ng5n9zMxeM7MVZvbfZrZfrnvqDzO7zszeir2XX5lZda57SpeZfc3M3jCzDjMrqClsZnayma0xs7Vm\ndkWu++kPM7vHzD4ys9dz3ctAmNkEM/tfM1sd++/p0lz3NBgU7n27zt0Pd/epwOPA1bluqJ+eBj7t\n7ocDbwNX5rif/ngd+ArwfK4bSYeZlQK3AqcAhwBnm9khue2qX+4FTs51ExmwE/ieux8MHANcXKD/\nHr1SuPfB3eNv1jqcJPeGLQTu/t/uvjO2upRwK8SC4u6r3X1Nrvvoh2nAWndf7+6twIPAjBz3lDZ3\nfx5oyHUfA+Xuf3L3/4t9vxVYDYzLbVeZl9LNOoqdmc0DzgU2A3+V43Yy4VvAQ7luooiMA96PW68D\npueoF4ljZhOBI4Flue0k8xTugJn9D7BPkoeucvdH3f0q4CozuxK4BLgmqw2mqK/3EdvmKsKfpQuy\n2VuqUnkPBciS1AryL8AoMbM9gP8C/iHhL/RIULgD7v7XKW66EHiCPA33vt6HmZ0HnA6c4Hl6gkMa\n/xaFpA6YELc+HtiQo14EMLMyQrAvcPdHct3PYNCYex/MbErc6t8Ab+Wql4Ews5OBy4G/cfcdue6n\nyLwCTDGzSWY2FDgLeCzHPRUtMzPgbmC1u9+Q634Gi85Q7YOZ/RdwENBBuETxbHevz21X6TOztUA5\nsClWWurus3PYUtrM7MvAvwFjgUZghbuflNuuUmNmpwI3AqXAPe4+L8ctpc3MfgkcT7hU7ofANe5+\nd06b6gczOw74HbCK8P81wA/d/cncdZV5CncRkQjSsIyISAQp3EVEIkjhLiISQQp3EZEIUriLiESQ\nwl1EJIIU7iIiEfT/ARf9uiyZLnvaAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.title(\"DRIV CATE: MSE {:.2}\".format(np.mean((true_fn(X_raw) - rf_dr_effect)**2)))\n", "plt.scatter(X[:, 4], rf_dr_effect, label='est')\n", "plt.scatter(X[:, 4], true_fn(X_raw), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIMAAAEDCAYAAACmg8SIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAAgohBAAAAAEHEIAAAAIAgfwFOPntjgIGTkAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import shap\n", "import pandas as pd\n", "\n", "Xdf = pd.DataFrame(X, columns=X_df.columns)\n", "# explain the model's predictions using SHAP values\n", "explainer = shap.TreeExplainer(rf_dr_cate.effect_model)\n", "shap_values = explainer.shap_values(Xdf)\n", "\n", "# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(explainer.expected_value, shap_values[0,:], Xdf.iloc[0,:], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjAAAAJICAYAAAB/gN7DAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\no+m4ZUanRI8AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x684 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values, Xdf)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjgAAAI4CAYAAABndZP2AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nN08AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x684 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values, Xdf, plot_type='bar')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}