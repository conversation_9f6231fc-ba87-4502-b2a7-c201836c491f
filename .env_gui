# GUI环境配置文件
# 使用方法: source .env_gui

# 基础显示配置
export DISPLAY=:0.0
export XAUTHORITY=$HOME/.Xauthority

# Qt配置
export QT_QPA_PLATFORM=cocoa
export QT_MAC_WANTS_LAYER=1
export QT_AUTO_SCREEN_SCALE_FACTOR=1
export QT_SCALE_FACTOR=1

# 强制GUI模式
export GUI_ENABLED=1
export HEADLESS=false
export INTERACTIVE_MODE=true
export FORCE_GUI=true
export NO_HEADLESS=true

# Python配置
export PYTHONDONTWRITEBYTECODE=1
export MPLBACKEND=TkAgg
export PYTHONPATH=$PYTHONPATH

# 会话配置
export XDG_SESSION_TYPE=x11
export SESSION_MANAGER=local
export DESKTOP_SESSION=default

# 终端配置
export TERM_PROGRAM=Terminal
export TERM_PROGRAM_VERSION=2.12.7

# 强制交互模式
export INTERACTIVE=1
export FORCE_INTERACTIVE=true
export DISABLE_HEADLESS_CHECK=true 