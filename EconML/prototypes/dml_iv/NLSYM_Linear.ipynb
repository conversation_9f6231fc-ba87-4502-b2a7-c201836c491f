{"cells": [{"cell_type": "code", "execution_count": 110, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 111, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression, Lasso, LogisticRegression\n", "from sklearn.ensemble import GradientBoostingRegressor, GradientBoostingClassifier\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.preprocessing import PolynomialFeatures, StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "import scipy.special"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# NLSYM DATA"]}, {"cell_type": "code", "execution_count": 112, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\preprocessing\\data.py:645: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.partial_fit(X, y)\n", "C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\base.py:464: DataConversionWarning: Data with input dtype int64, float64 were all converted to float64 by StandardScaler.\n", "  return self.fit(X, **fit_params).transform(X)\n"]}], "source": ["# Preprocess data\n", "df = pd.read_csv(\"data/card.csv\")\n", "data_filter = df['educ'].values >= 6\n", "T = df['educ'].values[data_filter]\n", "Z = df['nearc4'].values[data_filter]\n", "y = df['lwage'].values[data_filter]\n", "\n", "# Impute missing values with mean, add dummy columns\n", "# I excluded the columns 'weights' as we don't know what it is\n", "X_df = df[['exper', 'expersq']].copy()\n", "X_df['fatheduc'] = df['fatheduc'].fillna(value=df['fatheduc'].mean())\n", "X_df['fatheduc_nan'] = df['fatheduc'].isnull()*1\n", "X_df['motheduc'] = df['motheduc'].fillna(value=df['motheduc'].mean())\n", "X_df['motheduc_nan'] = df['motheduc'].isnull()*1\n", "X_df[['momdad14', 'sinmom14', 'reg661', 'reg662',\n", "        'reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']] = df[['momdad14', 'sinmom14', \n", "        'reg661', 'reg662','reg663', 'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66']]\n", "X_df[['black', 'smsa', 'south', 'smsa66']] = df[['black', 'smsa', 'south', 'smsa66']]\n", "columns_to_scale = ['fatheduc', 'motheduc', 'exper', 'expersq']\n", "scaler = StandardScaler()\n", "X_df[columns_to_scale] = scaler.fit_transform(X_df[columns_to_scale])\n", "X = X_df.values[data_filter]\n", "\n", "true_fn = lambda x: np.zeros(x.shape[0])"]}, {"cell_type": "code", "execution_count": 113, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["Index(['exper', 'expersq', 'fatheduc', 'fatheduc_nan', 'motheduc',\n", "       'motheduc_nan', 'momdad14', 'sinmom14', 'reg661', 'reg662', 'reg663',\n", "       'reg664', 'reg665', 'reg666', 'reg667', 'reg668', 'reg669', 'south66',\n", "       'black', 'smsa', 'south', 'smsa66'],\n", "      dtype='object')"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["X_df.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSIS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some hyperparameters"]}, {"cell_type": "code", "execution_count": 114, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.006686726847208292\n"]}], "source": ["random_seed = 123459 # random seed for each experiment\n", "N_SPLITS = 10 # number of splits for cross-fitting\n", "COV_CLIP = 20/(X.shape[0]) # covariance clipping in driv\n", "print(COV_CLIP)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining some generic non-parametric regressors and classifiers"]}, {"cell_type": "code", "execution_count": 115, "metadata": {"collapsed": false}, "outputs": [], "source": ["from utilities import RegWrapper\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from xgboost import XGBClassifier, XGBRegressor\n", "from xgb_utilities import XGBWrapper\n", "\n", "# Linear models for regression and classification \n", "model = lambda: LassoCV(cv=5, n_jobs=-1)\n", "model_clf = lambda: RegWrapper(LogisticRegressionCV(cv=5, solver='liblinear', scoring='neg_log_loss', n_jobs=-1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Some utility functions"]}, {"cell_type": "code", "execution_count": 116, "metadata": {"collapsed": true}, "outputs": [], "source": ["def nuisance_diagnostic(cate, nuisance_model, property_name, property_fn, \n", "                        index_names=None, statistic=np.std, threshold=None):\n", "    std = statistic([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]], axis=0)\n", "    if hasattr(std, '__len__'):\n", "        if threshold is None:\n", "            coefs = np.argmax(std).flatten()\n", "        else:\n", "            coefs = np.argwhere(std >= threshold).flatten()\n", "        if index_names is None:\n", "            index_names = np.arange(std.shape[0])\n", "        for high_var in coefs:\n", "            plt.figure(figsize=(4,3))\n", "            plt.title(\"{}: {}[{}] Across Folds\".format(nuisance_model, property_name, index_names[high_var]))\n", "            plt.plot([property_fn(ns)[high_var] for ns in cate.fitted_nuisances[nuisance_model]])\n", "            plt.xlabel('fold')\n", "            plt.ylabel('property')\n", "            plt.show()\n", "    else:\n", "        plt.figure(figsize=(4,3))\n", "        plt.title(\"{}: {} Across Folds\".format(nuisance_model, property_name))    \n", "        plt.plot([property_fn(ns) for ns in cate.fitted_nuisances[nuisance_model]])\n", "        plt.xlabel('fold')\n", "        plt.ylabel('property')\n", "        plt.show()\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE via DMLATEIV"]}, {"cell_type": "code", "execution_count": 117, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_ate_iv import DMLATEIV\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We need to specify models to be used for each of these residualizations\n", "model_Y_X = lambda: model() # model for E[Y | X]\n", "model_T_X = lambda: model() # model for E[T | X]. We use a regressor since T is continuous\n", "model_Z_X = lambda: model_clf() # model for E[Z | X]. We use a classifier since Z is binary\n", "\n", "dmlate = DMLATEIV(model_Y_X(), model_T_X(), model_Z_X(),\n", "                  n_splits=N_SPLITS, # n_splits determines the number of splits to be used for cross-fitting.\n", "                  binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                  binary_treatment=True # a flag whether to stratify cross-fitting by treatment\n", "                 )"]}, {"cell_type": "code", "execution_count": 118, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\model_selection\\_split.py:652: Warning: The least populated class in y has only 8 members, which is too few. The minimum number of members in any class cannot be less than n_splits=10.\n", "  % (min_groups, self.n_splits)), Warning)\n"]}, {"data": {"text/plain": ["<dml_ate_iv.DMLATEIV at 0x222d55666a0>"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["# We fit DMLATEIV with these models\n", "dmlate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 119, "metadata": {"collapsed": true}, "outputs": [], "source": ["# We call effect() to get the ATE\n", "ta_effect = dmlate.effect()"]}, {"cell_type": "code", "execution_count": 120, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.137\n", "Standard error: 0.056\n"]}], "source": ["# Comparison with true ATE\n", "print(\"ATE Estimate: {:.3f}\".format(ta_effect))\n", "print(\"Standard error: {:.3f}\".format(dmlate.std))"]}, {"cell_type": "code", "execution_count": 121, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate Interval: (0.027, 0.248)\n"]}], "source": ["# We can call normal_effect_interval to get confidence intervals based\n", "# based on the asympotic normal approximation\n", "lower, upper = dmlate.normal_effect_interval(lower=2.5, upper=97.5)\n", "# Comparison with true ATE\n", "print(\"ATE Estimate Interval: ({:.3f}, {:.3f})\".format(lower, upper))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE and CATE via DMLIV"]}, {"cell_type": "code", "execution_count": 122, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from utilities import SelectiveLasso, SeparateModel\n", "from sklearn.linear_model import LassoCV, LogisticRegressionCV\n", "from econml.utilities import hstack\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DMLIV we also need a model for E[T | X, Z]. To allow for heterogeneity in the compliance, i.e.\n", "# T = beta(X)*Z + gamma(X)\n", "# we train a separate model for Z=1 and Z=0. The model for Z=1 learns the\n", "# quantity beta(X) + gamma(X) and the model for Z=0 learns gamma(X).\n", "model_T_XZ = lambda: SeparateModel(model(), model())\n", "\n", "# We now specify the features to be used for heterogeneity. We will fit a CATE model of the form\n", "#      theta(X) = <theta, phi(X)>\n", "# for some set of features phi(X). The featurizer needs to support fit_transform, that takes\n", "# X and returns phi(X). We need to include a bias if we also want a constant term.\n", "dmliv_featurizer = lambda: PolynomialFeatures(degree=1, include_bias=True)\n", "\n", "# Then we need to specify a model to be used for fitting the parameters theta in the linear form.\n", "# This model will minimize the square loss:\n", "#        (Y - E[Y|X] - <theta, phi(X)> * (E[T|X,Z] - E[T|X]))**2\n", "dmliv_model_effect = lambda: LinearRegression(fit_intercept=False)\n", "\n", "\n", "# Potentially with some regularization on theta. Here we use an ell_1 penalty on theta\n", "# If we also have a prior that there is no effect heterogeneity we can use a selective lasso\n", "# that does not penalize the constant term in the CATE model\n", "#dmliv_model_effect = lambda: SelectiveLasso(np.arange(1, X.shape[1]+1), LassoCV(cv=5, fit_intercept=False))\n", "\n", "\n", "# We initialize DMLIV with all these models and call fit\n", "cate = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(), \n", "             dmliv_model_effect(), dmliv_featurizer(),\n", "             n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "             binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "             binary_treatment=True # a flag whether to stratify cross-fitting by treatment\n", "            )"]}, {"cell_type": "code", "execution_count": 123, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\model_selection\\_split.py:652: Warning: The least populated class in y has only 8 members, which is too few. The minimum number of members in any class cannot be less than n_splits=10.\n", "  % (min_groups, self.n_splits)), Warning)\n"]}, {"data": {"text/plain": ["<dml_iv.DMLIV at 0x222d55126a0>"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["cate.fit(y, T, X, Z)"]}, {"cell_type": "code", "execution_count": 124, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAeQAAAFJCAYAAABKLF7JAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nBvh/qvMGYoIVPLgAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# To get the CATE at every X we call effect(X)\n", "dml_effect = cate.effect(X)\n", "plt.hist(dml_effect, label='est')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 125, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.05707952 -0.1101906   0.12368182 -0.01630396  0.01638841  0.0206494\n", "  0.14824186  0.04105756  0.10153722  0.15699125 -0.03017467  0.04778027\n", "  0.08408015 -0.01652653 -0.00710907  0.05880703 -0.19192102 -0.15900693\n", "  0.03517143 -0.16334309  0.02435382  0.05080503  0.07929768]\n", "NA\n"]}], "source": ["# To get the parameter theta we call coef_. The first entry is the intercept of the CATE model\n", "print(cate.coef_)\n", "try:\n", "    print(cate.effect_model.lasso_model.alpha_)\n", "    plt.plot(cate.effect_model.lasso_model.alphas_, cate.effect_model.lasso_model.mse_path_)\n", "    plt.show()\n", "except:\n", "    print(\"NA\")"]}, {"cell_type": "code", "execution_count": 126, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ATE Estimate: 0.072\n"]}], "source": ["# We can average the CATE to get an ATE\n", "print(\"ATE Estimate: {:.3f}\".format(np.mean(dml_effect)))"]}, {"cell_type": "code", "execution_count": 127, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAecAAAFXCAYAAACYx4YhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also see how it compares to the true CATE at each target point and calculate MSE\n", "plt.title(\"DMLIV CATE as Function of {}\".format(X_df.columns[np.argmax(np.abs(cate.coef_[1:]))]))\n", "plt.scatter(X[:, np.argmax(np.abs(cate.coef_[1:]))], dml_effect, label='est')\n", "plt.scatter(X[:, np.argmax(np.abs(cate.coef_[1:]))], true_fn(X), label='true', alpha=.2)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Some Diagnostics of the Fitted Nuisance Models Across Folds"]}, {"cell_type": "code", "execution_count": 128, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARIAAADdCAYAAAB3/v32AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAASkAAADdCAYAAADn7YQ5AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nMklJkuTQZJKSJMmhySQlSZJD+/9D7I/B3DHIIAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARwAAADdCAYAAABpN81FAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nCYeiKJOhCYeiKJOhCYeiKJP5f0FC+5y71xFQAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# The property .fitted_nuisances is a dictionary of the form:\n", "# {'name_of_nuisance': [fitted_model_fold_1, fitted_model_fold_2, ...]}\n", "# then we can access all properties of each of the fitted models for each fold.\n", "# If for instance all nuisances have a linear form we can look at the standard deviation\n", "# of the coefficients of each of the nuisance model across folds to check for stability\n", "try:\n", "    nuisance_diagnostic(cate, 'model_T_XZ', 'coef', lambda ns: ns.coef_.flatten(),\n", "                        [c+\"_0\" for c in X_df.columns] + [c+\"_1\" for c in X_df.columns])\n", "    nuisance_diagnostic(cate, 'model_T_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "    nuisance_diagnostic(cate, 'model_Y_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "code", "execution_count": 129, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAe0AAAFXCAYAAACP5RboAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBo\ndHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzs3Xl8XGW9+PHPObNPZpLMTCZpui/Q\nDYRC1WspFYsCVwSLFgSuFpSi96Ui/kARqoilbAWRrReqIl6Uqy/QImhR2evCTltaum90b5NJMjOZ\n/cxynt8fJ5kkbSltlial3/dfmeWcec6ZyXznec73+T6aUkohhBBCiAFP7+8GCCGEEOLQSNAWQggh\njhIStIUQQoijhARtIYQQ4ighQVsIIYQ4SkjQFkIIIY4SErSPEbt27WLChAnMmDGDGTNmcP7553PJ\nJZfwt7/9rfyc+++/n6effhqAP/3pT3zqU59i9uzZ7N27l/POO48ZM2bwzjvv9NchHLIZM2aQSCT6\n7fXf79wtXbqUq6+++qDbdn4PuuPdd9/lpptuOqxtzjzzTFatWnVY2+zcuZPvfOc7h7XNQLNq1SrO\nPPPMD3zerFmzePbZZwFYuXIlM2fO5LOf/SyXX345kUikz9p3yimnsGvXrj7bf7tnn32WWbNmfeDz\n9v2cKKW4/vrreeSRR/qyeWIf9v5ugDhy3G43f/7zn8u3d+/ezVe/+lVsNhvnnHMO3/3ud8uPPf30\n01xzzTXMmDGDp59+mpqaGh599NF+aPXh63yM/eFg5+6jH/3oQbft/B50x+bNm2lsbOzRPg7Fnj17\n2Lp1a5+/zkCSz+e5+uqrueeee5g8eTK///3v+dGPfsTDDz/c30074rZs2cLNN9/Mu+++y9ixY/u7\nOccUCdrHsCFDhnD11VfzyCOPcM4553DDDTdw/PHH09jYyKpVq9i1axexWIxHH32UZDLJrFmzeOyx\nx3j55ZdZuHAhhUIBt9vN9ddfzymnnMKCBQtYsWIFkUiEcePGcffdd7Nw4UKef/55TNNkyJAh/OQn\nP6Guro5Zs2YxadIkli9fzt69e5kyZQq33HILuq6zZMkS7rvvPkzTxOv1cvPNNzN+/HiWL1/O3Xff\nTTabRdd1rrrqKqZPn77fcY0bN47XX3+df/zjH7zwwgvous727dtxu93ceeedjBkzZr9tfvGLX/DU\nU09ht9sZMWIE8+fPx+/38+CDD/LXv/4Vm83GqFGj+PGPf0w4HCaZTHLbbbexceNGCoUCU6ZM4Qc/\n+AF33XXX+567q666iltuuYVnnnmGdDrNrbfeyvLly7HZbHzmM5/hmmuuYc6cORx//PHMnj2bLVu2\ncNtttxGPxymVSsyaNYsLL7yQN998k3vvvZdhw4axadMmisUiN998M4MHD+aBBx4gmUwyZ84cbrzx\nRubMmcP27dvRdZ0TTjiBefPmoev7D7D9/ve/Z/369eTzeb72ta9x4YUXAhzwvT7ppJO48cYbaWxs\nZPbs2bhcLqZPn85FF13EO++8wyWXXMKLL77IsGHDeOihh0in01x33XXd+ix0NmvWLE444QRWrFhB\nNBrlS1/6Es3Nzbz11ltks1nuu+8+xo0bR0NDA3PnzmX37t0opbjgggu48sory8f5m9/8Bp/Pt1+w\neb/2tVu1ahU+n4/JkycDcOGFF3L77bcTi8UIBAJd9vXzn/+cl156iVwuRzab5frrr+ess85iwYIF\n7N69m6amJnbv3k1dXR0//elPqa2tZenSpdxyyy1omsZHPvIRTNM84P/tmWeeyXnnnccbb7xBa2sr\nV155JcuXL2fNmjXY7XYWLlxIXV0dmzZtYt68ecTjcTRN44orruCCCy4ArBGdxYsXU11dzYgRI8r7\nzufz3H333bz99tuUSiUmTpzIjTfeiM/n69KG3/3ud1x00UUMHjz4gG0UfUiJY8LOnTvVpEmT9rt/\n48aN6uSTT1ZKKXX99derX/3qV0oppb7yla+ov//970oppZ588kn1jW98Qyml1NatW9V5552notFo\nefupU6eqdDqtHnjgAXXOOeeoQqGglFLqqaeeUv/v//2/8u3HH39cXXnlleX9X3311apUKqlkMqlO\nP/109frrr6umpiY1efJktWbNGqWUUs8995yaPXu2isfj6uyzz1Y7d+5USinV0NCgPvnJT6rdu3fv\nd0xjx45VLS0t6sknn1STJ09We/fuVUopNW/ePPWDH/xgv+e/+OKL6uyzz1bxeFwppdTtt9+uHnro\nIbVo0SJ18cUXq3Q6rZRS6oEHHlBXXHGFUkqpG264Qf32t79VSilVLBbV97//ffXLX/7yoOfujTfe\nUJ/73OfKr3HNNdeoYrGoDMNQX/7yl9Ubb7xRfg8KhYI699xz1erVq5VSSiUSCfXZz35WvfPOO+qN\nN95QEyZMUGvXrlVKKfXII4+oL3/5y/u93lNPPVVub7FYVD/60Y/Utm3b9jv+6dOnq5/85Cfl8zpl\nyhS1cePGg77XnY/lqaeeUt/5zneUUkrdf//9aurUqerxxx9XSik1c+ZMtXLlym59Fvb1la98RV11\n1VVKKaVWrFihxo4dq1566SWllFK33XabuvHGG5VSSn35y19Wv/71r8vn7fzzz1fPPPOMWrt2rZoy\nZYqKRCJKKaV+/OMfq+nTp5eP4WDt+/vf/66eeeaZ8vlsN23aNLVu3bou9+3atUvNmjVLZbNZpZRS\nzzzzjDrvvPOUUtZn6NOf/rRKJpNKKaX++7//W91///3KMAx12mmnqddee00ppdTixYvV2LFjy5/3\nfd+v22+/XSml1F//+lc1fvz4chu+9a1vqYULF6pCoaA+/elPq+eee678vk6bNk0tX75cvfDCC+rc\nc89VyWRSFQoF9Y1vfEN95StfUUoptWDBAjV//nxlmqZSSqmf/exn5c/G9OnT1bvvvtulLZ2/M8SR\nIT3tY5ymabjd7kN+/quvvkokEuGrX/1ql33s2LEDgEmTJmG3Wx+rJUuWsGrVKmbOnAmAaZpks9ny\ndtOnT0fXdXw+HyNGjKC1tZXly5dz/PHHM3HiRADOPvtszj77bP75z3/S1NTEt7/97S6vu2HDhoP+\n2j/hhBMYNGgQABMnTuSFF17Y7zmvv/46//mf/0lVVRUAc+bMAayh6i9+8Yt4vV4ALrvsMn7+85+T\nz+f5xz/+wapVq1i0aBEAuVzukM8hwGuvvcacOXOw2WzYbDb+7//+D4CnnnoKgG3btrFjxw5++MMf\nlrfJ5XKsXbuWMWPGMHjwYCZMmFA+rvbtOps8eTL33nsvs2bN4rTTTuPyyy/v0qvq7JJLLgGgrq6O\nqVOn8vrrr2Oz2Q76XrebPn06d9xxB8VikVdeeYVvfvObvPrqq3zqU58iGo3ykY98hEceeeSwPwsH\nctZZZwEwbNgwAKZNmwbA8OHDeeutt8hkMixfvpxf//rXAPj9fr74xS/yr3/9i8bGRqZOnUo4HAbg\n4osv5pVXXgE++LPafp+maV3uU0phs9m63DdkyBDuuusuFi9ezPbt21m5ciXpdLr8+Mc//vFyz3Xi\nxIm0trayceNG7HY7U6ZMAeC88847aG7C2WefXT4PNTU1jB8/vnweWltb2bZtG4ZhlJ9XV1fH2Wef\nzb///W9aW1s566yzym2YOXMmjz32GAD/+Mc/SCaTvPbaawAUCgVCodD7tkMceRK0j3GrVq06rGtS\npmkyZcoU7rvvvvJ9e/fupba2lhdeeKEc4Nqfe+WVV/Jf//VfgDX01vnLuPOPBU3TUEpht9u7fDEq\npdiwYQOlUokxY8bwxz/+sfxYY2MjwWDwoO090Gvsy2azdXnNRCJBIpHY70vaNE2KxWL57/vvv788\n1J5IJPb7Qj+YfY9z7969XdpaKpXw+/1drs83Nzfj9/tZsWLFIR3XsGHDeOGFF3jzzTd54403+NrX\nvsa8efMOmHzVeSjaNE3sdjulUul93+ulS5eW76uqqmLChAksWbKEVCrFjBkzeOihh3jxxRf5zGc+\ng6Zp3fosHIjT6exy2+FwdLltmuZ+23Z+3zo/1jnYflD7AOrr67sknhUKBeLxeJchdIA1a9bwrW99\ni69+9atMnTqVj33sY9x8880feKz7trv9x++BdD4P+54DsD4/B/qBcSjn4Yc//CFnnHEGAOl0GsMw\n3rcd4siT7PFj2NatW3nooYe44oorDnmbKVOm8Oqrr7JlyxYA/vnPf/L5z3/+gD3N008/nUWLFpFK\npQDrOtoPfvCDg+7/5JNPZsuWLWzatAmAl156ieuuu45Jkyaxfft23n77bQDWrVvHOeec0ytJV6ed\ndhovvPBCuZ0LFizg0UcfZdq0aTz55JNkMhkAHnvsMT72sY/hdDo5/fTTefTRR1FKkc/n+eY3v1nu\nLR+KKVOm8NRTT2GaZjnBqf3YAEaNGtUlcbA9C3316tUH3a/NZit/Mf/+979nzpw5nH766Vx33XWc\nfvrprF279oDbtffU9+zZw+uvv86UKVMO+l7bbDYKhUJ5+7POOot77rmHKVOm4PP5GDlyJA8//HC5\np9edz0J3+Hw+Tj75ZH73u98BkEwmefrppznttNOYOnUqr776Kg0NDV2O+VDbd/LJJxOPx1m+fDkA\nTz75JJMmTaKysrLL895++21OPPFEvva1r/Hxj3+cl156iVKpdNB2jxs3DqUU//znPwHrc/9+ow2H\nYvTo0djtdp5//nnA+oH73HPPcdppp/HJT36SZ599tvzDtPMPw9NPP53f/e535PN5TNPkxz/+Mffc\nc0+32yF6n/S0jyG5XI4ZM2YAVs/K5XJx7bXX8qlPfeqQ93Hccccxb948rr322nLPeOHChVRUVOz3\n3IsuuojGxka+9KUvoWka9fX1zJ8//6D7r6mp4e677+b666+nVCrh8/m49957CQaDPPDAA9x1110Y\nhoFSirvuuouhQ4ce1jk4kDPOOIPNmzdz6aWXlo/xlltuwev1snfvXi666CJM02TEiBHcfffdAPzo\nRz/itttu4/zzz6dQKHDaaaeVk50OxVVXXcVtt93GjBkzKJVKnHvuuZx99tm8/PLLgNWTeuihh7jt\nttv41a9+RbFY5Lvf/S6TJ0/mzTfffN/9Tpo0iQcffJCrrrqKu+66i7feeotzzz0Xj8dDfX39+07t\nMQyDL3zhCxQKBW688UZGjRoF8L7v9XHHHYfL5eLCCy/kj3/8I5/5zGe45ZZb+P73vw90fPmfeuqp\nQPc+C9119913M2/ePP70pz+Rz+c5//zz+eIXv4imaVx33XVcfvnlVFRUcNJJJ5W3OZT2ORwO/ud/\n/od58+aRzWaprq7mzjvv3O/1zzvvPJ5//nk++9nPYpom06dPp7W1tfyD4EAcDgcPPvggc+fO5Z57\n7mHChAk9GpZ2OBw89NBD3HrrrSxYsIBSqcS3v/1tPvGJTwCwYcMGZs6cSWVlJePHjycWiwHwrW99\nizvvvJMvfOELlEolJkyYwA033NDtdojep6n3G4cSQgghxIAiw+NCCCHEUUKCthBCCHGUkKAthBBC\nHCUkaAshhBBHCQnaQgghxFFiwE/5ampK9ur+AgEvsVimV/cpDo+8B/1Lzn//k/egfw308x8O+9/3\nsWOup2232z74SaJPyXvQv+T89z95D/rX0Xz+j7mgLYQQQhytJGgLIYQQRwkJ2kIIIcRRQoK2EEII\ncZSQoC2EEEIcJSRoCyGEEEcJCdpCCCHEUUKCthBCCHGUkKAthBBCHCUkaAshhBBHiQFfe1wIIT5M\n1m+PUUDD0d8NEUcl6WkLIcQRki+UuOcPK3hw0cr+boo4SknQFkKIIySWNCiWFDsae3f1QnHskKAt\nhBBHSDSRAyCeNMjkiv3cGnE0kqAthBBHSDRplP9uHMDrOYuBS4K2EEIcIe09bYDGqARtcfgkaAsh\nxBHSuafdIEFbdIMEbSGEOEKiic7D49l+bIk4Wsk8bSGEOEKiyRxupw3TVNLTFt0iQVsIIY6QaMIg\nVOnGbteJxDIopdA0rb+bJY4iPQraK1eu5O677+axxx7rcv8zzzzDb37zG2w2G2PHjmXu3Lnous4F\nF1yA3+8HYOjQodxxxx09eXkhhDhqZI0iWaNIYHAlfp+L7Q1JEpkCVRXO/m6aOIp0O2g//PDD/OUv\nf8Hj8XS5P5fLcd9997F48WI8Hg/XXnstS5Ys4fTTTwfYL8ALIcSxINaWhBasdFEb8gFWBrkEbXE4\nup2INnz4cBYsWLDf/U6nk8cff7wczIvFIi6Xi/Xr15PNZrniiiu47LLLWLFiRfdbLYQQR5lo0pru\nFfS7GRKuACSDXBy+bve0zznnHHbt2rXf/bquU1NTA1i96kwmw9SpU9m4cSOzZ8/moosuYtu2bXz9\n61/n2WefxW4/eBMCAS92u627zTygcNjfq/sTh0/eg/4l5//IK7wXBWDEkCoGh62edjJXlPeinxyt\n571PEtFM0+SnP/0pW7duZcGCBWiaxqhRoxgxYkT57+rqapqamqivrz/ovmK9XDUoHPbT1CR1f/uT\nvAf9S85//9i+Ow6AXYMhbUH7vV1xeS/6wUD/HzjYD4o+mad90003YRgGDz30UHmYfNGiRcyfPx+A\nxsZGUqkU4XC4L15eCCEGnPbCKkG/i8oKJ16XXeZqi8PWaz3txYsXk8lkOPHEE1m0aBEf/ehHufzy\nywG47LLLuPDCC5kzZw6XXnopmqZx++23f+DQuBBCfFjE2kqYBivdaJpGXdDLzkgS01Toukz7Eoem\nR1Fz6NCh/OEPfwDg/PPPL9+/fv36Az7/Zz/7WU9eTgghjlrRpEGF247LYeXoDAp62Lo3QUsiR7ja\n8wFbC2GRMqZCCNHHlFJEEwbBSnf5vrqgF5CFQ8ThkaAthBB9LGMUMQolgn5X+b5BbUFbpn2JwyFB\nWwgh+lj7QiFdetqB9p62JKOJQydBWwgh+lj7OtqBTj3tuqB1Hbuhl6e1ig83CdpCCNHHOpcwbed2\n2qnyOeWatjgsErSFEKKPdS5h2tmggJeW1hyFYqk/miWOQhK0hRCij3Vc03Z1ub8u6EUBESmyIg6R\nBG0hhOhjHde09+lplzPIJWiLQyNBWwgh+lg0aVDpdeCw65SyWcxCAehIRmuUZDRxiCRoCyFEH1JK\nEUsaBCrdqFKJ7Tf9kM0P/hyQudri8EnQFkKIPpTMFigUTYJ+F8VYlGIsRnLdOgDC1R40TaqiiUMn\nQVsIIfpQrFNhlUJzMwC5SBOqWMRu0wlXeSRoi0MmQVsIIfpQtLy6l4tCixW0MU0KzU2AlUGeyBTI\n5Ar91URxFJGgLYQQfahjHe2OnjZAvrER6JyMJhnk4oNJ0BZCiD7UXlgl4HdR7BS0C21BW5LRxOGQ\noC2EEH0o1qmwSnl4HMg3NgCyRKc4PBK0hRCiD0UTOTSg2uei0NyM7vMBUIi09bTbV/uS4XFxCCRo\nCyFEH4omDap8TmzKpBiL4qofjDMULF/TDlS6cNh1GR4Xh0SCthBC9BGzrbBKsNJNMRYDpbCHQrjr\n6ynGopiFPLqmURewpn0ppfq7yWKAk6AthBB9JJHOUzIVQX/H9WxHTQ2ewfWgFIVIx7SvXL5EIp3v\nz+aKo4AEbSGE6CPRAxRWcYRqcNfXA1CIWMlokkEuDpUEbSGE6CPlwipdetphq6dNp7nakowmDpG9\nvxsghBAfVuXCKpVuiqusoG0P1eDxO4GOaV/S0xaHSoK2EEL0kfI62pUuq2yppuEIBnGHKkDTygVW\nylXRJGiLDyDD40II0Ue6lDBtacZeHUCz29GdTuzBIPm2udo+j4MKt1162uIDSdAWQog+EkvmsOka\nlS6dYiyGo6aGpkwLSSOFs3YQpXgc0zDQNI26oJdILItpyrQv8f4kaAshRB+JJgyqfU5KrdYcbWpC\n3P/Sb/ifl3+Po64O6KiMVhfwUjIVzW1D6kIcSI+C9sqVK5k1a9Z+97/88svMnDmTiy++mD/84Q8A\nmKbJTTfdxMUXX8ysWbPYvn17T15aCCEGtJJpEk8ZBCrd5YVCWkN+6tdOIvmWB0dtLdCRQT5IrmuL\nQ9DtRLSHH36Yv/zlL3g8ni73FwoF7rjjDhYtWoTH4+HSSy9l+vTpvPPOO+TzeZ544glWrFjB/Pnz\nWbhwYY8PQAghBqLWVB6luk73anJ60JWOI+UjHbK+O/ddOKQhmuEjo0P902gx4HW7pz18+HAWLFiw\n3/1btmxh+PDhVFVV4XQ6mTx5MkuXLmXZsmVMmzYNgEmTJrF69erut1oIIQa4AxVWiRatfpLNtLNT\ns55X2HeutvS0xUF0O2ifc8452O37d9RTqRR+v798u6KiglQqRSqVwte2ug2AzWajWCx29+WFEGJA\na19Hu3NPO57tSDLbk8yCrpczyGXalzgUvT5P2+fzkU6ny7fT6TR+v3+/+03TPGDQ31cg4MVut/Vq\nG8Nh/wc/SfQpeQ/6l5z/vmestoLxyKEBtH/FQNfJZTp6SrGWLO7aWkpNkfL7Eax009Sak/fnCDha\nz3GvB+0xY8awfft24vE4Xq+XpUuXMnv2bDRNY8mSJZx77rmsWLGCsWPHHtL+YrHe/dUZDvtpakr2\n6j7F4ZH3oH/J+T8ydu5tBcCOSaahEXt1NaWEHfQSumkj2VRArwmTW72Khh0RbB4PtdVuNuyIs3tP\nHKejdzsrosNA/x842A+KXgvaixcvJpPJcPHFF3PDDTcwe/ZslFLMnDmTuro6zjrrLF599VUuueQS\nlFLcfvvtvfXSQggx4LQXVgl47DTGYjjHHI895ybrbcVecGNPeWFwDay2rmvbRo6kLuhl/Y44kXiW\noWHfB7yCOBb1KGgPHTq0PKXr/PPPL99/5plncuaZZ3Z5rq7rzJs3rycvJ4QQR41oIofdpuPOJUEp\n4jV1aM06hidF0ZGnMl7H3kAFVVgZ5O6RI7sko0nQFgcixVWEEKIPRJMGQb+LUrQFgCaPNeSZ96TJ\nVVhDs3uwFg5pL7AiC4eIDyJBWwghelmxZJJI5wn42xYKAeK4ALD5TTxB63nNaSubvGOudnsGuSzR\nKQ5MgrYQQvSyWHlJzo7pXom8lVjmCzgZMsyK2qnWEths5Z52uNqDrmk09HICrvjwkKAthBC9rH1J\nzs6FVYyMDVMr4baFqPHXU7IVUAkXtnBNuZSp3aZTU+2WudrifUnQFkKIXtaxJKeLYksLStNQaSeG\nO439L9vZ9Zft5LwJnDkviSG1mOk0pVQKsK5rJzMFMrlCfx6CGKAkaAshRC9r72kH2nraxdAQdNNG\n0ZnijOYVjNv4JllvAg2NvVXWUHk+sk8505hc1xb7k6AthBC9rGOOto1iPEYqWA+AzbR60yGjFdNZ\nAqBZt5LPCm3JaO2rfUkGuTgQCdpCCNHLYm2LhVSVMqAUUb/Vm3bnrVLONkwCRSsxLZG1ymW0X9du\nX+1LrmuLA5GgLYQQvSyayOF06DiSMQBabVYgruq0/kIgnsfUTIppmastDp0EbSGE6GVWYRU3xRar\nsEqq4EShCLYmys/xNScwPEkc6QqyFa5yT7va78Jp12WutjggCdpCCNGLjEKJVLbQZY52Ieeg4MoQ\nSqcp2BwA+FqjZL1JdGWjcfgwCpFGlFLomkZtwEtDLINS6mAvJY5BErSFEKIXxcvTvazM8YLugoId\nw52iKp8hU12HUVFNOBcl77J6081VYcxsllLSKm86KOjByJdoTef77TjEwCRBWwghelFHYRWrp512\nVVsPaEl0FFpNLVr9ELymgVtZSWitmlWXvFAuZyrJaOLAJGgLIUQvKk/38rsotjSTCQwGwFm0pnsl\ngzUwbJj1nLZL3IbhBjoyyCUZTbwfCdpCCNGLyj1tr51iPE6rvwYAX9YK2qvdlaz3BwCojCbJuzLY\nMj6KekcGeUdPW5LRRFcStIUQohe197Sr2+ZoJx3W0Hcw0dbTrq1hW2UIgIpYlKwngb3oJFLtL6/2\nJT1t8X4kaAshRC+KthVW8eWtpLJMyU3BYRBKpClpOunKAFFvJUW7k3AuRs5tzd1uDNaVe9o+j4MK\nt51GWe1L7EOCthBC9KJoMofHZUNrjVLSbJQKDgx3imA2S8JbjdJ10DRyoTqChQTKZn0Nt/rryEci\n5WledUEvkViWkmn25+GIAUaCthBC9KJooq2wSnMzGUcVoGE6krjMIq1t5UwBjLrB6CiqUtbXcE6r\nRBkGpdY4YC0cUjIVLa25/jgMMUBJ0BZCiF6SNYpkjSKB9ulezioA7MoaKo9WBnG39ayzg4cA4I/l\nKNryKMOHAvIN+y4cIsloooMEbSGE6CXRfQqrtM/R9uasoN0aqmFCdQUaEAnVAuCPxshVJHAaFbR6\nXR1LdMpcbXEAErSFEKKXxPYprJLyhwGoSlkTslO1tVSlllJlL7KtIoBCI5yLknVZyWh7gzUU9p2r\nLcloohMJ2kII0UvKPW2PnVI8TspZRUkvEkxYgTcRrKG6uIMqs5mEplOoDhHOx8jbrbW1W3yhck+7\nNmANj0tPW3QmQVsIIXpJubAKGUw0cqYHw5MilDJIO7wUnC6CxKlW1pKdqn4IbrOAO28tIpJxhso9\nbbfTTsDvkqAtupCgLYQQvaS9p+03kuQcPkCn4EpRZRjEfQGqbUWcWpFqzRouLw4ZCkBlSwFTK1Ey\nq63VvtqmedUFPLQkDPKFUr8cjxh4JGgLIUQvab+m7cklSDusJDS9LUDHKoPU2KyEtEDbfa3hOgD8\nsVZyniR2w0dOlSjGokDHde1ITDLIhUWCthBC9JJo0qDCbYdYtDzdy11sBaA1FCaomtFtHmrdGgB7\nqqy65DW5GDlPCl3Z2BMKlRcOqZNypmIf9u5sZJomc+fOZcOGDTidTm699VZGjBgBQFNTE9dee235\nuevWreN73/sel156KRdccAF+v1WHd+jQodxxxx29cAhCCNH/lFJEEwa1AQ+F5mYybUHbn7F61clw\nmNGlPTgq6ghU1uBJZ9ll83CK20utESVrt9bObqpsyyCfeELHtC/JIBdtuhW0X3zxRfL5PE888QQr\nVqxg/vz5LFy4EIBwOMxjjz0GwDvvvMO9997Ll770JQzDutbT/pgQQnyYZIwiRqFE0O+isLmZlGsc\nSjM7FgoJhanR1uD0fAR/YCSRlwUaAAAgAElEQVSBnU3sKbqx1Q8hsHUTKKf1PFewnEE+SFb7Evvo\n1vD4smXLmDZtGgCTJk1i9erV+z1HKcUtt9zC3LlzsdlsrF+/nmw2yxVXXMFll13GihUretZyIYQY\nQNoXCglWusk3N5NxVmO40oQSRQq6jVKlHx8ZHJ5afMExVJMANGzDhwNQEVUoFAUtQKFtta+aKje6\npslcbVHWrZ52KpXC5/OVb9tsNorFInZ7x+5efvlljj/+eEaPHg2A2+1m9uzZXHTRRWzbto2vf/3r\nPPvss122EUKIo1X7dK+QVyebMiiFHRieZgJpg7g3SI3DQDPB4a7F5QkQshegAJnaely0ra0dzmAv\nVJNrXQqA3aYTrnbLtC9R1q2I6fP5SKfT5dumae4XfP/yl79w2WWXlW+PGjWKESNGoGkao0aNorq6\nmqamJurr6w/6WoGAF7vd1p1mvq9w2N+r+xOHT96D/iXnv/cVNjUDMNynSDmtzHFsSRymScwfJGxr\nBVNj8DCrIzMsUAURaK0NUIuVjPaeJ0h1fBB7DIP/CHrRbDaGDapk6bpGPBUufF5nPx3dh8/R+j/Q\nraB96qmnsmTJEs4991xWrFjB2LFj93vOmjVrOPXUU8u3Fy1axMaNG5k7dy6NjY2kUinC4fAHvlas\nl4eFwmE/TU3JXt2nODzyHvQvOf99Y/seK0vcEW8pZ447TOu+RCBETWEvdleAlqhBOOwk5LZW/HrP\nZies26gzYqxx5qgGGiqD7Fm/DWdtLUGfFajXbGpi9ODKI39gH0ID/X/gYD8ounVN+6yzzsLpdHLJ\nJZdwxx13MGfOHBYvXswTTzwBQDQapaKiAk3TyttceOGFJJNJLr30Uq655hpuv/12GRoXQnxoxNoK\nq3iNZNuSnODLW0E7WRMmqCI43LXl59dUD8dBgUhRQwvXEc7HKGCNKsY9IQoR67q2LBwiOutW1NR1\nnXnz5nW5b8yYMeW/g8Egf/7zn7s87nQ6+dnPftadlxNCiAGv/Zq2M9Va7mkHUp2CNutwej5Sfr7D\nVU1Q30RTqRLviOFkGvfgTFpBO2cPkG9spOJEGBRoX6JTgraQ4ipCCNErokmDSq8DM9pC2llN3pkl\nGC8CYA9XYddKODy15LIFCm1lSWucYKJTrA0A4GvOUrTnoFRdrkEuc7VFZxK0hRCih5RSxJIGgUo3\nmeYoebsXw5MimCyQcPkIuKyhc91Rw+O/eovFT6wEoNZbAUCs0rpuHc7FyXpS2AtempojAFT7XTgd\nuvS0BSBBWwgheiyZLVAomgT9LuKtVmWzojOJzygS9wUJqWY03UFLi41susDWzVameX2lVca00WcN\ngdcaUbIuKzhvz1r70TWNuoCXxmgWpdSRPjQxwEjQFkKIHoq1FVYJeW0k81aqkE21LQpSHaK6tBuH\nO0zDLitjOZ00yKQMBvmtqWENNjv4q6jNx8i25e9GNDeqaA2v1wW9GIUS8VT+SB6WGIAkaAshRA+1\nJ6HV6rlyzXFvoW26V6iGEFEcnjr27motb9McSRNwObBhEjN9uIbUUVnMQNYFQNoRoNDcBMCgoNUT\nlwxyIUFbCCF6qH0d7UApXc4cr8rGAcjX1eAlh90VpqFT0G6JpLBpGkEnxKnEVmslnHmbC5h6kZKq\n7ljtK9C22pckox3zJGgLIUQPtfe0/fkkaUc1JVueUMIKsK6wH02DbK6SQr7EkBHWkHhzo7WQSK3H\nQwEH2SprXDyci2O4k9jzflJ79wIyV1t0kKAthBA91N7TdqQTZB0+DE+KQMLEsDnw+6zr0k0RK0N8\n7ImDcLntNEesoF1XYWWQR33WHO1aI0bOmUFDZ0ujrPYlupKgLYQQPRRL5NA0MFozoOnk3CmqU0Xi\nFQECZiM2u489u6ze+OBhVdQNrqQ1mqFQKBF2W8G8qTIIdju1RpS03Qr0u9NW4pnP46DCbZdpX0KC\nthBC9FQ0aVDtc9Eat4Kspiewm4p4ZZCAuQeHp5Y9O1up8LvwV7kZNLgKpSDalKbWYwXtuFaNvS5A\nqNCKUbCS0WJ5R/k1BgW9NMWzlEzzyB+gGDAkaAshRA+YbYVVgn4XrVlrHrW7ZE33SoVqqCZBiSC5\nTIH6YVVomkZd28IfLZEUNW4HGhBTVeg1TuzKxN6ioVDklR+zYP0QqAt6KZmK5tZcvxynGBgkaAsh\nRA8k0nlKpiLk1UmZbgD8OStznEEhbJoimbKuW9cPtTLL24N2c2MKh64TcDmIU4WqsobFw9kkeVcK\nvVCF0bBPOVMZIj+mSdAWQogeiLYVVqmzGaSd1SitRCBl9bSd4bZA22gNgQ8eZmWO1w6yMspb2pLR\nwm4HWVwUgtaweK0RJe9MoZsOdmzbCnQkozVIMtoxTYK2EEL0QPt0r1ApQ8ZRSdGVJpQoYaLhrSoC\nGju2KlxuO4EaK/DaHTYCNRU0R1IopcrXtWMhq6xprREjaysA8N5uqwZ5XUAKrAgJ2kII0SPt071c\n2TSmbifrSRNIlEh4/ARowOYIkojnqR9qXc9uF6qtoFgwaY1lyxnkMWcIrcpNXT5GWlnlUCOJtmva\n7QVWJGgf0yRoCyFED7T3tM2MFbwLzgRewyTuDxJUTeRL1pB4/bCqLtvV1PoAa4i8vaed0GvRgna8\npRyFhHVfKmdlkLucNgJ+lyzReYyToC2EED3Q3tPOZawkMoeySpVmQiHcWp5Ewuoh17ddz25XU2cF\n7eZIqtzTbtVr0EJWDzuYylBwZDHzvvI2g4JeogkDo209bnHskaAthBA9EEvksOkaqbb8MJ9hZY7r\ndQEAGhsd2B16OUi3C7X3tBtTeOw2/A4bUbMCPdyWjJaPU3CmsBc9xJqjQEcGeVNMktGOVRK0hRCi\nB9oLq6SKDsCkOm1ljrvD1vSvPbvs1A2uxGbr+nXr8Tqp8DnL5Uxr3E4SRY1SsGNt7ZzNGnpfv24D\nAIPaktHkuvaxS4K2EEJ0U8k0iacMarw6KZsPbFmCSStxzFNtoHCSzbn2GxpvF6rzkU7myWby1Lqd\nKCAdGgUOnbp8jFTbV/SuPdYSneW52nJd+5glQVsIIbqpNZVHKRikGxRtbgx3hkCyRNbuosoeIV+s\nArRyUZV9dSSjpQm3JaOlXKPQahwE8wnShtVbj8at6+Udc7UlaB+rJGgLIUQ3tRdWqSxYw9hZT4qq\nZIm4P0illqK11YOua9QNqTzg9uVktMYUtW3JaHFbGD3kQkfhSxQo6QXyGeuxUJUbm67Jal/HMAna\nQgjRTdGkFawdhlUIRdlasSnIBYLomqIp4iI8yI/DYTvg9qFO077ae9oxswKtxvq71mgl70piMyow\n8gXsNp2aao/0tI9hErSFEKKb2nvaJcNaectTtDLHtbDVs25NePebn91ZZbUHu0OnOZKi0mHDpes0\nGwpHfRiwKqPl7Vk0NLbu2gNYyWipbIFUttBnxyUGLgnaQgjRTe2FVYyCVemsKhsDwFNrFURJpire\n93o2gK5rhGp9xFsylEomYY+DFiOPe8R4AOryMTJYK4e9t80K2pKMdmyToC2EEN3UXlglXXKgqwzB\nlHXbW5UlX/BQLNoZdJCgDVYymmkqYs0Zat1OSgqy/uPQqh3U5aPES1YyWmSP9YNgkKz2dUyToC2E\nEN0UTeRw6jo5zY3pyBBoLVLSNCor4sTjXoLhCtwex0H30X5du7mx47p2q16HVuPEWSpgS4PSTNJx\nawi+Tlb7OqbZu7ORaZrMnTuXDRs24HQ6ufXWWxkxYkT58f/93/9l0aJFBINBAG6++WZGjhx50G2E\nEOJoE00aDHJrkFFk3VlroRBvJfX2Eonkwa9nt2vPIG+JpKgd7rf+LuiMrAuQ35wmnEmTDzlxZCow\nTSU97WNct4L2iy++SD6f54knnmDFihXMnz+fhQsXlh9fs2YNd955JyeeeGL5vueff/6g2wghxNGk\nUDRJpPOM9SnAhuFK4y4oYrVWZyWRrOCECR8ctIPhCjTNqkE+vq2n3ZTLM374SPKv7qLOiNJid+LK\nVdIQiVJfF8Tp0CVoH6O6NTy+bNkypk2bBsCkSZNYvXp1l8fXrFnDL3/5Sy699FJ+8YtfHNI2Qghx\nNIm1Xb+uKFmLd9i0rpnjyWTF+1ZC68zhsFEV9NISSVHttGPTNCLZPN7RVqenLh8jq1mvsXnnbjRN\noy7gpSGWQSnV68clBrZuBe1UKoXP11H83mazUSwWy7c/97nPMXfuXH7zm9+wbNkylixZ8oHbCCHE\n0STWljmuF61rzb5CCwCeGh3T1LA5A/j8rkPaV02tj7xRIpMwqHE7aMrl8QyeCC6d+kKUVmX1wPfu\n7Vg4JF8wiafyvX1YYoDr1vC4z+cjnU6Xb5umid1u7UopxeWXX47fb12bOeOMM1i7du1BtzmYQMCL\n3X7gwgTdFQ77e3V/4vDJe9C/5Pz33JodVs+6VNLRzTyVSWvhj4qqNMmUl5Fjag96njs/NnxUkM3r\nIhRyJYZWeWlsiOMOhbHX+fDvSJDPWd+VrU05wmE/o4dWs3R9hJypBux7uW1LM9UBL9Vt1+AHmoF6\n3j5It4L2qaeeypIlSzj33HNZsWIFY8eOLT+WSqU477zz+Nvf/obX6+XNN99k5syZ5HK5993mYGK9\nPBcxHPbT1JTs1X2KwyPvQf+S8987tu2OowE504GdKKGkNYTtrTLYEw0SGFbxvud53/fA47MyzN/b\n1ETVaGt4ff3uKK7BgyjuSBBIGuSrMthjdiKRBH6X1ZHZ8F4L9VXuPjzK7km25vjdz9+gbnAlX5h1\nan83Zz8D/X/gYD8ouhW0zzrrLF599VUuueQSlFLcfvvtLF68mEwmw8UXX8w111zDZZddhtPpZMqU\nKZxxxhmYprnfNkIIcbSKJg1cgEIj78hSHSmScblwe2wkUxVMPITM8XbtC4c0R1IMnlgDQCSbZ+yI\n48i9sZHafJyc044zFSaZzA74hUM2r4+gFDTsTpBszeEfgD8sjlbdCtq6rjNv3rwu940ZM6b89wUX\nXMAFF1zwgdsIIcTRKpYwaA9FqYoCVSmT5rCfIGDkq6hqW/v6UHh9LjwVDloaU0zqlEF+ypiTiPE3\n6vJRNuh+fFjJaGNHjwQG7rSvzWsjHX+vj3DKfwzvx9Z8uEhxFSGE6IZoIkelZmVvm/YUGqAC1rBm\nRfVgNE07rP3V1PpIJgz8gAZEcgXcw8aArjGk2EJCs/pYO3c14fM48HkcNMQGXoGVeDRDc2OKuiGV\n6LrGlnWRD95IHDIJ2kII0Q3RpEFAt4K2u9QMQEVIJ5+3Ex5cd9j7a6+M1tqUIeBy0JTNozsc2Goq\nCWbjZPJWD7wlYiX01gU9NMezFEtmbxxOr2nvZZ94ymCGjAzQ1JCiVeqk9xoJ2kIIcZiMQolUtoBb\ngW6WqDKs6V7+QLZtkZAPnp+9r47KaGnCbgfpYolMsYRr6BBspok7aVK05cnGrCA9KOClZCpaWnO9\nd2A9pJRi07oINrvOyONrOG68tVrZ5nVN/dyyDw8J2kIIcZhibQuFKFPHWUoSSlnLZNpDNtIZX7nX\nfDg6J6PVtl3XjmTzeEaOA6A2l8Bwp9AyToxcoVMN8oHTi22JpIm3ZBgxJoTTZWfU2Bp0m8ZmGSLv\nNRK0hRDiMEUTOZyAQqdgNwgkShR1Dc1nR3eE0fXDu54NUBX0YLPrtDSmCLs7ktHcI6wk30H5Fgx7\nDg2NHXsiA7IGeXtwPm5CLQAut4Nho4JEm9LEmtMH21QcIgnaQghxmGLJjszxtKdEIFEi6fOi6Rq+\n6sHd2qeu64TCFUSb04Sc1rztSDaPa5iVeT2s1Eyy7bfA1l0NndbVHhjJaEopNq+L4HDaGDEmWL6/\nPYBLb7t3SNAWQojDFE3kaJ/QlXNlcRYV+UoPSkHN4O5Pbwq1ra3tzFolnptyeeyVleiVFdTk4qRK\nVjBvbEhQ2zalbKAMjzfuseZkjzq+BrvDxhtrGtjekGTkcSFsdp3N65ukVnovkKAthBCHKZo0cGN1\ne20qBoAnpJPJuqkbXNPt/bYno6WaMvgdNiJZq7a4a+gI3IaBStsxtRKpljwuh41gpYvGAZKZXR4a\nn1hLQzTDLxevZeGfV2Nv63nHWzJEm2SIvKckaHdT6p3l7Lj9Fkpp+RAKcayJJgy8SoFS+PNWZnSg\nRlEoVmOzd/9rNdQpGS3sdhLPF8mXTNzDRwIQTmUw3ClUwkGpZFIX8BJNGKSyhR4fU0+YpmLLuiZc\nbjtDRwZYut4K4JFYltXvRWWIvBdJ0O6mhmefo3XrDlLLlvZ3U4QQR1g0aQ2Pu0ppQjlroRBb0InN\nFe7RfkPhCgCaG1OEO1VGa7+uPagQxXBm0ZROU1MrJ462rh2/ubaxR6/bU3t3xsmk84wZH8Zm01m6\nPkJ7bZmXlu1i+JgQdofO5nURGSLvIQna3bS3IY5LFdj25vL+booQ4ghrbc1h03SUViDYtjymVu3A\nHxjSo/06XXaqAh5aIinCbuv6tRW0hwEwwmwipVvztLfs3MNpJ9Zj0zX+tXJPvwbDTWs7ssYbYxl2\nRFKMzuxhmJZi1XstRFMGI4+rIRHP0dyY6rd2fhhI0O4me8HK2IzvlaIBQhxLskYR8taKXmm3IpAo\nkfI40Jw6NfUjerz/UK0PI1fEb1pd1Ug2j7NuEJrdTtiIkTatFb5274lSVeFk0nE17Iyk2N7YP6tW\nlUom721owutzUj+sujw0Pi6xlVP2Wp2al5ft5rgJ7YVWZIi8JyRod0Mpk6aiaAVtRzaNmRsYUy6E\nEH0v2mm6V8pdwJ8xyfpclEwdj79nw+MANbXWELmesHrwTbkCms2Gc+gwfJkUWcPqgcebrO+daSfX\nA/CvlXt7/NrdsWtbDCNXZMx4a3760vURdGVyfG4P41Lb8ZPnlVV7CA+twumysUWGyHtEgnY3pLfu\nwG1aiR+BQpLYmvX93CIhxJESS+Tw0F48JQGAHtQpmtVoWs+/UkNtGeSZ5jQum05TOYN8GLqpqEqU\nMFxpCnEdpRQnjgoR8Lt4c20DRqHU49c/XO21xo+fWEcknmV7Y4qRmb3UnjoJ/wkncHLLWrJGibfX\nRxh5XA3JhEHjnsQRb+eHhQTtbmhcv7n8tw2TzW+u7MfWCCGOpM49bW/RCljeGhsOV22v7L+9nGk0\nkqbW7aTFyFNSqnxduz4fI+dKoxftJBNZdF1j6kfqyRql8tD0kVIslNi6qRl/lZvaej/L2ofGU9up\n+uQZhD5/AZMSG7Fh8tLy3YxpGyLfIrXIu02CdjfEduwGYE+F9U8abbsthPjwiyZyuAG7mSeQs+Zo\ne4Mu/MGeJaG1q/C7cHvsbRnkDkoKorlCOYN8pIqQsVnFV97baQ2JTzvJGiL/98o9vdKGQ7V9SwuF\nfInjJtSiaRpvrW1AVyYneHN4xo7DM+Y4aseNYVxyG3ua06Q0cLntbFkvQ+TdJUG7G9It1oo+m0ef\nAIAtnUSVjvywlBDiyGtpzeFSgJYnmLEKmzgDTiqqule+dF+aphGq9ZGI5wjarTW0m3J5XEOHAjDI\niJJR1vD8jt3WkqDhag8TRwbYuKv1iFZIa08qO35iLU3xLNsjaUZkGxj0yanl9cRD51/A5Lh1CXHJ\nij2MGltDOpVn787WI9bODxMJ2t2g0taUhYbho0g5vITyrTRv2trPrRJCHAmtLRk0TcNw6ATTeQo2\nDXw2HO7eGR6HjiFyV87qDESyeWzeCuyhEFXpJOmilYzWEumYPjXtJOtHw5HqbeeNItu3RAmEvATD\nFeWh+fHpHVSeNpWtySwxo4Dn+OM5blQtg3LNrNjUTHhYFQCbj/BQ/oeFBO1ucOQNFJAJh2gM1OMv\nZVn/5ur+btYxZ2+6kZZstL+bIY4xmbZlORNuCCSLJP1O0LzYHBW99hrtyWhaa3sGeVsy2rDh2AsF\n7Cmdot0gG+0Y4Tt1bA0VbjuvrtpLsWT2Wlvez9ZNzZSKJsdNbBsaf3cnmjKZNCZIk83Fr9bv4g/v\nNVjH8/kZTG5djwLWNKVwexy8t74J0+z7dn7YSNA+TGY+T0UxS9Jega7ZaKq3rmNF3tvZzy07tuRL\nBX627EEWvvu//d0UcQxRSlHMWDNHCnoaewmK1b3by4aOnnauKYNN0zrVIG9LRssmyXlSaFlrbW0A\nh93GlBMHkcgUeHdLS6+250A6L8PZ3JplezTPiGwDg884nRd3N6OA7akcrfkC3rHjOHWwG28xy7/f\n2c3IsSGymQJ7dsgQ+eGSoH2Y0rt24StliXsq8TVkaBluFVNQyVZJrDiC1rSsJ1vMsTfdSCTT3N/N\nEceIjFHEYVr/5+6CFbT0oB2Pv75XX6c65EW3aUQbU9S4HTTl8iilysloo80GMnYrkO/a0xGgP9k2\nRP6vPh4iz2UL7Noao6bOR3XQy9urrdc7gSjxEWNYF89QLDZhmjneaLRKrNbNmMGkxCYyBZOcy7pW\nL4VWDp8E7cPUsNaa7pWoqGakx01rZS0lTSNkxGncemQzN49lyyId0+zWtMg8eXFkRBPWdC8Nk6qs\nFYycQScOT12vvo7NphOsqSDaZJUzzZuK1nyxHLQHZ5vJaNbQ8tZdHUVVhtb6GFVfyar3Woi1DeP3\nhfc2NGGaiuMnWiMMb72zHU2ZfHTyKF7eG8M006SzT5POLObVvbsA8I4dxydqSmjK5LX1e/BWOHlv\nQxOlIzCU/2EiQfswRbdZH8CEv5qPTqzDnjJprqxlkBFl1bIN/dy6Y4NRyrO6eR1fer6Vz77SKkFb\nHDEt8awVtG1FQoaVBOYNOnF6end4HKwh8lJJ4W/LFG/K5XHU1KC53FRnEmRKVm+1saFroZJpJ9ej\nFLyyqu8qpLXXGh8zvpZoIsf2FAzPNqL9xydYF0+TM6yFlEwVJ2ms57Vd6wAY/flzGZfawe5EkeCQ\nSoxckd3bY33Wzg8jCdqHKdVkFQVIVms8G3uKGoo0DRqKDZM9m3b0c+uODaub1+JK5ghHDUbtMti5\ndxNGKd/fzRLHgMZICh0Nw6ERzGZQgLfaicPd8/Kl+2pPRnNkOjLINV3HNXQorryBkXFi6iWSzV17\n1P8xoQ6nQ+ffK/dg9sElu3TSYM+OOIOGVuGvcvP669aP5pMCJksSeZQyKebfQy/acRQqyRfW8sdN\n/8YoFPGOn8BpldaUtG0x68fGZim0clgkaB+mUsr6dZ212SktGYzb3UzzEGv+ZCkek+vaR8DyyLvU\nNxX4/WeDLD6jmuG7MmyIburvZoljQEtTGoCU00EwlSdZYcPpq0XT7b3+Wu3JaMRzQNcMcg2oS+fI\neZKYSTu5Tutpe1x2Pj6+jubWHBv6oBe7Zb0VZI9vWyP77TV70ZTJiNM+wobWDEZ+JUovMvq9akav\nHw/o5NUO7n7xTyilOOXzZ1JrRFnZlMVT4WTrxiZKRRkiP1QStA+TPW/9A2n5Spx5L5lkmljlIABq\njBg7d0pSVF/KFXOsaVmPL6MTq7Kzs95JXUuB1TJELo6AZNxapCOrFajImWQqHX0yNA4Qals4JNeQ\nRgMibVni7eVMjyvsJe6Loimdfz7X9dJceRGRd3t/iHzzOmut7NHjwzQ3tbIj72J4McrK6hoASpn1\nnLwhw+eWruXTW14hkDsJyLPHsYIn/70B7/gJfMIVw0RDuRR5o8TOrTJ181BJ0D4MqlSiopgjY3Ph\nzHkByDVCseAg7apgSK6Jd5dv/oC9iJ5Y1byOglkk7dKobhqCP1ZL0mtj9a7VMsoh+pzRtna2M29d\n0y0F7L2ehNbO5Xbgr3ITa0wRcNm7LBwCMCQdodGWJ1MR4731zWzpVKzkuCFV1Ie8LNvQRKpTL7yn\nEvEsjXsSDBkRwFvh5LWX3wFg9IhqNiWy5I1NnLyxkU8tS4Gu48jEOfP1HdhtQ0Av8mLzn3ljTSOf\n+txpuEsGa6PWyIUUWjl03Qrapmly0003cfHFFzNr1iy2b9/e5fFnnnmGiy66iEsuuYSbbrqpPIH+\nggsuYNasWcyaNYs5c+b0vPVHWHpvA5WFFC0VVTjzVtB2J6qp1BNE6obgL2XZulnqkPelZZGV2IqK\nlsoAQ7eezLAtk9g8tJLQ7hb2pBv6u3niQ87MFVEoKrNWD1YL2HH0UU8brCHyXKZA0GEnXSyRKZbK\nQTuYiVNsGs7u0e+i9BL/em4jmbQV2DVNY9pJgymWTN5Y03v/F53nZgMsey8GSpGcOAqUYvIb/+D0\nFWnyXg/Oi4diG+Rn6N71nLCzFk05sVW18L+v/ItI1WAm21powY7DpbNtUwvFflih7GjUraD94osv\nks/neeKJJ/je977H/Pnzy4/lcjnuu+8+fvvb3/L444+TSqVYsmQJhmElSzz22GM89thj3HHHHb1z\nBEfQ3nWbsaGI+kMAKL2EpnTsxSTN9dZ17UIsRkmq/PSJbDHLupYN1EUd+Jutuu+6aceVOI5QvMSy\nvVKVTvQd0zSxlxTKrggYcQAcASdOd9/0tKEjGc1rrQ9CJJtHd7lw1NbiLeYg78JlumgYuoFctsi/\nnt1YHnE67cRB2HSNf63c02ujUJvXRdB1jdHjamjcuJWdWhX1ToNdpuLUV/7M5PURWitsVF46Hj1o\nx/bpajSXk6mvLqG2MAk0sI9ewQNPreA/PjEOTZlEjRyFfIkd78kQ+aHoVtBetmwZ06ZNA2DSpEms\nXt3xZel0Onn88cfxeDwAFItFXC4X69ev///svXd8HOd57/ud2V6B7cAueiNBAOy9qZOyJdmSJcuW\nbUmOndiJ47STc869OZ9zHdvJjR077cbXsZO4xXJiy5ZlFatREqvETgIEid47sBXb+8z5Y0FKVKFA\nEKQkCt8P8cESuzPzzuzuPO/7lN9DMpnkc5/7HA899BBtbW2LMPxrS2CwkB0eNRS0c62ewqo6PSsR\nMhW+uK5UkMHx2XdngNc57b5OcnIeZcSBMWrDaMmiNojYvJXEtDpOjp19t4e4xHWM159AAWQ1IrZ0\nFACj3YBCXXTVjmmfi5OmcG8AACAASURBVGur4gUX94VktIpKRMCai5CbriLgGiZviTPU56evo1A/\nbjaoWV1vZ9wXZ3g6esVjCfnjBLxxKmqsaLQqDu8r3MPVVQ42v/I8Kzs7CJoV9N/ZDNowar0bsViF\nbtdyVNkMtxw8jSlagaDMkS1t5T+7s9QTYpQloZXLYUFGOxaLYTQaL/xfoVCQyxWmgqIoYrcXEhIe\neeQREokE27ZtQ6vV8vnPf54f/vCHfO1rX+O///f/fmGb9wvRmcKHKqUqnHu924ekSaIMGomKWvKC\niDvl4+yZpeYhV4PT3jMgCWhj9cjIjCv1pI1aRElJRNmIbmqUeCb+bg9zieuUiYmC5GZcrcKaSJJW\nCdjsngvdrK4GtrkMcjlYSIB9o5xpfWyMYNBBpUJJf8VxFCqBQy/2EZsTVlnMJiJ9513jK5xImQxt\n0xkEOc+63gMs7zyN36zmsVsstFhjKJRGnLWfRm0oQ6qIoV6/Bod3kk19oErrUNqm8eXHUWs1JACJ\n/IU2n0tcmgXVKRiNRuLx126OkiShVCov+v+3v/1thoaG+M53voMgCFRXV1NZWXnhcXFxMT6fj9LS\nS8v/WSx6lErFQob5tjgcpgVtl4sWZqs59CiRsRVL6O0+UhMV6IkRtLko8c/w6oCPLy7wGB8ULvc9\niGXidIX6cPkaUGeMJI3j9PncCKRZr5YoClSBuYujk518csPNV2fQ1xEL/Q58kImEC4YwCRTHM/it\nKmptFQu+lvPZzm43otEqycwkwFFEWJJwOEwompcReAKWxUY5ZmlG9laQtQ8irvCTOWPj8Mv9fOp3\nN3GjzcjP9vRwrMvLH96/Bq1mYaVpsiwz1ONHpVawfnMVw3sPMqGy8rHAYepnhwiYinjsNhUVZhNm\nUaaq+eMUO51olLcx0PpjrB8qZ6h/hBXdJwlL2zixrg/98nbOndqGJR9jRjRQmpUIeuM0r1mcvuTv\nxPv1O7Cgd3Dt2rXs27ePD3/4w7S1tdHQ0HDR81/5yldQq9X8y7/8C6JYWMw/9thj9Pb28tWvfpWZ\nmRlisRgOxzsLEoRCi9sb1uEw4fMtzFWkmOvuJWZNyNoEltLNVHpP0TNRgZDI4HN5cPinyIRCjE/O\nolEt7mTjemEh78HhyRPIWQHLeDl5MceURkKMC8jIBLQytoiSdG45+7pPckvVhqs08uuDK/kOfJCZ\nmltpi8kwChmSRUryWBZ0LS/nPbA5DEyOhTGutjIeTuDzRcmaC97M0rQfp16kd6gOj2OUNvUxbq74\nOAPdPg6+1MuK1W62NJXw9OFhnn91kG0tC9NI901HCfrj1DU6CEeS7N3Txj3TI9Qnxpl2lLKvyUla\nM8VqVR69ZQ1ZoRyfL4osl6HSOpkNdpC8/5NofvB91o20Mm1tYaxmiOKmHoST5fg1AqVA67FRXGXm\nBY3xcnivfwcuNaFYkHv8tttuQ61W88lPfpJvfOMb/MVf/AVPP/00jz76KB0dHRcM9MMPP8yDDz7I\niy++yH333Uc0GuWBBx7gz/7sz/ibv/mbi1bn73VkWcaQTRJTmVBIKtTGGLqiZSyvLEZSZCGkJWgs\nZFS6U356x5bi2ovJae8Z7FM1KPNq/CUDBGZdbG0uYcfKUoYiCnKqFKp4NUr/JNHk1dNcXuKDSyJc\ncFHrU4Vclnzx1c0cP895F3mxKDKbyZHJSygtVgSNBgH4cEkGWRZJTZcjCxCraUOtUXB47wCR2SQ7\nVpYicGVNRF6fNZ4aGcY+0U19YpyJ0iqO1WzBXzJDsShSqzVjKbv9wnaCIGB2bQNk3M4QpzfciCad\nYOfZcXTRItK6SfK1afJSlpRccJFn0u+vsOm1ZkFWUxRFvv71r1/0t9ra2guPu7vfWuji7//+7xdy\nuPcEcV+AomyMMWsVACZjApXWjtnegmjpRfSXEiouJIu4Uz5Ot43QUmN7F0d8/RDLxBmYHqNu6gay\nqhQhbRBRqueOrZVoVAqOdXrxGqK4Zx1YQ5U8d6ad+zcvrbaXWFyyiSw5JVhjBQElofjqZo6fxz6X\nQa7LFjLAfakMHoMWdUkp6ZFh6hPj1Jetom+0HqtzlNOJPh7atpljewPse7aHjzywihVVFjqGQ0wF\n4pTaLq/vtyzL9Hd5UWsUeEr1DH/1byhNhxgoquDwtt1oprqRRInVGjW2irtQKHUXba+3NBGe2k9u\n9iTRrQ8wOTGEe3yI7cdX89KNMWRPB/rplfhzDsokmaE+P8uaSxbn4l2HLImrzJPJrgHUco5ZgxWA\nErsSQRDRFa/A5SqUKghIJPRGPCkfHYNLymiLxRnfORzj9YiyyExZD4mom80rXLgseoqNGj60qYLJ\nmIGMOoEyUc2pkc4loZUlFpV0Kgc5iaxWiTVT8KJp7EZEpfaqH/v8SlsRuziDXFtTA0BmbIz7b64D\nWSQxU0YW6OdpquqsTI7Ocu7UBDtWzSWkLUAhbXoiQiySpqq6mKl/+jaEQ3QZK9mz+S6KhpNEPQMo\ngI3OFvTFy960vSCImFxbQM5Tp/Rx6KaPIBmMLPe2s6KjkqycxbRxgqBcSELrOL6UyHsploz2PAkM\nDAMQ1xTiLZXugvFWKHWsrrUiCxLKmBKfsyCykkmkFlWJ6IPMqf5uiv0ekpoYs7YJpJCD2zZV8NT+\nI+w72c7ujRUUac14LdMIsgJrQGBwKvLOO15iiXkyGyzk1qTUIrZ0HEkAy5w2w9XGajcgigKSvyCh\nej6DXFtVDUDW76XWXcTGRiep8XpEWeBkPExzywQarZKj+wepsRsxaJUcPjtF7jJbYfbPdfQyt79E\nemSYPmM5T7l2oNGCRjVFTJlhuUZLWeVdb7sPo3U1otKIO3mEpN5Ix+57EGWJLb2dWGecBHLTGKoH\niSMzM5MkEEou5FJ9IFgy2vNkdrKgKpQVTEhiDrvztYSOktJV5M1BVHEjvvNx7bSPswOBt9zXEvMn\nko6S7jAgIDBd1Uk+ZqFZr2DP8Cj1P/8Btp98n2gizj07a/BmVWQ0CUwRDy+e6H23h77EdUTQX6iW\nSQoC1kSSsFGBq7j8mhxboRSx2PQkpwqJU69vHAIgxeNkZma494ZaFIKKjK+MuCzTFjrMlp3F5HIS\nB5/vYXOTi0giy5n++XsBJUmiv2sGlZzBNNqOVGzj164bURZrKRlJkK4uaJ7fWHkLCqX+bfcjiErM\nzk0Y5AgedYbTjgoMt9yGIRPhppNJlFkNKecgIXUCBJFHH3mV7FITkbdkyWjPk0w0hoSILJkQDDE0\n+teMtraoniJHwUUeUhZcWZ6Un6Pt4+/KWK8nDrWdwRixkzBFiRf5yQddpJs8WI+/giKfR5tK0PP0\n02xrLqVErMRbMoCAAt/wNMmlhJarzgflxjozJ06STyfRZfOEzQrMhmuz0oaCi1yO51CLAt45D566\n1A1zNeIT//T3WMQst64rIztRCzKcSGUwqV6kZpmVmYkIHrFQzXI5LvLRsyOkkjmckUGKd+xgf/Ey\nEAT0egVVjkFGSOFS62n07HzHfRnt6xEUWqqkHmRg7IbdaCoqKQsPsOmEE1mWSdS2F84tFOUnz3Uv\nhbnegiWjPU9UmRRxdRECIlpjHJX2taxRUVTRVFtQRcoJWiRBxJP2MTSxlEF+JUiSxPDxODIyY/YB\nAHSym5RCZkXnKdArSGl1FB8+QCIS5pO3NuMXc2Q0caxJA6+cXpo0XU1O9Xj50j8c4HjXzLs9lKuO\n31tYaWujBe9Zskh1TTLHz2N3GRGAIkQC6Qx5SUZUqVC7PSCKZH1eRr/51+yu1aFXGMkHSwlIMn3J\nEM0rhtAZVPScnqDeYeDsYIBgJPWOx8xMT9P+m4MA1K/0IBpNdBTXA1CTjJEoG0ACbqi4aV4CM6JC\ng8m+nmq58F0+F0lR+oXfB5WKVaPtVA66yZhCxNVxsupiBk738PTh4YVesuuWJaM9TwzZBBFNIY5t\nK5YRxItrsBtr1pHRR1DGjPhsblzpIMmsTCC8FJtZKK2nhxHjGpLOABn7FFKsCE1NObtGO1BkMihX\nFRFeV4Uqm6H38d/QVGXFnSrF6+5HRKT9+Ni7fQrXLd7ZJD96tou8JHOg7crVtt7rREJJ8goBc6Iw\nQckVq1Bpr111yPlkNG1GQpIhkC6sto1r1sJcr4Oc14v3r/43Xxr8JXcdnmHT2TiD/XFy/lNs3q5H\nysuUpCWQ4dWzl15tpyfGGfnWN5lRlaBTSSy773ZODE8Tj8modQq2NZyjPZNFIyrZ5N407/MwOTZh\nErOUiEGGokkyNgeuTz+ESspy45kJ9HETEdcoCAKrs36eODTE0c6lRkCvZ8loz4NEKExRJkbIUPiS\nlrneXPiuM9egsQcRZQWTReUoZImSdIBX2pc+cAshm8lx6pVRJDGHtyiGIMiQKWNbfALn8YOgFjHv\n3EHNDSuJG83ojr1Cyu/noc1bCJn9pDVxtMkcPUt5BYtONifxvSfOkUznMWiVdI+GCMeu39r4fE4i\nHc+Q1Smw5+cqRax6BOHaiSedL/sSI4V49vm4tv3uj1HzD/+M+8t/gspZKD9T5TLUR6fZfDbOjr0B\n0j8eRfEf36VMGSAVSdOci3Hi1CDS27ieUyPDjH37m3hzRnIKDQ2rK4i1t/NqyUoAavVRQoYQUVlm\nU+kGtJeRQa9QGTDY11LDMDLQEYph3rYd/fr1FKd83HhYTcRSSHyLamx45Cg/eqZrSffidSwZ7Xkw\n0TOEXkoT0VoAqChzv+k1giBSU1X48IY1hdd5Uj5O91z/rsOrQeuxMfIp8JdOElEWDG+zP82mmSGk\nWAJFSxEp5Xo0+g34Ni5Hkc/T/9ijVC+rxOI34vP0IyJwcN/Au3wm1x+/2tfPyHSU7S2lfHR7NbIM\nJ67jfsizc6qMabUCa6agiqZzXf367Nej1akwmDTkfIWxnO+tDaA0mzGuXkPlV76GpqoaOZNhYvMd\nPFa7nqMtBibLdMiSTE3vC2hycXQKIx/tfoa+P/9vTP7Ldwg++1viHefIx2Ik+/sY/7u/RYrHCa+6\nFShojbd29hKMF2Q9bl3exam5lf4Oz5bLPhezcws1QiF0dTYYQxAESh/6HPliE3W+Lhq6i4kbg2RV\nFu5VjyFJ8P8/fpaZRVbHfL+yZLTngbevUDeYVhYhqVOYLW822gAbmjaSVaXI5ouREShL+5gOxJaS\nKS6TWDRN27FRsqoMUWsDgsGLmDRy28nDxDvbQSGQXr6Opx8d4KlfdFO7dQOzxTaUp06SnpriDqOb\nWdskaVWKjD9BILDURGSxONXj5aVT47jtBj59WwMbljsRBDh+HXdoCszEAEgJYEvHSWgEXM7Kaz4O\nu9NIPlAIt51fab8eUavF88d/hsrhxH30GdKmRo7UlvDLHSYSv7cW40MlbGjJIgsiraU3kU5liZ0+\nhf/xx5j4x79j4E+/zNi3voGUTmP/nS8wMStiLtZiVqZ51VpOJpTGbkgh6uKM5PLUFlXhNl6+CIpS\nXYTLVocLH0PRBLFsDoVeT+Xv/zGyANt7e0iqIggIHEXmsxuKiSWz/NOv2okm3nzeHzSWjPY88I9N\nkhXVSOhRGmKodW89yy42VyNb/Yh5NTNFFbjTfrKSQO94+BqP+P3NiUND5HMy4ZpiYqERBFGmcTyF\noNciheOIjWaOnHai1ihIp3KMdBiZ2rwWUZYZ/uUjrF23E21MxFvWgwi89PxS+ddiUIhjd6NWifzB\n3c1o1AqKjBqWV1jonwjjv07zNwZ7CyVSMfIUJVOEzEpcpoprPg67y4gymUfBa7Xab0RpNuP50z9H\naTKxs+cFclNVCAL8MuRDNBsprupieYuFrMrEryvuxfX1v8X9h3+E9Y670De3oPF4cP/Blwlaasll\nJepXuDh1uh1v1gAyrPFMsS+x8FX2ecyurdSIY8gIdIQKkyJ9XT3i7pvQ5hJs6hlBRiam8aAfO8iH\nNlUwE0zwz79uJ539YHcCWzLa8yAbjRNTF1zexqIsgvjW6q+CIFBaUYhzTVoqMWYTmHNx9rcuZTHP\nl4A3Rnf7NFmDglmdBrSFa7e6ZxpZlEGAMety7KVOPvXFTdhdRnrOzlDWvA6foxThbCdSPsfKiTRh\n+wQpRZbAWJjI7PVpUK4VubzEvz55jmQ6x2duW4bH/poU5sbGQhb19egiz6RzjA4EyGkUqCIhRGDW\nrMBRVHPNx2JzFjLIjbKAL5V5Ww+e2uXC/Ud/RkV+lprxHHJGw2w2yZSuDlnKUld5CqVWiQs42BnB\nuGYd9nvupexP/5zKv/wrjGvWXhBUqV5m41WFkbS34JqusvkZlmSMKgOrnS0LPheV1kGTWQ1Au893\n4e/1H3uQUFkxVZEhkKIYYlZ+o/eyu17F5iYXAxMR/vXJDvLSB6PU8K1YMtrzQJVOEpuLU7vsmku+\ndl3LKvJijrDqfPMQH90jS0kU82Xvi30AhOrMJEcmEYv8aNJadFkjxNJQa0Lh2s6dn1iJTq9m5+5C\nh7m+VyIMb9kKwMSj/8F6y3IQYMY5jAAc2rsU274Sfrmvn6GpKNuaS9i+8uJOUeuWOVGIAsc7rz+j\nPdwfIJ+XSTi0FIdfV+6lLrrmY7G7ChMlTSpPRpIJZ95eh0BXU0Pp7/8hN/nbyE9XIAgyj060oS1e\ngZwdZ+u2LAICQ62TZN+wn3Qqx8hgAKvDwODkCEF9EZlQGrc5yl5plqyUY6t7I6q3WbzMF49nEy78\nDCdkYtnCGARRpOoLf0xSLdIQ6Cz8LVPGr048wuc+3EhjpYW2fj8/29P7gQ07LhnteWDIJi6stKs9\nl67NrHE0kioOIMlG4qoiKjI+IrE0qUt8wZYocPzcJP6xMCmrBl+2jYw8i6DIUzFrQjk3V5LXbmXH\nrmYUisJH1+U2s2J1KSF/ArNrGZPuKuT+UUrsbjwzGaJlfSSRGO31L622F8jpXh8vnRyn1KbnM7ve\nrC1t1KloqrYyMhNlOnh9JQsNzMXqo04d1nQIgKxFO6+65MXGXKxDpVbAXF/vt4prvx7jylU0PnAf\nTf0p5LyCVDbDacmIQmXCpHwVtQ1UeZkXn7s4fDTU60PKy9Q2OjgQzZH2JpBlKCkOEVZoEBDYfhll\nXm+HRu9mmS6GjED7zMSFv7vcNXjv2kJJdAhkiWKfh9NFEdoHj/Dlj7VQ4TRyoG3yA1vDvWS034FE\nNEFRNkpYV9AXL/VcOpYlCALF7oKB9hrLceeCyILAKwsQ6n83kWSJ6biX49OneXXiGJJ8dd1RPaEY\nR/cPIQPpuklS43oU1kLmvW3MhGY2glhlov7W+950w9x0Qw1avYqpV6bp2VxQZgq9speG8SwIMG0p\n7Of4oeGreg7XI/7ZJD96pgu1UuRLc3Hst+K8i/x45/VTLZFO5RgdCoJKJCnL2ObKvUT7tV9lQ+He\nYnMayL9Bg/xSFG3fwd3rK2HGDQqJF/oPoXPvAiRuWHeOFDIjXV4mR1/zBp5vw5kqEpnVGVH4Cuc9\nYBkknkuwwrYMm866KOe0xl3oDtnuv1hadfttn6GvXoM1OYUuZUad0PPI8FPsHX+ZL36sAZtZyxOH\nhq6o3ej7lSWj/Q5M9I9iyiVIqIqRdXG0hnfOlmxaUYeMzExRFY6YH4WU59h7+GYmyRLehI+TM208\n3vdb/un09/kfB/+Svzr2d/z03M/5dfuv2Dt26KodvysU49cHB1DFstjrzQxHe5CiFpTFXlRpLatD\nXQBYbt+FKKretL1Wp2LLTbXksxIKZQkjVcuQJoI0aqwocjLx8m6SyPR1zBBeKhuZN7m8xPee7CCR\nzvHpXQ14HMa3fe2aegcqpcixrpnrxm053O9HysskzSpy8SzWbJicCCbXW1ePXAvsTiPK+MXdvt6J\n8o/exeaEDVkWkDPw4lQXRsdG1EKQstpJZODlZ7rIZnIkExnGh0M4Sk0cjkQhk2M2CAZdHLWx4A7f\n4dm8aOfjtFTjUkQYyxqZjb12j9Qpddg/fj/6bEEgqeZcPWJO4tmRl/n2yW+yet0IeoPET5/vuSwt\n9euBJaP9Dkx09pFSmpAFFVpTClGhfsdtVpWvJGEKEVfayItqStIBRr3vjdIvWZbxJ4Oc9rbzRP+z\n/H+t/8b/PPRVvnb02/y44794eewg/bNDFGuLuTlfzcPPydz5opW2vXsYjy7+rPZsMMp/9U5gHggj\nKkWMK8LkxxsQzQFQ5qib1qPxhRFLdVjXfvht97Os2UVpWRHpMz7ObtyJJAiox33UjqdAm2RSUyj7\nOvHKyKKfw/XKY/sHGJqKsKWphO0tpZd8rU6jZGWtjalAgnHf9VFiN9BVSJAK15jJh1PYUnHCJgUe\ny7VPQjuPzWlEmZjz5KXm10VQEAQ+8fDHUQSdZA1ZgvuOkDI1o9TY2VQ3QEqbJBZOc2TfIAPdPmQZ\ndOUmZpVqLIFRJFkkbR8jlU9h1Vposi1ftPMRBIEWixEZkdbx7oue21qxhaFtIoIsUZTW0dS+iS0d\nEmI6x+F4K8ple/A4DvOjp44wOPnB6eq3ZLTfAe/oFPG5JLRiy/wul16lQ+mMAwJ+fRmVeR/ZbJ4x\nb+wqjvTNyLJMMBWizXuWJwee4zut/87/dehr/OWRb/LDcz/jxdH99Ib6MatNrHet5q7qXdxTdwcf\ncm1j69EATY8eo9e0iR7nVoomdvCLJwZ44twY/eE4ydyVl120+iP8YmAa80gcRUZizcZyTvaMko9a\n0c65tHeOFyYK+rVr3iQd+3oEQWDH7nqUMkhpE4P1LcjBFOt9hXGmyoZJzK22Z6+zuOvVoLXXx54T\nY5Ta9Dy4u2FeMdxNjYVSyOtBizydyjI2FCSvEsma1Zj9ATT5PCGzktKi2ndtXHaXEVECXf5igZV3\nQqNRsav6RgAGXCY6/vNfsVfdDYjcuqEdSSXQ0TrJ6SOjAHSosojkSc4UXPFGV4CclGO7exOisLhm\nY3VpocVoV0wkl3mtPFYhKrht691IigBxjZWKqbPImdv5rOqj3DplQpWV8FeFoekgv3nmb+l+5SBy\n/vovB7uy9L8PANlYnOhcElpZSfG8t6td5mJ8APyGctz5ERAE9p2e4OEPLd4s9e047W3nyNQJRiPj\nxLIXr3rsOhvLrfW4jaVoFGoy+TQTsWmGI2OcnG6lbizNDadiGJMSA446IloHojCLJBgwTOQZmR2h\nvX6WlE2LRa2kzKDFY9DgNmjx6DXolPOTdjzpC/ObYS+6nEzRWAy1QYWsyxDsLgNkZIsXy6yIdjSC\nUKzCfsO977hPm8PIyg1lnDwzSdu67dT0n8MxncGQyJO2TjI50EgdCo4dGmL3R5sWcmk/EPjDSX74\nTBcqpcgffLQZrXp+t4mWWhsatYJjnTN8bGfNu5KstVgM9fqRJJm4xwB5CXO8EPMNmhU4DNdWDe31\nWO0GBAHUyRxhBSRyefTz/M7dsW4tLz+7l6TNR6I1x/BL+3Ft3AlT+ymtHMQ7UEM8msZYYmBMq6Yh\n18uhgAlBH0atkcnlFGx1b1z8c9KqcWvyTKSdTE8fo6xi14XnGq0N7FvRBucgrypiVe9jdIU2U7nr\nM/zlCgMHW59kf76Pwdos30s8zcofPcFN1nV4ttyIxu1Z9LG+kUQuz68Gp2m2mlhnN1/148GS0X5H\nNOkEcW3hza+pmL8K0urqZvq1pwlIHqonT4JZ5uzg1dfB9icD/Ljjv5BkCZvWQn1xDWUmNwalgayU\nYSruZSQ6Rqvv7EXJZY60igdO5XEOR5CBWb2RfsdOxIxEt3uce872EFa0MEk9jrYAGZOKUH0RZy05\nzoZe8yBYNSo8eg2e88b8LQz5vmEfjw970StF1k6nGclKOMqLOfTyJBEEzM4gWVWGG/vTIINoNqC2\nzq85w/ptlfR3eQkntXSvWMuKcye5YSDNsy0K0hY/iZCTwS4foe0JLLa37//7QSWXl/j+XBz7sx9a\nTpnz7ePYb0SjUrCm3s7RjhkGpyLUut+dhK3FoL+74BqPlxlQhjLo04XPeNysQjOPENnVQqlSUGzT\nE55Ng1GJN5mhyqSb17aiIHB79Y08Pf0r9rc4WPbUHij9IhmVk/W1YxxJVxAcU+C1KlCQRx6OIKND\nUzpGNBtjnXMVJvX8Pw+Xw0qHg8nxIB3+AKWlcRSq13QAPrJ1B092dDFYvILSSA+rpvcz8ZtR9g3t\n4uZ7H+JWrcCPXniUHs5xulbgTK6NliePsDVqx7NhJ6aNm1AYDJc4+sLIShI/7ZtkNJai2nTt7iVL\nRvsdMGbjzJgtSIosVuf8++eWGUtJWwNoU5WkxSLMuTjBqEA8lcWgfXMy1WKxZ2Q/kiyxw7MZpaBk\nJDrGueEustJrJWcqUUWVuYJKcxlVBg/O1hHSz76AnJ5r+qBU8urmh9COJphCRjtbzcsbhnngxcPY\nEz2crN+OOmrBddqPpBBIF6vRVBUhOHQEcnnOhmIXGXKbRoXboMGj15LOS+ybCmJUKrjPbmHvnjOo\n1ApGB4NMiHmQRPQlIbLJPOX9YTAo0Ja8uczo7VCplWy7pY7Ay720r9lGQ3cbtf0JlI06LLV+Jk86\nqEPg8P5B7ri3edGu+/XCrw8MMDgZYXOTix0rLx3Hfis2Nro42jHD8U7v+9Zop5JZxoeCZHUKcnol\na1QaMtmC2zZrffcnejankbFQDDDgS83faAPsblzPC+N7SDtmOePwIPzgBzj/6IsEU3tYVXeMkRUf\n40BWZIXcz9nxwvtnL0kRzF6ZAto70WI18/x4kAHJwybfMYrdN194rsxaSlFlD9FhLQerP05Z8ByN\nwTaSh3/KMyN9lN29mt/Z/Ql+tmcNxwdPoCkboG2ZwNl8nKaex9nw1C8oXb660JhkRTOCeOXu/bws\n84uBaUZjKVZajWy/DC/slbJktC9BIp7ClIszpDIhGKIoLqObjSAIlNSYSE6Cz1BOleynXTZwqtvH\nztVXJ/s0lJrlyOQJREHk0MRRAERBpNTgospcTqWpnEpzOaUGFwpRQWpkmJkf/oTUyDAoCqthhc1G\n692/i/oVL1lkUwwc5QAAIABJREFUgiqBr390DT96+hiHVxnZ2RqgbnIvozdtxTJcSyiQQBdMQ6BQ\nJlJj0VG9thRtRRHeTJbJRIrxeJqzwRhngwVDXqxV8Tt1bo78phNZhmwmj8qhIeyTMFiShMUxdnQk\nESQQVCKG+obLug7VDXYa2qc4nhHpaNnEqtZX2dKd4NXmMeJiHQlJzWifn5A/jsW++DPw9yttfX5e\nOD5GiVXPQ7uXLci93VxtxaBVcrx7hk/cXIcovv9c5EO9fmQZ4m4DQiSDyWSCXKFGW+G4du043w67\n04hqvOCun0/Z1+sRBIHbqm7gmfGn2L/SQfO+SQL/+mOm7roRj72PU7kUCkGDpjOCX7IjmALM5oKU\nGlzUFVdfjdMBwKJR4dGrmUi48Pmexezahqh4Tcjqk/ftoPXYKKePjDJlXc20uZ41U/tZPfoyIz8Z\n5R9a7OhWyrgkE5P9q6irVZIwddLeIHCuTs+KgS42/NtJLGoz5s1bMdy5G1ljXtBnXJZlnhrx0jUb\np9as475qF+I1DAUtGe1LMD44gVJQgSBiLL78BIeWulqOHp7GbyinVOqhXRA43DF11Yz2i6P7kSj0\ny91YspYdns2UGd2o3+DOk1IpfE/+ktBLe0CWETQa5HQafXMLyU8+zNCLgxjzMqPIfHhbDdYKN/eE\nBL7foqd6Ik29N8rgmR7cn6hkZaCB44eGSMazKJQi0dkkZ14eRKkSWdZcwt3rPFga9ITSOSYSKULp\nHDfWl3Bm/wATc7Why1eW8LJ/AhBoWC4xmEywsj8JOgXybBZdXf1lXQdBENh+Wz09T5ylY9VmmjpP\nsLI7wfEGHSU1YSb67dQjsP+lfu755KrFuvzvawLhFD98prMQx777nePYM3Eve8cO0e7v5DONH7+Q\nUaxUiKxb5uDgmSn6xmdZVmG5FsNfVHo7Col0CZeOFr2O1s4Zbs1EiOlEbLbyd3l0hWQ0VeLyyr5e\nz211m9kz9iIZ+ww/X9PE/ecG8I6k2Wv+CGmFjrrUMN4JCVkUsVZNk5Qldni2XPUchRarmYlEhsGc\nA5f/JGbXtgvPKZUKNmyrprGllCP7B+nv9HK6/A4ssRGafEfYfWyK1pkWhlZ3oVmRYTSvwJZxs85Z\nxWB4hHP10FmnY8VIlvWHnif0/LOIOh1qTxkaj6fw2+1B4ylDYXpz6+XXs3cyyAlfhFK9ho9XOjnT\nF6CyxIi9aP4ejythyWhfgoEzfehUBbeH03H5b0ijrYE9lnbEQDmWcArUMDgZIS9JKBbBRfN6wuko\nr0wcQwD0KgP3N9yN7i08A7H2Nrw/e4RcMIDCZCKfSCCn01jv+ijaD93JI4f7KZpIkEQmZ1Rz67pC\nSMCyfhO3HHmCF7eY+fQzIW7qGeeR357hTz5Ty6caN9F2bIwzx8eQZdAb1EiyTEfrJB2tk3gqi2lZ\n56Gpzo4oCkx2eHnlpX4AVm4so7zJxSM/nkQ0hklIMVb1JVFmZRRWA5KUQe2Zf1jiPOZiHTsanDyZ\nS3Jm1TbWHd/Hus4E/esnme63E0dmajhEwBvDdhlx2+uRQhz7HPFUjodvX0b521wPWZYZCA/z0ugB\nzvm7kCmUMD458BwrrK+tzDc2ujh4ZopjXd73ndFOJbNMjs6SManIAzfVONm3pxdzJsm4S0V50dVb\nbc4Xm9OImJNR5eTLXmkDqEQlN5VvY8/4S3iNFn7+qS8jqNUocjk8s1OoWyP0FOkhKiMaQ6hRs7Fk\n7VU4k4tpthp5ftzPoFxJk/cYJsemN/V5MJq13PaRFTStdvPKi30EqOQVQxl1/tNs7T9KRbCRiV21\nnMqfI6gYI+gt1Hlr5lbt56qgo8rB6qiR5u44loEBUv19Fx1DYTaj8ZSh9njQuAu/1W4PCp2OE74w\nL08GMeZlrNNp/vdLx4gls+xc5eaz1yDJGJaM9iXxjU9hnSv3qn6LHtrvhF6lQ+cBApCVDKjkHNm8\nkoGJCA3lixsDeXn0AHm54A34UNUtbzLYudlZvD//GbFTJ0FUoCmvID02iqjXU/K7X8DQsoqf9k6i\n6ZtFAMaQuXdnNQn/IULRYUyrtlL9WIaONVr2bTBy+5EIuzsG+dtHX+VvHryDjTurWbG6lOOHhuk5\nOw0UVgSCIDAxMsvEyCxGs4bSsiL65jSqPZXFbLu5jn947CQAdY0Z/KledncnQKMkPxNB39i04BjU\nmo0VHHm6nc7mDbS0H2ZNb5K2ZWNoDcuZSWqpkeClF3r5xINX/4b0Xubxg4MMTEbYtMLFzlVv/pzn\npTxtvnO8PHqQkWjhJlhlruCWip20ec9yynuGjkA3zfZGAJZXWDAb1Jzs9vKpW+tRKt4/laUDcwlo\nCZeOUhR0jYawZCMIQMikpNpwaRnja4HeoEZvVKNKZJlVCmTyEup5XmNJlumejTOVqwFZicI+QmS0\ngQYklOOgzOYojfSyV16P2jVBPB9jm3vTWy4AFpvzSawTCReJbIZYsA2Tff1bvtZdUcx9v7OOzrYp\njh0Yot+xgbGiZTT6jtD06wPU3XAPvwpmSKpnWN4k4c+PE84UarllZFpNUVo3gHlrFWu0NWzJlqKd\nCpKZGCc9OUGiq5NEV+dFxxxvWsuzjTeSGo8y408zDDhEmUZtBnNiEFgy2u86UjxOTF2Q66usrFvQ\nPhoaPIy05wjoy6lQhhnI2zjWNb2oRjuWiXNw/DAAVq2F7a9TLJIlifCB/fgf/xVSMommsgopmyU9\nNoqmvJzSL/0RaoeTwzOzjAwFcQTShJEx2TTUqJ8jMl2ok86kZtCtr2HbwVH+8w4by6dkqoZ9NPWO\n8ZX/OMRffmYnZrOWm+9YTss6D0f2DTAxMosgQM0yO0qVgsEeH32dXgRRQBTg5juWM+GLca4/jGCI\nsNxtYeZACH1aRr9+BYmT7WjrFnbdARRKkTtXV/CDKR+n19zI5iMvsPFcgtF1M3ScLMeFgDwRwTsd\nxVlyaZfY9cqZfj/PHxvFZdG9KY6dyqU4MnWSfWOHCKRCCAissjdxS8UN1BRVIggCLr2DU94zvDCy\n74LRFkWBDcucvHx6nO6REM01734ceL50tBU+7wm7hodbyvjFi72UpgqKW8EiBXbde+NcbE4jE5EM\nmNX4Uhk8hksb1WQuzyl/hCPeWULpQlKqTb+CgNBOJjYMgTLsihjLxl6kdc1qCCooKvcR4+omoL2R\nFquRiUSaYSowzBzGaFuL8DZ14aIo0rzWQ12jk2MHh+hshTbPbhyxEWr3P85HXfU8aWqi+xUFf/6J\nj1Jky9Ib6qc72EfPbD+pXJpINsaBbDsHaEfv0FFXX80G1y3U6tyo/WEyE+NExiZ5KaDgiOxCbvNj\nAWrFPFpJCZIIKR2W7LUTFFoy2pdAnYkSM9chKxNo9QtLWGpxLWNIfZA4pZQwzAA22nr9PLjrnbed\nL3vHDpGVC1/EO6t3Xei+k54YZ+anPyE10I+o01F8622EjxxGjscxb92G89MPIWo0TMZTPDfixdEb\nRqawyv5YVRu5ZIDBkJupqIVtFV2wUcAe0bJFqOD5dUN83p9lR6iNoSk3X/3JUf7nJ9dTYtXjKDFx\n1ydXMToQ5Mi+AQZ7/KjUClauLyPojzPcF2DVlgqMZi0/e/IcIKAtG2bGr2ddVwJJIaAucpEAdLUL\nN9oA1dVWyoe99DStYXXbQZr7kww1dQEeonothkSOF57t5sHPbbii47wfCUZS/OC3nSgVhTi2TlP4\n3Mymw+wfe5VXJo+SzKVQiUp2eLZwc/l2nHrHRfvwGEtptjVyLtBF/+zQhWSljSsKRvtY58z7xmgn\nExkCMzHSZhW6nECxTkXXcIjPRrqRBBgp12JSvTdCKXanEeV4oYT0Ukbbl8xwxDvLaX+EjCSjFAQ2\nOMwsF1WcPFBBwN2OsmSY3rQW/bY4lfJn+N6JIQR1gpg4Q7W5gnLTtZNtbbaaeH48wLBiBcszT5EI\ndWCwXroFqFan4obdDTStLmX/C734JivxG8qoDJ3lgcBzvOjYzHd+fZa/+MxadpZtZWfZVux2Ix0j\ng3QF+zjlbWMsOkEil6Td30m7v7DCNkp2FP5GguNOTJJANRJFCAgIIImYdTFK8eIc68G95sZrcHUK\nLBntS2CQkoQVWgRjaMH7KDO6iTtmMU2UYkxLoIRQLINvNomj+MoTFxLZBPvmdMFLDS42lKxBymQI\n/vYpgi88B/k8xvUbUDkchJ5/DkQR54MPU7TzRvKSzNBUmJ+P+9BOJlAlcviQKbWFKCua5bEzy+iY\ncSDLkMitZ1dtG+rbnGwYCNLuMvPCRpk796b4SGAvP1bdw//705P8yX2rqCsrQhAEKutslNdY6Doz\nxfFDwxfUlvRGNWs2VzAViHOiy4ugD7O61o7y5CnMcQnl5g2kx8dAENDWXLlk5N2ry/nn3glOrrmF\n7Yd/y7K2EKaWaU52uTELInjjjI+FKSt/f5YoLYTz9djxVI6Hdi+jwmViIjbFy6MHOTnTRl7OY1IZ\nubN6Fzs8WzCq337SurvqJs4FunhhZC91xZ8HoNZThM2s4XSfj4dyeVTzFAB5N+meC+skXDp217s4\nMxDAnvTjSM8y7FYjFxe9ZwRj7C4jqp5Cwtwb49qSLNMfSXB4ZpbecEH9r0it5CZnEauLjHQcHePA\niUL+icdZxYRhmLQoc3piFMo1ZGftGGr6kbi2q2x4zUU+moCUQkNk5lX0luZ5XXe7y8S9D66lr9PL\nwT29DFtXM2WqY6f/FOPxcf7xF/C/Ht6I1Vzo0uYyOHEZnNxYXkh4GwyP8PLIQc75e5DyEsqQGnMg\ng0cyIlI4vtYYQ+9KU1tipDynQZ7WkMw5UdgclxraorIgoy1JEl/96lfp6elBrVbz13/911RWviY8\nsnfvXr773e+iVCq59957uf/++99xm/ca8WQGNYUbjdmx8BuOIAg4V9hITkAub0CpkcjlRc70+7l1\n/ZVnou4ff5WMVMgkvbv2wwiSzOi3vkF6eAilzYb9vk8QPXaE0HPPgrmYwO4HOJoqZuSnJxnzxtE3\nFGNw6vD0zSIhM4HEfVVhZpT3cvcuN192GPjJc9282jmDzXorazXPoqmVuRkHT5bEmFxdjrttjJty\nL/Ji+na+9fNWvnDXCtYvL8T+RFGkaY2H+hUuWo+O0tk2ya6PNKHWKPntnh5kQO0ZYLmyHEVHFEmA\nsjvvZeTrXykkfyzQw/F6SiwGKkUl/c0rWXlmL8uHEyRWdaBSOFHYLTAT54Xnuvn8F6683eD7hd8c\nGqR/IsyGRgeO8ijfaf13ukOFhByX3sktFTvY6FqLSvHOmgI1RVXUF9fQGehhLDpBucmDKAhsaHTx\n/LFRzg4GWdtw7W5qC6X1VKE9ZFanZF2lje/++gyrw4VrcqZeh9t4+XXrVwub04jqDY1D0nmJ03Mu\ncP+cLnmlUctWVzErLEbGB4M8/ZtTRCNpzMVadu5uQLLV8q2T30FZMkx2dDknk+OAA4VtCp1Kz1rn\nymt+bs1zLvJJ/UZqkodIRfrQFc2v7FMQBBqaXFTX2zh8YIjOU+N0lt5AcWKK2wf28qMfx/jSF297\n03axZJYTx2OMtHtwZT0UIVww1Chm0UmjlERHcY3NYmgreDXDr9u+U1HElvVvHX9fbBZktF966SUy\nmQyPPvoobW1tfPOb3+R73/seANlslm984xs89thj6HQ6HnjgAW666SZaW1vfdpv3IgO9EwgUDEZl\n6ZW591rKltEm9RFVO3DoM0xF1Zzo8l6x0U7mUrw0egCAGnMlTbblhPfvJT08hLBiFZMrthD9z5+j\ni4UY0ZfwhG0nydY4EEchCpTUFZN3G7D3+hDyMIHMqloFt9762YviSA/dvoyRmShPHYtQW9uIqaiN\nZXYvbrWO3zQk+MMJG+tGfQxtPMBI5Ea+98Q57r+5jl0byi/MkNUaJZtuqGHTDTU4HCbO9sxwtHMG\nlSGB3jZL7KyP2nCe6ZpyqlIp5Ezmil3jr+fDy0v5Xvc4ratv44ZXn8R+KsiNq8c4PamnVCFAMEl/\nv5+6OvuiHfO9ysmuGZ47Noylwk/A1ca/tBdWmA3FtdxSsZMVtmWXrS+9q/Im+mYH2TOyj883fwYo\naJE/f2yU410z73mjHY+lSUXSZIrUrPZYSWfzdPVN8fuxQXIGBSOlam56F+VL30iRRYdaAjEvMxlP\n88yoj5P+COm8hEIQWGszscVVjMegJRFLs/epTvq7fIiiwJrNFazbVolKpQCs1BfX0Mcg2bFl5MYb\nUFinyZJiZ+nOeU3aFpsWi4kXxgMMylXUcIjwzCtozfWX5eVQqZXccFs9q9Z5+O2v25mllLDORcNU\nL09/6yf83t/9EQBneybZ/1I7+VkJtajFM/e5N2RmcUUHccaGMWRfa0YS0YuMlGiIq4rJKEqIaoz4\ni6BybQnXyiexIKN96tQpduzYAcDq1as5d+7checGBgaoqKigqKjgaly3bh0nT56kra3tbbd5L9Lb\n1o9yrtyrruby6oTfSKO1nlPaQ8gZO+5cminUDEyGSWVy89Z1fisOjh0mnS/Msu+pvxMpkcD7m8fJ\niCr2eTXc1PUD1HKOY9Zmhhp3sLG0iMoSE1UlJnQmDd/rHkUZT6ObSJFBwC8K/Lddm9+U+KFVK/nS\nPS381X+c4KfDJn5vaArt3VXcbIGfZQRe3mbhlqdC3H5ugl/cfJTM2A4e3duPfzbFA7fWv6XAxjOH\nh5FlEEp7uN3ixvhCKwDO2+8jOVBY3VxuffalKDfpcKtVDDU10djxEpXjcfRr+zidd+GqryLc7eeF\nZ3uY3njlCSXZdJqBgVayFiuCRoWsoNCaRwSEuUIpqfC70PhNPv+v0Alu7jEXXiejlo1oZTOCKBa6\n/AiFH0GYWxEIAogX/gyiULjJSTLIMnJeBkkmE4sQn+hnlTZD3i8jBCysVHoo0pjQiCr62sfpY3xe\n5ynJElk5xoc+fjuN1gbKjW5avWfxJnw49Q4qXEZcFh1t/X7Smfzb9uJ+L3DowAACkLRouKPZTftA\ngIbwEGopR1uNAVkUKNG/+5nj5xHFQm/tyViWkELg1ZlZTCoFO0qsbHAUYVIpkWWZzrZJjuwbJJPO\n4XKbueH2hjeVON5SsZO+2cHCanuoBV1DB3lgu3vxWnBeDlatCrdew1A8jVDUSCbWRTo2gtZUddn7\nKrbq+czvbaazY5qDT59jong5qnyKn375H4mrzMQ0LpSiFqUC9JlZXLFhnNEhZDWE7E7GHA3o5CLU\nJW7SZTrGDGP0pLuJ5+PAFG5DCc32Rm4u277o1+HtWJDFiMViGI2vvfEKhYJcLodSqSQWixUUhOYw\nGAzEYrFLbvNeJDg9g15tAfI4nVe2+tKr9MieHAyBLlXQ+5Zk6BoOsWaBK5B0PsOe0X0ANNmWU22u\noPW7P8CYiDOtdbLbfxxJpUH58c/ywA1bLyq7yeYlvneum4ykYsXgCFHJwBgSt26owFb01gktHruB\nh3cv599/28m0rpSSXw1S9z/upCl9mnNE2LijGdOBdnacHaHjZhum7tW8fHqcYDTFFz7ShEb12g17\nyh/nSMcMJrOEbJnBPaZG7csyVGJk15qVTP9bwQOjXcSVNsDNFXZ+1j9FW/M2dr2yB8WxIB+6YYDu\neDlZlYgqkeXs/qFFOpoLvK8zvnNIYp68Iock5pEUOfKKLJLi/OMc0txPXnzdY0UeQQYhrUOIFyEk\nTYgpA2JehQIu/Iive/za3948YRLx8Hq5HQlYeNaGhl99/2eU3n8X28tu4efdj/DiyAE+3XgfgiCw\nsdHF04eHaev3s2nFe2el+kb6B4IoAKvNgFIhcvTEOdaEe5EFOFmrxW0oYUPJmnd7mBdhdxoxjs9i\ncpWwyW2h2WJCOTdBDvnj7H++l+nxMCq1gh276lmx2v2WE+gm23Jceide+xT5QCl5XYBGawNO/bvn\ndWqxGpkcTzOpX09prIvIzKvzNtqyLCPl4mTTAXLpINlUAIcuwJ13Bnn1aDEhr4Mpc6HSQZONUpye\nwUgaVbEdzbJ1nKv4MN1KgXKdhs83elArLp5s5qU8ncEejk6d5Ky/i8mRaVK5FJ9Yds9iX4a3ZEEW\n02g0Eo+/tiKRJOmC8X3jc/F4HJPJdMltLoXFoke5yEksDsc8ynsyQeLqJgRFDJfryhOUlm9bxWBv\nhIzSiFYLqTR0j4fZtW1hiVZPdb9EKl/QCn9wzb08/vgJVrQdIanQUJHyoivz0Pi//m90noszPzOp\nWX5y9FWmM6XURkaJThtIABmdkofuasaoe3t32EduMjHqj9P6Qjkfkicwey18ovF2/urMb/mFe5ov\nV3tYNjTBYE8H63Y30nXMQWufj3/81Rn+n89tpthUEDj450dbkWQZddkgaww60oemUQPTq9bhdJoZ\nGRpEVWTG3VS7qIk/O+xGnuobZXLZWkb6D1E5nYTEJKfDfXz8U7s5dmTk/NJ3XmSzGRKJMEJ8lqJI\nAH0khkKSkUSI6DUY/w979x0e1XUmfvx77/Q+o9Go9y4kgZCophkMtnFvOLZjO/HacRLHSTZOnN3N\nluxmN8XJZjfJbpxkf05ix3Hce8U2YMAY00WXUO9lpBmNptf7+2MwhtiAAAmBuZ/n4QHmzr33zGg0\n7z3nnvO+gQRxUcWYXs+I2UpCVCMmRIS4AmVchRAREKWJXcMsCSApBCRRQBIEYiIoogHs3kH0YT8C\nMUaNViSlgsN9++McKdlnTwhxEkKMhBAhIUQ5cg0ggZBQIEWLkOIFbNjzJxTpV5BiupLtzgZur4+S\nakjh8gWFyaDdOsJVSyb2ImyidHSMIAZihM0qHri6GrNWyXBbP+kRNy05aiJGDd9d8hWyTBM7C35c\n30MnUFCcyoGGfq4yWqitSBY1ikXjvL+mhffXNpOIS1TUZHD59dWYT5Kt67ppK/jd9icwVOwhBlw1\nbdkZt+9MLNark0PkUT2ltiJ87lYMGg9688eJluKxEKHAMGG/k1DASdg/TCjgJBQYJhELfeKYolLL\nJUu1OMd8bNmiILcwg8VLFpKaZkJ9eOXEm60DNDb1kWXU8u35ZRhUnx6jMtLnsqxyLmNhHzt691Du\nKMZxkkxqE+W0gnZdXR3r1q3jiiuuoKGhgbKyjycJFBcX09nZyejoKHq9nu3bt3P33XcjCMJx9zkR\nt3tiax87HCacTu9Jn6dJhAmoFCh04XE9/2SKLGUMxF8hpKrEqhcZCMf4YG8fn1tafMp5a6PxKM/v\nex2AalsN//lIE7N3v4qCBDriiDod6V/7Jj61Cd9RbQ+4D9DQuZVt0QVYCBDdkTxvJwmuml9E0Bci\n6Pvkh/1oNyws4GdN04g7t9Dy5lqqFv6Q5Zm9vNm/iw9mxqjvUbJsu48n0l/lnqX3Y9Ao2LRvgG/9\n93t86+YZqBQia7d3Y7eqiBpamDOmIdETpidNRcWMZfQ1dRAZHsZQO5Ph4YmvP74wO5U3+jw0VFSR\nP7Cd0GYXK65pobG7jlU3nLiAiCQl8I120tOzBynQgm5omHiLh3hHAAHw6UT2lJkYTS1mVfF0fI8/\nChoRoTMKIsRq7KguuZaSqoVHbkEkEgmikTiRcDz5dyR2+P+xYx4TBAGVWsQv+XFGhugN99Ed6CEk\n+A/30OOkm+yUpZRQbiumyJRL1673UG54F6E5+RlwFZtIWfU3LJi/+MhnWkrEiUWSvZFw0EnPWCct\n3j7agx66o1GOnpecphDJlQTywyJFxmxSCmp458k+nLE0Kg/oaDC+jEa/Ep3uCv5tYyOX5eYx024i\nx2Fg+8FBOrpdk1os53T9+YU9yWsRi4ZEKMbbG9cwzZ3M2Le3VM+t5TeiCulxhs78e+Aj4/0eOhGN\nPvn13dE6THahld5ON+tXH8LjCmIwaVi0opTCslTCkdhJz1VpmIZJZcQb9WHVWMhV5k/I997pEoAs\nvYYDw2NcXjwX3G207nkOlS6NWHiEaMhFIvYp3w+CApUmBY2hAJUmBaXWjlJjR6WxIyr1CIJAKrD4\n0o/ff89Ysmb4zuExXmgfxKJSckdxBoHRIOOJPtWmGggx4Z+P4zmtoL1ixQo2bdrELbfcgiRJ/OhH\nP+LVV18lEAjwuc99jr//+7/n7rvvRpIkbrzxRtLT0z91n3OZJi4SAAyOifmSyTFmkdA5QaokLRxm\nAAX+YIyuQS8FGadWh/X93g8JxkMICOzblEbaUDNl/m4EtRopEiH9C19G7fj4/lsiHsbd8xbOkSbW\nxFciIpFxIEogIeJGQmvRsrRufKlCVUoF99w8i4aDqykY7KW7sZ3Lylex1dXKB4xRNd+CdsMIyz/w\n8LjtSf7+sr8l1arj5ffb+dHjO8hLNxFPSOSWenHo1MQ3uBCAbUXpPFCUS2hXMjuarnji7mcfbU5G\nKu90DuLOn0VT/l7KO8Oo+ly06j9Akgo/0bNPxMOMuQ4x0LcPZbQDlRRGc8hHbPcYUVcypA3alewq\n19NdUM7sfWOsXH4d6Y48nJ19uFe/ibs4FeOgG9XuEaTmxzg4+x0Mi64jL78OURTRaEU0pxTMkpmX\n4ok4Pb4+mtwtHHK30jLaTn/PJt7vfJ/apgBz9wUQYhJjKSoGl8/lsuVfRHlUWkhJkhgIDtPkbqHZ\n3Uqzuw1/7OOvqVRJS/ZwiNxAhFylAnOWGTFFQ4IgMIxnaC0zFoisW2clJpWj96wlLLyCpJpBUFXN\ny51DrO4ZJq06lf4Pe9h5yMmi6Wdvze94xOMJQp4wKuDiufnEYwF27Wtnoa+dUaNIvDiPuZn1U93M\nT2V3JCfK9vd4WPdGI42HJxTW1GczZ3Hhkd7jeKgUKpbkLOC19tUszJqLQpz6+QfVNiN9gTBtsTSy\ndZlEAr1EAskZ/gq1Fa2pGKXWngzOhwOzQm0+bjKWEznk8fNCxyA6hcgXy7OwqM+9i8uPnFbQFkWR\nH/zgB8c8VlxcfOTfy5YtY9myZSfd51zl8YcQpeRwUnFp8UmePT6CIKDNM6BsCyMEP/pQSexuGTml\noB1LxHjhAVeCAAAgAElEQVS9/R0AIoM5iEEVN0X2Jo8WiWBZugzTrI8ThYR93Qx3vkgsPMo6LiOI\nlrkqDX39fUcSqXxxSTEq5fg/6Gk2PemLFsCbz/D+U29y/ffu5aay6/nd3sd4u1DHdZ068jqD5Ozu\n4Xnbq9y+cBV2s5bH3mrkYKebLIeBUc0HrAxDotXPkE1JOKMalVLEfWQS2uQMpaoVIoXREQ7ps9lZ\nnkNpVyuhD0eZ/rk2Wjo7KS0oIBbx4Bk+yMjQftSJPkRBQuePEWjwEz3gQRGJkUCgNcfAzmlqBh02\n0sPTuGzjFqruu490Rx4A9muvx7d7F7a2QSJ33cS+He8ybd8oyvXdRJr+jwPzCrDPvJL0rJrTug2g\nEBXkm5OV21bkLqatbRMDO97CtGUI7ViMoEZgfZ2JA0VaBEULjbt+R5mthBxPGg09B2lyt+CNHFUL\nXWtjmi6PrPZRHJsPYvBGETRazBctwLbsEtSZyYCbiEcO93aGiQT6yLb00uUtpKqhkM2L21GEt6KO\nNlCUejXuaCp9YpzU+Zm86/aQ7rFQYtaf1apIJ/LqxlZUvihRo4raEgfDnW8gDnShSkjsK9bzhapb\nprqJx6VSK7Gk6HAOeHEOeLGnGVhyeTnpWafWCfjIivwl2HU2Zk7BMq9PU5Ni5O3eEfa5fcwovoVI\noA+VOgWlxvaJnORnoscX4i8t/YgI3FmaRbpOc/KdptC5OQtsiu3d00NCSA5PVJ9iWcgTyaudDfua\nGTAVY9MlGA3CrmYn1y4cfxGCdzs2EYyHkBICVm8NX6kME3speYWtyc3DcXPyS0aS4ngGNjI2sBGQ\naDRcTveYjTKznuE3k5OtBpHIzDAxu/LUZ8VWr1xC8+rnyR1q5rG3mvjSVZVUppRx0HWI/qWFpD/V\nyOIGP09nbKbBXsHC6TXYzBqeXtPCZUvtDAyGELd6kCTYPk3PvNxaAIItLaBQoMkvOOU2jdeSyhJa\n2nwEUyvYX9xLTUsIoXmMkPQyLW4FalwAaIGhDoHE3gCWniEUUoKoqKaxMIMPp0fxGRSoVVXUtyko\nPLSWom9+myzHxz9LUa0m46576P7JDzG8toEV//h9XtvzAvZ3tlDQFUb1UhP+Q30cqC8kvWIl9rSK\nUw7ekpSgr3sb3qZ3UX7Yg6MziCTA3nI9ystXsDizkmxPO4fcrbR7umjzdEJHcl+z2sSs9FrKzEXk\n9AUR1n1AqGUDAKr0dKxXLse8YCEK3bH3Q0WFGrU+E7U+E0NKDRffGeSJ/3mfoLKAqvYAjYWDBBNh\njJ5XWFVyFd1CKc8fHCKqV/LooT7sGhXz0izUp5rRTnHSleYuN0YgM89CJDjEppa9VDs7iQvgm1lG\n9lnMBnY68ovsHBjrY9aiAqbPykFxBnnelaLyrBQGGS+7Vk2mXkPLWICokIHeUj7h5xgJRXisuY9o\nQuLzJZnkn0Jt8qkiB+1P0dLUgqSyokiEMJomLlF+RXE9rug6oBizSo07GKFr0MeoL4zVePKru+Ze\nN6+2rgYFpIQr+ftVdQz86/eSG9UaMr9yH6JKTTQ0wkjnS0QCvShUFoLp17CxM4ZJpaBoIMS+UJQE\nAn1IPLC05LR6PQq9AWN1DeKeBl5qOMT6XCs3lV3ND7f+N++Kce5eWUPoxd2s2uLjOdtzFFjyqCpI\n4Qd3z2F1x0vUxgRijT48RgWH7A7uKSsmEQ4T7u5Cm1+AqFafvBGnqTAth4xNr9CTXcHWqi1UtoWJ\nb/FgLjMSTyhodlqJdkBm5wBmZzIXtV9loSU3jx2zxvAqg4iiHbNqAUs2bEHrbSHvm98hL+2TozK6\n4hJsl16Ge/VbBF99g1tv+Rt2l8zhjXVPMH+rE9sBL6qWfYzN6WWospDsksswp5ScNHhLUgJnfwOu\n9jWoGnoQd3tIJKA3Q82eBXlcu+BOCszJHn91anI4PRgL0TraTlwdIV2RgT2mZWzjejzrnybsTs4f\n11dPx3bJcvRV1eMu1GIw6cjLVNM+qMLWYmRZaJgNlQk+DEdQtL7GAp2WW4y5vNxkx1JWyVAkxuvd\nw7zTO8JMu5l56ZYp6d3sbx9B5YsiAVdcXEJf11M0DjqpHIvTkm5gcdmykx5jqs1fVsz8ZcXnZd3y\n8aixGXk7EObgqJ+61NMbQTgebzTGHw/14Y/FuTbfwTTbuZGi9mTkoP0pvN4elKoS1JJzQo9rUBtI\nGDwIUhxbWKITEJDY0zryqdWVPiJJEut29fL07ndQ5scQUfKPl92K98UXSfiTw5sZX7gLRYoRV/eb\n+EZ2gJRAb6tBn3kZf2kcRJLg6owU3n+7AQGBXhJUFdupzD/90onmOXMI7GlgRqiLJ9+18r3Mei7O\nWcDa7o0cqLqMynYP4YYObjowykuWP3Nn7VeTOzr3wp4xhITEjml6rFIBRp2KQFMjxOMTmlTlePJF\nF31CJVFDLrvLA9QfDNC6zojemk5u0w7wJGt9D+tz6M2soHO+n/ZEKwIiGnUd6dFClr74DD6Nl6xv\nfoui9OOPyNivvQHf7gZG17yDsa6eGWXVFF/3TzxT8QLRTVtYsNcP74+g2j+Ga1EP/TmF5BStwGj9\n5MoCSZJwO/fg7FyLuqUPcbOLeCCO1yCyoc5E4UWXcl/RpZ+aFEOn1FKdWonOM0T7Uy/TsW0LUiyG\nqNVivWQF1qWXoM7IOK33c/HN8+j85Ub8hlwG+1WsatnE0ytT2BSK4BfUrNB0cEdlJ6HYPvRpdTRR\nxvZRiS1OD1ucHopMOuanW6mwGlCcpaHzNxu6MYxFEcxqhEQnzw02UdmanEzUPMPKZanTzko7zsRn\nNVh/pPqjIXKXb0KDdigW57FDfbjCUZZlpTA3bWKrLk4mOWh/CkUkuZRKVIUn/Nia/BxsbQO4hGzU\nJBCVMXa3DB83aIcjcf60upHN+wfQ1SVntF5ZeAlK9xijb78NgGnxfOI5Pvr2/wpJiqFU27BmXYLO\nWslTbQO4IzEuzrTRubGLREIiKgg4Jbj/4jO7X2+snYmgUjE73sd7sWoefnEf371jCVsHdrK6cx2z\nP38/0Y7/gF0eLsrvYYvpFRzmAsqiUSL7vIR0Kg4WallkTBYECLUmX9+ZVPYar/IZtbQ3dNCdVsn2\nad3UHAqTc2gfsI+4oKTPUoEzoxpxvpptiU0EYkFUijQ0mkWUOT3Me/0R+h0Cmfd9g7L0yhOe6+hh\n8sFH/0D+93+AUWPgb6bfwc6MGTxT8hwzdgxR3RIi+soAyqIxhhf00GfOJ7fkUnSmPCRJYsx1gIGO\nNWicA4gbR4gOhokpBLbVGOiuz+W2mlspshw/NbCUSDD05z/h2fAeAKqMDKzLlmO5aAGi9syGBfUG\nDUUFRlq6QuREA3QrS7n9jRYeu8JGQ9BHRFNI4YieQmMfCdeHlPIh5WoHfaZ6dofTaPMGafMGyTdq\n+WJZNppJLufp8oaJRpOlbGfVZ/HaoSfo90W5ojuCW62nfPbSc2Iy1oUuVasmQ6emeSxAKBY/o9sp\ncUnCF43hicTY0D5AXyDMrFQzl2SlTGCLJ58ctD+FKioQAzTWib+Kza+dS3zvRlz6bBxaBX0h2N/u\nIvopRRUGXAF+/eJeep1+0kuHGFNG0So0rMi/mN6f/AikBIppdmIzRvEOfYBCZcKcsRijvRZBULDd\n6WGvy0eeUUtVQsErrS4EBDqlBAtnZJLtOLPhIFGrw1AzHd/OHdxwkZ7nDwZ5cnUH18y7nL80Pc+r\nfeu4+d6v0/OzH8MaJ+n2Xfi9BzDs9yJEE+yoMhCNGFlcnuylBg8Xoz8bPe3ivBqyX/oZ/VnXEdDq\n2DQjzqx9MGAqZjSrmoKL0nAqNtE42oxCUKHVzEevnsa8xu2UrH+HjiwN6V/5KlWZJ14m9hFdcQm2\nFZfhfvsthl98nrRbbgOgLm06pUuKeCrjBZ5s2sXFO/xktQUIdwRR1nkZivYQ1eYjxQOoA0MoPnQR\nPuhFAA7laXh/ponZFUv4btHlqE+QclKSJJzPPo1nw3sYCguwXnsj+mmnX6v80yy8ro62X27EYyrE\nHRmm1NPOratdPHtlOgdG2+lXpfPCphnctdhCeWo/AU8TGZG3yAB8pgq2xGto9oV4vLmPL5RloZrA\ntv21Zz9sQzccQgLipr1sGPAwuyWCMiGxt8DKzdkXTh76c111iol3e0c4OOpn5nF629FEAm8kjica\nwxOJMhZJBuexw0F6LBLDG40fk5WgwmLg2oK0c6YIzHjJQftTqOJaYgrILM6e8GPnVtYzGvgLMA8z\nIr0kSCSiNHWNHlPCcEfTEL9//SChSJxlddns0rwHcbiq6DJ827YQamtFcKhRXmxGEBVYMi/FmFqP\nKCa/uIeCEV7tcqJViKwqSOetx3YC4BchIIhcu/DMq2cBmGbPxbdzB3PivRzIL2ZX8zAl2UXkmrLZ\nNriLxfXzsV12Be633iC6aQTzolSCuz2g1bC3VIPOl0N6ih5Jkgi2tqBMTUVpPf0h+/FSiAr0lhiW\nUTchbSF7KhrBtpgFFbVobG081/EEkUQUkyYPlBeRqrWyaONr2Bp20pqjwX7PPdRm1Z7SOe3XfTxM\nbqqfhe7wJEeT2sg91XewI206zzheJLvVzcW7g+i2jxI+4Ee3MEDCHyewZRRFLI7HpuHdOh3hgkzu\nqbz5SDnME3G9/iqj76xGnZlF1Q++z2h44r+odHo1JSVWDrV6KfU08+HMa1m24xmuWONkz/UzaR3r\nQFXp5e2mJSyYfROJWIjA6AH8rj3gb+RiqYm4Yhlt3jSeaOnn9pJMlJMQuKOxOP2xKOljUUxpap4d\nfB9RkqhqjBIVFCiXVGBWX5j11c9FNTYj7/aOsG14jAQcDsJRxo4E6RiBWPy4+ysEAbNaQb5Ri1mt\nxKJWUpBqpkSjOmu3YiaSHLT/Sk/fEEhGkBJUz5j4pQ9KjZaIGYzhESQhBRGw6MLsbhmhushOPJHg\n+fVtvLWlC7VK5EtXTyNgOsTmlhB6pY5aUWToxUcBUC1Kx5q1DJNjLqLi44lb0USCp1r7iSYkVhWn\nM9DoZGw02avoSCS49KICbKaJmfhjmD4DQa3Gt30bX/re1fzbo9t4fn07t1+/jG7v4zx76GW+c81X\nCOzfS/hgN65AHEMoQXNtOhFViHpbVbLNgwMk/H4M1SeunTuR7PVzqXpnK66L6ojEGolU9PNmvIOO\nti60Sh027SLiYhHTzDpmvfQYipYWDuVpsH3xC8zJOfX628lh8rvpfuhHDPzx9+R//weImuTPQRAE\nZqXXUmot5knb8/wxex9zD4aZedBP9O0hACStinW1evaWaFmct5Bri1eiUZx8wt7oujWMvPQCSrud\n7G99B5XZDJOUOGP+FTW0/Op9hs2lhLp7GKieR96+D4lsaKXk2hWs7lyL07GGht4carNLMabWYUyt\nIxZ2Mzb4AZcMryMmLuOQx8FTrQPcWpyJYoLv276+qxuNP1mpaciyn6AkcXmLAkskyH5bFosrlkzo\n+WRnxqFLDpF3eIN0eIPHbFOLAma1kky9GotKiVmtPBKYP/q/Qan4RG96IpLbTBU5aP+VnQf2ERGt\naGNeUm0Tm7bwI8r8Qhwd3fg0dqzC4VKdrcNc5cvnty/vp6l7lPQUPV+7vprsVAPf2fBrAK426Bjd\n8CLScARFrpW8S7+HqDz2XmQ4nuCVziEGghHmOMyU6rX8aW0DAG4BFDoVK+fmTdhrETUajDNq8W7b\ninakn69cU8XPnmzglbc91C6aQcPIbrYM76b+ni/T+e//iqEziKBWsb4wQiKsY2F1chlHsCV5P/ts\nDI1/pLJiPpHHn0MfXUJINB0pTZlrnoYnXgcKHVekmcl67NfEO7tozNdgvPM2FuRddNrn1JWUfjxM\n/tILpH3u1mO2WzQmvlzzBbal7eIZ3cvsLVRxaaOIXy2xtkLAYLHzjcqbKbONbz7C2JbNDP3lzyjM\nZnIe+C6qlMm9f6c3qCmtsNPU5KbMM8Y6/TyudByg5MAIsTIXI45lbPOt5Q9Nf+TL2jupsidntis1\nNmy5VyCqDFzav443xWUcGIVn2we4uShjwtZ1S5JEw4gX61AQCYkOYwfT1DpSG5LL/NqnZXCdeeJ+\nP2QT48bCdJo8fsyqj4OyWaVEqxDPu+HtMzW5sz3OQ51D3cRFNSrJO2kfhrza2aT6uwCwqxR4QloC\ngTH++fdbaeoepb7Mwb98YRbZqQbWND1DKB4mX6kgPxYhunEYBMi59++OCdhxSWLLkIef7+lg14iX\nDJ2aK/McfLi+jVg0jiRAl5Tg2oWF6E4hU9J4GGcn7/95t22lPM/GDUuKGPVFGGkqQC2qebn1TeJp\ndhw33ASAeNFM/LoECm8mJdnJWZsfVfbSTmBlr5MxqY2M5tuoOLALtboOmzaDUvvVjEkLsGpM3J1n\nJ/uP/0O8s4sDhVrUn7+JZfln3guzX3cDqvQMRt99m2DzoU9sFwSBORl1/NPcB8gpqObZ2SJvzFAw\nu2gB35vzwLgDtm9PAwN/eARRqyXnW99BnX52CnbMW1GBQIJBSwVprbtoW3oHAY2A+Mo7XKfPId5a\nRzyR4Ld7HmVz37Yj+wmCgDXzYuxZS7hcWEeG4GKPy8eLHUMkTiEn/InsbB8hrlWgGYviswxj1cS4\noteGLepjSG2ldvGcCy4InA+yDVqWZdmZ5bBQZjGQrtOg+5Qe9IVADtp/JeZLpqYUxeBJnnn6rOVV\nmMIuFJIfU3KUjgyzj0Aoxs1LS/jqdVUQaqP/4O/4cDBZsvJiSwHhJ7ohImFZdDGazEwg2XM4OOrj\nV/u6eLlziHAiwbKsFL5cmYvXFeTArn4A+iWJFJvuhEvLTpehpgZRq8W7fSuSJHH53DxmFNs51BYm\nT6jFF/XzZse7WFdcRs63v8vG6uQ96zJTxZElK6GWFgSNFk32+NKpThRt7Qwq9m9HqygmobqaoUgG\npWY9X8mzIf36P4l197CvWIvwuWu4vGjFhJxTVKvJ+OLdAAw8+nsS4U9fpWDVWPjq9Lv4Us2dfKvu\nq9xSfj1a5fhuawQONdH/m18jKBRkf+MBNLlnr/eoN2oor0ojpDJSHPPzQYObXZfWJSfDPfIws3TZ\nhBtnoxE1/LnxWd5sX5MsSXqYJWMhjpxlrBTX4hBG2TE8xmtdzmOec7pWN/WjHUquSfel9HOzowTf\ne42ISOxJz2VhwbmTXEQm+zRy0P4rysMpRkXN8Sc2nPE5UuyEdWrs/m4UCTABOrXId2+bycXT4gy1\nPIqz7Sn2efvojydIURlJf6kXAlFEne5I1rMef4hHmnp5vLmf4VCE2Q4z364pYHm2HbUosP6tZC8u\nIQr0I3HTxcXHlOicKKJKjaF2JrHhYULtbYiCwN1XTcNu1rLvQwtmpZX3ejYxGBhCU15Og/sgUkTD\ngqLkOti4z0ekvw9dURGC4uwusympvogEQYoPJYtGLM9O4fNpekb++yFivX3sLtURv+FyrileOaHn\n1ZWWYlt+KdHBQUZeeuG4zxMEgVpH9bgmm30k1NlB3//8AimRIOu++9GVnr3Ri4/MubgUAYleaxW1\nQ3sZic5h/SwT+AMsPPAGijEjM7gWu9bGa+2rebLpBeKJj3/nzGnzyMhbwZXiGlIEDx8OeXirZ/iM\nAnefK4DPLKAbdCMJCeZmj5IxlAGxGBFBSXRmGepxzBGQyaaSHLSPkoiHUEWSGdCs6ZOXzk4QBBR5\nBWSNJYfIU0SBbrcOc+BFhloeJ+LvQW0u561gsht+bY+dcHfyuY7P3YZHUPBUaz8PH+im3RukwmLg\nG9V5XF+Qjll9uPJP8zADPR4AOhNxirIt1J1m7e7xMM35eIgcwKhT8dXrqhEFJb7WUhJSgueaX6Vl\ntJ2oFCIxmk51YXLOQLCtFZj4+tnjkWfJpTvPyPwNb/JNQ4xFOpGe//wJsf5+dpXriFy9lBvLrpmU\nYbjkMHk67nffJtjcPCHHjAz00/uLn5MIhci8+14M1VOTR9pg0lBenU5IZSI3EaKr0clQ0Sz2lOhQ\nOPu52vkB+w+GeKDuPnKMWWzq28L/2/cnIvGP64qZUmeRlb+Sq8S1WPGycWCUNX2u027Ts7s6iPq2\no/MbEa0u5ufPZuyt99BIMQ6YCrli7qyJeOky2aSSg/ZRvJ5uxLgBRSJKZsXxk1RMhIyaGdgCA0hC\nFKsgMBbS0Od0ojUVk15+D5slE+FEjIywFvOa5H0/VU4um3LL+K+9nexx+cjWa7i7PJs7y45Nch+L\nxdnwdjIIRJUCw8DNS0+eGvNMGKZVI+oN+LZvRUokACjKMnPLJaX4B1PQhNI56DrE000vA5CnKUWj\nTvaqP0qqMllFQk5EFESoKkOUJMbWvEb3T39EbHCQ7ZV6AisXcmvlTcnnTMa5NRoyvngPAAOPPkIi\nEjnJHicWdY3Q818/I+71knb7F45cSE2V2YuLEJDottawwLUbT3MB780048m2UubtpKRtK6OjAn9b\n9xUqbKXsHT7Iv27+KS80v0aXtwdJkjDaZ5BbeCVXKdZixsfaPhfr+089cPuDUbqFQ+idyVsR1Tke\n1IFswiPJofLGrAJK0zMn9PXLZJNBDtpH2dPWSFwyY4i4ySs9cZarM6UvLkMkgYZ+1PFkcYrXWxaw\nzbmA5iEl73W/D5LEVWucSIeD7epZS3l/aAyTSsHNRel8dVouxWb9MceVJIkdmzoJHL433xqLU1/m\noCTHMqmvR1AqMdbVEXO7jwRhgGV12cyuSMdzqBQQGAwOIsVUzCv4+P0NfpQJrWhiKqqdqtzaBYRV\nAond+4k5nWyp1uNbPoc7p90yaQH7I7rSUqwfDZO/+PxpHyc2NkbPf/2MmMtF6o2rsC65eOIaeZqM\nZi3lNRkE1WbSEwmEYRe60Rk8M1dFzGxhsauBptUb0Sm1fHXGXVySu5hIIsqa7g08tO1X/PuWn/Nm\n+xqC2gwKiq7iKuU6DARY3TPCB4Ojp9SWJ3ftIsgWLK5MBCFB5YxqPG+vRSRBv8ZOzsyJKwwkk00m\nOWgfpbnXCYKILurBaj31ylenQpOfT0IUcPi6AcjSqmgfCPP8+jZ+tf4VYlIMdW8W+9UlCJJEe345\nQ1n5rMxJ5Vs1+dTazccsg4nHEjTu6eeZP2xn5+bkUHpAKRIQBW48w3Sl42U6PIt8bOuWI48JgsAX\nV1aQpk8jNpCcDBV3pzGzJDmTWYrHCbW1os7KRqE3nJV2/rXKtAracpIjFZtrDHguruOumtvPWhrL\n1KOHyVtOfZg8HgjQ+4ufEx0YwHb5FaSsvHISWnl6Zi0sRECiM2U6F7sbGO3KwIeeg9dOJyooyd70\nEsHuLpSikhtKr+LHC/+Ze2u+wMy06bhCLl5rX833Nz/Ewy1rGDAWsUJci54gr3U52eb0jKsN3rCP\n3cG3UAe16AJm0tL8mI0l+PfsRgB2Wcq4vHZyL9Jlsokir9M+itsLekAkMOlLCUS1GjEji4KhTnoM\nF5GmV3H/PXPY3zXIX/regZgSf38Z5a7XiSHyFtNQbR2kMTtEyDFGhkmDVhDwuIN0tboYHvQSjx81\nSUeA1liMJXXZZKToj9+QCaSvqERhNOHbsY20Wz9/JEWmTqPka9dV8+9/9hKVRHIV044kdwn3dCNF\nIujO4lKvv6ZT6uheWs3eklYMpWV8reZOVBNYr/dkPhom7/7pUUlXxlnlLBGJ0Pe/vyTc1Yl50WJS\nb1w1ya09NSaLlvLpmTTuESiURBz+YTw9M3ivZBvS7OVM3/oW3b/8JcXf/1cUJhMqUckMRxUzHFUE\nYyEanPvYPrCLJncL7WOdiAhkK59HUtbzQnsElShQaz9+IQlJkvjV9idICD7SPPMAKKvOw7NhI5Ik\nERFV9GYVkGU/Pyo8yWRyT/uwRDwC/uQXtag4s3uL42WtrEQbixDTDhNzhVBKsD/0IZqQjpTwPC7S\nBBg2l9NQdi0lJjvZoxEi+4doea+d919t5N1XDrJtYweDfWPE4xKCkPySzMyz0qcSkNQKrlkw/lnH\nZ0pQKDDW1xMfGyPY1HjMtpw0I3csrybWXc7Smoojj3/Us5yKSWhHu6xyJaW1i/nq9LumZAbxx8Pk\nAyecTX40KRaj/7e/JnioCeOs2aTf8cVzct1q/UX5CEB7ygxWjDYQHLEQddsZqRN53zYdRkfo+93D\nSLHYMfvplFrmZ87i6zO/xA8X/CM3llxFjimb7liQsdD7jPme4NH9T/J6x85jZp4f7d2u9fSFW1Eo\nskhzGRFFiZKqCkbXvosA7DMVMbu6YNLfA5lsosg97cOiwQHUgeTMcZVhYhI5nIy2uATWvItW6iOO\ng8d/sxkSaZSSHJoPAh0pDkiAGIphM6oRlCJjwShj4Rh+JAJA5PAfCYE8vRINCXojca5fVIjZcHYD\nkGn2XDzr38O7bSv6ymNLGy6cnkl5npXyYgeukWRJ0amchHa0UlsxpeNMWjJZUq+7Af/uBtzvrMZY\nV3/C0QcpkWDgj7/Hv2c3+qpqMu6+d0KLf0wks1VHWU0GTXtBKakoDvXT3lXFQetm4nmLyYp7KGo8\niPOZJ0m77Y5PPYZFY2ZZ3mKW5S1m0D/Ehz0b2Ny/C2+slTfaWlnb9Qpz0muZnTGTQnMegiDQ7G7l\n5dY3EdDjiM0j4guRV2Qi1rSfhC/5+Wswl/G1yonPXSCTTRY5aB825u1GETGiifoxFZyd2qofpezM\nGGujzVoBAoRVHqzBKCWhCKqhbjIuWUTagrkM9o2xd3svzgEvIlCWaWbGnFxSsk209Xlp6fXQ3OOh\na9BLPCFhNaq5dPbZT8eoKytHYbHg3bmdtNtuR1Ae+xFzWHXH5JIOtrSgMJpQpZ2dbF3nMlGjSeYm\n/+mPTzhMLkkSQ08+gXfLZrTFJWTd93VE1fErfJ0L6i/K49C+AdpTalk+vInfaVcS6MmjoMLJi96L\neEATZnTtGtSZWViXXnLCY6Ub0ri2/CYuz1nAuwdfZG1QRzjWzobeD9jQ+wF2bQr16TP4sH87kgQG\n/SW+5TEAABlwSURBVCXkd7vwo6e0KgfXa79HAnq1qUQdaeSmyUPjsvOHHLQPa+5rQ5ByMUZ6SCma\n3OVeH1Gm2MFoItfp5Z2L3iMhxkkZi3HVQT2q9l5UmVl4Ugt59and+MaSS1UKy1KZMSeXjGzzkaHQ\nFLOOWRXJ3nk4GqdzwEuKSXNkSdXZJIgipvpZjK5dQ6Dx4AkLgERdLmKuEQy1M8/JYd2poCstw3rJ\nCkbffZuRl144kkjnaCMvv4hn3RrU2Tlkf+NbR4qOnMssNj2lVekc2gdRUU99tJsdAwUMOrYTVdrZ\nUXctczY9wdATjxNzu7Ffd8NJRw40hkwurVpFStObvBa5hUSinxzdAK2jB3m7cx0AWnEWaqUDadCH\nQiGQZYnT13woOQHNXM6c8gz5syc7r5yb42lToK0/WfHFGHGTWVJ1Vs4pCAKGsjIMoQSGUAQEqG8M\nokuoQZLYoaxh8/oOQsEo1XXZ3PblOVx+QzWZOZbjftFoVArKcq2kWicvOczJmGYfm2jleEJtZ79I\nyPkg9fobUaWl435n9Sdmk7vfXo3rtVdQOdLI+dZ3UBimZsb96aibn7wYbk+p5aLBnYgJCX97MYbs\nPj7oiZL14D+gSkvH9cZr9P/m18dN73o0lc7BrIorWalpQKHIwRmdxddmPMhdVbeRKyxEbailyN9N\nwK8jr9hO4P33AAgplDQZ86kvn7yEQzLZZJCDNpBIRHGNJYcXtVEPFsfZu8d1ZIh8OIrdJaJVzSbW\n0YZTn4s/JY+5Swq54775LLq0FIvt7MwCP1Pa4hKUNhu+XTs+MbnoaB9V9prqSWjnGlGjIf2LfwMc\nzk1+OOmKZ9NGnM88icJiJeeBB1Faz85tnIlis+spnZaGT5NCQGFhhdRFwmsnoQoQiEQ4FNCQ971/\nRldRiW/XDrp/8kOirpGTHleltTOv4gpWqHcTleDPLf2kqEvwx3OTRUj6k/evi0pS8Gx4D4C95mL0\nJj3F2ZObv0Amm2hy0AaiwSHC/mSPRSSMeBYn9OiKkgGrqENPatd8Mlp2IQkiqTfdzO1fnUfd/Hy0\nunP7fuVfE0QR46w5JAIB/Pv3Hfd5odYWUCjQFpy9Ge7nC31ZOdZLlhMdGGDk5Rfw7tzB4KN/QDQY\nyHngQVSO87OHWH9RsrfdZq9lWtd2NEKEYHchYkofWw8OojAayfnbb2NZvIRwdxddP/wBwba2kx5X\nUpgpzVjMEsVewpLIY83dxC1G0qUhfCN2lEqRlNEWpMMXQLtMFdSVOSas5KdMdrbI97SBsL8PZUCP\nICVAffye4WTQ5OeDQkGmK4pJ24c+5sW6/FLSFh//XvD5wDR7DqPvrMa7bQvGGbWf2J4Ihwl1daLN\nzx/3muQLTer1N+HfvRv326sRFO8iqNVkf/MBNNnZU92002ZLNVBS6aDlIHjVdq6TOng6WoYoiOxq\nHiIcrUCjUpJ2xxdRZ2XjfPpJen72Y9Lvuht17SyG3EGG3EGco0EGD/895A7iGgshAUa1kUVzd7NL\nnfzMZQ87cfqsFJU78K75CwDdJgsutYW6stQpfCdkstMjB21g2NuJJmTFEBkF89m9Fyyq1Why87B0\nd2Hr2wMGA/arrz2rbZgM2sIilKmp+Bt2kYhGEFXHBuZQZwfE42iLpy6pyrlO1GhIv+tuen72EwCy\n7/8muilK9TqR6i7Kp+Wgk7aUmcxsewv7tGxGhjNJmIfYcmCQ7FQDQ6NBhrSlxObeSMW2lxn4v9/y\nvm0676fMgL/qHVuNakpzLKTZ9DhsOtKMEobQTkYEPUpXBhAizy4R7e0FYLe1Gr1GSUWebQpevUx2\nZuSgDbQNDCJIqRgibjQ5Z/8XWVdcQrijnUQwiOPWz59Xk4uORxAETLPm4H7rDfx792Kqqz9me+jw\nBCt5EtqJ6cvKkzPEDUZ0RUVT3ZwJYXcYKSpPpa0JxrRpXBds4/dCDVJUy6NvHgSODsp6tmav5OaB\ndSx076HSEMV76U04Us04bDocVh0alYJ4PEFPh5uWA0Ps2zJMNJKCRhPHpwGlSsTYtJkgEFKKHFAX\nMLckdVLK1Mpkk+2CD9pSIkb/SDKbkjHsxlZ49gsHaIuLYc07qDMysS5ZetbPP1lMs5NB27dtyyeC\ndvAcSapyPjDUTE15zclUf1EBbU3DtNrrqG97nYJZmXS4U0lNj1KXV0SaTZf8Y9WRYtZCYDn9D/8v\n9uaDZK39M9n3fwPRrKe3c5SWg0O0HxomHEre2jKaNUyrzSIty8zbL+6nqMRG8J0PATiQlUdCUExq\nmVqZbDJd8EE7GnIyOmZASXK5V1rp2VnudTTj9FpM8y/CunT5J5KRnM80efmo0tPx7W4gEQ4fWU8s\nSRLB1haUdjtKqzxEeSFKTTdSWJpKezN4tOmsHGznNzoLw4NKvEXdXD9jIZqj08mazGQ/8CCDf3qM\nroYW9v7iJZyWYkLhZBlYg1FN+awciisdpGclcxhsWZ+cwJYZH4R48sL8gLEOdUKkuijlrL9mmWwi\nnFaECIVCPPjgg4yMjGAwGHjooYdISTn2l+DRRx/l9ddfB2DJkiXcf//9SJLE4sWLKSgoAKC2tpZv\nf/vbZ/YKzlAk0E/Eb0QJ6MOjWDPOfhYxUasl8+57z/p5J5sgCJhmz8H12qv4dzccqe8c6usn4fNh\nmFY9xS2UTaX6Bfm0Nw/TkjqLWV2vcs0NFby6X8fmzQr2Of/IdXOrWJg1F6WoZKB3jNaDQ7QGphHI\nTo7OqAN+yvLNVC6uIjP32NwFkiTR2uhEqRLR7XgbCeh26OmJGKkvs6NRnf3EQzLZRDitoP3kk09S\nVlbG17/+dV5//XUefvhh/umf/unI9u7ubl555RWeffZZBEHgtttuY/ny5eh0Oqqqqvjtb387YS/g\nTAUDvSiCBhSJMDEV53w6yPONafZcXK+9infb1iNBe6wxWUxEHhq/sDkyTOQX2+lshVFdBsWb9/Ld\nL3+Z/35mD96WUp73NbJN0YnNnU0skNxHo1VSOSOTbJWHxItPIbSH0NhuhNyrjjn28KAPjztIQbYG\n6eAwAN0FteBGHhqXnddOK2jv2LGDe+65B4DFixfz8MMPH7M9IyODRx55BIUieTUbi8XQaDTs37+f\nwcFB7rjjDrRaLf/wD/9A0RRPrulxd6MOVWIKDxLWTV0Wsc8qTXYO6qws/Ht3Ew8GUeh0eA82AaCd\nwnKcsnPDrIX5dLaO0JI6hzndr6Bsbueaiixe2dtPYKAIHwJ6RZRwupuamnyW1tajOnwLKVyeQe//\n/IKRF58n3NuH7vpbcY2EcA766G51AZDqPABAQCPQKk5DIUaZUWKfstcrk52pkwbtZ599lscee+yY\nx+x2OyaTCQCDwYDX6z1mu0qlIiUlBUmS+OlPf8q0adMoLCxkeHiYe++9l5UrV7J9+3YefPBBnn/+\n+ROe32bTo1RO7FCWw5Fsu5SI0+0MIiBgDLvBYjiyTTZxgksW0f3k04htB3FcvITuxkZErZac2koE\nhTxMORXOlc+5w2Fid0UPLY3g1qZjeeI3pOjSudKUyzZjDj2CEWNlgh5DA21j29m+axM3TbuCEk0Z\nLsmCa+VXaN9ykFGnjtjvdx5z7IwMA8ZNHwDQU5VH70iEuvI08nPPjfvZ58rP4EJ1vr7/Jw3aq1at\nYtWqVcc8dv/99+P3+wHw+/2YzZ8sQh8Oh/ne976HwWDg+9//PgDV1dVHet+zZs1icHAQSZJOmLDf\n7Q6M/9WMg8NhwulMXmREgoMMjyXLcRojbtR285FtsomjqKoFnqZvzXoSBeUEu3vQVVQy7JrYn61s\nfI7+HTgX1MzOpqVxiOb8ZcwYXEfK6AApwQHKh7bhUVtoHC0mt2AhAQuMHojwzrpe1iQGjjqCBaMq\njMHThkURpOjq5WRWF+F76xVcUhwJ8OQthn1QXWA7J177ufYzuNCc6+//iS4oTmt4vK6ujvXr1zN9\n+nQ2bNhAff2xy3kkSeK+++5j7ty53HvvxxOs/vd//xer1cqXvvQlGhsbycrKmtIKO5FAPz6vARXJ\noG3JnzNlbfksU2dkosnNxb9/H/59ewD5frbsYxnZFnIKbPR0uFHe8U2CXj8Dzb2MDAcYi6mRBAV4\nAA8Y0KJSBXGZRxgze7E6tFxas4CqtDLcb7zGyEsvEHt0L9G/uRv3ujUA9Obo6Rq1IuBhZqmcBU12\nfjutoH3rrbfyd3/3d9x6662oVCp+/vOfA/DHP/6RvLw8EokEW7duJRKJsHHjRgAeeOAB7r33Xh58\n8EHWr1+PQqHgxz/+8cS9ktMQCQ4Q85tQAYawG3u5PJt5sphmzyX8wnOMvPQCADo5E5rsKLMWFtDT\n4ea9N5uOPKZQGnBkGTCqojibm0l3tZDr70YkucwrYNPTmCbxunMva0pKuXLBSjIzMhn4w/+j/ze/\nPnKc+Ox5tOz1UJJjwWI898uYymQnclpBW6fT8atf/eoTj991111H/r13795P3ff//u//TueUk8Lj\n7UYVyEMZ96GUYliz5cIVk8U4ew7DLzxH1OkEQPsZSMcpmziZORbmLS0i4I2QmmHEkW7CatcdKd7j\nC9bz86cbcHUPsNziZabgRGg8QF1TmLqmIJEN22nO2M2+klym33Ez0nOvEvd48OtE4o75SHTLs8Zl\nnwmfnUwep0iSEnS5hlHGSjFGhgiqtCi02qlu1meW2pGGpqCQcEc7+rzcz0SqVtnEmjn3+DkSjDoV\nD94yk188u5sXez10ltVw7z1fIdrWjH/vHkZ376CkZwR6Wom/10pIr0ILjMws4kBHcv6NHLRlnwUX\nbPLdWHiEgdFkkLYEXYTk5V6TzjQ7OWfAVFE+xS2RnY/0WiUPfG4Glfk2dh5y8utXG1GVVZJ2y22U\n/fjnFPzwIbj2MoZyLShDUcJKgezFV3Gww01euhGHVf4dl53/LtiediTQz6hXD4Ap7CJhkXvZk82y\nYBGh9jYyVl5GcKobIzsvadVKvnnTdH794j72to3wi2d3842bpqNVK1Gnp1N29a2UXnULjYP7CQU8\nxALpxBMu6uVetuwz4oLtaUcC/QT9RiA5c1yTapniFn32KYxGsr7yNYyfkWpVsqmhVim4/4Ya6soc\nNHaN8l9P7yZwuFgIJNPnVmZUM7toATubk9nQ5KFx2WfFBRu0w4F+JL8RiKOPejHm5Ex1k2Qy2Tip\nlCJfva6KedPSaen18LOnduELRo95TjgaZ2/bCOkperJS5TkUss+GCzJoS5LE0Fgv6qARRcKLgERK\nxdmv7iWTyU6fQhS556ppLJqeSeeAl4f+shOPP3Jk+/52F5Fogvoyx5Tmg5DJJtIFGbRjYRc9YwpE\nSYEuOgaANU9O9iGTnW9EUeALKyu4pD6HXqefnzyxE9dYCICdh5LLC+WhcdlnyQUZtCPBAYYPT0Iz\nB11EFCqUxvMzD61MdqETBYHblpeycl4eg64AP3liJwOuAA3Nw9hMGgoy5d9t2WfHBTl7PBLow+s1\nIgKpAScheX22THZeEwSBm5YUo1EqeOn9dv7t0W2EI3HmV2cgykPjss+QC7OnHRgg4k9OTLGER0gY\n5KAtk53vBEHgmoWF3Ly0hHAkDshD47LPnguupy1JEsFAH6K/hoQYQR0PEUvJmupmyWSyCXL53DyM\nOhUtvaOU5cpLOWWfLRdc0I4E3QwEwqgjeiTBBYBeXu4lk32mLJyeycLpmVPdDJlswl1ww+MBbw99\nY8lJaKqoDwBLaeVUNkkmk8lksnG58IL2WC+uwzPHjUEPACmFci5smUwmk537LsCg3YPfl0xfmuIf\nIS6IKK3WKW6VTCaTyWQnd0EFbUmSCIz1kvAbkZBICw4Q0mgRxAvqbZDJZDLZeeqCilbxqBdPyIcq\nYCKqCmGIh0joNVPdLJlMJpPJxuWCCtqRQD99ASWKuIrE4eKQaptxilslk8lkMtn4XFhBO9jP4OGZ\n44pIMmhrsrOnskkymUwmk43bhRW0A/14vMlMaLqgHwBzkTxzXCaTyWTnhwsqaMcjY4T9ZgAsgVEA\nrCUVU9kkmUwmk8nG7YIK2rb8a8BvIC7GSAk5SSCgSZVzE8tkMpns/HBBBW13XEQdMhBWh0mJeomo\n1QjKCy6Tq0wmk8nOUxdU0G7t7UOQRCJiDGM8REKnnuomyWQymUw2bhdU0O7tHwFAikYBUFrl5V4y\nmUwmO39cUEHb7QwAoPIdXqOdIVcBkslkMtn544IK2uFRCQBjcCz5d2HJVDZHJpPJZLJTclqzsEKh\nEA8++CAjIyMYDAYeeughUlJSjnnOf/zHf7Bz504MhuS66IcffhiVSnXS/SaTOWHDp4iSFklW97KV\nTjtr55bJZDKZ7EydVk/7ySefpKysjL/85S9cd911PPzww594zv79+3nkkUd4/PHHefzxxzGZTOPa\nbzItWFSOU6fGGvUCoJGHx2UymUx2HjmtoL1jxw4WLVoEwOLFi9m8efMx2xOJBJ2dnfzLv/wLt9xy\nC88999y49pts4Wgcr1LEFvUSUaoQNXKxEJlMJpOdP046PP7ss8/y2GOPHfOY3W7HZDIBYDAY8Hq9\nx2wPBALcfvvt3HXXXcTjce68806qq6vx+Xwn3O/T2Gx6lErFuF/QiYyMBIhFY5hjfqImHQ6HaUKO\nKzt18ns/teT3f+rJP4Opdb6+/ycN2qtWrWLVqlXHPHb//ffj9ydzd/v9fsxm8zHbdTodd955Jzqd\nDoB58+bR2NiI0Wg84X6fxu0OjO+VjENv9yi6gAcRCdFkxOk8+UWDbOI5HCb5vZ9C8vs/9eSfwdQ6\n19//E11QnNbweF1dHevXrwdgw4YN1NfXH7O9o6OD2267jXg8TjQaZef/b+9+QqJMAziO/17HMV1H\nmwXd2M3/aRsZ0bptneoSUgRmQQRDIDRe6mJuaJnRPxxSqE4RHSJakA5FJy9dCsRD4aGYWset9tAG\n/SESzHXc1tFm9uCuW7FsaObTM8/3c5l5B4b58QwPv/d93nfeuXNH1dXVH3zfpzb6+5/KG5u6CM2/\n6Kt5/WwAAD7WrK4eD4VCOnDggEKhkPx+v06fPi1JunjxokpKSrRhwwbV1dVpx44d8vv9qq+vV1VV\nlYqKiv7zffNlJCdTXyam9q4CpRXz+tkAAHwsL5VKpUyH+D9zuYTR8lO/vvu5Vz+M/KLFrW3K/ZZ/\n+DLhc1+aSneMv3l8B2Z97uM/58vjtsrPWzD9c6/sxUWG0wAAMDNOlXYy+UbByVG9ychQxt83fQEA\nwBZOlfbwqz8UnIgrucAvz/NMxwEAYEacKm3/yKj8qTfy8jnKBgDYx6nS/j7zN0lSZkGh2SAAAMyC\nU6Vd8epXSdIXxWVmgwAAMAtOlbZ/ZOpubAsr+akXAMA+TpV29nhy6rGk1HASAABmzq3SDuTL8/mU\nGQyajgIAwIzN6jamtsr6+htllRTLy3BqXwUAkCacKu3FTT+qcNFCDQ3FTUcBAGDGnDrk9Hw+bqoC\nALCWU6UNAIDNKG0AACxBaQMAYAlKGwAAS1DaAABYgtIGAMASlDYAAJagtAEAsASlDQCAJShtAAAs\nQWkDAGAJL5VKpUyHAAAAH8aRNgAAlqC0AQCwBKUNAIAlKG0AACxBaQMAYAlKGwAAS2SaDjBfksmk\njh07pgcPHigrK0uRSESlpaWmYzll69atysvLkyQVFRWps7PTcCJ33L17V6dOnVJ3d7ceP36strY2\neZ6nqqoqHT16VBkZ7L9/Sm+PfywW0+7du1VWViZJCoVC2rx5s9mAaWxiYkLt7e16+vSpEomE9uzZ\no8rKSmvngDOlff36dSUSCV2+fFnRaFRdXV06d+6c6VjOGB8flyR1d3cbTuKe8+fPq6enRzk5OZKk\nzs5ONTc3a+3atTpy5Ihu3Lih2tpawynT1/vjPzg4qF27dikcDhtO5oaenh4Fg0GdPHlSw8PD2rZt\nm5YtW2btHLBj12IO3L59W+vWrZMkrVq1SgMDA4YTueX+/ft6/fq1wuGwGhoaFI1GTUdyRklJic6c\nOTO9HYvFtGbNGknS+vXrdfPmTVPRnPD++A8MDKi3t1c7d+5Ue3u74vG4wXTpb9OmTdq7d+/0ts/n\ns3oOOFPa8XhcgUBgetvn82lyctJgIrdkZ2ersbFRFy5c0PHjx9XS0sL4z5ONGzcqM/PfRbVUKiXP\n8yRJubm5Gh0dNRXNCe+P/8qVK7V//35dunRJxcXFOnv2rMF06S83N1eBQEDxeFxNTU1qbm62eg44\nU9qBQEBjY2PT28lk8p2JhE+rvLxcW7Zsked5Ki8vVzAY1MuXL03HctLb5+7GxsaUn59vMI17amtr\ntWLFiunng4ODhhOlv+fPn6uhoUH19fWqq6uzeg44U9o1NTXq6+uTJEWjUS1dutRwIrdcvXpVXV1d\nkqQXL14oHo+rsLDQcCo3LV++XP39/ZKkvr4+rV692nAitzQ2NurevXuSpFu3bqm6utpwovQ2NDSk\ncDis1tZWbd++XZLdc8CZPwz55+rxhw8fKpVK6cSJE1qyZInpWM5IJBI6ePCgnj17Js/z1NLSopqa\nGtOxnPHkyRPt27dPV65c0aNHj3T48GFNTEyooqJCkUhEPp/PdMS09vb4x2IxdXR0yO/3q6CgQB0d\nHe+cusPcikQiunbtmioqKqZfO3TokCKRiJVzwJnSBgDAds4sjwMAYDtKGwAAS1DaAABYgtIGAMAS\nlDYAAJagtAEAsASlDQCAJShtAAAs8Rcd7W/587H1vAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 576x396 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["try:\n", "    for i in range(N_SPLITS):\n", "        plt.plot([c0 - c1 for c0, c1 in zip(cate.fitted_nuisances['model_T_XZ'][i].model0.coef_.flatten(),\n", "              cate.fitted_nuisances['model_T_XZ'][i].model1.coef_.flatten())])\n", "    \n", "    plt.title(\"Difference in coefficients betwen model0 and model1\")\n", "    plt.show()\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ATE via DRIV"]}, {"cell_type": "code", "execution_count": 130, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper, StatsModelLinearRegression, ConstantModel\n", "from sklearn.dummy import DummyRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "# For DRIV we need a model for predicting E[T*Z | X]. We use a classifier\n", "model_TZ_X = lambda: model()\n", "\n", "# We also need a model for the final regression that will fit the function theta(X)\n", "# If we want to fit an ATE, we simply fit a constant functin theta(X) = theta\n", "# We can do this with a pipeline where the preprocessing step only creates a bias column\n", "# and the regression step fits a linear regression with no intercept.\n", "# To get normal confidence intervals easily we can use a statsmodels linear regression\n", "# wrapped in an sklearn interface\n", "const_driv_model_effect = lambda: ConstantModel()\n", "\n", "# As in OrthoDMLIV we need a perliminary estimator of the CATE.\n", "# We use a DMLIV estimator with no cross-fitting (n_splits=1)\n", "dmliv_prel_model_effect = DMLIV(model_Y_X(), model_T_X(), model_T_XZ(),\n", "                                dmliv_model_effect(), dmliv_featurizer(),\n", "                                n_splits=1, binary_instrument=True, binary_treatment=True)\n", "\n", "const_dr_cate = DRIV(model_Y_X(), model_T_X(), model_Z_X(), # same as in DMLATEIV\n", "                        dmliv_prel_model_effect, # preliminary model for CATE, must support fit(y, T, X, Z) and effect(X)\n", "                        model_TZ_X(), # model for E[T * Z | X]\n", "                        const_driv_model_effect(), # model for final stage of fitting theta(X)\n", "                        cov_clip=COV_CLIP, # covariance clipping to avoid large values in final regression from weak instruments\n", "                        n_splits=N_SPLITS, # number of splits to use for cross-fitting\n", "                        binary_instrument=True, # a flag whether to stratify cross-fitting by instrument\n", "                        binary_treatment=True # a flag whether to stratify cross-fitting by treatment\n", "                       )"]}, {"cell_type": "code", "execution_count": 131, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\sklearn\\model_selection\\_split.py:652: Warning: The least populated class in y has only 8 members, which is too few. The minimum number of members in any class cannot be less than n_splits=10.\n", "  % (min_groups, self.n_splits)), Warning)\n"]}, {"data": {"text/plain": ["<dr_iv.DRIV at 0x222e1dcbef0>"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["const_dr_cate.fit(y, T, X, Z, store_final=True)"]}, {"cell_type": "code", "execution_count": 132, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\statsmodels\\regression\\linear_model.py:1554: RuntimeWarning: divide by zero encountered in double_scalars\n", "  return self.ess/self.df_model\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>  -0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>  -0.000</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>    -inf</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>   nan</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>16:53:02</td>     <th>  Log-Likelihood:    </th> <td> -5935.3</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.187e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2990</td>      <th>  BIC:               </th> <td>1.188e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     0</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "    <td></td>       <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th> <td>    0.0718</td> <td>    0.032</td> <td>    2.231</td> <td> 0.026</td> <td>    0.009</td> <td>    0.135</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>1560.900</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   1.967</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>3111172.573</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td> 0.819</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>160.992</td> <th>  Cond. No.          </th>  <td>    1.00</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                      -0.000\n", "Model:                            OLS   Adj. R-squared:                 -0.000\n", "Method:                 Least Squares   F-statistic:                      -inf\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):                nan\n", "Time:                        16:53:02   Log-Likelihood:                -5935.3\n", "No. Observations:                2991   AIC:                         1.187e+04\n", "Df Residuals:                    2990   BIC:                         1.188e+04\n", "Df Model:                           0                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.0718      0.032      2.231      0.026       0.009       0.135\n", "==============================================================================\n", "Omnibus:                     1560.900   <PERSON><PERSON><PERSON>-Watson:                   1.967\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          3111172.573\n", "Skew:                           0.819   Prob(JB):                         0.00\n", "Kurtosis:                     160.992   Cond. No.                         1.00\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is the pipeline, we then look\n", "# at the reg step of the pipeline which is the statsmodel wrapper and then we look\n", "# at the model attribute of the statsmodel wrapper and print the summary()\n", "const_dr_cate.effect_model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Some diagnostics of the fitted nuisance models across folds"]}, {"cell_type": "code", "execution_count": 133, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model prel_model_effect max std of coefs: 0.08861170536431623\n", "Model model_TZ_X max std of coefs: 0.1551255726963632\n", "Model model_T_X max std of coefs: 0.07953278250754123\n", "Model model_Z_X max std of coefs: 0.014391208119373949\n", "Model model_Y_X max std of coefs: 0.026554847406610734\n"]}], "source": ["# The property .fitted_nuisances is a dictionary of the form:\n", "# {'name_of_nuisance': [fitted_model_fold_1, fitted_model_fold_2, ...]}\n", "# then we can access all properties of each of the fitted models for each fold.\n", "# If for instance all nuisances have a linear form we can look at the standard deviation\n", "# of the coefficients of each of the nuisance model across folds to check for stability\n", "try:\n", "    [print(\"Model {} max std of coefs: {}\".format(name,\n", "                                                  np.max(np.std([ns.coef_ for ns in nuisance_insts], axis=0)))) \n", "     if hasattr(nuisance_insts[0], 'coef_') else None\n", "     for name, nuisance_insts in const_dr_cate.fitted_nuisances.items()]\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "code", "execution_count": 134, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARAAAADdCAYAAABzCy3LAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nJBBBEHQmEoggCDr7/xN/tPLENyndAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARAAAADdCAYAAABzCy3LAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n/wHEJBEPI1T/NQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAASkAAADdCAYAAADn7YQ5AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nMklJkuTQZJKSJMmhySQlSZJD+/9D7I/B3DHIIAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAR0AAADdCAYAAACG9aZ7AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n/wMZWwLeQjNrEAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["try:\n", "    nuisance_diagnostic(const_dr_cate, 'model_TZ_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "    nuisance_diagnostic(const_dr_cate, 'model_TZ_X', 'predict', lambda ns: ns.predict(X), np.arange(X.shape[0]))\n", "    nuisance_diagnostic(const_dr_cate, 'model_T_X', 'coef', lambda ns: ns.coef_.flatten(), X_df.columns)\n", "    nuisance_diagnostic(const_dr_cate, 'prel_model_effect', 'effect', lambda ns: ns.effect(X), np.arange(X.shape[0]))\n", "except:\n", "    print(\"Unavailable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Projecting CATE to a pre-chosen subset of variables in final model"]}, {"cell_type": "code", "execution_count": 135, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4]\n"]}], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "\n", "np.random.seed(random_seed)\n", "\n", "# We could also fit a projection on a subset of the features by using the\n", "# subset wrapper from our utilities.\n", "\n", "# Example: including everything for expository purposes, but any array_like of indices would work\n", "subset_names = set(['motheduc'])\n", "# list of indices of features X to use in the final model\n", "feature_inds = np.argwhere([(x in subset_names) for x in X_df.columns.values]).flatten()\n", "print(feature_inds)\n", "# Because we are projecting to a low dimensional model space, we can\n", "# do valid inference and we can use statsmodel linear regression to get all\n", "# the hypothesis testing capability\n", "proj_driv_model_effect = lambda: SubsetWrapper(StatsModelLinearRegression(),\n", "                                          feature_inds # list of indices of features X to use in the final model\n", "                                         )"]}, {"cell_type": "code", "execution_count": 136, "metadata": {"collapsed": true}, "outputs": [], "source": ["proj_dr_cate = const_dr_cate.refit_final(proj_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 137, "metadata": {"collapsed": true}, "outputs": [], "source": ["# To get the CATE at every X we call effect(X[:, feature_inds])\n", "proj_dr_effect = proj_dr_cate.effect(X[:, feature_inds])"]}, {"cell_type": "code", "execution_count": 138, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>            <td>y</td>        <th>  R-squared:         </th> <td>   0.002</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.002</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>   6.316</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Sat, 01 Jun 2019</td> <th>  Prob (F-statistic):</th>  <td>0.0120</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>16:53:03</td>     <th>  Log-Likelihood:    </th> <td> -5932.2</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  2991</td>      <th>  AIC:               </th> <td>1.187e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  2989</td>      <th>  BIC:               </th> <td>1.188e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     1</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>        <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>    <td>    0.0726</td> <td>    0.032</td> <td>    2.257</td> <td> 0.024</td> <td>    0.010</td> <td>    0.136</td>\n", "</tr>\n", "<tr>\n", "  <th>motheduc</th> <td>   -0.0815</td> <td>    0.032</td> <td>   -2.513</td> <td> 0.012</td> <td>   -0.145</td> <td>   -0.018</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>1570.882</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>  <td>   1.972</td>  \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>3062292.770</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td> 0.842</td>  <th>  Prob(JB):          </th>  <td>    0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>159.746</td> <th>  Cond. No.          </th>  <td>    1.01</td>  \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.002\n", "Model:                            OLS   Adj. R-squared:                  0.002\n", "Method:                 Least Squares   F-statistic:                     6.316\n", "Date:                Sat, 01 Jun 2019   Prob (F-statistic):             0.0120\n", "Time:                        16:53:03   Log-Likelihood:                -5932.2\n", "No. Observations:                2991   AIC:                         1.187e+04\n", "Df Residuals:                    2989   BIC:                         1.188e+04\n", "Df Model:                           1                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.0726      0.032      2.257      0.024       0.010       0.136\n", "motheduc      -0.0815      0.032     -2.513      0.012      -0.145      -0.018\n", "==============================================================================\n", "Omnibus:                     1570.882   <PERSON><PERSON><PERSON>-<PERSON>:                   1.972\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          3062292.770\n", "Skew:                           0.842   Prob(JB):                         0.00\n", "Kurtosis:                     159.746   Cond. No.                         1.01\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 138, "metadata": {}, "output_type": "execute_result"}], "source": ["# To get the statsmodel summary we look at the effect_model, which is\n", "# an instance of SubsetWrapper, we look at the model of the SubsetWrapper which is \n", "# and instance of the pipeline, we then look at the reg step of the pipeline which is the statsmodel wrapper and\n", "# call summary() of the wrapper (most prob there is a better API for this, but we can go with this for now :)\n", "proj_dr_cate.effect_model.summary(alpha=.05, xname=['const']+list(X_df.columns[feature_inds]))"]}, {"cell_type": "code", "execution_count": 139, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAN4AAAEFCAYAAACM6VnvAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nwAgI4QkERuD/AVt7uYaEzwP9AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 216x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# We can also evaluate coverage and create prediction intervals using statsmodels attributes\n", "import statsmodels.api as sm\n", "from statsmodels.sandbox.regression.predstd import wls_prediction_std\n", "res = proj_dr_cate.effect_model.model\n", "predictions = res.get_prediction(PolynomialFeatures(degree=1, include_bias=True).fit_transform(X[:, feature_inds]))\n", "frame = predictions.summary_frame(alpha=0.05)\n", "pred = frame['mean']\n", "iv_l = frame['mean_ci_lower']\n", "iv_u = frame['mean_ci_upper']\n", "\n", "fig, ax = plt.subplots(figsize=(3,4))\n", "order = np.argsort(X[:, feature_inds[0]])\n", "ax.plot(X[order, feature_inds[0]], iv_u[order], 'C3--', label=\"Upper 95% CI\")\n", "ax.plot(X[order, feature_inds[0]], iv_l[order], 'C3--', label=\"Lower 95% CI\")\n", "ax.plot(X[order, feature_inds[0]], pred[order], 'C0--.', label=\"Prediction\")\n", "ax.legend(loc='best')\n", "plt.xlabel(\"Mother's Education (scaled)\")\n", "plt.ylabel(\"Treatment Effect\")\n", "#plt.savefig(\"NLSYM_momeduc_linear_projection_2.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random Forest Based CATE and Tree Explainer"]}, {"cell_type": "code", "execution_count": 140, "metadata": {"collapsed": true}, "outputs": [], "source": ["from dml_iv import DMLIV\n", "from dr_iv import DRIV, ProjectedDRIV\n", "from utilities import SubsetWrapper\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "\n", "np.random.seed(random_seed)\n", "\n", "rf_driv_model_effect = lambda: RandomForestRegressor(n_estimators=5000, max_depth=2, min_impurity_decrease=0.01,\n", "                                                     min_samples_leaf=100, bootstrap=True)"]}, {"cell_type": "code", "execution_count": 141, "metadata": {"collapsed": true}, "outputs": [], "source": ["rf_dr_cate = const_dr_cate.refit_final(rf_driv_model_effect())"]}, {"cell_type": "code", "execution_count": 142, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\ProgramData\\Anaconda3\\lib\\site-packages\\matplotlib\\axes\\_axes.py:6521: MatplotlibDeprecationWarning: \n", "The 'normed' kwarg was deprecated in Matplotlib 2.1 and will be removed in 3.1. Use 'density' instead.\n", "  alternative=\"'density'\", removal=\"3.1\")\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAANYAAAEJCAYAAADo/NlgAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nQYgACYsgRICERRAiQMIiCBEgYRGECPx/ZTLnqqz8N3cAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 216x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "\n", "rf_dr_effect = rf_dr_cate.effect(X)\n", "plt.figure(figsize=(3,4))\n", "sns.distplot(rf_dr_effect)\n", "plt.xlabel(\"Treatment Effect\")\n", "plt.ylabel(\"Frequency\")\n", "plt.savefig(\"NLSYM_hte_distribution_2.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 143, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIQAAAD/CAYAAACAeA0nAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import shap\n", "import pandas as pd\n", "\n", "Xdf = pd.DataFrame(X, columns=X_df.columns)\n", "# explain the model's predictions using SHAP values\n", "explainer = shap.TreeExplainer(rf_dr_cate.effect_model)\n", "shap_values = explainer.shap_values(Xdf)\n", "\n", "# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(explainer.expected_value, shap_values[0,:], Xdf.iloc[0,:], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 183, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAWoAAAEdCAYAAAAl/y2aAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 288x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(4,4))\n", "shap.summary_plot(shap_values, Xdf, plot_type=\"violin\", max_display=10, show=False, auto_size_plot=False)\n", "plt.savefig(\"NLSYM_shap_summary_violin_2.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 145, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAY8AAAEXCAYAAABVr8jJAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRK5CYII=\n", "text/plain": ["<Figure size 360x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(5,4))\n", "shap.summary_plot(shap_values, Xdf, plot_type='bar', max_display=10, show=False, auto_size_plot=False)\n", "plt.xlabel(\"mean(|SHAP value|)\\n average impact on model output magnitude\")\n", "plt.savefig(\"NLSYM_shap_summary_bar.pdf\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}