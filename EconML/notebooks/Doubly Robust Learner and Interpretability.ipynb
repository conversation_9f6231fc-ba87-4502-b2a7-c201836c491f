{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Doubly Robust Learner and Interpretability\n", "\n", "Double Machine Learning (DML) is an algorithm that applies arbitrary machine learning methods\n", "to fit the treatment and response, then uses a linear model to predict the response residuals\n", "from the treatment residuals."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Helper imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "%matplotlib inline\n", "\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generating Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import scipy.special\n", "\n", "np.random.seed(123)\n", "n=2000 # number of raw samples\n", "d=10 # number of binary features + 1\n", "\n", "# Generating random segments aka binary features. We will use features 0,...,3 for heterogeneity.\n", "# The rest for controls. Just as an example.\n", "X = np.random.binomial(1, .5, size=(n, d))\n", "# Generating an imbalanced A/B test\n", "T = np.random.binomial(1, scipy.special.expit(X[:, 0]))\n", "# Generating an outcome with treatment effect heterogeneity. The first binary feature creates heterogeneity\n", "# We also have confounding on the first variable. We also have heteroskedastic errors.\n", "y = (-1 + 2 * X[:, 0]) * T + X[:, 0] + (1*X[:, 0] + 1)*np.random.normal(0, 1, size=(n,))\n", "X_test = np.random.binomial(1, .5, size=(10, d))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Applying the LinearDRLearner"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._drlearner.LinearDRLearner at 0x2900f9fb488>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.linear_model import LassoCV\n", "from econml.dr import LinearDRLearner\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from sklearn.dummy import DummyClassifier\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = LinearDRLearner(model_regression=LassoCV(cv=3),\n", "                      model_propensity=DummyClassifier(strategy='prior'))\n", "est.fit(y, T, X=X[:, :4])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.02346725])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Treatment Effect of particular segments\n", "est.effect(np.array([[1, 0, 0, 0]])) # effect of segment with features [1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([0.66350818]), array([1.38342633]))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Confidence interval for effect. Produces the (alpha*100/2, (1-alpha)*100/2)% Confidence Interval\n", "est.effect_interval(np.array([[1, 0, 0, 0]]), alpha=.05) # effect of segment with features [1, 0, 0, 0]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.023</td>\n", "      <td>0.184</td>\n", "      <td>5.573</td>\n", "      <td>0.0</td>\n", "      <td>0.664</td>\n", "      <td>1.383</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   point_estimate  stderr  zstat  pvalue  ci_lower  ci_upper\n", "0           1.023   0.184  5.573     0.0     0.664     1.383"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Other inference for effect, including point estimate, standard error, z score, p value and confidence interval\n", "est.effect_inference(np.array([[1, 0, 0, 0]])).summary_frame(alpha=.05)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[['A' '2.0632770380417167']\n", " ['B' '-0.0021408002029088546']\n", " ['C' '-0.130752418085345']\n", " ['D' '0.0860397486668365']]\n", "-1.039809787014317\n"]}], "source": ["# Getting the coefficients of the linear CATE model together with the corresponding feature names\n", "print(np.array(list(zip(est.cate_feature_names(['A', 'B', 'C', 'D']), est.coef_(T=1)))))\n", "print(est.intercept_(T=1))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Getting the confidence intervals of the coefficients of the CATE model\n", "# together with the corresponding feature names.\n", "feat_names = est.cate_feature_names(['A', 'B', 'C', 'D'])\n", "point = est.coef_(T=1)\n", "lower, upper = np.array(est.coef__interval(T=1))\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style(\"darkgrid\"):\n", "    fig, ax = plt.subplots(1,1) \n", "    x = np.arange(len(point))\n", "    plt.errorbar(x, point, yerr, fmt='o')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(feat_names, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>A</th>\n", "      <td>2.063</td>\n", "      <td>0.143</td>\n", "      <td>14.431</td>\n", "      <td>0.000</td>\n", "      <td>1.828</td>\n", "      <td>2.298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B</th>\n", "      <td>-0.002</td>\n", "      <td>0.142</td>\n", "      <td>-0.015</td>\n", "      <td>0.988</td>\n", "      <td>-0.236</td>\n", "      <td>0.232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>C</th>\n", "      <td>-0.131</td>\n", "      <td>0.143</td>\n", "      <td>-0.916</td>\n", "      <td>0.359</td>\n", "      <td>-0.365</td>\n", "      <td>0.104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>D</th>\n", "      <td>0.086</td>\n", "      <td>0.143</td>\n", "      <td>0.603</td>\n", "      <td>0.546</td>\n", "      <td>-0.149</td>\n", "      <td>0.321</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   point_estimate  stderr   zstat  pvalue  ci_lower  ci_upper\n", "A           2.063   0.143  14.431   0.000     1.828     2.298\n", "B          -0.002   0.142  -0.015   0.988    -0.236     0.232\n", "C          -0.131   0.143  -0.916   0.359    -0.365     0.104\n", "D           0.086   0.143   0.603   0.546    -0.149     0.321"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the inference of the coefficients of the CATE model\n", "# together with the corresponding feature names.\n", "est.coef__inference(T=1).summary_frame(feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>cate_intercept</th>\n", "      <td>-1.04</td>\n", "      <td>0.146</td>\n", "      <td>-7.114</td>\n", "      <td>0.0</td>\n", "      <td>-1.28</td>\n", "      <td>-0.799</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                point_estimate  stderr  zstat  pvalue  ci_lower  ci_upper\n", "cate_intercept           -1.04   0.146 -7.114     0.0     -1.28    -0.799"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the inference of the intercept of the CATE model\n", "est.intercept__inference(T=1).summary_frame()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "  <td></td>  <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>A</th>      <td>2.063</td>      <td>0.143</td> <td>14.431</td>   <td>0.0</td>    <td>1.828</td>    <td>2.298</td> \n", "</tr>\n", "<tr>\n", "  <th>B</th>     <td>-0.002</td>      <td>0.142</td> <td>-0.015</td>  <td>0.988</td>  <td>-0.236</td>    <td>0.232</td> \n", "</tr>\n", "<tr>\n", "  <th>C</th>     <td>-0.131</td>      <td>0.143</td> <td>-0.916</td>  <td>0.359</td>  <td>-0.365</td>    <td>0.104</td> \n", "</tr>\n", "<tr>\n", "  <th>D</th>      <td>0.086</td>      <td>0.143</td>  <td>0.603</td>  <td>0.546</td>  <td>-0.149</td>    <td>0.321</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>-1.04</td>      <td>0.146</td> <td>-7.114</td>   <td>0.0</td>    <td>-1.28</td>   <td>-0.799</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                  Coefficient Results                  \n", "=======================================================\n", "  point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "-------------------------------------------------------\n", "A          2.063  0.143 14.431    0.0    1.828    2.298\n", "B         -0.002  0.142 -0.015  0.988   -0.236    0.232\n", "C         -0.131  0.143 -0.916  0.359   -0.365    0.104\n", "D          0.086  0.143  0.603  0.546   -0.149    0.321\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept          -1.04  0.146 -7.114    0.0    -1.28   -0.799\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where $T$ is the one-hot-encoding of the discrete treatment and for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and the designated treatment $j$ passed to summary. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["est.summary(T=1, feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Polynomial Features"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._drlearner.LinearDRLearner at 0x29010c26908>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "from econml.dr import LinearDRLearner\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from sklearn.dummy import DummyClassifier\n", "from sklearn.preprocessing import PolynomialFeatures\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = LinearDRLearner(model_regression=WeightedLassoCV(cv=3),\n", "                      model_propensity=DummyClassifier(strategy='prior'),\n", "                      featurizer=PolynomialFeatures(degree=2, interaction_only=True, include_bias=False))\n", "est.fit(y, T, X=X[:, :4])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Getting the confidence intervals of the coefficients of the CATE model\n", "# together with the corresponding feature names.\n", "feat_names = est.cate_feature_names(['A', 'B', 'C', 'D'])\n", "point = est.coef_(T=1)\n", "lower, upper = np.array(est.coef__interval(T=1, alpha=0.05))\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style(\"darkgrid\"):\n", "    fig, ax = plt.subplots(1,1) \n", "    x = np.arange(len(point))\n", "    plt.errorbar(x, point, yerr, fmt='o')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(feat_names, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.192</td>\n", "      <td>0.292</td>\n", "      <td>4.081</td>\n", "      <td>0.000</td>\n", "      <td>0.711</td>\n", "      <td>1.672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.057</td>\n", "      <td>0.189</td>\n", "      <td>-5.585</td>\n", "      <td>0.000</td>\n", "      <td>-1.368</td>\n", "      <td>-0.746</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.065</td>\n", "      <td>0.253</td>\n", "      <td>4.202</td>\n", "      <td>0.000</td>\n", "      <td>0.648</td>\n", "      <td>1.482</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1.057</td>\n", "      <td>0.189</td>\n", "      <td>-5.585</td>\n", "      <td>0.000</td>\n", "      <td>-1.368</td>\n", "      <td>-0.746</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1.081</td>\n", "      <td>0.177</td>\n", "      <td>-6.095</td>\n", "      <td>0.000</td>\n", "      <td>-1.372</td>\n", "      <td>-0.789</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.861</td>\n", "      <td>0.277</td>\n", "      <td>3.107</td>\n", "      <td>0.002</td>\n", "      <td>0.405</td>\n", "      <td>1.318</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.926</td>\n", "      <td>0.273</td>\n", "      <td>3.397</td>\n", "      <td>0.001</td>\n", "      <td>0.478</td>\n", "      <td>1.375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-0.892</td>\n", "      <td>0.186</td>\n", "      <td>-4.784</td>\n", "      <td>0.000</td>\n", "      <td>-1.199</td>\n", "      <td>-0.585</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.926</td>\n", "      <td>0.273</td>\n", "      <td>3.397</td>\n", "      <td>0.001</td>\n", "      <td>0.478</td>\n", "      <td>1.375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0.861</td>\n", "      <td>0.277</td>\n", "      <td>3.107</td>\n", "      <td>0.002</td>\n", "      <td>0.405</td>\n", "      <td>1.318</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   point_estimate  stderr  zstat  pvalue  ci_lower  ci_upper\n", "0           1.192   0.292  4.081   0.000     0.711     1.672\n", "1          -1.057   0.189 -5.585   0.000    -1.368    -0.746\n", "2           1.065   0.253  4.202   0.000     0.648     1.482\n", "3          -1.057   0.189 -5.585   0.000    -1.368    -0.746\n", "4          -1.081   0.177 -6.095   0.000    -1.372    -0.789\n", "5           0.861   0.277  3.107   0.002     0.405     1.318\n", "6           0.926   0.273  3.397   0.001     0.478     1.375\n", "7          -0.892   0.186 -4.784   0.000    -1.199    -0.585\n", "8           0.926   0.273  3.397   0.001     0.478     1.375\n", "9           0.861   0.277  3.107   0.002     0.405     1.318"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the inference of the CATE at different X vector values\n", "est.effect_inference(X_test[:,:4]).summary_frame()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Uncertainty of Mean Point Estimate</caption>\n", "<tr>\n", "  <th>mean_point</th> <th>stderr_mean</th> <th>zstat</th> <th>pvalue</th> <th>ci_mean_lower</th> <th>ci_mean_upper</th>\n", "</tr>\n", "<tr>\n", "     <td>0.175</td>      <td>0.243</td>    <td>0.719</td>  <td>0.472</td>    <td>-0.225</td>         <td>0.574</td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Distribution of Point Estimate</caption>\n", "<tr>\n", "  <th>std_point</th> <th>pct_point_lower</th> <th>pct_point_upper</th>\n", "</tr>\n", "<tr>\n", "    <td>0.982</td>        <td>-1.07</td>           <td>1.135</td>     \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Total Variance of Point Estimate</caption>\n", "<tr>\n", "  <th>stderr_point</th> <th>ci_point_lower</th> <th>ci_point_upper</th>\n", "</tr>\n", "<tr>\n", "      <td>1.012</td>         <td>-1.25</td>          <td>1.389</td>    \n", "</tr>\n", "</table><br/><br/>Note: The stderr_mean is a conservative upper bound."], "text/plain": ["<econml.inference._inference.PopulationSummaryResults at 0x29010c28f08>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the population inference given sample X\n", "est.effect_inference(X_test[:,:4]).population_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Polynomial Features and Debiased Lasso Inference"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._drlearner.SparseLinearDRLearner at 0x29010cba348>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.sklearn_extensions.linear_model import WeightedLassoCV\n", "from econml.dr import SparseLinearDRLearner\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from sklearn.dummy import DummyClassifier\n", "from sklearn.preprocessing import PolynomialFeatures\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = SparseLinearDRLearner(model_regression=WeightedLassoCV(cv=3),\n", "                            model_propensity=DummyClassifier(strategy='prior'),\n", "                            featurizer=PolynomialFeatures(degree=3, interaction_only=True, include_bias=False))\n", "est.fit(y, T, X=X[:, :4])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Parameter Intervals"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Getting the confidence intervals of the coefficients of the CATE model\n", "# together with the corresponding feature names.\n", "feat_names = est.cate_feature_names(['A', 'B', 'C', 'D'])\n", "point = est.coef_(T=1)\n", "lower, upper = np.array(est.coef__interval(T=1, alpha=0.05))\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style(\"darkgrid\"):\n", "    fig, ax = plt.subplots(1,1) \n", "    x = np.arange(len(point))\n", "    plt.errorbar(x, point, yerr, fmt='o')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(feat_names, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CATE(x) intervals"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import itertools\n", "# Getting the confidence intervals of the CATE at different X vector values\n", "feat_names = np.array(['A', 'B', 'C', 'D'])\n", "lst = list(itertools.product([0, 1], repeat=4))\n", "point = []\n", "lower = []\n", "upper = []\n", "fnames = []\n", "for x in lst:\n", "    x_test = np.array([x])\n", "    fnames.append(\" \".join(np.array(feat_names)[x_test.flatten()>0]))\n", "    point.append(est.effect(x_test)[0])\n", "    lb, ub = est.effect_interval(x_test, alpha=.05)\n", "    lower.append(lb[0])\n", "    upper.append(ub[0])\n", "\n", "fnames = np.array(fnames)\n", "point = np.array(point)\n", "lower = np.array(lower)\n", "upper = np.array(upper)\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style('darkgrid'):\n", "    fig, ax = plt.subplots(1,1, figsize=(20, 5)) \n", "    x = np.arange(len(point))\n", "    stat_sig = (lower>0) | (upper<0)\n", "    plt.errorbar(x[stat_sig], point[stat_sig], yerr[:, stat_sig], fmt='o', label='stat_sig')\n", "    plt.errorbar(x[~stat_sig], point[~stat_sig], yerr[:, ~stat_sig], fmt='o', color='red', label='insig')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(fnames, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CATE(x) inference"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.198</td>\n", "      <td>0.274</td>\n", "      <td>4.370</td>\n", "      <td>0.000</td>\n", "      <td>0.747</td>\n", "      <td>1.648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.087</td>\n", "      <td>0.271</td>\n", "      <td>-4.009</td>\n", "      <td>0.000</td>\n", "      <td>-1.533</td>\n", "      <td>-0.641</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.129</td>\n", "      <td>0.280</td>\n", "      <td>4.033</td>\n", "      <td>0.000</td>\n", "      <td>0.669</td>\n", "      <td>1.590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1.087</td>\n", "      <td>0.271</td>\n", "      <td>-4.009</td>\n", "      <td>0.000</td>\n", "      <td>-1.533</td>\n", "      <td>-0.641</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1.095</td>\n", "      <td>0.269</td>\n", "      <td>-4.065</td>\n", "      <td>0.000</td>\n", "      <td>-1.537</td>\n", "      <td>-0.652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.725</td>\n", "      <td>0.291</td>\n", "      <td>2.491</td>\n", "      <td>0.013</td>\n", "      <td>0.246</td>\n", "      <td>1.204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.882</td>\n", "      <td>0.272</td>\n", "      <td>3.239</td>\n", "      <td>0.001</td>\n", "      <td>0.434</td>\n", "      <td>1.330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-0.803</td>\n", "      <td>0.273</td>\n", "      <td>-2.941</td>\n", "      <td>0.003</td>\n", "      <td>-1.251</td>\n", "      <td>-0.354</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.882</td>\n", "      <td>0.272</td>\n", "      <td>3.239</td>\n", "      <td>0.001</td>\n", "      <td>0.434</td>\n", "      <td>1.330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0.725</td>\n", "      <td>0.291</td>\n", "      <td>2.491</td>\n", "      <td>0.013</td>\n", "      <td>0.246</td>\n", "      <td>1.204</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   point_estimate  stderr  zstat  pvalue  ci_lower  ci_upper\n", "0           1.198   0.274  4.370   0.000     0.747     1.648\n", "1          -1.087   0.271 -4.009   0.000    -1.533    -0.641\n", "2           1.129   0.280  4.033   0.000     0.669     1.590\n", "3          -1.087   0.271 -4.009   0.000    -1.533    -0.641\n", "4          -1.095   0.269 -4.065   0.000    -1.537    -0.652\n", "5           0.725   0.291  2.491   0.013     0.246     1.204\n", "6           0.882   0.272  3.239   0.001     0.434     1.330\n", "7          -0.803   0.273 -2.941   0.003    -1.251    -0.354\n", "8           0.882   0.272  3.239   0.001     0.434     1.330\n", "9           0.725   0.291  2.491   0.013     0.246     1.204"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the inference of the CATE at different X vector values\n", "est.effect_inference(X_test[:,:4]).summary_frame()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Uncertainty of Mean Point Estimate</caption>\n", "<tr>\n", "  <th>mean_point</th> <th>stderr_mean</th> <th>zstat</th> <th>pvalue</th> <th>ci_mean_lower</th> <th>ci_mean_upper</th>\n", "</tr>\n", "<tr>\n", "     <td>0.147</td>      <td>0.277</td>    <td>0.531</td>  <td>0.595</td>    <td>-0.308</td>         <td>0.602</td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Distribution of Point Estimate</caption>\n", "<tr>\n", "  <th>std_point</th> <th>pct_point_lower</th> <th>pct_point_upper</th>\n", "</tr>\n", "<tr>\n", "    <td>0.965</td>       <td>-1.091</td>           <td>1.167</td>     \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Total Variance of Point Estimate</caption>\n", "<tr>\n", "  <th>stderr_point</th> <th>ci_point_lower</th> <th>ci_point_upper</th>\n", "</tr>\n", "<tr>\n", "      <td>1.004</td>        <td>-1.359</td>          <td>1.388</td>    \n", "</tr>\n", "</table><br/><br/>Note: The stderr_mean is a conservative upper bound."], "text/plain": ["<econml.inference._inference.PopulationSummaryResults at 0x2901100c1c8>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the population inference given sample X\n", "est.effect_inference(X_test[:,:4]).population_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Non-Linear Models with Forest CATEs"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._drle<PERSON>ner.ForestDRLearner at 0x29011010748>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.dr import ForestDRLearner\n", "from sklearn.ensemble import GradientBoostingRegressor\n", "\n", "est = ForestDRLearner(model_regression=GradientBoostingRegressor(),\n", "                      model_propensity=DummyClassifier(strategy='prior'),\n", "                      cv=5,\n", "                      n_estimators=1000,\n", "                      min_samples_leaf=10,\n", "                      verbose=0, min_weight_fraction_leaf=.01)\n", "est.fit(y, T, X=X[:, :4])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.97459949, 0.00870163, 0.00810112, 0.00859776])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["est.feature_importances_(T=1)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x223.2 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import shap\n", "import pandas as pd\n", "# explain the model's predictions using SHAP values\n", "shap_values = est.shap_values(X[:100, :4], feature_names=['A', 'B', 'C', 'D'], background_samples=100)\n", "shap.summary_plot(shap_values['Y0']['T0_1'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CATE(x) intervals"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import itertools\n", "# Getting the confidence intervals of the CATE at different X vector values\n", "feat_names = np.array(['A', 'B', 'C', 'D'])\n", "lst = list(itertools.product([0, 1], repeat=4))\n", "point = []\n", "lower = []\n", "upper = []\n", "fnames = []\n", "for x in lst:\n", "    x_test = np.array([x])\n", "    fnames.append(\" \".join(np.array(feat_names)[x_test.flatten()>0]))\n", "    point.append(est.effect(x_test)[0])\n", "    lb, ub = est.effect_interval(x_test, alpha=.05)\n", "    lower.append(lb[0])\n", "    upper.append(ub[0])\n", "\n", "fnames = np.array(fnames)\n", "point = np.array(point)\n", "lower = np.array(lower)\n", "upper = np.array(upper)\n", "yerr = np.zeros((2, point.shape[0]))\n", "yerr[0, :] = point - lower\n", "yerr[1, :] = upper - point\n", "\n", "with sns.axes_style('darkgrid'):\n", "    fig, ax = plt.subplots(1,1, figsize=(20, 5)) \n", "    x = np.arange(len(point))\n", "    stat_sig = (lower>0) | (upper<0)\n", "    plt.errorbar(x[stat_sig], point[stat_sig], yerr[:, stat_sig], fmt='o', label='stat_sig')\n", "    plt.errorbar(x[~stat_sig], point[~stat_sig], yerr[:, ~stat_sig], fmt='o', color='red', label='insig')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(fnames, rotation='vertical', fontsize=18)\n", "    ax.set_ylabel('coef')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CATE(x) inference"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.160</td>\n", "      <td>0.336</td>\n", "      <td>3.452</td>\n", "      <td>0.001</td>\n", "      <td>0.607</td>\n", "      <td>1.713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.112</td>\n", "      <td>0.198</td>\n", "      <td>-5.610</td>\n", "      <td>0.000</td>\n", "      <td>-1.438</td>\n", "      <td>-0.786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.387</td>\n", "      <td>0.282</td>\n", "      <td>4.913</td>\n", "      <td>0.000</td>\n", "      <td>0.923</td>\n", "      <td>1.851</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1.112</td>\n", "      <td>0.198</td>\n", "      <td>-5.610</td>\n", "      <td>0.000</td>\n", "      <td>-1.438</td>\n", "      <td>-0.786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1.173</td>\n", "      <td>0.187</td>\n", "      <td>-6.257</td>\n", "      <td>0.000</td>\n", "      <td>-1.481</td>\n", "      <td>-0.865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.132</td>\n", "      <td>0.339</td>\n", "      <td>3.342</td>\n", "      <td>0.001</td>\n", "      <td>0.575</td>\n", "      <td>1.689</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.938</td>\n", "      <td>0.320</td>\n", "      <td>2.933</td>\n", "      <td>0.003</td>\n", "      <td>0.412</td>\n", "      <td>1.464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-0.890</td>\n", "      <td>0.219</td>\n", "      <td>-4.071</td>\n", "      <td>0.000</td>\n", "      <td>-1.250</td>\n", "      <td>-0.531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.938</td>\n", "      <td>0.320</td>\n", "      <td>2.933</td>\n", "      <td>0.003</td>\n", "      <td>0.412</td>\n", "      <td>1.464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1.132</td>\n", "      <td>0.339</td>\n", "      <td>3.342</td>\n", "      <td>0.001</td>\n", "      <td>0.575</td>\n", "      <td>1.689</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   point_estimate  stderr  zstat  pvalue  ci_lower  ci_upper\n", "0           1.160   0.336  3.452   0.001     0.607     1.713\n", "1          -1.112   0.198 -5.610   0.000    -1.438    -0.786\n", "2           1.387   0.282  4.913   0.000     0.923     1.851\n", "3          -1.112   0.198 -5.610   0.000    -1.438    -0.786\n", "4          -1.173   0.187 -6.257   0.000    -1.481    -0.865\n", "5           1.132   0.339  3.342   0.001     0.575     1.689\n", "6           0.938   0.320  2.933   0.003     0.412     1.464\n", "7          -0.890   0.219 -4.071   0.000    -1.250    -0.531\n", "8           0.938   0.320  2.933   0.003     0.412     1.464\n", "9           1.132   0.339  3.342   0.001     0.575     1.689"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the inference of the CATE at different X vector values\n", "est.effect_inference(X_test[:,:4]).summary_frame()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Uncertainty of Mean Point Estimate</caption>\n", "<tr>\n", "  <th>mean_point</th> <th>stderr_mean</th> <th>zstat</th> <th>pvalue</th> <th>ci_mean_lower</th> <th>ci_mean_upper</th>\n", "</tr>\n", "<tr>\n", "     <td>0.24</td>       <td>0.281</td>    <td>0.855</td>  <td>0.393</td>    <td>-0.222</td>         <td>0.702</td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Distribution of Point Estimate</caption>\n", "<tr>\n", "  <th>std_point</th> <th>pct_point_lower</th> <th>pct_point_upper</th>\n", "</tr>\n", "<tr>\n", "    <td>1.08</td>        <td>-1.145</td>           <td>1.285</td>     \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Total Variance of Point Estimate</caption>\n", "<tr>\n", "  <th>stderr_point</th> <th>ci_point_lower</th> <th>ci_point_upper</th>\n", "</tr>\n", "<tr>\n", "      <td>1.116</td>        <td>-1.326</td>          <td>1.607</td>    \n", "</tr>\n", "</table><br/><br/>Note: The stderr_mean is a conservative upper bound."], "text/plain": ["<econml.inference._inference.PopulationSummaryResults at 0x29010b06688>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the population inference given sample X\n", "est.effect_inference(X_test[:,:4]).population_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tree Interpretation of the CATE Model"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from econml.cate_interpreter import SingleTreeCateInterpreter"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.cate_interpreter._interpreters.SingleTreeCateInterpreter at 0x29010f7ff08>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["intrp = SingleTreeCateInterpreter(include_model_uncertainty=True, max_depth=2, min_samples_leaf=10)\n", "# We interpret the CATE models behavior on the distribution of heterogeneity features\n", "intrp.interpret(est, X[:, :4])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# exporting to a dot file\n", "intrp.export_graphviz(out_file='cate_tree.dot', feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# or we can directly render. Requires the graphviz python library\n", "intrp.render(out_file='dr_cate_tree', format='pdf', view=True, feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# or we can also plot inline with matplotlib. a bit uglier\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=['A', 'B', 'C', 'D'], fontsize=12)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tree Based Treatment Policy Based on CATE Model"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from econml.cate_interpreter import SingleTreePolicyInterpreter"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.cate_interpreter._interpreters.SingleTreePolicyInterpreter at 0x290122895c8>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["intrp = SingleTreePolicyInterpreter(risk_level=0.05, max_depth=2, min_samples_leaf=1, min_impurity_decrease=.001)\n", "# We find a tree based treatment policy based on the CATE model\n", "# sample_treatment_costs is the cost of treatment. Policy will treat if effect is above this cost.\n", "# It can also be an array that has a different cost for each sample. In case treating different segments\n", "# has different cost.\n", "intrp.interpret(est, X[:, :4],\n", "                sample_treatment_costs=0.2)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# exporting to a dot file\n", "intrp.export_graphviz(out_file='cate_tree.dot', feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"PATH\"] += os.pathsep + 'D:/Program Files (x86)/Graphviz2.38/bin/'"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# or we can directly render. Requires the graphviz python library\n", "intrp.render(out_file='dr_policy_tree', format='pdf', view=True, feature_names=['A', 'B', 'C', 'D'])"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# or we can also plot inline with matplotlib. a bit uglier\n", "plt.figure(figsize=(25, 5))\n", "intrp.plot(feature_names=['A', 'B', 'C', 'D'], fontsize=12)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SHAP Interpretability with Final Tree CATE Model"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.dr._drlearner.DRLearner at 0x29012664b48>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# We need to use a scikit-learn final model\n", "from econml.dr import DRLearner\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, GradientBoostingClassifier\n", "\n", "# One can replace model_y and model_t with any scikit-learn regressor and classifier correspondingly\n", "# as long as it accepts the sample_weight keyword argument at fit time.\n", "est = DRLearner(model_regression=GradientBoostingRegressor(max_depth=3, n_estimators=100, min_samples_leaf=30),\n", "                model_propensity=GradientBoostingClassifier(max_depth=3, n_estimators=100, min_samples_leaf=30),\n", "                model_final=RandomForestRegressor(max_depth=3, n_estimators=100, min_samples_leaf=30))\n", "est.fit(y, T, X=X[:, :4], W=X[:, 4:])"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import shap\n", "import pandas as pd\n", "# explain the model's predictions using SHAP values\n", "shap_values = est.shap_values(X[:, :4], feature_names=['A', 'B', 'C', 'D'], background_samples=100)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# visualize the first prediction's explanation (use matplotlib=True to avoid Javascript)\n", "shap.force_plot(shap_values[\"Y0\"][\"T0_1\"][0], matplotlib=True)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x223.2 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["shap.summary_plot(shap_values[\"Y0\"][\"T0_1\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}