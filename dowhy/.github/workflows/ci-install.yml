# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions
name: CI Installation Check

on:
  push:
    branches: [main]
    paths:
      - '.github/workflows/ci-install.yml'
      - 'dowhy/**'
      - 'tests/**'
      - 'pyproject.toml'
      - 'poetry.lock'

  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/ci-install.yml'
      - 'dowhy/**'
      - 'tests/**'
      - 'pyproject.toml'
      - 'poetry.lock'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    # At the moment needed to support scipy in Python 3.12
    - name: Install LLVM and Clang
      uses: KyleMayes/install-llvm-action@v2
      with:
        version: "14.0"
        directory: ${{ runner.temp }}/llvm

    - name: Install graphviz
      run: |
        sudo apt install graphviz libgraphviz-dev graphviz-dev pkg-config

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        echo "HEAD_REPO='$HEAD_REPO'"
        echo "HEAD_BRANCH='$HEAD_BRANCH'"
        echo "GITHUB_CONTEXT='$GITHUB_CONTEXT'"
        pip install git+https://github.com/${HEAD_REPO}@${HEAD_BRANCH:-main}
      env:
        GITHUB_CONTEXT: ${{ toJson(github) }}
        HEAD_REPO: ${{ github.event.pull_request.head.repo.full_name || github.repository }}
        HEAD_BRANCH: ${{ github.event.pull_request.head.ref || github.head_ref || 'main' }}

    - name: Notify Discord
      if: failure() && github.ref == 'refs/heads/main'
      uses: th0th/notify-discord@v0.4.1
      env:
        DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK }}
        GITHUB_ACTOR: ${{ github.actor }}
        GITHUB_JOB_NAME: CI Install Check Failed on Main
        GITHUB_JOB_STATUS: ${{ job.status }}
