


# 加载 autoreload 扩展。这允许你在不重新启动 Jupyter Notebook 的情况下自动重新加载已更改的模块。
%load_ext autoreload
# 设置 autoreload 模式为 2。这意味着所有模块都将在每次执行之前自动重新加载。
%autoreload 2


#pip install econml


# 导入所需的库和模块
import numpy as np  # NumPy 用于数值计算
import pandas as pd  # Pandas 用于数据处理和分析
import logging  # logging 用于日志管理

# 导入 DoWhy 库，它用于因果推断
import dowhy
from dowhy import CausalModel  # 从 DoWhy 导入 CausalModel 类
import dowhy.datasets  # 用于导入 DoWhy 自带的数据集

# 导入 EconML 库，这是一个用于估计因果效应的库
import econml

# 忽略警告
import warnings
warnings.filterwarnings('ignore')

# 设置 BETA 值为 10，这可能是用于模拟数据或模型参数的常量
BETA = 10



# 使用 DoWhy 库中的 `linear_dataset` 函数生成一个线性数据集。
# 这个数据集包括一系列公共原因、仪器变量、效应修饰符等。
# 参数设置如下：
# - BETA：因果效应的真实值
# - num_common_causes：公共原因（混杂变量）的数量
# - num_samples：样本数量
# - num_instruments：仪器变量的数量
# - num_effect_modifiers：效应修饰符的数量
# - num_treatments：处理（即干预或治疗）的数量
# - treatment_is_binary：处理是否是二元的（这里设置为 False）
# - num_discrete_common_causes：离散的公共原因的数量
# - num_discrete_effect_modifiers：离散的效应修饰符的数量
# - one_hot_encode：是否进行 one-hot 编码（这里设置为 False）
data = dowhy.datasets.linear_dataset(BETA, 
                                     num_common_causes=4, 
                                     num_samples=10000,
                                     num_instruments=2, 
                                     num_effect_modifiers=2,
                                     num_treatments=1,
                                     treatment_is_binary=False,
                                     num_discrete_common_causes=2,
                                     num_discrete_effect_modifiers=0,
                                     one_hot_encode=False)

# 从生成的数据字典中提取 DataFrame
df = data['df']

# 打印 DataFrame 的前几行以查看数据
print(df.head())

# 打印真实的因果效应估计值
# 这是我们希望通过因果分析接近的值
print("True causal estimate is", data["ate"])



# 创建一个因果模型对象。
# - data：包含观察数据的 DataFrame。
# - treatment：处理（或干预）变量的名称。
# - outcome：结果变量的名称。
# - graph：指定因果关系的 GML 图（Graph Modeling Language）。
# 这个图定义了各个变量之间的因果关系，帮助进行后续的因果效应识别和估计。
model = CausalModel(data=data["df"], 
                    treatment=data["treatment_name"], outcome=data["outcome_name"], 
                    graph=data["gml_graph"])



model.view_model()



# 使用 'identify_effect' 方法来识别因果效应的估算量（Estimand）。
# 这个步骤基于先前定义的因果模型和因果图。
# 'proceed_when_unidentifiable=True' 参数意味着，即使估算量不可识别，也会继续执行。
# 执行这个方法会返回一个估算量对象，其中包含了因果效应的数学表达式和需要满足的假设。
identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)

# 打印识别的估算量及其相关属性和假设。
# 这将帮助你了解可以用哪些方法来进行因果效应的估计，以及这些方法需要满足哪些假设。
print(identified_estimand)






# 使用 model 对象的 'estimate_effect' 方法来估计识别出来的因果效应（identified_estimand）。
# 这里选择了 "backdoor.linear_regression" 作为估计方法。
# control_value=0 和 treatment_value=1 分别指定了处理变量的对照组和处理组的取值。
linear_estimate = model.estimate_effect(identified_estimand, 
                                        method_name="backdoor.linear_regression",
                                        control_value=0,
                                        treatment_value=1)

# 打印出估计的因果效应结果。
print(linear_estimate)  






# 从 scikit-learn 中导入所需的模块
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

# 使用 econml 包中的 DML 方法来估计因果效应。
# control_value 设置为 0，treatment_value 设置为 1。
# target_units 指定我们对数据子集中 "X0" > 1 的情况感兴趣。
# 我们对结果（'model_y'）和处理（'model_t'）模型都使用 GradientBoostingRegressor。
# 对于最终模型（'model_final'）使用 LassoCV，并使用 PolynomialFeatures 进行特征工程。
dml_estimate = model.estimate_effect(identified_estimand, 
                                     method_name="backdoor.econml.dml.DML",
                                     control_value = 0,
                                     treatment_value = 1,
                                     target_units = lambda df: df["X0"] > 1,  # 仅考虑 X0 > 1，用来计算CATE
                                     confidence_intervals=False,  # 不计算置信区间
                                     method_params={
                                         "init_params": {
                                             'model_y': GradientBoostingRegressor(),  # 结果的模型
                                             'model_t': GradientBoostingRegressor(),  # 处理的模型
                                             "model_final": LassoCV(fit_intercept=False),  # 最终模型
                                             'featurizer': PolynomialFeatures(degree=1, include_bias=False)  # 特征工程
                                         },
                                         "fit_params": {}  # fit 方法的额外参数
                                     })

# 打印估计的效应
print(dml_estimate)



print("True causal estimate is", data["ate"])


# 使用 EconML 库中的 DML 方法来估计因果效应
dml_estimate = model.estimate_effect(
    identified_estimand,  # 已识别的因果关系
    method_name="backdoor.econml.dml.DML",  # 指定使用 EconML 的 DML 方法
    control_value=0,  # 处理变量（treatment）的控制组值
    treatment_value=1,  # 处理变量（treatment）的实验组值
    target_units=1,  # 用于 CATE 估计的目标单位条件，这里设置为 1
    confidence_intervals=False,  # 不计算置信区间
    # 传递额外的参数
    method_params={
        "init_params": {
            'model_y': GradientBoostingRegressor(),  # 用于预测结果（outcome）的模型
            'model_t': GradientBoostingRegressor(),  # 用于预测处理（treatment）的模型
            "model_final": LassoCV(fit_intercept=False),  # 最终用于估计因果效应的模型
            'featurizer': PolynomialFeatures(degree=1, include_bias=True)  # 特征工程
        },
        "fit_params": {}  # 训练模型阶段的额外参数，这里为空
    }
)
# 打印出估计的因果效应
print(dml_estimate)






# 从sklearn库中导入必要的模块进行特征工程和建模
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LassoCV
from sklearn.ensemble import GradientBoostingRegressor

# 从econml库中导入BootstrapInference用于估计置信区间
from econml.inference import BootstrapInference

# 使用DoWhy的 estimate_effect 方法来估计因果效应，特别是使用EconML库中的DML估计器
dml_estimate = model.estimate_effect(identified_estimand, 
                                     method_name="backdoor.econml.dml.DML",  # 指定使用EconML的DML方法
                                     target_units = "ate",  # 我们对平均处理效应（ATE）感兴趣
                                     confidence_intervals=True,  # 计算置信区间
                                     method_params={
                                         "init_params": {
                                             'model_y': GradientBoostingRegressor(),  # 用于结果（outcome）的模型
                                             'model_t': GradientBoostingRegressor(),  # 用于处理（treatment）的模型
                                             "model_final": LassoCV(fit_intercept=False),  # 最终用于处理效应的模型
                                             'featurizer': PolynomialFeatures(degree=1, include_bias=True)  # 特征工程
                                         },
                                         "fit_params": {
                                             'inference': BootstrapInference(n_bootstrap_samples=100, n_jobs=-1)  # 用于置信区间估计的参数
                                         }
                                     })

# 打印估计的因果效应
print(dml_estimate)






# 获取所有效应修饰变量的列名
test_cols = data['effect_modifier_names']

# 为每个效应修饰变量生成随机样本数据，这里生成了 10 个样本
test_arr = [np.random.uniform(0, 1, 10) for _ in range(len(test_cols))]

# 创建包含随机样本数据的DataFrame，列名为效应修饰变量的列名
test_df = pd.DataFrame(np.array(test_arr).transpose(), columns=test_cols)

# 使用因果模型估计因果效应，针对指定的目标单元（test_df）
dml_estimate = model.estimate_effect(identified_estimand, 
                                     method_name="backdoor.econml.dml.DML",
                                     target_units=test_df,
                                     confidence_intervals=False,
                                     method_params={"init_params": {'model_y': GradientBoostingRegressor(),
                                                                   'model_t': GradientBoostingRegressor(),
                                                                   "model_final": LassoCV(), 
                                                                   'featurizer': PolynomialFeatures(degree=1, include_bias=True)},
                                                    "fit_params": {}}
                                    )

# 打印估计的因果效应估计值
print(dml_estimate.cate_estimates)






print(dml_estimate._estimator_object)








# 创建一个具有二进制处理和结果的数据集
data_binary = dowhy.datasets.linear_dataset(BETA, num_common_causes=4, num_samples=10000,
                                    num_instruments=2, num_effect_modifiers=2,
                                    treatment_is_binary=True, outcome_is_binary=True)

# 将布尔值转换为{0,1}的数值
data_binary['df'].v0 = data_binary['df'].v0.astype(int)
data_binary['df'].y = data_binary['df'].y.astype(int)

# 创建因果模型（CausalModel）
model_binary = CausalModel(data=data_binary["df"], 
                    treatment=data_binary["treatment_name"], outcome=data_binary["outcome_name"], 
                    graph=data_binary["gml_graph"])

# 识别因果效应
identified_estimand_binary = model_binary.identify_effect(proceed_when_unidentifiable=True)






from sklearn.linear_model import LogisticRegressionCV

# 使用 LogisticRegressionCV 来拟合处理和结果之间的关系（需要二进制的 y）
drlearner_estimate = model_binary.estimate_effect(identified_estimand_binary, 
                                method_name="backdoor.econml.dr.LinearDRLearner",
                                confidence_intervals=False,
                                method_params={"init_params":{
                                                    'model_propensity': LogisticRegressionCV(cv=3, solver='lbfgs', multi_class='auto')
                                                    },
                                               "fit_params":{}
                                              })

# 打印估计的因果效应
print(drlearner_estimate)
# 打印真实因果估计值
print("True causal estimate is", data_binary["ate"])






import tensorflow as tf
print("Num GPUs Available: ", len(tf.config.experimental.list_physical_devices('GPU')))



# 导入Keras库，用于构建深度神经网络模型
import keras

# 计算输入层的维度
dims_zx = len(model.get_instruments()) + len(model.get_effect_modifiers())
dims_tx = len(model._treatment) + len(model.get_effect_modifiers())

# 创建处理模型，这是一个包含多个隐藏层的神经网络
treatment_model = keras.Sequential([
    keras.layers.Dense(128, activation='relu', input_shape=(dims_zx,)),  # 输入层，使用ReLU激活函数
    keras.layers.Dropout(0.17),  # 添加Dropout层，以防止过拟合
    keras.layers.Dense(64, activation='relu'),  # 隐藏层1，使用ReLU激活函数
    keras.layers.Dropout(0.17),  # 添加Dropout层
    keras.layers.Dense(32, activation='relu'),  # 隐藏层2，使用ReLU激活函数
    keras.layers.Dropout(0.17)  # 添加Dropout层
])

# 创建响应模型，也是一个包含多个隐藏层的神经网络
response_model = keras.Sequential([
    keras.layers.Dense(128, activation='relu', input_shape=(dims_tx,)),  # 输入层，使用ReLU激活函数
    keras.layers.Dropout(0.17),  # 添加Dropout层，以防止过拟合
    keras.layers.Dense(64, activation='relu'),  # 隐藏层1，使用ReLU激活函数
    keras.layers.Dropout(0.17),  # 添加Dropout层
    keras.layers.Dense(32, activation='relu'),  # 隐藏层2，使用ReLU激活函数
    keras.layers.Dropout(0.17),  # 添加Dropout层
    keras.layers.Dense(1)  # 输出层，一个神经元用于回归问题
])

# 使用EconML中的DeepIV方法估计因果效应
deepiv_estimate = model.estimate_effect(
    identified_estimand,
    method_name="iv.econml.iv.nnet.DeepIV",
    target_units=lambda df: df["X0"] > -1,  # 设置目标单位的条件
    confidence_intervals=False,  # 不计算置信区间
    method_params={
        "init_params": {
            'n_components': 10,  # 混合密度网络中的高斯成分数量
            'm': lambda z, x: treatment_model(keras.layers.concatenate([z, x])),  # 处理模型
            "h": lambda t, x: response_model(keras.layers.concatenate([t, x])),  # 响应模型
            'n_samples': 1,  # 用于估计响应的样本数量
            'first_stage_options': {'epochs': 25},  # 第一阶段训练参数
            'second_stage_options': {'epochs': 25}  # 第二阶段训练参数
        },
        "fit_params": {}
    }
)

# 打印DeepIV估计的因果效应
print(deepiv_estimate)






# 创建一个线性数据集，包括5个共同原因、2个工具变量、5个影响修饰因素
data_experiment = dowhy.datasets.linear_dataset(BETA, num_common_causes=5, num_samples=10000,
                                    num_instruments=2, num_effect_modifiers=5,
                                    treatment_is_binary=True, outcome_is_binary=False)

# 将布尔值转换为 {0,1} 数值
data_experiment['df'].v0 = data_experiment['df'].v0.astype(int)

# 打印数据集的前几行
print(data_experiment['df'])

# 创建因果模型，包括数据、处理变量和结果变量，以及因果图
model_experiment = CausalModel(data=data_experiment["df"], 
                    treatment=data_experiment["treatment_name"], outcome=data_experiment["outcome_name"], 
                    graph=data_experiment["gml_graph"])

# 识别因果估计
identified_estimand_experiment = model_experiment.identify_effect(proceed_when_unidentifiable=True)



from sklearn.ensemble import RandomForestRegressor

# 使用 T-Learner 方法估计因果效应
metalearner_estimate = model_experiment.estimate_effect(identified_estimand_experiment, 
                                method_name="backdoor.econml.metalearners.TLearner",
                                confidence_intervals=False,
                                method_params={"init_params":{
                                                    'models': RandomForestRegressor()
                                                    },
                                               "fit_params":{}
                                              })

# 打印估计的因果效应
print(metalearner_estimate)

# 打印真实因果估计值
print("True causal estimate is", data_experiment["ate"])









# For metalearners, need to provide all the features (except treatmeant and outcome)
metalearner_estimate = model_experiment.estimate_effect(identified_estimand_experiment, 
                                method_name="backdoor.econml.metalearners.TLearner",
                                confidence_intervals=False,
                                fit_estimator=False,
                                target_units=data_experiment["df"].drop(["v0","y", "Z0", "Z1"], axis=1)[9995:],                        
                                method_params={})
print(metalearner_estimate)
print("True causal estimate is", data_experiment["ate"])








res_random=model.refute_estimate(identified_estimand, dml_estimate, method_name="random_common_cause")
print(res_random)





res_unobserved=model.refute_estimate(identified_estimand, dml_estimate, method_name="add_unobserved_common_cause",
                                     confounders_effect_on_treatment="linear", confounders_effect_on_outcome="linear",
                                    effect_strength_on_treatment=0.01, effect_strength_on_outcome=0.02)
print(res_unobserved)





res_placebo=model.refute_estimate(identified_estimand, dml_estimate,
        method_name="placebo_treatment_refuter", placebo_type="permute",
        num_simulations=10 # at least 100 is good, setting to 10 for speed 
        ) 
print(res_placebo)





res_subset=model.refute_estimate(identified_estimand, dml_estimate,
        method_name="data_subset_refuter", subset_fraction=0.8,
        num_simulations=10)
print(res_subset)












