# Maintainers

This document lists the Maintainers of the Project. Maintainers may be added once approved by the existing maintainers as described in the [Governance document](./GOVERNANCE.md). By adding your name to this list you are agreeing to abide by the Project governance documents and to abide by all of the PyWhy's polices, including the [code of conduct](https://github.com/py-why/governance/blob/main/CODE-OF-CONDUCT.md), [trademark policy](https://github.com/py-why/governance/blob/main/TRADEMARKS.md), and [antitrust policy](https://github.com/py-why/governance/blob/main/ANTITRUST.md). If you are participating because of your affiliation with another organization (designated below), you represent that you have the authority to bind that organization to these policies.

| **NAME** | **Organization** |
| --- | --- |
| <PERSON><PERSON> | <PERSON> |
| Am<PERSON> | Microsoft |
| <PERSON> | Microsoft |
| <PERSON> | Amazon Web Services |
| <PERSON> | Amazon Web Services |

---
Part of MVG-0.1-beta.
Made with love by GitHub. Licensed under the [CC-BY 4.0 License](https://creativecommons.org/licenses/by-sa/4.0/).
