{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Orthogonal Random Forest and Causal Forest: Use Cases and Examples\n", "\n", "Causal Forests and Generalized Random Forests are a flexible method for estimating treatment effect heterogeneity with Random Forests. Orthogonal Random Forest (ORF) combines orthogonalization, a technique that effectively removes the confounding effect in two-stage estimation, with generalized random forests. Due to the orthogonalization aspect of this method, the ORF performs especially well in the presence of high-dimensional confounders. For more details, see [this paper](https://arxiv.org/abs/1806.03467) or the [EconML docummentation](https://econml.azurewebsites.net/).\n", "\n", "The EconML SDK implements the following OrthoForest variants:\n", "\n", "* DMLOrthoForest: suitable for continuous or discrete treatments\n", "\n", "* DROrthoForest: suitable for discrete treatments\n", "\n", "* CausalForest: suitable for both discrete and continuous treatments\n", "\n", "In this notebook, we show the performance of the ORF on synthetic and observational data. \n", "\n", "## Notebook Contents\n", "\n", "1. [Example Usage with Continuous Treatment Synthetic Data](#1.-Example-Usage-with-Continuous-Treatment-Synthetic-Data)\n", "2. [Example Usage with Binary Treatment Synthetic Data](#2.-Example-Usage-with-Binary-Treatment-Synthetic-Data)\n", "3. [Example Usage with Multiple Treatment Synthetic Data](#3.-Example-Usage-with-Multiple-Treatment-Synthetic-Data)\n", "4. [Example Usage with Real Continuous Treatment Observational Data](#4.-Example-Usage-with-Real-Continuous-Treatment-Observational-Data)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import econml"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Main imports\n", "from econml.orf import DMLOrthoForest, DROrthoForest\n", "from econml.dml import CausalForestDML\n", "from econml.sklearn_extensions.linear_model import WeightedLassoCVWrapper, WeightedLasso, WeightedLassoCV\n", "\n", "# Helper imports\n", "import numpy as np\n", "from itertools import product\n", "from sklearn.linear_model import Lasso, LassoCV, LogisticRegression, LogisticRegressionCV\n", "import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. Example Usage with Continuous Treatment Synthetic Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.1 DGP \n", "We use the data generating process (DGP) from [here](https://arxiv.org/abs/1806.03467). The DGP is described by the following equations:\n", "\n", "\\begin{align}\n", "T =& \\langle W, \\beta\\rangle + \\eta, & \\;\\eta \\sim \\text{Uniform}(-1, 1)\\\\\n", "Y =& T\\cdot \\theta(X) + \\langle W, \\gamma\\rangle + \\epsilon, &\\; \\epsilon \\sim \\text{Uniform}(-1, 1)\\\\\n", "W \\sim& \\text{Normal}(0,\\, I_{n_w})\\\\\n", "X \\sim& \\text{Uniform}(0,1)^{n_x}\n", "\\end{align}\n", "\n", "where $W$ is a matrix of high-dimensional confounders and $\\beta, \\gamma$ have high sparsity.\n", "\n", "For this DGP, \n", "\\begin{align}\n", "\\theta(x) = \\exp(2\\cdot x_1).\n", "\\end{align}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Treatment effect function\n", "def exp_te(x):\n", "    return np.exp(2*x[0])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# DGP constants\n", "np.random.seed(123)\n", "n = 1000\n", "n_w = 30\n", "support_size = 5\n", "n_x = 1\n", "# Outcome support\n", "support_Y = np.random.choice(range(n_w), size=support_size, replace=False)\n", "coefs_Y = np.random.uniform(0, 1, size=support_size)\n", "epsilon_sample = lambda n: np.random.uniform(-1, 1, size=n)\n", "# Treatment support \n", "support_T = support_Y\n", "coefs_T = np.random.uniform(0, 1, size=support_size)\n", "eta_sample = lambda n: np.random.uniform(-1, 1, size=n) \n", "\n", "# Generate controls, covariates, treatments and outcomes\n", "W = np.random.normal(0, 1, size=(n, n_w))\n", "X = np.random.uniform(0, 1, size=(n, n_x))\n", "# Heterogeneous treatment effects\n", "TE = np.array([exp_te(x_i) for x_i in X])\n", "T = np.dot(W[:, support_T], coefs_T) + eta_sample(n)\n", "Y = TE * T + np.dot(W[:, support_Y], coefs_Y) + epsilon_sample(n)\n", "\n", "# ORF parameters and test data\n", "subsample_ratio = 0.3\n", "lambda_reg = np.sqrt(np.log(n_w) / (10 * subsample_ratio * n))\n", "X_test = np.array(list(product(np.arange(0, 1, 0.01), repeat=n_x)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2. <PERSON> Estimator\n", "\n", "**Note:** The models in the final stage of the estimation (``model_T_final``, ``model_Y_final``) need to support sample weighting. \n", "\n", "If the models of choice do not support sample weights (e.g. ``sklearn.linear_model.LassoCV``), the ``econml`` packages provides a convenient wrapper for these models ``WeightedModelWrapper`` in order to allow sample weights."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["est = DMLOrthoForest(\n", "    n_trees=1000, min_leaf_size=5,\n", "    max_depth=50, subsample_ratio=subsample_ratio,\n", "    model_T=Lasso(alpha=lambda_reg),\n", "    model_Y=Lasso(alpha=lambda_reg),\n", "    model_T_final=WeightedLasso(alpha=lambda_reg),\n", "    model_Y_final=WeightedLasso(alpha=lambda_reg),\n", "    global_residualization=False,\n", "    random_state=123)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use the built-in confidence intervals constructed via Bootstrap of Little Bags, we can specify `inference=\"blb\"` at `fit` time or leave the default `inference='auto'` which will automatically use the Bootstrap of Little Bags."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   21.6s\n", "[Parallel(n_jobs=-1)]: Done 176 tasks      | elapsed:   22.6s\n", "[Parallel(n_jobs=-1)]: Done 816 tasks      | elapsed:   25.6s\n", "[Parallel(n_jobs=-1)]: Done 1000 out of 1000 | elapsed:   26.5s finished\n", "[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    0.0s\n", "[Parallel(n_jobs=-1)]: Done 368 tasks      | elapsed:    1.7s\n", "[Parallel(n_jobs=-1)]: Done 984 tasks      | elapsed:    4.7s\n", "[Parallel(n_jobs=-1)]: Done 1000 out of 1000 | elapsed:    4.7s finished\n"]}, {"data": {"text/plain": ["<econml.orf._ortho_forest.DMLOrthoForest at 0x1a7d2b58a58>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["est.fit(Y, T, X=X, W=W, inference=\"blb\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   21.6s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:   23.9s finished\n"]}], "source": ["# Calculate treatment effects\n", "treatment_effects = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  18 tasks      | elapsed:    3.3s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:    7.6s finished\n"]}], "source": ["# Calculate default (95%) confidence intervals for the test data\n", "te_lower, te_upper = est.effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    3.4s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:    7.4s finished\n"]}], "source": ["res = est.effect_inference(X_test)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>X</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.161</td>\n", "      <td>0.183</td>\n", "      <td>6.339</td>\n", "      <td>0.0</td>\n", "      <td>0.802</td>\n", "      <td>1.520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.171</td>\n", "      <td>0.177</td>\n", "      <td>6.628</td>\n", "      <td>0.0</td>\n", "      <td>0.825</td>\n", "      <td>1.518</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.182</td>\n", "      <td>0.171</td>\n", "      <td>6.925</td>\n", "      <td>0.0</td>\n", "      <td>0.847</td>\n", "      <td>1.516</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.192</td>\n", "      <td>0.165</td>\n", "      <td>7.228</td>\n", "      <td>0.0</td>\n", "      <td>0.869</td>\n", "      <td>1.515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.202</td>\n", "      <td>0.160</td>\n", "      <td>7.533</td>\n", "      <td>0.0</td>\n", "      <td>0.890</td>\n", "      <td>1.515</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   point_estimate  stderr  zstat  pvalue  ci_lower  ci_upper\n", "X                                                           \n", "0           1.161   0.183  6.339     0.0     0.802     1.520\n", "1           1.171   0.177  6.628     0.0     0.825     1.518\n", "2           1.182   0.171  6.925     0.0     0.847     1.516\n", "3           1.192   0.165  7.228     0.0     0.869     1.515\n", "4           1.202   0.160  7.533     0.0     0.890     1.515"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["res.summary_frame().head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Uncertainty of Mean Point Estimate</caption>\n", "<tr>\n", "  <th>mean_point</th> <th>stderr_mean</th> <th>zstat</th> <th>pvalue</th> <th>ci_mean_lower</th> <th>ci_mean_upper</th>\n", "</tr>\n", "<tr>\n", "     <td>3.179</td>      <td>0.287</td>    <td>11.06</td>   <td>0.0</td>      <td>2.616</td>         <td>3.742</td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Distribution of Point Estimate</caption>\n", "<tr>\n", "  <th>std_point</th> <th>pct_point_lower</th> <th>pct_point_upper</th>\n", "</tr>\n", "<tr>\n", "    <td>1.715</td>        <td>1.187</td>           <td>6.276</td>     \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Total Variance of Point Estimate</caption>\n", "<tr>\n", "  <th>stderr_point</th> <th>ci_point_lower</th> <th>ci_point_upper</th>\n", "</tr>\n", "<tr>\n", "      <td>1.739</td>         <td>1.079</td>          <td>6.525</td>    \n", "</tr>\n", "</table><br/><br/>Note: The stderr_mean is a conservative upper bound."], "text/plain": ["<econml.inference._inference.PopulationSummaryResults at 0x1a7b5af2f98>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["res.population_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Similarly we can estimate effects and get confidence intervals and inference results using a `CausalForest`."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["est2 = CausalForestDML(model_t=Lasso(alpha=lambda_reg),\n", "                       model_y=Lasso(alpha=lambda_reg),\n", "                       n_estimators=4000, min_samples_leaf=5,\n", "                       max_depth=50,\n", "                       verbose=0, random_state=123)\n", "est2.tune(Y, T, X=X, W=W)\n", "est2.fit(Y, T, X=X, W=W)\n", "treatment_effects2 = est2.effect(X_test)\n", "te_lower2, te_upper2 = est2.effect_interval(X_test, alpha=0.01)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.3. Performance Visualization"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"Continuous<PERSON>rthoFores<PERSON>\")\n", "plt.plot(X_test, treatment_effects, label='ORF estimate')\n", "expected_te = np.array([exp_te(x_i) for x_i in X_test])\n", "plt.plot(X_test[:, 0], expected_te, 'b--', label='True effect')\n", "plt.fill_between(X_test[:, 0], te_lower, te_upper, label=\"95% BLB CI\", alpha=0.3)\n", "plt.ylabel(\"Treatment Effect\")\n", "plt.xlabel(\"x\")\n", "plt.legend()\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"CausalForest\")\n", "plt.plot(X_test, treatment_effects2, label='ORF estimate')\n", "expected_te = np.array([exp_te(x_i) for x_i in X_test])\n", "plt.plot(X_test[:, 0], expected_te, 'b--', label='True effect')\n", "plt.fill_between(X_test[:, 0], te_lower2, te_upper2, label=\"95% BLB CI\", alpha=0.3)\n", "plt.ylabel(\"Treatment Effect\")\n", "plt.xlabel(\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Example Usage with Binary Treatment Synthetic Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.1. DGP \n", "We use the following DGP:\n", "\n", "\\begin{align}\n", "T \\sim & \\text{<PERSON><PERSON><PERSON>}\\left(f(W)\\right), &\\; f(W)=\\sigma(\\langle W, \\beta\\rangle + \\eta), \\;\\eta \\sim \\text{Uniform}(-1, 1)\\\\\n", "Y = & T\\cdot \\theta(X) + \\langle W, \\gamma\\rangle + \\epsilon, & \\; \\epsilon \\sim \\text{Uniform}(-1, 1)\\\\\n", "W \\sim & \\text{Normal}(0,\\, I_{n_w}) & \\\\\n", "X \\sim & \\text{Uniform}(0,\\, 1)^{n_x}\n", "\\end{align}\n", "\n", "where $W$ is a matrix of high-dimensional confounders, $\\beta, \\gamma$ have high sparsity and $\\sigma$ is the sigmoid function.\n", "\n", "For this DGP, \n", "\\begin{align}\n", "\\theta(x) = \\exp( 2\\cdot x_1 ).\n", "\\end{align}"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# DGP constants\n", "np.random.seed(1234)\n", "n = 1000\n", "n_w = 30\n", "support_size = 5\n", "n_x = 1\n", "# Outcome support\n", "support_Y = np.random.choice(range(n_w), size=support_size, replace=False)\n", "coefs_Y = np.random.uniform(0, 1, size=support_size)\n", "epsilon_sample = lambda n: np.random.uniform(-1, 1, size=n)\n", "# Treatment support\n", "support_T = support_Y\n", "coefs_T = np.random.uniform(0, 1, size=support_size)\n", "eta_sample = lambda n: np.random.uniform(-1, 1, size=n) \n", "\n", "# Generate controls, covariates, treatments and outcomes\n", "W = np.random.normal(0, 1, size=(n, n_w))\n", "X = np.random.uniform(0, 1, size=(n, n_x))\n", "# Heterogeneous treatment effects\n", "TE = np.array([exp_te(x_i) for x_i in X])\n", "# Define treatment\n", "log_odds = np.dot(W[:, support_T], coefs_T) + eta_sample(n)\n", "T_sigmoid = 1/(1 + np.exp(-log_odds))\n", "T = np.array([np.random.binomial(1, p) for p in T_sigmoid])\n", "# Define the outcome\n", "Y = TE * T + np.dot(W[:, support_Y], coefs_Y) + epsilon_sample(n)\n", "\n", "# ORF parameters and test data\n", "subsample_ratio = 0.4\n", "X_test = np.array(list(product(np.arange(0, 1, 0.01), repeat=n_x)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.2. <PERSON> Estimator "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["est = DROrthoForest(\n", "    n_trees=200, min_leaf_size=10,\n", "    max_depth=30, subsample_ratio=subsample_ratio,\n", "    propensity_model = LogisticRegression(C=1/(X.shape[0]*lambda_reg), penalty='l1', solver='saga'),\n", "    model_Y = Lasso(alpha=lambda_reg),\n", "    propensity_model_final=LogisticRegression(C=1/(X.shape[0]*lambda_reg), penalty='l1', solver='saga'), \n", "    model_Y_final=WeightedLasso(alpha=lambda_reg)\n", ")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   26.6s\n", "[Parallel(n_jobs=-1)]: Done 176 tasks      | elapsed:   27.6s\n", "[Parallel(n_jobs=-1)]: Done 200 out of 200 | elapsed:   27.8s finished\n", "[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    0.2s\n", "[Parallel(n_jobs=-1)]: Done 185 out of 200 | elapsed:    1.0s remaining:    0.0s\n", "[Parallel(n_jobs=-1)]: Done 200 out of 200 | elapsed:    1.0s finished\n"]}, {"data": {"text/plain": ["<econml.orf._ortho_forest.DROrthoForest at 0x1a7b974ee48>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["est.fit(Y, T, X=X, W=W)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   37.4s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:   41.0s finished\n"]}], "source": ["# Calculate treatment effects for the default treatment points T0=0 and T1=1\n", "treatment_effects = est.effect(X_test)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    1.8s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:    3.5s finished\n"]}], "source": ["# Calculate default (95%) confidence intervals for the default treatment points T0=0 and T1=1\n", "te_lower, te_upper = est.effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["est2 = CausalForestDML(model_y=Lasso(alpha=lambda_reg),\n", "                       model_t=LogisticRegression(C=1/(X.shape[0]*lambda_reg)),\n", "                       n_estimators=200, min_samples_leaf=5,\n", "                       max_depth=50, max_samples=subsample_ratio/2,\n", "                       discrete_treatment=True,\n", "                       random_state=123)\n", "est2.fit(Y, T, X=X, W=W, cache_values=True)\n", "treatment_effects2 = est2.effect(X_test)\n", "te_lower2, te_upper2 = est2.effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Population summary of CATE predictions on Training Data\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Uncertainty of Mean Point Estimate</caption>\n", "<tr>\n", "  <th>mean_point</th> <th>stderr_mean</th>  <th>zstat</th> <th>pvalue</th> <th>ci_mean_lower</th> <th>ci_mean_upper</th>\n", "</tr>\n", "<tr>\n", "     <td>3.088</td>      <td>0.157</td>    <td>19.677</td>   <td>0.0</td>      <td>2.78</td>          <td>3.396</td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Distribution of Point Estimate</caption>\n", "<tr>\n", "  <th>std_point</th> <th>pct_point_lower</th> <th>pct_point_upper</th>\n", "</tr>\n", "<tr>\n", "    <td>1.757</td>        <td>0.846</td>           <td>6.962</td>     \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Total Variance of Point Estimate</caption>\n", "<tr>\n", "  <th>stderr_point</th> <th>ci_point_lower</th> <th>ci_point_upper</th>\n", "</tr>\n", "<tr>\n", "      <td>1.764</td>         <td>0.774</td>          <td>6.951</td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Doubly Robust ATE on Training Data Results</caption>\n", "<tr>\n", "   <td></td>   <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>ATE</th>      <td>3.158</td>      <td>0.082</td> <td>38.551</td>   <td>0.0</td>    <td>2.997</td>    <td>3.318</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Doubly Robust ATT(T=0) on Training Data Results</caption>\n", "<tr>\n", "   <td></td>   <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>ATT</th>       <td>3.1</td>       <td>0.096</td> <td>32.322</td>   <td>0.0</td>    <td>2.912</td>    <td>3.288</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Doubly Robust ATT(T=1) on Training Data Results</caption>\n", "<tr>\n", "   <td></td>   <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>ATT</th>      <td>3.218</td>      <td>0.134</td> <td>23.965</td>   <td>0.0</td>    <td>2.955</td>    <td>3.481</td> \n", "</tr>\n", "</table><br/><br/>Note: The stderr_mean is a conservative upper bound."], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "               Uncertainty of Mean Point Estimate               \n", "================================================================\n", "mean_point stderr_mean zstat  pvalue ci_mean_lower ci_mean_upper\n", "----------------------------------------------------------------\n", "     3.088       0.157 19.677    0.0          2.78         3.396\n", "      Distribution of Point Estimate     \n", "=========================================\n", "std_point pct_point_lower pct_point_upper\n", "-----------------------------------------\n", "    1.757           0.846           6.962\n", "     Total Variance of Point Estimate     \n", "==========================================\n", "stderr_point ci_point_lower ci_point_upper\n", "------------------------------------------\n", "       1.764          0.774          6.951\n", "        Doubly Robust ATE on Training Data Results       \n", "=========================================================\n", "    point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "ATE          3.158  0.082 38.551    0.0    2.997    3.318\n", "     Doubly Robust ATT(T=0) on Training Data Results     \n", "=========================================================\n", "    point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "ATT            3.1  0.096 32.322    0.0    2.912    3.288\n", "     Doubly Robust ATT(T=1) on Training Data Results     \n", "=========================================================\n", "    point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "---------------------------------------------------------\n", "ATT          3.218  0.134 23.965    0.0    2.955    3.481\n", "---------------------------------------------------------\n", "\n", "Note: The stderr_mean is a conservative upper bound.\n", "\"\"\""]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["est2.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.3. Performance Visualization"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA24AAAFNCAYAAAB49jzWAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAACam0lEQVR4nOzdd3zdVfnA8c+5Kz<PERSON><PERSON>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", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"DROrthoForest\")\n", "plt.plot(X_test, treatment_effects, label='ORF estimate')\n", "expected_te = np.array([exp_te(x_i) for x_i in X_test])\n", "plt.plot(X_test[:, 0], expected_te, 'b--', label='True effect')\n", "plt.fill_between(X_test[:, 0], te_lower, te_upper, label=\"95% BLB CI\", alpha=0.3)\n", "plt.ylabel(\"Treatment Effect\")\n", "plt.xlabel(\"x\")\n", "plt.legend()\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"CausalForest\")\n", "plt.plot(X_test, treatment_effects2, label='ORF estimate')\n", "expected_te = np.array([exp_te(x_i) for x_i in X_test])\n", "plt.plot(X_test[:, 0], expected_te, 'b--', label='True effect')\n", "plt.fill_between(X_test[:, 0], te_lower2, te_upper2, label=\"95% BLB CI\", alpha=0.3)\n", "plt.ylabel(\"Treatment Effect\")\n", "plt.xlabel(\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Example Usage with Multiple Treatment Synthetic Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.1. DGP \n", "We use the following DGP:\n", "\n", "\\begin{align}\n", "Y = & \\sum_{t=1}^{n_{\\text{treatments}}} 1\\{T=t\\}\\cdot \\theta_{T}(X) + \\langle W, \\gamma\\rangle + \\epsilon, \\; \\epsilon \\sim \\text{Unif}(-1, 1), \\\\\n", "\\text{Pr}[T=t \\mid W] \\propto & \\exp\\{\\langle W, \\beta_t \\rangle\\}, \\;\\;\\;\\; \\forall t\\in \\{0, 1, \\ldots, n_{\\text{treatments}}\\} \n", "\\end{align}\n", "\n", "where $W$ is a matrix of high-dimensional confounders, $\\beta_t, \\gamma$ are sparse.\n", "\n", "For this particular example DGP we used $n_{\\text{treatments}}=3$ and \n", "\\begin{align}\n", "\\theta_1(x) = & \\exp( 2 x_1 ),\\\\\n", "\\theta_2(x) = &  3 \\cdot \\sigma(100\\cdot (x_1 - .5)),\\\\\n", "\\theta_3(x) = & -2 \\cdot \\sigma(100\\cdot (x_1 - .25)),\n", "\\end{align}\n", "where $\\sigma$ is the sigmoid function."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def get_test_train_data(n, n_w, support_size, n_x, te_func, n_treatments):\n", "    # Outcome support\n", "    support_Y = np.random.choice(range(n_w), size=support_size, replace=False)\n", "    coefs_Y = np.random.uniform(0, 1, size=support_size)\n", "    epsilon_sample = lambda n: np.random.uniform(-1, 1, size=n)\n", "    # Treatment support \n", "    support_T = support_Y\n", "    coefs_T = np.random.uniform(0, 1, size=(support_size, n_treatments))\n", "    eta_sample = lambda n: np.random.uniform(-1, 1, size=n) \n", "    # Generate controls, covariates, treatments and outcomes\n", "    W = np.random.normal(0, 1, size=(n, n_w))\n", "    X = np.random.uniform(0, 1, size=(n, n_x))\n", "    # Heterogeneous treatment effects\n", "    TE = np.array([te_func(x_i, n_treatments) for x_i in X])\n", "    log_odds = np.dot(W[:, support_T], coefs_T)\n", "    T_sigmoid = np.exp(log_odds)\n", "    T_sigmoid = T_sigmoid/np.sum(T_sigmoid, axis=1, keepdims=True)\n", "    T = np.array([np.random.choice(n_treatments, p=p) for p in T_sigmoid])\n", "    TE = np.concatenate((np.zeros((n,1)), TE), axis=1)\n", "    Y = TE[np.arange(n), T] + np.dot(W[:, support_Y], coefs_Y) + epsilon_sample(n)\n", "    X_test = np.array(list(product(np.arange(0, 1, 0.01), repeat=n_x)))\n", "\n", "    return (Y, T, X, W), (X_test, np.array([te_func(x, n_treatments) for x in X_test]))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import scipy.special\n", "def te_func(x, n_treatments):\n", "    return [np.exp(2*x[0]), 3*scipy.special.expit(100*(x[0] - .5)) - 1, -2*scipy.special.expit(100*(x[0] - .25))]\n", "\n", "np.random.seed(123)\n", "(Y, T, X, W), (X_test, te_test) = get_test_train_data(2000, 3, 3, 1, te_func, 4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.2. <PERSON> Estimator"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["est = DROrthoForest(n_trees=500, model_Y = WeightedLasso(alpha=lambda_reg))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   31.2s\n", "[Parallel(n_jobs=-1)]: Done 112 tasks      | elapsed:   32.9s\n", "[Parallel(n_jobs=-1)]: Done 272 tasks      | elapsed:   35.5s\n", "[Parallel(n_jobs=-1)]: Done 500 out of 500 | elapsed:   39.5s finished\n", "[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    0.3s\n", "[Parallel(n_jobs=-1)]: Done 208 tasks      | elapsed:    3.4s\n", "[Parallel(n_jobs=-1)]: Done 500 out of 500 | elapsed:    7.9s finished\n"]}, {"data": {"text/plain": ["<econml.orf._ortho_forest.DROrthoForest at 0x1a7bac95828>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["est.fit(Y, T, X=X, W=W)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   20.6s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:   22.8s finished\n"]}], "source": ["# Calculate marginal treatment effects\n", "treatment_effects = est.const_marginal_effect(X_test)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    2.7s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:    5.1s finished\n"]}], "source": ["# Calculate default (95%) marginal confidence intervals for the test data\n", "te_lower, te_upper = est.const_marginal_effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    2.5s\n", "[Parallel(n_jobs=-1)]: Done 100 out of 100 | elapsed:    4.9s finished\n"]}], "source": ["res = est.const_marginal_effect_inference(X_test)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>point_estimate</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>pvalue</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>X</th>\n", "      <th>T</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">0</th>\n", "      <th>T0_1</th>\n", "      <td>1.013</td>\n", "      <td>0.159</td>\n", "      <td>6.360</td>\n", "      <td>0.000</td>\n", "      <td>0.701</td>\n", "      <td>1.325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T0_2</th>\n", "      <td>-0.989</td>\n", "      <td>0.149</td>\n", "      <td>-6.636</td>\n", "      <td>0.000</td>\n", "      <td>-1.281</td>\n", "      <td>-0.697</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T0_3</th>\n", "      <td>0.034</td>\n", "      <td>0.226</td>\n", "      <td>0.152</td>\n", "      <td>0.879</td>\n", "      <td>-0.408</td>\n", "      <td>0.477</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">1</th>\n", "      <th>T0_1</th>\n", "      <td>1.018</td>\n", "      <td>0.160</td>\n", "      <td>6.379</td>\n", "      <td>0.000</td>\n", "      <td>0.705</td>\n", "      <td>1.331</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T0_2</th>\n", "      <td>-0.987</td>\n", "      <td>0.147</td>\n", "      <td>-6.717</td>\n", "      <td>0.000</td>\n", "      <td>-1.276</td>\n", "      <td>-0.699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">98</th>\n", "      <th>T0_2</th>\n", "      <td>1.967</td>\n", "      <td>0.210</td>\n", "      <td>9.345</td>\n", "      <td>0.000</td>\n", "      <td>1.554</td>\n", "      <td>2.379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T0_3</th>\n", "      <td>-2.021</td>\n", "      <td>0.163</td>\n", "      <td>-12.414</td>\n", "      <td>0.000</td>\n", "      <td>-2.340</td>\n", "      <td>-1.702</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">99</th>\n", "      <th>T0_1</th>\n", "      <td>6.867</td>\n", "      <td>0.244</td>\n", "      <td>28.194</td>\n", "      <td>0.000</td>\n", "      <td>6.390</td>\n", "      <td>7.344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T0_2</th>\n", "      <td>1.966</td>\n", "      <td>0.212</td>\n", "      <td>9.276</td>\n", "      <td>0.000</td>\n", "      <td>1.551</td>\n", "      <td>2.382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T0_3</th>\n", "      <td>-2.017</td>\n", "      <td>0.163</td>\n", "      <td>-12.352</td>\n", "      <td>0.000</td>\n", "      <td>-2.337</td>\n", "      <td>-1.697</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>300 rows × 6 columns</p>\n", "</div>"], "text/plain": ["         point_estimate  stderr   zstat  pvalue  ci_lower  ci_upper\n", "X  T                                                               \n", "0  T0_1           1.013   0.159   6.360   0.000     0.701     1.325\n", "   T0_2          -0.989   0.149  -6.636   0.000    -1.281    -0.697\n", "   T0_3           0.034   0.226   0.152   0.879    -0.408     0.477\n", "1  T0_1           1.018   0.160   6.379   0.000     0.705     1.331\n", "   T0_2          -0.987   0.147  -6.717   0.000    -1.276    -0.699\n", "...                 ...     ...     ...     ...       ...       ...\n", "98 T0_2           1.967   0.210   9.345   0.000     1.554     2.379\n", "   T0_3          -2.021   0.163 -12.414   0.000    -2.340    -1.702\n", "99 T0_1           6.867   0.244  28.194   0.000     6.390     7.344\n", "   T0_2           1.966   0.212   9.276   0.000     1.551     2.382\n", "   T0_3          -2.017   0.163 -12.352   0.000    -2.337    -1.697\n", "\n", "[300 rows x 6 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["res.summary_frame()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["est2 = CausalForestDML(model_y=Lasso(alpha=lambda_reg),\n", "                       model_t=LogisticRegression(C=1/(X.shape[0]*lambda_reg)),\n", "                       n_estimators=4000, min_samples_leaf=5,\n", "                       max_depth=50, max_samples=subsample_ratio/2,\n", "                       discrete_treatment=True,\n", "                       random_state=123)\n", "est2.fit(Y, T, X=X, W=W)\n", "treatment_effects2 = est2.const_marginal_effect(X_test)\n", "te_lower2, te_upper2 = est2.const_marginal_effect_interval(X_test, alpha=.01)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.3. Performance Visualization"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.title(\"DROrthoForest\")\n", "y = treatment_effects\n", "colors = ['b', 'r', 'g']\n", "for it in range(y.shape[1]):\n", "    plt.plot(X_test[:, 0], te_test[:, it], '--', label='True effect T={}'.format(it), color=colors[it])\n", "    plt.fill_between(X_test[:, 0], te_lower[:, it], te_upper[:, it], alpha=0.3, color='C{}'.format(it))\n", "    plt.plot(X_test, y[:, it], label='ORF estimate T={}'.format(it), color='C{}'.format(it))\n", "plt.ylabel(\"Treatment Effect\")\n", "plt.xlabel(\"x\")\n", "plt.legend()\n", "plt.subplot(1, 2, 2)\n", "plt.title(\"CausalForest\")\n", "y = treatment_effects2\n", "colors = ['b', 'r', 'g']\n", "for it in range(y.shape[1]):\n", "    plt.plot(X_test[:, 0], te_test[:, it], '--', label='True effect T={}'.format(it), color=colors[it])\n", "    plt.fill_between(X_test[:, 0], te_lower2[:, it], te_upper2[:, it], alpha=0.3, color='C{}'.format(it))\n", "    plt.plot(X_test, y[:, it], label='ORF estimate T={}'.format(it), color='C{}'.format(it))\n", "plt.ylabel(\"Treatment Effect\")\n", "plt.xlabel(\"x\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. Example Usage with Real Continuous Treatment Observational Data\n", "\n", "We applied our technique to <PERSON><PERSON>’s dataset, a popular historical dataset of store-level orange juice prices and sales provided by University of Chicago Booth School of Business. \n", "\n", "The dataset is comprised of a large number of covariates $W$, but researchers might only be interested in learning the elasticity of demand as a function of a few variables $x$ such\n", "as income or education. \n", "\n", "We applied the `DMLOrthoForest` to estimate orange juice price elasticity\n", "as a function of income, and our results, unveil the natural phenomenon that lower income consumers are more price-sensitive."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.1. Data"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# A few more imports\n", "import os\n", "import pandas as pd\n", "import urllib.request\n", "from sklearn.preprocessing import StandardScaler"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>brand</th>\n", "      <th>week</th>\n", "      <th>logmove</th>\n", "      <th>feat</th>\n", "      <th>price</th>\n", "      <th>AGE60</th>\n", "      <th>EDUC</th>\n", "      <th>ETHNIC</th>\n", "      <th>INCOME</th>\n", "      <th>HHLARGE</th>\n", "      <th>WORKWOM</th>\n", "      <th>HVAL150</th>\n", "      <th>SSTRDIST</th>\n", "      <th>SSTRVOL</th>\n", "      <th>CPDIST5</th>\n", "      <th>CPWVOL5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>tropicana</td>\n", "      <td>40</td>\n", "      <td>9.018695</td>\n", "      <td>0</td>\n", "      <td>3.87</td>\n", "      <td>0.232865</td>\n", "      <td>0.248935</td>\n", "      <td>0.11428</td>\n", "      <td>10.553205</td>\n", "      <td>0.103953</td>\n", "      <td>0.303585</td>\n", "      <td>0.463887</td>\n", "      <td>2.110122</td>\n", "      <td>1.142857</td>\n", "      <td>1.92728</td>\n", "      <td>0.376927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>tropicana</td>\n", "      <td>46</td>\n", "      <td>8.723231</td>\n", "      <td>0</td>\n", "      <td>3.87</td>\n", "      <td>0.232865</td>\n", "      <td>0.248935</td>\n", "      <td>0.11428</td>\n", "      <td>10.553205</td>\n", "      <td>0.103953</td>\n", "      <td>0.303585</td>\n", "      <td>0.463887</td>\n", "      <td>2.110122</td>\n", "      <td>1.142857</td>\n", "      <td>1.92728</td>\n", "      <td>0.376927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>tropicana</td>\n", "      <td>47</td>\n", "      <td>8.253228</td>\n", "      <td>0</td>\n", "      <td>3.87</td>\n", "      <td>0.232865</td>\n", "      <td>0.248935</td>\n", "      <td>0.11428</td>\n", "      <td>10.553205</td>\n", "      <td>0.103953</td>\n", "      <td>0.303585</td>\n", "      <td>0.463887</td>\n", "      <td>2.110122</td>\n", "      <td>1.142857</td>\n", "      <td>1.92728</td>\n", "      <td>0.376927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>tropicana</td>\n", "      <td>48</td>\n", "      <td>8.987197</td>\n", "      <td>0</td>\n", "      <td>3.87</td>\n", "      <td>0.232865</td>\n", "      <td>0.248935</td>\n", "      <td>0.11428</td>\n", "      <td>10.553205</td>\n", "      <td>0.103953</td>\n", "      <td>0.303585</td>\n", "      <td>0.463887</td>\n", "      <td>2.110122</td>\n", "      <td>1.142857</td>\n", "      <td>1.92728</td>\n", "      <td>0.376927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>tropicana</td>\n", "      <td>50</td>\n", "      <td>9.093357</td>\n", "      <td>0</td>\n", "      <td>3.87</td>\n", "      <td>0.232865</td>\n", "      <td>0.248935</td>\n", "      <td>0.11428</td>\n", "      <td>10.553205</td>\n", "      <td>0.103953</td>\n", "      <td>0.303585</td>\n", "      <td>0.463887</td>\n", "      <td>2.110122</td>\n", "      <td>1.142857</td>\n", "      <td>1.92728</td>\n", "      <td>0.376927</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   store      brand  week   logmove  feat  price     AGE60      EDUC   ETHNIC  \\\n", "0      2  tropicana    40  9.018695     0   3.87  0.232865  0.248935  0.11428   \n", "1      2  tropicana    46  8.723231     0   3.87  0.232865  0.248935  0.11428   \n", "2      2  tropicana    47  8.253228     0   3.87  0.232865  0.248935  0.11428   \n", "3      2  tropicana    48  8.987197     0   3.87  0.232865  0.248935  0.11428   \n", "4      2  tropicana    50  9.093357     0   3.87  0.232865  0.248935  0.11428   \n", "\n", "      INCOME   HHLARGE   WORKWOM   HVAL150  SSTRDIST   SSTRVOL  CPDIST5  \\\n", "0  10.553205  0.103953  0.303585  0.463887  2.110122  1.142857  1.92728   \n", "1  10.553205  0.103953  0.303585  0.463887  2.110122  1.142857  1.92728   \n", "2  10.553205  0.103953  0.303585  0.463887  2.110122  1.142857  1.92728   \n", "3  10.553205  0.103953  0.303585  0.463887  2.110122  1.142857  1.92728   \n", "4  10.553205  0.103953  0.303585  0.463887  2.110122  1.142857  1.92728   \n", "\n", "    CPWVOL5  \n", "0  0.376927  \n", "1  0.376927  \n", "2  0.376927  \n", "3  0.376927  \n", "4  0.376927  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Import the data\n", "file_name = \"oj_large.csv\"\n", "\n", "if not os.path.isfile(file_name):\n", "    print(\"Downloading file (this might take a few seconds)...\")\n", "    urllib.request.urlretrieve(\"https://msalicedatapublic.z5.web.core.windows.net/datasets/OrangeJuice/oj_large.csv\", file_name)\n", "oj_data = pd.read_csv(file_name)\n", "oj_data.head()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# Prepare data\n", "Y = oj_data['logmove'].values\n", "T = np.log(oj_data[\"price\"]).values\n", "scaler = StandardScaler()\n", "W1 = scaler.fit_transform(oj_data[[c for c in oj_data.columns if c not in ['price', 'logmove', 'brand', 'week', 'store']]].values)\n", "W2 = pd.get_dummies(oj_data[['brand']]).values\n", "W = np.concatenate([W1, W2], axis=1)\n", "X = oj_data[['INCOME']].values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.2. <PERSON> Estimator"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# Define some parameters\n", "n_trees = 1000\n", "min_leaf_size = 50\n", "max_depth = 20\n", "subsample_ratio = 0.04"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["est = DMLOrthoForest(\n", "        n_trees=n_trees, min_leaf_size=min_leaf_size, max_depth=max_depth, \n", "        subsample_ratio=subsample_ratio,\n", "        model_T=Lasso(alpha=0.1),\n", "        model_Y=Lasso(alpha=0.1),\n", "        model_T_final=WeightedLassoCVWrapper(cv=3), \n", "        model_Y_final=WeightedLassoCVWrapper(cv=3)\n", "       )"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   20.3s\n", "[Parallel(n_jobs=-1)]: Done 152 tasks      | elapsed:   21.0s\n", "[Parallel(n_jobs=-1)]: Done 1000 out of 1000 | elapsed:   22.5s finished\n", "[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    0.0s\n", "[Parallel(n_jobs=-1)]: Done 888 tasks      | elapsed:    1.6s\n", "[Parallel(n_jobs=-1)]: Done 1000 out of 1000 | elapsed:    2.1s finished\n"]}, {"data": {"text/plain": ["<econml.orf._ortho_forest.DMLOrthoForest at 0x1a7cdd37588>"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["est.fit(Y, T, X=X, W=W)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["min_income = 10.0 \n", "max_income = 11.1\n", "delta = (max_income - min_income) / 100\n", "X_test = np.arange(min_income, max_income + delta - 0.001, delta).reshape(-1, 1)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:   23.0s\n", "[Parallel(n_jobs=-1)]: Done 101 out of 101 | elapsed:   35.2s finished\n"]}], "source": ["# Calculate marginal treatment effects\n", "treatment_effects = est.const_marginal_effect(X_test)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done  16 tasks      | elapsed:    6.1s\n", "[Parallel(n_jobs=-1)]: Done 101 out of 101 | elapsed:   21.3s finished\n"]}], "source": ["# Calculate default (95%) marginal confidence intervals for the test data\n", "te_upper, te_lower = est.const_marginal_effect_interval(X_test)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["est2 = CausalForestDML(model_y=WeightedLassoCVWrapper(cv=3),\n", "                       model_t=WeightedLassoCVWrapper(cv=3),\n", "                       n_estimators=n_trees, min_samples_leaf=min_leaf_size, max_depth=max_depth,\n", "                       max_samples=subsample_ratio/2,\n", "                       random_state=123)\n", "est2.fit(Y, T, X=X, W=W)\n", "treatment_effects2 = est2.effect(X_test)\n", "te_lower2, te_upper2 = est2.effect_interval(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.3. Performance Visualization"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1080x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Plot Orange Juice elasticity as a function of income\n", "plt.figure(figsize=(15, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.plot(X_test.flatten(), treatment_effects, label=\"OJ Elasticity\")\n", "plt.fill_between(X_test.flatten(), te_lower, te_upper, label=\"95% BLB CI\", alpha=0.3)\n", "plt.xlabel(r'$\\log$(Income)')\n", "plt.ylabel('Orange Juice Elasticity')\n", "plt.legend()\n", "plt.title(\"Orange Juice Elasticity vs Income: DMLOrthoForest\")\n", "plt.subplot(1, 2, 2)\n", "plt.plot(X_test.flatten(), treatment_effects2, label=\"OJ Elasticity\")\n", "plt.fill_between(X_test.flatten(), te_lower2, te_upper2, label=\"95% BLB CI\", alpha=0.3)\n", "plt.xlabel(r'$\\log$(Income)')\n", "plt.ylabel('Orange Juice Elasticity')\n", "plt.legend()\n", "plt.title(\"Orange Juice Elasticity vs Income: CausalForest\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "d2603944574c6ce6e242666bf20bfb1bc23ccfec8e562036b69397ff157ca866"}}}, "nbformat": 4, "nbformat_minor": 2}