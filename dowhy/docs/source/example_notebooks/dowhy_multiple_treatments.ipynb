{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Estimating effect of multiple treatments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dowhy import CausalModel\n", "import dowhy.datasets\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = dowhy.datasets.linear_dataset(10, num_common_causes=4, num_samples=10000,\n", "                                     num_instruments=0, num_effect_modifiers=2,\n", "                                     num_treatments=2,\n", "                                     treatment_is_binary=False,\n", "                                     num_discrete_common_causes=2,\n", "                                     num_discrete_effect_modifiers=0,\n", "                                     one_hot_encode=False)\n", "df=data['df']\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = CausalModel(data=data[\"df\"], \n", "                    treatment=data[\"treatment_name\"], outcome=data[\"outcome_name\"], \n", "                    graph=data[\"gml_graph\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.view_model()\n", "from IPython.display import Image, display\n", "display(Image(filename=\"causal_model.png\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["identified_estimand= model.identify_effect(proceed_when_unidentifiable=True)\n", "print(identified_estimand)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Linear model\n", "\n", "Let us first see an example for a linear model. The control_value and treatment_value can be provided as a tuple/list when the treatment is multi-dimensional.\n", "\n", "The interpretation is change in y when v0 and v1 are changed from (0,0) to (1,1)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["linear_estimate = model.estimate_effect(identified_estimand, \n", "                                        method_name=\"backdoor.linear_regression\",\n", "                                        control_value=(0,0),\n", "                                        treatment_value=(1,1),\n", "                                        method_params={'need_conditional_estimates': False})\n", "print(linear_estimate) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can estimate conditional effects, based on effect modifiers. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["linear_estimate = model.estimate_effect(identified_estimand, \n", "                                        method_name=\"backdoor.linear_regression\",\n", "                                        control_value=(0,0),\n", "                                        treatment_value=(1,1))\n", "print(linear_estimate) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## More methods\n", "\n", "You can also use methods from EconML or CausalML libraries that support multiple treatments. You can look at examples from the conditional effect notebook: https://py-why.github.io/dowhy/example_notebooks/dowhy-conditional-treatment-effects.html\n", "\n", "Propensity-based methods do not support multiple treatments currently. \n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": true, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}