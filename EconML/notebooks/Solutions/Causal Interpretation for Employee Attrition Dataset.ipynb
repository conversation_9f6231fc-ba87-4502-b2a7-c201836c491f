{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Causal Interpretation for Employee Attrition Dataset\n", "\n", "This notebook uses the popular kaggle Employee Attrition dataset to showcase how we could interpret a blackbox model from both the correlation and causation perspective, leveraging the power of model interpretation tools like [SHAP](https://shap.readthedocs.io/en/latest/index.html) and [EconML](https://aka.ms/econml). We start with a fine-tuned ML model and learn the top important features to predict employee attrition, it will help us to better understand the correlations between features and target and which features are the strongest predictors. In addition, this notebook will take a step further and focus more on figuring out which features cause the employees leave the company, instead of just predicting how likely they are going to leave. This extra causal interpretation could better help company to make corresponding changes in order to minimize the attrition rate.  \n", "\n", "It includes the following sections:\n", "1. [Train a Fine-tuned ML Model](#Train-a-Fine-tuned-ML-Model)\n", "2. [Correlation Interpretation](#Correlation-Interpretation)\n", "    * Feature Importance -- Learn the top predictors for a given ML model\n", "3. [Causal Interpretation](#Causal-Interpretation)\n", "    * Direct Causal Effect -- Do the top predictors also have a direct effect on outcome of interest?\n", "    * Segmentation -- How to make individaulized plans to reduce the attrition?\n", "    * Cohort Analysis -- What is the causal effect on a new dataset?"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Some imports to get us started\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from lightgbm import LGBMClassifier\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "pd.set_option(\"display.max_columns\", 100)\n", "pd.set_option(\"display.max_rows\", 100)\n", "pd.set_option(\"display.max_colwidth\", 100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Train a Fine-tuned ML Model\n", "### Load the employee attrition data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Attrition</th>\n", "      <th>BusinessTravel</th>\n", "      <th>DailyRate</th>\n", "      <th>Department</th>\n", "      <th>DistanceFromHome</th>\n", "      <th>Education</th>\n", "      <th>EducationField</th>\n", "      <th>EmployeeCount</th>\n", "      <th>EmployeeNumber</th>\n", "      <th>EnvironmentSatisfaction</th>\n", "      <th>Gender</th>\n", "      <th>HourlyRate</th>\n", "      <th>JobInvolvement</th>\n", "      <th>JobLevel</th>\n", "      <th>JobRole</th>\n", "      <th>JobSatisfaction</th>\n", "      <th>MaritalStatus</th>\n", "      <th>MonthlyIncome</th>\n", "      <th>MonthlyRate</th>\n", "      <th>NumCompaniesWorked</th>\n", "      <th>Over18</th>\n", "      <th>OverTime</th>\n", "      <th>PercentSalaryHike</th>\n", "      <th>PerformanceRating</th>\n", "      <th>RelationshipSatisfaction</th>\n", "      <th>StandardHours</th>\n", "      <th>StockOptionLevel</th>\n", "      <th>TotalWorkingYears</th>\n", "      <th>TrainingTimesLastYear</th>\n", "      <th>WorkLifeBalance</th>\n", "      <th>YearsAtCompany</th>\n", "      <th>YearsInCurrentRole</th>\n", "      <th>YearsSinceLastPromotion</th>\n", "      <th>YearsWithCurrManager</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>41</td>\n", "      <td>Yes</td>\n", "      <td>Travel_Rarely</td>\n", "      <td>1102</td>\n", "      <td>Sales</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>Life Sciences</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>Female</td>\n", "      <td>94</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>Sales Executive</td>\n", "      <td>4</td>\n", "      <td>Single</td>\n", "      <td>5993</td>\n", "      <td>19479</td>\n", "      <td>8</td>\n", "      <td>Y</td>\n", "      <td>Yes</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49</td>\n", "      <td>No</td>\n", "      <td>Travel_Frequently</td>\n", "      <td>279</td>\n", "      <td>Research &amp; Development</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>Life Sciences</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>Male</td>\n", "      <td>61</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Research Scientist</td>\n", "      <td>2</td>\n", "      <td>Married</td>\n", "      <td>5130</td>\n", "      <td>24907</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "      <td>No</td>\n", "      <td>23</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>80</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>37</td>\n", "      <td>Yes</td>\n", "      <td>Travel_Rarely</td>\n", "      <td>1373</td>\n", "      <td>Research &amp; Development</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Other</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>Male</td>\n", "      <td>92</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>Laboratory Technician</td>\n", "      <td>3</td>\n", "      <td>Single</td>\n", "      <td>2090</td>\n", "      <td>2396</td>\n", "      <td>6</td>\n", "      <td>Y</td>\n", "      <td>Yes</td>\n", "      <td>15</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>No</td>\n", "      <td>Travel_Frequently</td>\n", "      <td>1392</td>\n", "      <td>Research &amp; Development</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>Life Sciences</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>Female</td>\n", "      <td>56</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Research Scientist</td>\n", "      <td>3</td>\n", "      <td>Married</td>\n", "      <td>2909</td>\n", "      <td>23159</td>\n", "      <td>1</td>\n", "      <td>Y</td>\n", "      <td>Yes</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>27</td>\n", "      <td>No</td>\n", "      <td>Travel_Rarely</td>\n", "      <td>591</td>\n", "      <td>Research &amp; Development</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>Male</td>\n", "      <td>40</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Laboratory Technician</td>\n", "      <td>2</td>\n", "      <td>Married</td>\n", "      <td>3468</td>\n", "      <td>16632</td>\n", "      <td>9</td>\n", "      <td>Y</td>\n", "      <td>No</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>80</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Age Attrition     BusinessTravel  DailyRate              Department  \\\n", "0   41       Yes      Travel_Rarely       1102                   Sales   \n", "1   49        No  Travel_Frequently        279  Research & Development   \n", "2   37       Yes      Travel_Rarely       1373  Research & Development   \n", "3   33        No  Travel_Frequently       1392  Research & Development   \n", "4   27        No      Travel_Rarely        591  Research & Development   \n", "\n", "   DistanceFromHome  Education EducationField  EmployeeCount  EmployeeNumber  \\\n", "0                 1          2  Life Sciences              1               1   \n", "1                 8          1  Life Sciences              1               2   \n", "2                 2          2          Other              1               4   \n", "3                 3          4  Life Sciences              1               5   \n", "4                 2          1        Medical              1               7   \n", "\n", "   EnvironmentSatisfaction  Gender  HourlyRate  JobInvolvement  JobLevel  \\\n", "0                        2  Female          94               3         2   \n", "1                        3    Male          61               2         2   \n", "2                        4    Male          92               2         1   \n", "3                        4  Female          56               3         1   \n", "4                        1    Male          40               3         1   \n", "\n", "                 JobRole  JobSatisfaction MaritalStatus  MonthlyIncome  \\\n", "0        Sales Executive                4        Single           5993   \n", "1     Research Scientist                2       Married           5130   \n", "2  Laboratory Technician                3        Single           2090   \n", "3     Research Scientist                3       Married           2909   \n", "4  Laboratory Technician                2       Married           3468   \n", "\n", "   MonthlyRate  NumCompaniesWorked Over18 OverTime  PercentSalaryHike  \\\n", "0        19479                   8      Y      Yes                 11   \n", "1        24907                   1      Y       No                 23   \n", "2         2396                   6      Y      Yes                 15   \n", "3        23159                   1      Y      Yes                 11   \n", "4        16632                   9      Y       No                 12   \n", "\n", "   PerformanceRating  RelationshipSatisfaction  StandardHours  \\\n", "0                  3                         1             80   \n", "1                  4                         4             80   \n", "2                  3                         2             80   \n", "3                  3                         3             80   \n", "4                  3                         4             80   \n", "\n", "   StockOptionLevel  TotalWorkingYears  TrainingTimesLastYear  \\\n", "0                 0                  8                      0   \n", "1                 1                 10                      3   \n", "2                 0                  7                      3   \n", "3                 0                  8                      3   \n", "4                 1                  6                      3   \n", "\n", "   WorkLifeBalance  YearsAtCompany  YearsInCurrentRole  \\\n", "0                1               6                   4   \n", "1                3              10                   7   \n", "2                3               0                   0   \n", "3                3               8                   7   \n", "4                3               2                   2   \n", "\n", "   YearsSinceLastPromotion  YearsWithCurrManager  \n", "0                        0                     5  \n", "1                        1                     7  \n", "2                        0                     0  \n", "3                        3                     0  \n", "4                        2                     2  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["file_url = \"https://msalicedatapublic.z5.web.core.windows.net/datasets/EmployeeAttrition/Employee-Attrition.csv\"\n", "attritionData = pd.read_csv(file_url)\n", "attritionData.head(5)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Dropping Employee count as all values are 1 and hence attrition is independent of this feature\n", "attritionData = attritionData.drop([\"EmployeeCount\"], axis=1)\n", "# Dropping Employee Number since it is merely an identifier\n", "attritionData = attritionData.drop([\"EmployeeNumber\"], axis=1)\n", "attritionData = attritionData.drop([\"Over18\"], axis=1)\n", "\n", "# Since all values are 80\n", "attritionData = attritionData.drop([\"StandardHours\"], axis=1)\n", "\n", "# change the unit of income related variables\n", "attritionData[[\"MonthlyIncome/1K\", \"MonthlyRate/1K\"]] = (\n", "    attritionData[[\"MonthlyIncome\", \"MonthlyRate\"]] / 1000\n", ")\n", "attritionData = attritionData.drop([\"MonthlyIncome\", \"MonthlyRate\"], axis=1)\n", "\n", "# Converting target variables from string to numerical values\n", "target_map = {\"Yes\": 1, \"No\": 0}\n", "attritionData[\"Attrition_numerical\"] = attritionData[\"Attrition\"].apply(\n", "    lambda x: target_map[x]\n", ")\n", "target = attritionData[\"Attrition_numerical\"]\n", "\n", "attritionXData = attritionData.drop([\"Attrition_numerical\", \"Attrition\"], axis=1)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Split data into train and test\n", "from sklearn.model_selection import train_test_split\n", "\n", "x_train, x_test, y_train, y_test = train_test_split(\n", "    attritionXData, target, test_size=0.2, random_state=0, stratify=target\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["categorical = []\n", "for col, value in attritionXData.items():\n", "    if value.dtype == \"object\":\n", "        categorical.append(col)\n", "\n", "# Store the numerical columns in a list numerical\n", "numerical = attritionXData.columns.difference(categorical)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Make training pipeline"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from sklearn.compose import ColumnTransformer\n", "\n", "# We create the preprocessing pipelines for both numeric and categorical data.\n", "numeric_transformer = Pipeline(\n", "    steps=[(\"imputer\", SimpleImputer(strategy=\"median\")), (\"scaler\", StandardScaler())]\n", ")\n", "\n", "categorical_transformer = Pipeline(\n", "    steps=[\n", "        (\"imputer\", SimpleImputer(strategy=\"constant\", fill_value=\"missing\")),\n", "        (\"onehot\", OneHotEncoder(handle_unknown=\"error\", drop=\"first\")),\n", "    ]\n", ")\n", "\n", "transformations = ColumnTransformer(\n", "    transformers=[\n", "        (\"num\", numeric_transformer, numerical),\n", "        (\"cat\", categorical_transformer, categorical),\n", "    ]\n", ")\n", "\n", "# Append classifier to preprocessing pipeline.\n", "# Now we have a full prediction pipeline.\n", "clf = Pipeline(\n", "    steps=[(\"preprocessor\", transformations), (\"classifier\", LGBMClassifier())]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Train a LightGBM classification model, which you want to explain"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import GridSearchCV\n", "\n", "param_grid = {\n", "    \"classifier__learning_rate\": [0.1, 0.05, 0.01],\n", "    \"classifier__max_depth\": [3, 5, 10],\n", "}\n", "search = GridSearchCV(clf, param_grid, n_jobs=-1)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'classifier__learning_rate': 0.1, 'classifier__max_depth': 3}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["search.fit(x_train, y_train)\n", "search.best_params_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Correlation Interpretation\n", "We explain this ML model by understanding the top important features to predict the employee attrition, internally using shap value."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# get the fitted model and transformer\n", "fitted_model = search.best_estimator_[\"classifier\"]\n", "fitted_transformer = search.best_estimator_[\"preprocessor\"]\n", "# get the feature name after featurization\n", "column_names = numerical.tolist()\n", "column_names += (\n", "    search.best_estimator_[\"preprocessor\"]\n", "    .transformers_[1][1]\n", "    .steps[1][1]\n", "    .get_feature_names_out(categorical)\n", "    .tolist()\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Passing 1176 background samples may lead to slow runtimes. Consider using shap.sample(data, 100) to create a smaller background data set.\n", " 95%|=================== | 280/294 [00:13<00:00]       "]}], "source": ["import shap\n", "\n", "# use interventional approach\n", "background = shap.maskers.Independent(\n", "    fitted_transformer.transform(x_train), max_samples=2000\n", ")\n", "explainer = shap.TreeExplainer(\n", "    fitted_model, data=background, feature_names=column_names\n", ")\n", "shap_values = explainer(fitted_transformer.transform(x_test))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 576x684 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot the feature importance\n", "shap.summary_plot(shap_values, fitted_transformer.transform(x_test))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the summary plot above, we could see the most important features sorted by their importance level. Taking the top 5 important features as an example, employees who often work overtime, have frequently changed jobs or live far away from their worksite are more likely to leave the company, and employees with higher income and stock options are less likely to leave. However, it doesn't mean these are the drivers that directly cause the employee to leave, it might have hidden variables that affect both the top features and outcome. For example, maybe the inefficient collaboration environment forces the employee to work overtime and also causes them to leave, instead of working overtime itself. In order to correctly find the direct reason and make improvements accordingly, we have to train a different model controlling on all the possible hidden variables (confounders) and learn the direct causal effect for a given feature. That's what the causal interpretation tool is doing. In the following session, we will explain the causal relationship for the top 5 important features."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Causal Interpretation\n", "### Direct Causal Effect --  Do the top predictors also have a direct effect on outcome of interest?"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["classification = True\n", "k = 5\n", "# get top feature names according to shap values\n", "vals = np.abs(shap_values.values).mean(0)\n", "feature_importance = pd.DataFrame(\n", "    list(zip(shap_values.feature_names, vals)), columns=[\"features\", \"importance\"]\n", ")\n", "feature_importance.sort_values(by=[\"importance\"], ascending=False, inplace=True)\n", "top_features = feature_importance.iloc[:k][\"features\"]\n", "# extract the raw feature name for top features\n", "top_features = [i.split(\"_\")[0] for i in top_features]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["<econml.solutions.causal_analysis._causal_analysis.CausalAnalysis at 0x2182cd9f788>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.solutions.causal_analysis import CausalAnalysis\n", "\n", "ca = CausalAnalysis(\n", "    top_features,\n", "    categorical,\n", "    heterogeneity_inds=None,\n", "    classification=True,\n", "    nuisance_models=\"automl\",\n", "    heterogeneity_model=\"forest\",\n", "    n_jobs=-1,\n", "    random_state=123,\n", ")\n", "ca.fit(x_train, y_train.values)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>point</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>p_value</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>feature</th>\n", "      <th>feature_value</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>OverTime</th>\n", "      <th>YesvNo</th>\n", "      <td>0.208962</td>\n", "      <td>0.023348</td>\n", "      <td>8.950043</td>\n", "      <td>3.553429e-19</td>\n", "      <td>0.163201</td>\n", "      <td>0.254722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>StockOptionLevel</th>\n", "      <th>num</th>\n", "      <td>-0.013464</td>\n", "      <td>0.019818</td>\n", "      <td>-0.679381</td>\n", "      <td>4.968964e-01</td>\n", "      <td>-0.052306</td>\n", "      <td>0.025378</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NumCompaniesWorked</th>\n", "      <th>num</th>\n", "      <td>0.024316</td>\n", "      <td>0.007775</td>\n", "      <td>3.127622</td>\n", "      <td>1.762264e-03</td>\n", "      <td>0.009078</td>\n", "      <td>0.039554</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MonthlyIncome/1K</th>\n", "      <th>num</th>\n", "      <td>-0.011661</td>\n", "      <td>0.011280</td>\n", "      <td>-1.033794</td>\n", "      <td>3.012323e-01</td>\n", "      <td>-0.033768</td>\n", "      <td>0.010447</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DistanceFromHome</th>\n", "      <th>num</th>\n", "      <td>0.004206</td>\n", "      <td>0.002414</td>\n", "      <td>1.742049</td>\n", "      <td>8.149992e-02</td>\n", "      <td>-0.000526</td>\n", "      <td>0.008937</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     point    stderr     zstat       p_value  \\\n", "feature            feature_value                                               \n", "OverTime           YesvNo         0.208962  0.023348  8.950043  3.553429e-19   \n", "StockOptionLevel   num           -0.013464  0.019818 -0.679381  4.968964e-01   \n", "NumCompaniesWorked num            0.024316  0.007775  3.127622  1.762264e-03   \n", "MonthlyIncome/1K   num           -0.011661  0.011280 -1.033794  3.012323e-01   \n", "DistanceFromHome   num            0.004206  0.002414  1.742049  8.149992e-02   \n", "\n", "                                  ci_lower  ci_upper  \n", "feature            feature_value                      \n", "OverTime           YesvNo         0.163201  0.254722  \n", "StockOptionLevel   num           -0.052306  0.025378  \n", "NumCompaniesWorked num            0.009078  0.039554  \n", "MonthlyIncome/1K   num           -0.033768  0.010447  \n", "DistanceFromHome   num           -0.000526  0.008937  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["global_summ = ca.global_causal_effect(alpha=0.05)\n", "global_summ"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# helper function to plot error bar\n", "def errorbar(res):\n", "    xticks = res.index.get_level_values(0)\n", "    lowererr = res[\"point\"] - res[\"ci_lower\"]\n", "    uppererr = res[\"ci_upper\"] - res[\"point\"]\n", "    xticks = [\n", "        \"{}***\".format(t)\n", "        if p < 1e-6\n", "        else (\"{}**\".format(t) if p < 1e-3 else (\"{}*\".format(t) if p < 1e-2 else t))\n", "        for t, p in zip(xticks, res[\"p_value\"])\n", "    ]\n", "    plot_title = \"Direct Causal Effect of Each Feature with 95% Confidence Interval, \"\n", "    plt.figure(figsize=(15, 5))\n", "    plt.errorbar(\n", "        np.arange(len(xticks)),\n", "        res[\"point\"],\n", "        yerr=[lowererr, uppererr],\n", "        fmt=\"o\",\n", "        capsize=5,\n", "        capthick=1,\n", "        barsabove=True,\n", "    )\n", "    plt.xticks(np.arange(len(xticks)), xticks, rotation=45)\n", "    plt.title(plot_title)\n", "    plt.axhline(0, color=\"r\", linestyle=\"--\", alpha=0.5)\n", "    plt.ylabel(\"Average Treatment Effect\")\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Matplotlib is currently using agg, which is a non-GUI backend, so cannot show the figure.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["errorbar(global_summ)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We learn the Average Treatment Effect(ATE) for each of the top 5 important features, assuming they are the treatment. From the summary table and the error bar plot above, we see the causal effect directions we learnt here are in line with the correlation directions we learnt above. However, features like `StockOptionLevel` or `MonthlyIncome/1K` although they are the strongest predictors on how likely employees will leave, we are less confident to say these are the drivers causing them leave. This is super valuable for the managers when they are trying to make plans to reduce the employee attrition rate, improving work life balance or providing extra support for employees who are living far away from the company might be more effective than raise their salary/stocks.          "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Segmentation -- How to make individaulized plans to reduce the attrition?\n", "From the analysis above, we learnt the direct treatment effect of each top features from an overall average level. However, people in different life stage or working experience might have different response to each of this potential reasons. Since the salary related features are not sigificant in an average level, we are interested to find the sub-groups who will respond positively to the income raise. If we could find a sub-group who have sigificant effect on income, we could further help the managers to refine their strategy and make individualized plans to different employees. "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(12, 8))\n", "ca.plot_heterogeneity_tree(\n", "    x_test, \"MonthlyIncome/1K\", max_depth=2, min_impurity_decrease=1e-7\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This shallow tree interpreter gives us clear guidance on what we should target. Employees with less than 1.5 years of total working experience have siginificant negative effects on monthly income, which means increasing income will definitely reduce the rate of attrition for them. On the other side, for employees who have already worked for this company for a long time, salary might not be an important driver if they decide to leave."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cohort Analysis -- What is the causal effect on a new dataset?\n", "The causal analysis class can also help us to learn the global and local causal effect of a new dataset given the model trained with training set. From the two tables below, you can see the global effect on the test set is similar to the training set, and the local effect gives you the heterogeneous treatment effect for each observation."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>point</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>p_value</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>feature</th>\n", "      <th>feature_value</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>OverTime</th>\n", "      <th>YesvNo</th>\n", "      <td>0.208087</td>\n", "      <td>0.046269</td>\n", "      <td>4.497307</td>\n", "      <td>0.000007</td>\n", "      <td>0.131981</td>\n", "      <td>0.284193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>StockOptionLevel</th>\n", "      <th>num</th>\n", "      <td>-0.013789</td>\n", "      <td>0.019792</td>\n", "      <td>-0.696716</td>\n", "      <td>0.485980</td>\n", "      <td>-0.046344</td>\n", "      <td>0.018765</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NumCompaniesWorked</th>\n", "      <th>num</th>\n", "      <td>0.023802</td>\n", "      <td>0.007499</td>\n", "      <td>3.174153</td>\n", "      <td>0.001503</td>\n", "      <td>0.011468</td>\n", "      <td>0.036137</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MonthlyIncome/1K</th>\n", "      <th>num</th>\n", "      <td>-0.012582</td>\n", "      <td>0.011563</td>\n", "      <td>-1.088137</td>\n", "      <td>0.276535</td>\n", "      <td>-0.031601</td>\n", "      <td>0.006437</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DistanceFromHome</th>\n", "      <th>num</th>\n", "      <td>0.004214</td>\n", "      <td>0.002323</td>\n", "      <td>1.814352</td>\n", "      <td>0.069623</td>\n", "      <td>0.000394</td>\n", "      <td>0.008035</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     point    stderr     zstat   p_value  \\\n", "feature            feature_value                                           \n", "OverTime           YesvNo         0.208087  0.046269  4.497307  0.000007   \n", "StockOptionLevel   num           -0.013789  0.019792 -0.696716  0.485980   \n", "NumCompaniesWorked num            0.023802  0.007499  3.174153  0.001503   \n", "MonthlyIncome/1K   num           -0.012582  0.011563 -1.088137  0.276535   \n", "DistanceFromHome   num            0.004214  0.002323  1.814352  0.069623   \n", "\n", "                                  ci_lower  ci_upper  \n", "feature            feature_value                      \n", "OverTime           YesvNo         0.131981  0.284193  \n", "StockOptionLevel   num           -0.046344  0.018765  \n", "NumCompaniesWorked num            0.011468  0.036137  \n", "MonthlyIncome/1K   num           -0.031601  0.006437  \n", "DistanceFromHome   num            0.000394  0.008035  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# global effect on new dataset\n", "ca.cohort_causal_effect(x_test)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>point</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>p_value</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>sample</th>\n", "      <th>feature</th>\n", "      <th>feature_value</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">0</th>\n", "      <th>OverTime</th>\n", "      <th>YesvNo</th>\n", "      <td>0.158891</td>\n", "      <td>0.047575</td>\n", "      <td>3.339816</td>\n", "      <td>0.000838</td>\n", "      <td>0.080638</td>\n", "      <td>0.237145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>StockOptionLevel</th>\n", "      <th>num</th>\n", "      <td>-0.017921</td>\n", "      <td>0.022392</td>\n", "      <td>-0.800333</td>\n", "      <td>0.423518</td>\n", "      <td>-0.054752</td>\n", "      <td>0.018910</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NumCompaniesWorked</th>\n", "      <th>num</th>\n", "      <td>0.024037</td>\n", "      <td>0.007214</td>\n", "      <td>3.332012</td>\n", "      <td>0.000862</td>\n", "      <td>0.012171</td>\n", "      <td>0.035903</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MonthlyIncome/1K</th>\n", "      <th>num</th>\n", "      <td>-0.030902</td>\n", "      <td>0.019577</td>\n", "      <td>-1.578481</td>\n", "      <td>0.114455</td>\n", "      <td>-0.063104</td>\n", "      <td>0.001299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DistanceFromHome</th>\n", "      <th>num</th>\n", "      <td>0.005203</td>\n", "      <td>0.002287</td>\n", "      <td>2.275026</td>\n", "      <td>0.022904</td>\n", "      <td>0.001441</td>\n", "      <td>0.008964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">293</th>\n", "      <th>OverTime</th>\n", "      <th>YesvNo</th>\n", "      <td>0.336686</td>\n", "      <td>0.071859</td>\n", "      <td>4.685370</td>\n", "      <td>0.000003</td>\n", "      <td>0.218488</td>\n", "      <td>0.454884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>StockOptionLevel</th>\n", "      <th>num</th>\n", "      <td>-0.041878</td>\n", "      <td>0.038472</td>\n", "      <td>-1.088514</td>\n", "      <td>0.276368</td>\n", "      <td>-0.105159</td>\n", "      <td>0.021404</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NumCompaniesWorked</th>\n", "      <th>num</th>\n", "      <td>0.027948</td>\n", "      <td>0.010851</td>\n", "      <td>2.575654</td>\n", "      <td>0.010005</td>\n", "      <td>0.010100</td>\n", "      <td>0.045796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MonthlyIncome/1K</th>\n", "      <th>num</th>\n", "      <td>-0.054883</td>\n", "      <td>0.029937</td>\n", "      <td>-1.833259</td>\n", "      <td>0.066764</td>\n", "      <td>-0.104126</td>\n", "      <td>-0.005640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DistanceFromHome</th>\n", "      <th>num</th>\n", "      <td>0.006410</td>\n", "      <td>0.004121</td>\n", "      <td>1.555528</td>\n", "      <td>0.119820</td>\n", "      <td>-0.000368</td>\n", "      <td>0.013188</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1470 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                            point    stderr     zstat  \\\n", "sample feature            feature_value                                 \n", "0      OverTime           YesvNo         0.158891  0.047575  3.339816   \n", "       StockOptionLevel   num           -0.017921  0.022392 -0.800333   \n", "       NumCompaniesWorked num            0.024037  0.007214  3.332012   \n", "       MonthlyIncome/1K   num           -0.030902  0.019577 -1.578481   \n", "       DistanceFromHome   num            0.005203  0.002287  2.275026   \n", "...                                           ...       ...       ...   \n", "293    OverTime           YesvNo         0.336686  0.071859  4.685370   \n", "       StockOptionLevel   num           -0.041878  0.038472 -1.088514   \n", "       NumCompaniesWorked num            0.027948  0.010851  2.575654   \n", "       MonthlyIncome/1K   num           -0.054883  0.029937 -1.833259   \n", "       DistanceFromHome   num            0.006410  0.004121  1.555528   \n", "\n", "                                          p_value  ci_lower  ci_upper  \n", "sample feature            feature_value                                \n", "0      OverTime           YesvNo         0.000838  0.080638  0.237145  \n", "       StockOptionLevel   num            0.423518 -0.054752  0.018910  \n", "       NumCompaniesWorked num            0.000862  0.012171  0.035903  \n", "       MonthlyIncome/1K   num            0.114455 -0.063104  0.001299  \n", "       DistanceFromHome   num            0.022904  0.001441  0.008964  \n", "...                                           ...       ...       ...  \n", "293    OverTime           YesvNo         0.000003  0.218488  0.454884  \n", "       StockOptionLevel   num            0.276368 -0.105159  0.021404  \n", "       NumCompaniesWorked num            0.010005  0.010100  0.045796  \n", "       MonthlyIncome/1K   num            0.066764 -0.104126 -0.005640  \n", "       DistanceFromHome   num            0.119820 -0.000368  0.013188  \n", "\n", "[1470 rows x 6 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# local effect on new dataset\n", "ca.local_causal_effect(x_test)"]}], "metadata": {"authors": [{"name": "mesameki"}], "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}