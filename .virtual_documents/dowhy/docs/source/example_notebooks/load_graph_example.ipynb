


import os, sys
import random
sys.path.append(os.path.abspath("../../../"))


import numpy as np
import pandas as pd

import dowhy
from dowhy import CausalModel
from IPython.display import Image, display





z=[i for i in range(10)]
random.shuffle(z)
df = pd.DataFrame(data = {'Z': z, 'X': range(0,10), 'Y': range(0,100,10)})
df





# With GML string
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="""graph[directed 1 node[id "Z" label "Z"]  
                    node[id "X" label "X"]
                    node[id "Y" label "Y"]      
                    edge[source "Z" target "X"]    
                    edge[source "Z" target "Y"]     
                    edge[source "X" target "Y"]]"""
                    
        )
model.view_model()


# With GML file
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="../example_graphs/simple_graph_example.gml"
        )
model.view_model()





# With DOT string
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="digraph {Z -> X;Z -> Y;X -> Y;}"
        )
model.view_model()



# With DOT file
model=CausalModel(
        data = df,
        treatment='X',
        outcome='Y',
        graph="../example_graphs/simple_graph_example.dot"
        )
model.view_model()



