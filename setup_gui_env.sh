#!/bin/bash
# GUI环境配置脚本 - 确保interactive_feedback工具正常工作

echo "🔧 配置GUI环境变量..."

# macOS显示设置
export DISPLAY=:0.0
echo "✅ DISPLAY设置为: $DISPLAY"

# Qt平台配置
export QT_QPA_PLATFORM=cocoa
export QT_MAC_WANTS_LAYER=1
export QT_AUTO_SCREEN_SCALE_FACTOR=1
echo "✅ Qt平台配置完成"

# 强制GUI模式
export GUI_ENABLED=1
export HEADLESS=false
export INTERACTIVE_MODE=true
export FORCE_GUI=true
echo "✅ 强制GUI模式启用"

# Python GUI配置
export PYTHONDONTWRITEBYTECODE=1
export MPLBACKEND=TkAgg
echo "✅ Python GUI后端配置"

# 终端配置
export TERM_PROGRAM=Terminal
export TERM_PROGRAM_VERSION=2.12.7
echo "✅ 终端环境配置"

# 会话类型
export XDG_SESSION_TYPE=x11
export SESSION_MANAGER=local
echo "✅ 会话类型配置"

echo "🎉 GUI环境配置完成！"
echo "现在可以测试 interactive_feedback 工具"

# 验证配置
echo ""
echo "📊 当前环境变量:"
echo "DISPLAY: $DISPLAY"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"
echo "GUI_ENABLED: $GUI_ENABLED"
echo "HEADLESS: $HEADLESS"
echo "INTERACTIVE_MODE: $INTERACTIVE_MODE" 