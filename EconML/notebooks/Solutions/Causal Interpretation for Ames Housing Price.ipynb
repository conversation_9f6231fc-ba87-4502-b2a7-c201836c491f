{"cells": [{"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Causal Interpretation for Ames Housing Price\n", "\n", "This notebook uses the Ames Housing dataset to showcase how we can interpret a blackbox model from both the **correlation and causation** perspective by leveraging the power of model interpretation tools like [SHAP](https://shap.readthedocs.io/en/latest/index.html) and [EconML](https://aka.ms/econml). This housing dataset collects house prices and characteristics for homes sold in Ames, Iowa from the period of 2006 to 2010. We start with a linear regression to build intuition. We then train a fine-tuned predictive ML model and use SHAP to better understand the correlations between features and target and which features are the strongest predictors. Finally, we train a separate causal model using EconML, which identifies features that have a **direct causal effect** on housing price, instead of just predicting the housing price given a set of characteristics.\n", "\n", "Note: A previous version of this notebook used the Boston Housing dataset. Due to ethical concerns with the Boston Housing dataset, we have decided to redo the analysis on the similar Ames Housing dataset. More details can be found on the sklearn page for the [Boston Housing dataset](https://scikit-learn.org/stable/modules/generated/sklearn.datasets.load_boston.html).\n", "\n", "This notebook includes the following sections:\n", "1. [A Gentle Start: Linear Regression](#A-Gentle-Start:-Linear-Regression)\n", "2. [Train a Fine-tuned Predictive ML Model](#Train-a-Fine-tuned-Predictive-ML-Model)\n", "3. [Correlation Interpretation](#Correlation-Interpretation)\n", "    * Feature Importance -- Learn the top predictors for a given ML model\n", "4. [Causal Interpretation](#Causal-Interpretation)\n", "    * Direct Causal Effect -- Do certain house characteristics have a direct effect on home value?\n", "    * Segmentation -- How do different type of houses respond differently to having a fireplace?\n", "    * Policy Analysis -- What is the best policy considering cost?\n", "    * What If Analysis -- How does the overall housing price change if every home had a fireplace?\n", "    * Cohort Analysis -- What is the causal effect on a new dataset?\n", "    "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Some imports to get us started\n", "from lightgbm import LGBMClassifier, LGBMRegressor\n", "from sklearn.model_selection import GridSearchCV\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import statsmodels.api as sm\n", "import shap\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# A Gentle Start: Linear Regression\n", "\n", "### Data Description\n", "\n", "The data contain features of Ames homes sold between 2006 and 2010 and was originally introduced by <PERSON>. [Ames, Iowa: Alternative to the Boston Housing Data as an End of Semester Regression Project](http://jse.amstat.org/v19n3/decock.pdf), Journal of Statistics Education Volume 19, Number 3(2011)\n", "\n", "Below is a sample of the features and their descriptions:\n", "\n", "Feature Name|Description\n", ":--- |:---\n", "**Neighborhood**|Physical locations within Ames city limits\n", "**BsmtFullBath**|Basement full bathrooms\n", "**Alley**|Type of alley access to property\n", "**BsmtFinType1**|Rating of basement finished area\n", "**Fence**|Fence quality\n", "**MiscFeature**|Miscellaneous feature not covered in other categories\n", "**RoofStyle**|Type of roof\n", "**GrLivArea**|Above grade (ground) living area square feet\n", "**SalePrice**|Sale price of home\n", "\n", "A more complete data dictionary can be found on the [OpenML page for the dataset](https://www.openml.org/d/42165)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We start with a linear regression to learn the conditional correlation between each predictor and the outcome variable. The coefficients  tell us how the housing price will change with one unit increase of each feature (holding all other features constant), and the p-value tells us the variable significance.\n", "\n", "Under certain strong assumptions, the coefficients from linear regression can lead us to a causal interpretation too. But as we will see later in this notebook, techniques like DoubleML can allow us to use the predictive power of machine learning to do causal inference while also minimizing assumptions about the functional form of our data (e.g. additive separability)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from sklearn.datasets import fetch_openml\n", "\n", "ames_housing = fetch_openml(name=\"house_prices\", as_frame = True)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1460, 80)\n"]}], "source": ["ames_df = ames_housing.data\n", "print(ames_df.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Data processing\n", "\n", "Minor feature engineering via feature creation/removal, and outlier removal"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>LandContour</th>\n", "      <th>Utilities</th>\n", "      <th>LotConfig</th>\n", "      <th>LandSlope</th>\n", "      <th>...</th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>SalePrice</th>\n", "      <th>AgeAtSale</th>\n", "      <th>YearsSinceRemodel</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>HasPorch</th>\n", "      <th>HasFireplace</th>\n", "      <th>HasFence</th>\n", "      <th>Intercept</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1.0</th>\n", "      <td>60.0</td>\n", "      <td>RL</td>\n", "      <td>65.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>208500.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.0</th>\n", "      <td>20.0</td>\n", "      <td>RL</td>\n", "      <td>80.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>181500.0</td>\n", "      <td>31.0</td>\n", "      <td>31.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3.0</th>\n", "      <td>60.0</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>223500.0</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4.0</th>\n", "      <td>70.0</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Corner</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>140000.0</td>\n", "      <td>91.0</td>\n", "      <td>36.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5.0</th>\n", "      <td>60.0</td>\n", "      <td>RL</td>\n", "      <td>84.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>IR1</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>FR2</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>250000.0</td>\n", "      <td>8.0</td>\n", "      <td>8.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1456.0</th>\n", "      <td>60.0</td>\n", "      <td>RL</td>\n", "      <td>62.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>175000.0</td>\n", "      <td>8.0</td>\n", "      <td>7.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1457.0</th>\n", "      <td>20.0</td>\n", "      <td>RL</td>\n", "      <td>85.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>210000.0</td>\n", "      <td>32.0</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1458.0</th>\n", "      <td>70.0</td>\n", "      <td>RL</td>\n", "      <td>66.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>266500.0</td>\n", "      <td>69.0</td>\n", "      <td>4.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1459.0</th>\n", "      <td>20.0</td>\n", "      <td>RL</td>\n", "      <td>68.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>142125.0</td>\n", "      <td>60.0</td>\n", "      <td>14.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1460.0</th>\n", "      <td>20.0</td>\n", "      <td>RL</td>\n", "      <td>75.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>Lvl</td>\n", "      <td>AllPub</td>\n", "      <td>Inside</td>\n", "      <td>Gtl</td>\n", "      <td>...</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>147500.0</td>\n", "      <td>43.0</td>\n", "      <td>43.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1451 rows × 65 columns</p>\n", "</div>"], "text/plain": ["        MSSubClass MSZoning  LotFrontage Street Alley LotShape LandContour  \\\n", "Id                                                                           \n", "1.0           60.0       RL         65.0   Pave    NA      Reg         Lvl   \n", "2.0           20.0       RL         80.0   Pave    NA      Reg         Lvl   \n", "3.0           60.0       RL         68.0   Pave    NA      IR1         Lvl   \n", "4.0           70.0       RL         60.0   Pave    NA      IR1         Lvl   \n", "5.0           60.0       RL         84.0   Pave    NA      IR1         Lvl   \n", "...            ...      ...          ...    ...   ...      ...         ...   \n", "1456.0        60.0       RL         62.0   Pave    NA      Reg         Lvl   \n", "1457.0        20.0       RL         85.0   Pave    NA      Reg         Lvl   \n", "1458.0        70.0       RL         66.0   Pave    NA      Reg         Lvl   \n", "1459.0        20.0       RL         68.0   Pave    NA      Reg         Lvl   \n", "1460.0        20.0       RL         75.0   Pave    NA      Reg         Lvl   \n", "\n", "       Utilities LotConfig LandSlope  ... SaleType SaleCondition SalePrice  \\\n", "Id                                    ...                                    \n", "1.0       AllPub    Inside       Gtl  ...       WD        Normal  208500.0   \n", "2.0       AllPub       FR2       Gtl  ...       WD        Normal  181500.0   \n", "3.0       AllPub    Inside       Gtl  ...       WD        Normal  223500.0   \n", "4.0       AllPub    Corner       Gtl  ...       WD       Abnorml  140000.0   \n", "5.0       AllPub       FR2       Gtl  ...       WD        Normal  250000.0   \n", "...          ...       ...       ...  ...      ...           ...       ...   \n", "1456.0    AllPub    Inside       Gtl  ...       WD        Normal  175000.0   \n", "1457.0    AllPub    Inside       Gtl  ...       WD        Normal  210000.0   \n", "1458.0    AllPub    Inside       Gtl  ...       WD        Normal  266500.0   \n", "1459.0    AllPub    Inside       Gtl  ...       WD        Normal  142125.0   \n", "1460.0    AllPub    Inside       Gtl  ...       WD        Normal  147500.0   \n", "\n", "       AgeAtSale YearsSinceRemodel  HasDeck  HasPorch HasFireplace HasFence  \\\n", "Id                                                                            \n", "1.0          5.0               5.0        0         1            0        0   \n", "2.0         31.0              31.0        1         0            1        0   \n", "3.0          7.0               6.0        0         1            1        0   \n", "4.0         91.0              36.0        0         1            1        0   \n", "5.0          8.0               8.0        1         1            1        0   \n", "...          ...               ...      ...       ...          ...      ...   \n", "1456.0       8.0               7.0        0         1            1        0   \n", "1457.0      32.0              22.0        1         0            1        1   \n", "1458.0      69.0               4.0        0         1            1        1   \n", "1459.0      60.0              14.0        1         1            0        0   \n", "1460.0      43.0              43.0        1         1            0        0   \n", "\n", "       Intercept  \n", "Id                \n", "1.0            1  \n", "2.0            1  \n", "3.0            1  \n", "4.0            1  \n", "5.0            1  \n", "...          ...  \n", "1456.0         1  \n", "1457.0         1  \n", "1458.0         1  \n", "1459.0         1  \n", "1460.0         1  \n", "\n", "[1451 rows x 65 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Xy = (\n", "    ames_df\n", "    .assign(SalePrice = ames_housing.target) # add target feature\n", "    .set_index('Id')\n", "    .loc[\n", "        lambda df: df['MasVnrType'].notna() # drop outliers with missing detail in this column\n", "    ]\n", "    .loc[\n", "        lambda df: df['Electrical'].notna() # drop outlier with missing electrical row\n", "    ]\n", "    .assign(\n", "        AgeAtSale = lambda df: df['YrSold'].sub(df['YearBuilt']), # add interpretable year columns\n", "        YearsSinceRemodel = lambda df: df['YrSold'].sub(df['YearRemodAdd']).clip(lower = 0), # clip lower for outlier\n", "        \n", "        HasDeck = lambda df: df['WoodDeckSF'].gt(0).map(int),\n", "        HasPorch = lambda df: \n", "        df[['OpenPorchSF', 'EnclosedPorch', '3SsnPorch', 'ScreenPorch']]\n", "        .gt(0)\n", "        .max(axis = 1)\n", "        .map(int),\n", "        \n", "        HasFireplace = lambda df: df['Fireplaces'].clip(upper = 1).map(int),\n", "        HasFence = lambda df: df['<PERSON>ce'].notna().map(int)\n", "    )\n", "    \n", "    # drop year columns\n", "    .drop(\n", "        columns = [\n", "            'GarageYrBlt', 'YearBuilt', 'YrSold', 'YearRemodAdd', \n", "            'WoodDeckSF', 'OpenPorchSF', 'EnclosedPorch', '3SsnPorch', 'ScreenPorch',\n", "            'FireplaceQu', 'Fireplaces',\n", "            'LotArea', 'MasVnrArea', 'BsmtFinSF1', 'BsmtFinSF2', 'BsmtUnfSF', 'TotalBsmtSF', \n", "            '1stFlrSF', '2ndFlrSF', 'LowQualFinSF', 'GarageArea', 'PoolArea'\n", "        ]\n", "    ) \n", "    .assign(LotFrontage = lambda df: df['LotFrontage'].fillna(0)) # fill missing with 0\n", "    .fillna('NA') # rest of missing values are in categorical columns, so fill with NA category\n", "    .assign(Intercept = 1) # add constant column for OLS\n", ")\n", "\n", "Xy"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# identify categorical columns for one hot encoding\n", "categorical = list(\n", "    Xy\n", "    .apply(lambda series: series.dtype)\n", "    .loc[\n", "        lambda df: df.eq('object')\n", "    ]\n", "    .index\n", ") + ['MSSubClass']\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["X = Xy.drop(columns = 'SalePrice')\n", "X_ohe = (\n", "    X\n", "    .pipe(pd.get_dummies, prefix_sep = '_OHE_', columns = categorical, dtype='uint8')\n", ")\n", "y = Xy['SalePrice']"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>        <td>SalePrice</td>    <th>  R-squared:         </th> <td>   0.923</td> \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th> <td>   0.908</td> \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th> <td>   59.41</td> \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td>Fri, 22 Apr 2022</td> <th>  Prob (F-statistic):</th>  <td>  0.00</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>11:20:06</td>     <th>  Log-Likelihood:    </th> <td> -16565.</td> \n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td>  1451</td>      <th>  AIC:               </th> <td>3.362e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td>  1206</td>      <th>  BIC:               </th> <td>3.491e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>   244</td>      <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "              <td></td>                 <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>LotFrontage</th>               <td>    9.1690</td> <td>   24.385</td> <td>    0.376</td> <td> 0.707</td> <td>  -38.674</td> <td>   57.012</td>\n", "</tr>\n", "<tr>\n", "  <th>OverallQual</th>               <td> 6443.4507</td> <td> 1098.337</td> <td>    5.867</td> <td> 0.000</td> <td> 4288.587</td> <td> 8598.315</td>\n", "</tr>\n", "<tr>\n", "  <th>OverallCond</th>               <td> 5761.3467</td> <td>  921.305</td> <td>    6.253</td> <td> 0.000</td> <td> 3953.808</td> <td> 7568.885</td>\n", "</tr>\n", "<tr>\n", "  <th>GrLivArea</th>                 <td>   71.2724</td> <td>    3.926</td> <td>   18.155</td> <td> 0.000</td> <td>   63.570</td> <td>   78.975</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFullBath</th>              <td> 7808.0939</td> <td> 1862.581</td> <td>    4.192</td> <td> 0.000</td> <td> 4153.836</td> <td> 1.15e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtHalfBath</th>              <td> 2704.3964</td> <td> 3161.287</td> <td>    0.855</td> <td> 0.392</td> <td>-3497.837</td> <td> 8906.630</td>\n", "</tr>\n", "<tr>\n", "  <th>FullBath</th>                  <td> 4353.2284</td> <td> 2358.076</td> <td>    1.846</td> <td> 0.065</td> <td> -273.159</td> <td> 8979.616</td>\n", "</tr>\n", "<tr>\n", "  <th>HalfBath</th>                  <td> 1125.2313</td> <td> 2213.287</td> <td>    0.508</td> <td> 0.611</td> <td>-3217.089</td> <td> 5467.552</td>\n", "</tr>\n", "<tr>\n", "  <th>BedroomAbvGr</th>              <td>-4678.0376</td> <td> 1473.399</td> <td>   -3.175</td> <td> 0.002</td> <td>-7568.747</td> <td>-1787.328</td>\n", "</tr>\n", "<tr>\n", "  <th>KitchenAbvGr</th>              <td>-1.089e+04</td> <td> 6648.976</td> <td>   -1.639</td> <td> 0.102</td> <td>-2.39e+04</td> <td> 2150.473</td>\n", "</tr>\n", "<tr>\n", "  <th>TotRmsAbvGrd</th>              <td> 1013.7051</td> <td> 1016.102</td> <td>    0.998</td> <td> 0.319</td> <td> -979.818</td> <td> 3007.228</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCars</th>                <td> 8663.3405</td> <td> 1708.010</td> <td>    5.072</td> <td> 0.000</td> <td> 5312.340</td> <td>  1.2e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Mis<PERSON><PERSON><PERSON></th>                   <td>    9.0392</td> <td>    6.684</td> <td>    1.352</td> <td> 0.177</td> <td>   -4.074</td> <td>   22.153</td>\n", "</tr>\n", "<tr>\n", "  <th><PERSON>Sold</th>                    <td> -510.0946</td> <td>  260.244</td> <td>   -1.960</td> <td> 0.050</td> <td>-1020.675</td> <td>    0.486</td>\n", "</tr>\n", "<tr>\n", "  <th>AgeAtSale</th>                 <td> -305.4555</td> <td>   85.143</td> <td>   -3.588</td> <td> 0.000</td> <td> -472.501</td> <td> -138.410</td>\n", "</tr>\n", "<tr>\n", "  <th>YearsSinceRemodel</th>         <td>  -27.7465</td> <td>   58.392</td> <td>   -0.475</td> <td> 0.635</td> <td> -142.308</td> <td>   86.815</td>\n", "</tr>\n", "<tr>\n", "  <th>HasDeck</th>                   <td> 1621.8650</td> <td> 1585.671</td> <td>    1.023</td> <td> 0.307</td> <td>-1489.115</td> <td> 4732.845</td>\n", "</tr>\n", "<tr>\n", "  <th>Has<PERSON><PERSON><PERSON></th>                  <td> 1870.3246</td> <td> 1739.962</td> <td>    1.075</td> <td> 0.283</td> <td>-1543.365</td> <td> 5284.014</td>\n", "</tr>\n", "<tr>\n", "  <th>HasFireplace</th>              <td> 2375.0085</td> <td> 1818.079</td> <td>    1.306</td> <td> 0.192</td> <td>-1191.941</td> <td> 5941.958</td>\n", "</tr>\n", "<tr>\n", "  <th>HasFence</th>                  <td>-5877.2263</td> <td> 2031.768</td> <td>   -2.893</td> <td> 0.004</td> <td>-9863.419</td> <td>-1891.033</td>\n", "</tr>\n", "<tr>\n", "  <th>Intercept</th>                 <td>-8887.1036</td> <td> 3683.133</td> <td>   -2.413</td> <td> 0.016</td> <td>-1.61e+04</td> <td>-1661.044</td>\n", "</tr>\n", "<tr>\n", "  <th>MSZoning_OHE_C (all)</th>      <td>-2.978e+04</td> <td> 8731.959</td> <td>   -3.411</td> <td> 0.001</td> <td>-4.69e+04</td> <td>-1.27e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSZoning_OHE_FV</th>           <td> 1.271e+04</td> <td> 6497.547</td> <td>    1.957</td> <td> 0.051</td> <td>  -33.757</td> <td> 2.55e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSZoning_OHE_RH</th>           <td> 4234.7895</td> <td> 6421.424</td> <td>    0.659</td> <td> 0.510</td> <td>-8363.614</td> <td> 1.68e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSZoning_OHE_RL</th>           <td> 4630.5784</td> <td> 3487.791</td> <td>    1.328</td> <td> 0.185</td> <td>-2212.233</td> <td> 1.15e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSZoning_OHE_RM</th>           <td> -682.4301</td> <td> 3937.317</td> <td>   -0.173</td> <td> 0.862</td> <td>-8407.183</td> <td> 7042.323</td>\n", "</tr>\n", "<tr>\n", "  <th>Street_OHE_Grvl</th>           <td>-8776.6589</td> <td> 6992.025</td> <td>   -1.255</td> <td> 0.210</td> <td>-2.25e+04</td> <td> 4941.225</td>\n", "</tr>\n", "<tr>\n", "  <th>Street_OHE_Pave</th>           <td> -110.4448</td> <td> 6575.318</td> <td>   -0.017</td> <td> 0.987</td> <td> -1.3e+04</td> <td> 1.28e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Alley_OHE_Grvl</th>            <td>-1810.1857</td> <td> 3604.374</td> <td>   -0.502</td> <td> 0.616</td> <td>-8881.726</td> <td> 5261.355</td>\n", "</tr>\n", "<tr>\n", "  <th>Alley_OHE_NA</th>              <td>-4693.0936</td> <td> 2616.409</td> <td>   -1.794</td> <td> 0.073</td> <td>-9826.313</td> <td>  440.125</td>\n", "</tr>\n", "<tr>\n", "  <th>Alley_OHE_Pave</th>            <td>-2383.8243</td> <td> 3913.934</td> <td>   -0.609</td> <td> 0.543</td> <td>-1.01e+04</td> <td> 5295.053</td>\n", "</tr>\n", "<tr>\n", "  <th>LotShape_OHE_IR1</th>          <td>-1.047e+04</td> <td> 2822.356</td> <td>   -3.711</td> <td> 0.000</td> <td> -1.6e+04</td> <td>-4935.205</td>\n", "</tr>\n", "<tr>\n", "  <th>LotShape_OHE_IR2</th>          <td>-2372.8849</td> <td> 3985.625</td> <td>   -0.595</td> <td> 0.552</td> <td>-1.02e+04</td> <td> 5446.644</td>\n", "</tr>\n", "<tr>\n", "  <th>LotShape_OHE_IR3</th>          <td> 1.303e+04</td> <td> 6882.455</td> <td>    1.894</td> <td> 0.059</td> <td> -469.573</td> <td> 2.65e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>LotShape_OHE_Reg</th>          <td>-9075.0815</td> <td> 2919.659</td> <td>   -3.108</td> <td> 0.002</td> <td>-1.48e+04</td> <td>-3346.907</td>\n", "</tr>\n", "<tr>\n", "  <th>LandContour_OHE_Bnk</th>       <td>-8623.9473</td> <td> 3406.031</td> <td>   -2.532</td> <td> 0.011</td> <td>-1.53e+04</td> <td>-1941.544</td>\n", "</tr>\n", "<tr>\n", "  <th>LandContour_OHE_HLS</th>       <td> 2046.0477</td> <td> 3508.549</td> <td>    0.583</td> <td> 0.560</td> <td>-4837.490</td> <td> 8929.585</td>\n", "</tr>\n", "<tr>\n", "  <th>LandContour_OHE_Low</th>       <td>-2995.0390</td> <td> 4504.323</td> <td>   -0.665</td> <td> 0.506</td> <td>-1.18e+04</td> <td> 5842.141</td>\n", "</tr>\n", "<tr>\n", "  <th>LandContour_OHE_Lvl</th>       <td>  685.8349</td> <td> 2559.439</td> <td>    0.268</td> <td> 0.789</td> <td>-4335.612</td> <td> 5707.282</td>\n", "</tr>\n", "<tr>\n", "  <th>Utilities_OHE_AllPub</th>      <td> 9427.5382</td> <td> 1.43e+04</td> <td>    0.658</td> <td> 0.511</td> <td>-1.87e+04</td> <td> 3.75e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Utilities_OHE_NoSeWa</th>      <td>-1.831e+04</td> <td> 1.58e+04</td> <td>   -1.161</td> <td> 0.246</td> <td>-4.93e+04</td> <td> 1.26e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>LotConfig_OHE_Corner</th>      <td> 1416.0814</td> <td> 3192.892</td> <td>    0.444</td> <td> 0.657</td> <td>-4848.159</td> <td> 7680.322</td>\n", "</tr>\n", "<tr>\n", "  <th>LotConfig_OHE_CulDSac</th>     <td> 1.221e+04</td> <td> 3776.342</td> <td>    3.234</td> <td> 0.001</td> <td> 4803.547</td> <td> 1.96e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>LotConfig_OHE_FR2</th>         <td>-5854.6021</td> <td> 4099.815</td> <td>   -1.428</td> <td> 0.154</td> <td>-1.39e+04</td> <td> 2188.960</td>\n", "</tr>\n", "<tr>\n", "  <th>LotConfig_OHE_FR3</th>         <td>-1.593e+04</td> <td> 1.07e+04</td> <td>   -1.487</td> <td> 0.137</td> <td>-3.69e+04</td> <td> 5082.020</td>\n", "</tr>\n", "<tr>\n", "  <th>LotConfig_OHE_Inside</th>      <td> -728.1482</td> <td> 3025.508</td> <td>   -0.241</td> <td> 0.810</td> <td>-6663.992</td> <td> 5207.696</td>\n", "</tr>\n", "<tr>\n", "  <th>LandSlope_OHE_Gtl</th>         <td>-4550.2129</td> <td> 4120.952</td> <td>   -1.104</td> <td> 0.270</td> <td>-1.26e+04</td> <td> 3534.819</td>\n", "</tr>\n", "<tr>\n", "  <th>LandSlope_OHE_Mod</th>         <td> 1596.2168</td> <td> 4053.999</td> <td>    0.394</td> <td> 0.694</td> <td>-6357.457</td> <td> 9549.891</td>\n", "</tr>\n", "<tr>\n", "  <th>LandSlope_OHE_Sev</th>         <td>-5933.1075</td> <td> 6686.760</td> <td>   -0.887</td> <td> 0.375</td> <td>-1.91e+04</td> <td> 7185.867</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Blmngtn</th>  <td>-8611.0187</td> <td> 7466.688</td> <td>   -1.153</td> <td> 0.249</td> <td>-2.33e+04</td> <td> 6038.122</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Blueste</th>  <td> 8758.5694</td> <td> 1.88e+04</td> <td>    0.465</td> <td> 0.642</td> <td>-2.82e+04</td> <td> 4.57e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_BrDale</th>   <td> 7873.6626</td> <td> 8884.523</td> <td>    0.886</td> <td> 0.376</td> <td>-9557.175</td> <td> 2.53e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_BrkSide</th>  <td>-2860.6985</td> <td> 5242.223</td> <td>   -0.546</td> <td> 0.585</td> <td>-1.31e+04</td> <td> 7424.192</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_ClearCr</th>  <td>-7321.5088</td> <td> 5986.224</td> <td>   -1.223</td> <td> 0.222</td> <td>-1.91e+04</td> <td> 4423.061</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_CollgCr</th>  <td>-7448.2921</td> <td> 3217.816</td> <td>   -2.315</td> <td> 0.021</td> <td>-1.38e+04</td> <td>-1135.153</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Crawfor</th>  <td> 1.347e+04</td> <td> 4666.804</td> <td>    2.885</td> <td> 0.004</td> <td> 4310.036</td> <td> 2.26e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighbor<PERSON>_<PERSON>E_Edwards</th>  <td>-1.837e+04</td> <td> 3377.030</td> <td>   -5.441</td> <td> 0.000</td> <td> -2.5e+04</td> <td>-1.17e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Gilbert</th>  <td>-1.216e+04</td> <td> 4007.323</td> <td>   -3.036</td> <td> 0.002</td> <td>   -2e+04</td> <td>-4302.317</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_IDOTRR</th>   <td>-8124.2762</td> <td> 7043.131</td> <td>   -1.154</td> <td> 0.249</td> <td>-2.19e+04</td> <td> 5693.874</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_MeadowV</th>  <td>-4486.2237</td> <td> 9507.217</td> <td>   -0.472</td> <td> 0.637</td> <td>-2.31e+04</td> <td> 1.42e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Mitchel</th>  <td>-1.792e+04</td> <td> 4222.368</td> <td>   -4.245</td> <td> 0.000</td> <td>-2.62e+04</td> <td>-9640.032</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_NAmes</th>    <td>-1.482e+04</td> <td> 2980.842</td> <td>   -4.970</td> <td> 0.000</td> <td>-2.07e+04</td> <td>-8967.059</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_NPkVill</th>  <td> 1.457e+04</td> <td>  1.3e+04</td> <td>    1.117</td> <td> 0.264</td> <td> -1.1e+04</td> <td> 4.01e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_NWAmes</th>   <td>-1.563e+04</td> <td> 3810.048</td> <td>   -4.103</td> <td> 0.000</td> <td>-2.31e+04</td> <td>-8155.691</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_NoRidge</th>  <td> 3.405e+04</td> <td> 5108.666</td> <td>    6.664</td> <td> 0.000</td> <td>  2.4e+04</td> <td> 4.41e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_NridgHt</th>  <td> 2.408e+04</td> <td> 4495.108</td> <td>    5.357</td> <td> 0.000</td> <td> 1.53e+04</td> <td> 3.29e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_OldTown</th>  <td>-1.378e+04</td> <td> 5188.658</td> <td>   -2.656</td> <td> 0.008</td> <td> -2.4e+04</td> <td>-3600.088</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_SWISU</th>    <td>-1.101e+04</td> <td> 6473.184</td> <td>   -1.700</td> <td> 0.089</td> <td>-2.37e+04</td> <td> 1694.043</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Sawyer</th>   <td>-9096.5155</td> <td> 3707.536</td> <td>   -2.454</td> <td> 0.014</td> <td>-1.64e+04</td> <td>-1822.579</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_SawyerW</th>  <td>-2713.1583</td> <td> 4030.968</td> <td>   -0.673</td> <td> 0.501</td> <td>-1.06e+04</td> <td> 5195.331</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Somerst</th>  <td>  473.0768</td> <td> 6378.177</td> <td>    0.074</td> <td> 0.941</td> <td> -1.2e+04</td> <td>  1.3e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_StoneBr</th>  <td> 4.238e+04</td> <td> 5929.178</td> <td>    7.148</td> <td> 0.000</td> <td> 3.08e+04</td> <td>  5.4e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Timber</th>   <td>-4534.9132</td> <td> 4950.992</td> <td>   -0.916</td> <td> 0.360</td> <td>-1.42e+04</td> <td> 5178.602</td>\n", "</tr>\n", "<tr>\n", "  <th>Neighborhood_OHE_Veenker</th>  <td> 4353.6079</td> <td> 8133.294</td> <td>    0.535</td> <td> 0.593</td> <td>-1.16e+04</td> <td> 2.03e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_Artery</th>     <td>-7801.2753</td> <td> 5084.697</td> <td>   -1.534</td> <td> 0.125</td> <td>-1.78e+04</td> <td> 2174.560</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_Feedr</th>      <td> -429.5102</td> <td> 4172.041</td> <td>   -0.103</td> <td> 0.918</td> <td>-8614.775</td> <td> 7755.754</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_Norm</th>       <td> 9584.1240</td> <td> 3175.648</td> <td>    3.018</td> <td> 0.003</td> <td> 3353.715</td> <td> 1.58e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_PosA</th>       <td>-1570.5646</td> <td> 8957.572</td> <td>   -0.175</td> <td> 0.861</td> <td>-1.91e+04</td> <td>  1.6e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_PosN</th>       <td> 2006.2559</td> <td> 6446.011</td> <td>    0.311</td> <td> 0.756</td> <td>-1.06e+04</td> <td> 1.47e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_RRAe</th>       <td>-1.461e+04</td> <td> 8110.361</td> <td>   -1.801</td> <td> 0.072</td> <td>-3.05e+04</td> <td> 1302.710</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_RRAn</th>       <td> 5895.9682</td> <td> 5781.315</td> <td>    1.020</td> <td> 0.308</td> <td>-5446.584</td> <td> 1.72e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_RRNe</th>       <td>-6683.7253</td> <td> 1.61e+04</td> <td>   -0.416</td> <td> 0.678</td> <td>-3.82e+04</td> <td> 2.49e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition1_OHE_RRNn</th>       <td> 4720.8981</td> <td> 1.17e+04</td> <td>    0.403</td> <td> 0.687</td> <td>-1.83e+04</td> <td> 2.77e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_Artery</th>     <td> 2.001e+04</td> <td> 2.45e+04</td> <td>    0.816</td> <td> 0.415</td> <td>-2.81e+04</td> <td> 6.81e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_Feedr</th>      <td> 1.612e+04</td> <td>  1.7e+04</td> <td>    0.949</td> <td> 0.343</td> <td>-1.72e+04</td> <td> 4.95e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_Norm</th>       <td> 1.434e+04</td> <td> 1.17e+04</td> <td>    1.221</td> <td> 0.222</td> <td>-8698.454</td> <td> 3.74e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_PosA</th>       <td> 7.161e+04</td> <td> 3.15e+04</td> <td>    2.271</td> <td> 0.023</td> <td> 9740.065</td> <td> 1.33e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_PosN</th>       <td>-1.802e+05</td> <td> 2.05e+04</td> <td>   -8.777</td> <td> 0.000</td> <td> -2.2e+05</td> <td> -1.4e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_RRAe</th>       <td> 1.586e+04</td> <td> 6.38e+04</td> <td>    0.248</td> <td> 0.804</td> <td>-1.09e+05</td> <td> 1.41e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_RRAn</th>       <td> 7256.3041</td> <td> 2.53e+04</td> <td>    0.287</td> <td> 0.774</td> <td>-4.23e+04</td> <td> 5.69e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Condition2_OHE_RRNn</th>       <td> 2.611e+04</td> <td> 2.01e+04</td> <td>    1.300</td> <td> 0.194</td> <td>-1.33e+04</td> <td> 6.55e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BldgType_OHE_1Fam</th>         <td> -716.5811</td> <td> 9984.921</td> <td>   -0.072</td> <td> 0.943</td> <td>-2.03e+04</td> <td> 1.89e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BldgType_OHE_2fmCon</th>       <td> 8059.3471</td> <td> 2.37e+04</td> <td>    0.341</td> <td> 0.733</td> <td>-3.84e+04</td> <td> 5.45e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BldgType_OHE_Duplex</th>       <td>-3857.5649</td> <td> 4671.286</td> <td>   -0.826</td> <td> 0.409</td> <td> -1.3e+04</td> <td> 5307.186</td>\n", "</tr>\n", "<tr>\n", "  <th>BldgType_OHE_Twnhs</th>        <td>-8883.8366</td> <td> 1.14e+04</td> <td>   -0.778</td> <td> 0.437</td> <td>-3.13e+04</td> <td> 1.35e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BldgType_OHE_TwnhsE</th>       <td>-3488.4680</td> <td> 1.07e+04</td> <td>   -0.326</td> <td> 0.744</td> <td>-2.45e+04</td> <td> 1.75e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_1.5Fin</th>     <td>-4604.0771</td> <td> 8092.784</td> <td>   -0.569</td> <td> 0.570</td> <td>-2.05e+04</td> <td> 1.13e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_1.5Unf</th>     <td> 2.016e+04</td> <td> 2.27e+04</td> <td>    0.889</td> <td> 0.374</td> <td>-2.43e+04</td> <td> 6.46e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_1Story</th>     <td> 8291.4055</td> <td> 6853.861</td> <td>    1.210</td> <td> 0.227</td> <td>-5155.411</td> <td> 2.17e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_2.5Fin</th>     <td>-2.954e+04</td> <td> 1.44e+04</td> <td>   -2.052</td> <td> 0.040</td> <td>-5.78e+04</td> <td>-1299.420</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_2.5Unf</th>     <td> 9664.9619</td> <td> 1.39e+04</td> <td>    0.694</td> <td> 0.488</td> <td>-1.77e+04</td> <td>  3.7e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_2Story</th>     <td>-1.393e+04</td> <td> 6785.508</td> <td>   -2.053</td> <td> 0.040</td> <td>-2.72e+04</td> <td> -619.319</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_<PERSON>er</th>     <td> 4838.1623</td> <td> 9922.688</td> <td>    0.488</td> <td> 0.626</td> <td>-1.46e+04</td> <td> 2.43e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HouseStyle_OHE_SLvl</th>       <td>-3756.6333</td> <td> 1.19e+04</td> <td>   -0.316</td> <td> 0.752</td> <td>-2.71e+04</td> <td> 1.95e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofStyle_OHE_Flat</th>        <td>-1.689e+04</td> <td> 1.73e+04</td> <td>   -0.975</td> <td> 0.330</td> <td>-5.09e+04</td> <td> 1.71e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofStyle_OHE_Gable</th>       <td>-1.148e+04</td> <td> 7732.231</td> <td>   -1.485</td> <td> 0.138</td> <td>-2.66e+04</td> <td> 3690.317</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofStyle_OHE_Gambrel</th>     <td>-7415.3888</td> <td> 1.04e+04</td> <td>   -0.713</td> <td> 0.476</td> <td>-2.78e+04</td> <td>  1.3e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofStyle_OHE_Hip</th>         <td>-9351.0472</td> <td> 7843.684</td> <td>   -1.192</td> <td> 0.233</td> <td>-2.47e+04</td> <td> 6037.736</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofStyle_OHE_Mansard</th>     <td> -481.5264</td> <td> 1.12e+04</td> <td>   -0.043</td> <td> 0.966</td> <td>-2.24e+04</td> <td> 2.14e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofStyle_OHE_Shed</th>        <td> 3.673e+04</td> <td>    3e+04</td> <td>    1.223</td> <td> 0.221</td> <td>-2.22e+04</td> <td> 9.56e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_ClyTile</th>      <td>-4.909e+05</td> <td> 3.37e+04</td> <td>  -14.583</td> <td> 0.000</td> <td>-5.57e+05</td> <td>-4.25e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_CompShg</th>      <td> 5.286e+04</td> <td> 1.07e+04</td> <td>    4.958</td> <td> 0.000</td> <td> 3.19e+04</td> <td> 7.38e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_Membran</th>      <td> 1.018e+05</td> <td> 2.88e+04</td> <td>    3.540</td> <td> 0.000</td> <td> 4.54e+04</td> <td> 1.58e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_Metal</th>        <td> 6.736e+04</td> <td> 2.72e+04</td> <td>    2.474</td> <td> 0.014</td> <td> 1.39e+04</td> <td> 1.21e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_Roll</th>         <td> 5.087e+04</td> <td> 2.61e+04</td> <td>    1.949</td> <td> 0.051</td> <td> -327.761</td> <td> 1.02e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_Tar&Grv</th>      <td> 4.625e+04</td> <td>  1.5e+04</td> <td>    3.084</td> <td> 0.002</td> <td> 1.68e+04</td> <td> 7.57e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_WdShake</th>      <td>  4.21e+04</td> <td> 1.75e+04</td> <td>    2.400</td> <td> 0.017</td> <td> 7677.711</td> <td> 7.65e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>RoofMatl_OHE_WdShngl</th>      <td> 1.208e+05</td> <td> 1.44e+04</td> <td>    8.413</td> <td> 0.000</td> <td> 9.26e+04</td> <td> 1.49e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_AsbShng</th>   <td> 9696.9557</td> <td> 1.27e+04</td> <td>    0.761</td> <td> 0.447</td> <td>-1.53e+04</td> <td> 3.47e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_AsphShn</th>   <td>-2.415e+04</td> <td> 3.09e+04</td> <td>   -0.781</td> <td> 0.435</td> <td>-8.48e+04</td> <td> 3.65e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_BrkComm</th>   <td> 1.531e+04</td> <td> 2.56e+04</td> <td>    0.598</td> <td> 0.550</td> <td>-3.49e+04</td> <td> 6.56e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_BrkFace</th>   <td> 1.711e+04</td> <td> 6574.131</td> <td>    2.602</td> <td> 0.009</td> <td> 4208.008</td> <td>    3e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_CBlock</th>    <td>-1796.2630</td> <td> 1.35e+04</td> <td>   -0.133</td> <td> 0.894</td> <td>-2.83e+04</td> <td> 2.48e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_CemntBd</th>   <td> 2.217e+04</td> <td> 1.51e+04</td> <td>    1.470</td> <td> 0.142</td> <td>-7412.081</td> <td> 5.18e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_HdBoard</th>   <td>-6860.0299</td> <td> 5879.900</td> <td>   -1.167</td> <td> 0.244</td> <td>-1.84e+04</td> <td> 4675.940</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_ImStucc</th>   <td>-1.281e+04</td> <td> 2.52e+04</td> <td>   -0.509</td> <td> 0.611</td> <td>-6.22e+04</td> <td> 3.66e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_MetalSd</th>   <td> 2689.8186</td> <td> 8964.917</td> <td>    0.300</td> <td> 0.764</td> <td>-1.49e+04</td> <td> 2.03e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_Plywood</th>   <td>-5150.1853</td> <td> 5983.701</td> <td>   -0.861</td> <td> 0.390</td> <td>-1.69e+04</td> <td> 6589.436</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_Stone</th>     <td>-1.816e+04</td> <td> 2.09e+04</td> <td>   -0.867</td> <td> 0.386</td> <td>-5.92e+04</td> <td> 2.29e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_Stucco</th>    <td>  510.5993</td> <td> 9283.028</td> <td>    0.055</td> <td> 0.956</td> <td>-1.77e+04</td> <td> 1.87e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_VinylSd</th>   <td>-4822.0000</td> <td> 8171.964</td> <td>   -0.590</td> <td> 0.555</td> <td>-2.09e+04</td> <td> 1.12e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_Wd Sdng</th>   <td>-1860.6358</td> <td> 5630.041</td> <td>   -0.330</td> <td> 0.741</td> <td>-1.29e+04</td> <td> 9185.128</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior1st_OHE_WdShing</th>   <td> -769.1740</td> <td> 7248.323</td> <td>   -0.106</td> <td> 0.916</td> <td> -1.5e+04</td> <td> 1.35e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_AsbShng</th>   <td>-3203.8250</td> <td> 1.19e+04</td> <td>   -0.269</td> <td> 0.788</td> <td>-2.66e+04</td> <td> 2.02e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_AsphShn</th>   <td> 3915.7836</td> <td> 1.88e+04</td> <td>    0.209</td> <td> 0.835</td> <td>-3.29e+04</td> <td> 4.07e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Brk Cmn</th>   <td>-3173.2980</td> <td> 1.74e+04</td> <td>   -0.182</td> <td> 0.855</td> <td>-3.74e+04</td> <td>  3.1e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_BrkFace</th>   <td> 1258.8278</td> <td> 7598.163</td> <td>    0.166</td> <td> 0.868</td> <td>-1.36e+04</td> <td> 1.62e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_CBlock</th>    <td>-1796.2630</td> <td> 1.35e+04</td> <td>   -0.133</td> <td> 0.894</td> <td>-2.83e+04</td> <td> 2.48e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_CmentBd</th>   <td>-1.574e+04</td> <td> 1.51e+04</td> <td>   -1.044</td> <td> 0.297</td> <td>-4.53e+04</td> <td> 1.38e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_HdBoard</th>   <td> 6872.3491</td> <td> 5364.921</td> <td>    1.281</td> <td> 0.200</td> <td>-3653.266</td> <td> 1.74e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_ImStucc</th>   <td> 1.311e+04</td> <td> 9301.970</td> <td>    1.409</td> <td> 0.159</td> <td>-5144.716</td> <td> 3.14e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_MetalSd</th>   <td>  435.0711</td> <td> 8724.900</td> <td>    0.050</td> <td> 0.960</td> <td>-1.67e+04</td> <td> 1.76e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Other</th>     <td>-1.489e+04</td> <td> 2.48e+04</td> <td>   -0.600</td> <td> 0.548</td> <td>-6.36e+04</td> <td> 3.38e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Plywood</th>   <td> 3124.0477</td> <td> 5007.673</td> <td>    0.624</td> <td> 0.533</td> <td>-6700.671</td> <td> 1.29e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Stone</th>     <td>-5797.3288</td> <td> 1.44e+04</td> <td>   -0.402</td> <td> 0.688</td> <td>-3.41e+04</td> <td> 2.25e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Stucco</th>    <td>-1983.3949</td> <td> 8964.950</td> <td>   -0.221</td> <td> 0.825</td> <td>-1.96e+04</td> <td> 1.56e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_VinylSd</th>   <td> 7700.3839</td> <td> 7298.120</td> <td>    1.055</td> <td> 0.292</td> <td>-6618.039</td> <td>  2.2e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Wd Sdng</th>   <td> 3585.3264</td> <td> 5011.472</td> <td>    0.715</td> <td> 0.474</td> <td>-6246.847</td> <td> 1.34e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Exterior2nd_OHE_Wd Shng</th>   <td>-2299.6747</td> <td> 5899.731</td> <td>   -0.390</td> <td> 0.697</td> <td>-1.39e+04</td> <td> 9275.203</td>\n", "</tr>\n", "<tr>\n", "  <th>MasVnrType_OHE_BrkCmn</th>     <td>-6274.5203</td> <td> 5517.488</td> <td>   -1.137</td> <td> 0.256</td> <td>-1.71e+04</td> <td> 4550.421</td>\n", "</tr>\n", "<tr>\n", "  <th>MasVnrType_OHE_BrkFace</th>    <td>-2262.0880</td> <td> 2368.739</td> <td>   -0.955</td> <td> 0.340</td> <td>-6909.395</td> <td> 2385.219</td>\n", "</tr>\n", "<tr>\n", "  <th>MasVnrType_OHE_None</th>       <td>-3672.8917</td> <td> 2259.807</td> <td>   -1.625</td> <td> 0.104</td> <td>-8106.482</td> <td>  760.698</td>\n", "</tr>\n", "<tr>\n", "  <th>MasVnrType_OHE_Stone</th>      <td> 3322.3963</td> <td> 2919.671</td> <td>    1.138</td> <td> 0.255</td> <td>-2405.803</td> <td> 9050.596</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterQual_OHE_Ex</th>          <td> 1.422e+04</td> <td> 5116.160</td> <td>    2.780</td> <td> 0.006</td> <td> 4185.143</td> <td> 2.43e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterQual_OHE_Fa</th>          <td>-6367.8330</td> <td> 8771.450</td> <td>   -0.726</td> <td> 0.468</td> <td>-2.36e+04</td> <td> 1.08e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterQual_OHE_Gd</th>          <td>-8843.3857</td> <td> 3594.454</td> <td>   -2.460</td> <td> 0.014</td> <td>-1.59e+04</td> <td>-1791.308</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterQual_OHE_TA</th>          <td>-7898.5910</td> <td> 3564.036</td> <td>   -2.216</td> <td> 0.027</td> <td>-1.49e+04</td> <td> -906.190</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterCond_OHE_Ex</th>          <td> 1874.0404</td> <td> 1.58e+04</td> <td>    0.118</td> <td> 0.906</td> <td>-2.92e+04</td> <td>  3.3e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterCond_OHE_Fa</th>          <td> -823.5374</td> <td> 8072.232</td> <td>   -0.102</td> <td> 0.919</td> <td>-1.67e+04</td> <td>  1.5e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterCond_OHE_Gd</th>          <td>-1806.4046</td> <td> 7095.527</td> <td>   -0.255</td> <td> 0.799</td> <td>-1.57e+04</td> <td> 1.21e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterCond_OHE_Po</th>          <td>-8032.0747</td> <td> 2.28e+04</td> <td>   -0.352</td> <td> 0.725</td> <td>-5.29e+04</td> <td> 3.68e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>ExterCond_OHE_TA</th>          <td>  -99.1272</td> <td> 6795.914</td> <td>   -0.015</td> <td> 0.988</td> <td>-1.34e+04</td> <td> 1.32e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Foundation_OHE_BrkTil</th>     <td> -735.2174</td> <td> 4378.082</td> <td>   -0.168</td> <td> 0.867</td> <td>-9324.722</td> <td> 7854.287</td>\n", "</tr>\n", "<tr>\n", "  <th>Foundation_OHE_CBlock</th>     <td> 4520.0426</td> <td> 3868.479</td> <td>    1.168</td> <td> 0.243</td> <td>-3069.654</td> <td> 1.21e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Foundation_OHE_PConc</th>      <td> 4739.6745</td> <td> 3924.169</td> <td>    1.208</td> <td> 0.227</td> <td>-2959.282</td> <td> 1.24e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Foundation_OHE_Slab</th>       <td>-6697.5708</td> <td> 9147.951</td> <td>   -0.732</td> <td> 0.464</td> <td>-2.46e+04</td> <td> 1.13e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Foundation_OHE_Stone</th>      <td> 5152.2702</td> <td> 1.07e+04</td> <td>    0.481</td> <td> 0.631</td> <td>-1.59e+04</td> <td> 2.62e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Foundation_OHE_Wood</th>       <td>-1.587e+04</td> <td> 1.29e+04</td> <td>   -1.229</td> <td> 0.219</td> <td>-4.12e+04</td> <td> 9465.221</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtQual_OHE_Ex</th>           <td> 1.391e+04</td> <td> 4262.926</td> <td>    3.262</td> <td> 0.001</td> <td> 5543.931</td> <td> 2.23e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtQual_OHE_Fa</th>           <td>-2292.1347</td> <td> 4875.000</td> <td>   -0.470</td> <td> 0.638</td> <td>-1.19e+04</td> <td> 7272.289</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtQual_OHE_Gd</th>           <td>-5966.2262</td> <td> 3339.328</td> <td>   -1.787</td> <td> 0.074</td> <td>-1.25e+04</td> <td>  585.312</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtQual_OHE_NA</th>           <td>-9754.4051</td> <td>    1e+04</td> <td>   -0.971</td> <td> 0.332</td> <td>-2.95e+04</td> <td> 9955.272</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtQual_OHE_TA</th>           <td>-4781.8426</td> <td> 3332.797</td> <td>   -1.435</td> <td> 0.152</td> <td>-1.13e+04</td> <td> 1756.881</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtCond_OHE_Fa</th>           <td>-4845.8247</td> <td> 8149.163</td> <td>   -0.595</td> <td> 0.552</td> <td>-2.08e+04</td> <td> 1.11e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtCond_OHE_Gd</th>           <td>-7111.8847</td> <td> 8377.604</td> <td>   -0.849</td> <td> 0.396</td> <td>-2.35e+04</td> <td> 9324.413</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtCond_OHE_NA</th>           <td>-9754.4051</td> <td>    1e+04</td> <td>   -0.971</td> <td> 0.332</td> <td>-2.95e+04</td> <td> 9955.272</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtCond_OHE_Po</th>           <td> 1.298e+04</td> <td> 2.42e+04</td> <td>    0.537</td> <td> 0.591</td> <td>-3.44e+04</td> <td> 6.04e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtCond_OHE_TA</th>           <td> -150.8057</td> <td> 7993.237</td> <td>   -0.019</td> <td> 0.985</td> <td>-1.58e+04</td> <td> 1.55e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtExposure_OHE_Av</th>       <td>-1690.9167</td> <td> 5189.468</td> <td>   -0.326</td> <td> 0.745</td> <td>-1.19e+04</td> <td> 8490.473</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtExposure_OHE_Gd</th>       <td> 1.881e+04</td> <td> 5462.687</td> <td>    3.444</td> <td> 0.001</td> <td> 8096.549</td> <td> 2.95e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtExposure_OHE_Mn</th>       <td>-5336.8363</td> <td> 5397.044</td> <td>   -0.989</td> <td> 0.323</td> <td>-1.59e+04</td> <td> 5251.801</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtExposure_OHE_NA</th>       <td>-1.321e+04</td> <td> 1.95e+04</td> <td>   -0.676</td> <td> 0.499</td> <td>-5.16e+04</td> <td> 2.51e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtExposure_OHE_No</th>       <td>-7467.7095</td> <td> 5089.471</td> <td>   -1.467</td> <td> 0.143</td> <td>-1.75e+04</td> <td> 2517.491</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_ALQ</th>      <td> 1183.0046</td> <td> 2593.365</td> <td>    0.456</td> <td> 0.648</td> <td>-3905.004</td> <td> 6271.013</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_BLQ</th>      <td> 2715.0436</td> <td> 2736.678</td> <td>    0.992</td> <td> 0.321</td> <td>-2654.135</td> <td> 8084.222</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_GLQ</th>      <td> 4919.8223</td> <td> 2639.327</td> <td>    1.864</td> <td> 0.063</td> <td> -258.360</td> <td> 1.01e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_LwQ</th>      <td>-4735.4031</td> <td> 3382.713</td> <td>   -1.400</td> <td> 0.162</td> <td>-1.14e+04</td> <td> 1901.253</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_NA</th>       <td>-9754.4051</td> <td>    1e+04</td> <td>   -0.971</td> <td> 0.332</td> <td>-2.95e+04</td> <td> 9955.272</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_Rec</th>      <td> 1496.4299</td> <td> 2817.792</td> <td>    0.531</td> <td> 0.595</td> <td>-4031.888</td> <td> 7024.748</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType1_OHE_Unf</th>      <td>-4711.5957</td> <td> 2408.305</td> <td>   -1.956</td> <td> 0.051</td> <td>-9436.529</td> <td>   13.337</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_ALQ</th>      <td> 1997.7787</td> <td> 6684.893</td> <td>    0.299</td> <td> 0.765</td> <td>-1.11e+04</td> <td> 1.51e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_BLQ</th>      <td>-7776.7648</td> <td> 5524.662</td> <td>   -1.408</td> <td> 0.159</td> <td>-1.86e+04</td> <td> 3062.252</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_GLQ</th>      <td>-3241.5252</td> <td> 7811.910</td> <td>   -0.415</td> <td> 0.678</td> <td>-1.86e+04</td> <td> 1.21e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_LwQ</th>      <td>-9113.0595</td> <td> 5262.793</td> <td>   -1.732</td> <td> 0.084</td> <td>-1.94e+04</td> <td> 1212.187</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_NA</th>       <td> 1.938e+04</td> <td> 2.13e+04</td> <td>    0.911</td> <td> 0.362</td> <td>-2.24e+04</td> <td> 6.11e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_Rec</th>      <td>-4760.1815</td> <td> 5159.896</td> <td>   -0.923</td> <td> 0.356</td> <td>-1.49e+04</td> <td> 5363.188</td>\n", "</tr>\n", "<tr>\n", "  <th>BsmtFinType2_OHE_Unf</th>      <td>-5368.5507</td> <td> 4092.223</td> <td>   -1.312</td> <td> 0.190</td> <td>-1.34e+04</td> <td> 2660.116</td>\n", "</tr>\n", "<tr>\n", "  <th>Heating_OHE_Floor</th>         <td>  439.6600</td> <td> 2.34e+04</td> <td>    0.019</td> <td> 0.985</td> <td>-4.55e+04</td> <td> 4.64e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Heating_OHE_GasA</th>          <td> 3335.7938</td> <td> 6837.557</td> <td>    0.488</td> <td> 0.626</td> <td>-1.01e+04</td> <td> 1.68e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Heating_OHE_GasW</th>          <td> 6368.9145</td> <td> 8447.243</td> <td>    0.754</td> <td> 0.451</td> <td>-1.02e+04</td> <td> 2.29e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Heating_OHE_Grav</th>          <td> 1322.5788</td> <td> 1.18e+04</td> <td>    0.112</td> <td> 0.911</td> <td>-2.19e+04</td> <td> 2.45e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Heating_OHE_OthW</th>          <td>-3.457e+04</td> <td> 1.72e+04</td> <td>   -2.012</td> <td> 0.044</td> <td>-6.83e+04</td> <td> -859.113</td>\n", "</tr>\n", "<tr>\n", "  <th>Heating_OHE_Wall</th>          <td> 1.422e+04</td> <td> 1.41e+04</td> <td>    1.006</td> <td> 0.315</td> <td>-1.35e+04</td> <td>  4.2e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HeatingQC_OHE_Ex</th>          <td> 2004.6743</td> <td> 5845.507</td> <td>    0.343</td> <td> 0.732</td> <td>-9463.819</td> <td> 1.35e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HeatingQC_OHE_Fa</th>          <td> 2442.9863</td> <td> 6730.368</td> <td>    0.363</td> <td> 0.717</td> <td>-1.08e+04</td> <td> 1.56e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HeatingQC_OHE_Gd</th>          <td>-1012.4556</td> <td> 5879.638</td> <td>   -0.172</td> <td> 0.863</td> <td>-1.25e+04</td> <td> 1.05e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HeatingQC_OHE_Po</th>          <td>-1.197e+04</td> <td> 2.27e+04</td> <td>   -0.527</td> <td> 0.598</td> <td>-5.66e+04</td> <td> 3.26e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>HeatingQC_OHE_TA</th>          <td> -348.6333</td> <td> 5795.189</td> <td>   -0.060</td> <td> 0.952</td> <td>-1.17e+04</td> <td>  1.1e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>CentralAir_OHE_N</th>          <td>-5246.3361</td> <td> 2681.618</td> <td>   -1.956</td> <td> 0.051</td> <td>-1.05e+04</td> <td>   14.818</td>\n", "</tr>\n", "<tr>\n", "  <th>CentralAir_OHE_Y</th>          <td>-3640.7675</td> <td> 2852.738</td> <td>   -1.276</td> <td> 0.202</td> <td>-9237.649</td> <td> 1956.114</td>\n", "</tr>\n", "<tr>\n", "  <th>Electrical_OHE_FuseA</th>      <td> -791.5328</td> <td> 1.09e+04</td> <td>   -0.072</td> <td> 0.942</td> <td>-2.22e+04</td> <td> 2.06e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Electrical_OHE_FuseF</th>      <td>-1438.2511</td> <td> 1.18e+04</td> <td>   -0.122</td> <td> 0.903</td> <td>-2.45e+04</td> <td> 2.17e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Electrical_OHE_FuseP</th>      <td>-1.348e+04</td> <td>  1.7e+04</td> <td>   -0.795</td> <td> 0.427</td> <td>-4.68e+04</td> <td> 1.98e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Electrical_OHE_Mix</th>        <td> 1.031e+04</td> <td> 3.75e+04</td> <td>    0.275</td> <td> 0.784</td> <td>-6.33e+04</td> <td> 8.39e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Electrical_OHE_SBrkr</th>      <td>-3482.1497</td> <td> 1.09e+04</td> <td>   -0.319</td> <td> 0.750</td> <td>-2.49e+04</td> <td>  1.8e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>KitchenQual_OHE_Ex</th>        <td> 1.696e+04</td> <td> 3297.921</td> <td>    5.143</td> <td> 0.000</td> <td> 1.05e+04</td> <td> 2.34e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>KitchenQual_OHE_Fa</th>        <td>-7828.8880</td> <td> 4265.506</td> <td>   -1.835</td> <td> 0.067</td> <td>-1.62e+04</td> <td>  539.748</td>\n", "</tr>\n", "<tr>\n", "  <th>KitchenQual_OHE_Gd</th>        <td>-9598.4777</td> <td> 2218.775</td> <td>   -4.326</td> <td> 0.000</td> <td> -1.4e+04</td> <td>-5245.390</td>\n", "</tr>\n", "<tr>\n", "  <th>KitchenQual_OHE_TA</th>        <td>-8419.3093</td> <td> 2131.317</td> <td>   -3.950</td> <td> 0.000</td> <td>-1.26e+04</td> <td>-4237.808</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Maj1</th>       <td> -715.7203</td> <td> 8569.672</td> <td>   -0.084</td> <td> 0.933</td> <td>-1.75e+04</td> <td> 1.61e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Maj2</th>       <td>-8942.8567</td> <td> 1.22e+04</td> <td>   -0.735</td> <td> 0.462</td> <td>-3.28e+04</td> <td> 1.49e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Min1</th>       <td> 4869.5743</td> <td> 6253.392</td> <td>    0.779</td> <td> 0.436</td> <td>-7399.161</td> <td> 1.71e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Min2</th>       <td> 6029.6443</td> <td> 6505.458</td> <td>    0.927</td> <td> 0.354</td> <td>-6733.629</td> <td> 1.88e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Mod</th>        <td> 1383.6326</td> <td> 8505.898</td> <td>    0.163</td> <td> 0.871</td> <td>-1.53e+04</td> <td> 1.81e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Sev</th>        <td>-3.265e+04</td> <td> 2.59e+04</td> <td>   -1.261</td> <td> 0.208</td> <td>-8.35e+04</td> <td> 1.82e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>Functional_OHE_Typ</th>        <td> 2.114e+04</td> <td> 5172.138</td> <td>    4.088</td> <td> 0.000</td> <td>  1.1e+04</td> <td> 3.13e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_2Types</th>     <td>-1.567e+04</td> <td> 9932.399</td> <td>   -1.578</td> <td> 0.115</td> <td>-3.52e+04</td> <td> 3813.919</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_Attchd</th>     <td> 1981.8353</td> <td> 3076.841</td> <td>    0.644</td> <td> 0.520</td> <td>-4054.720</td> <td> 8018.390</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_Basment</th>    <td>-1115.6943</td> <td> 6412.207</td> <td>   -0.174</td> <td> 0.862</td> <td>-1.37e+04</td> <td> 1.15e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_BuiltIn</th>    <td>-1923.5918</td> <td> 4083.332</td> <td>   -0.471</td> <td> 0.638</td> <td>-9934.816</td> <td> 6087.633</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_CarPort</th>    <td> 3052.2831</td> <td> 9302.887</td> <td>    0.328</td> <td> 0.743</td> <td>-1.52e+04</td> <td> 2.13e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_Detchd</th>     <td> 3845.0677</td> <td> 3216.441</td> <td>    1.195</td> <td> 0.232</td> <td>-2465.373</td> <td> 1.02e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageType_OHE_NA</th>         <td>  945.7773</td> <td> 1729.266</td> <td>    0.547</td> <td> 0.585</td> <td>-2446.927</td> <td> 4338.482</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageFinish_OHE_Fin</th>      <td>-2013.3263</td> <td> 1793.821</td> <td>   -1.122</td> <td> 0.262</td> <td>-5532.684</td> <td> 1506.031</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageFinish_OHE_NA</th>       <td>  945.7773</td> <td> 1729.266</td> <td>    0.547</td> <td> 0.585</td> <td>-2446.927</td> <td> 4338.482</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageFinish_OHE_RFn</th>      <td>-4884.6414</td> <td> 1679.299</td> <td>   -2.909</td> <td> 0.004</td> <td>-8179.313</td> <td>-1589.970</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageFinish_OHE_Unf</th>      <td>-2934.9133</td> <td> 1816.802</td> <td>   -1.615</td> <td> 0.106</td> <td>-6499.357</td> <td>  629.530</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageQual_OHE_Ex</th>         <td> 9.166e+04</td> <td> 2.53e+04</td> <td>    3.624</td> <td> 0.000</td> <td>  4.2e+04</td> <td> 1.41e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageQual_OHE_Fa</th>         <td>-2.851e+04</td> <td> 8707.863</td> <td>   -3.274</td> <td> 0.001</td> <td>-4.56e+04</td> <td>-1.14e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageQual_OHE_Gd</th>         <td>-2.447e+04</td> <td> 1.08e+04</td> <td>   -2.260</td> <td> 0.024</td> <td>-4.57e+04</td> <td>-3224.112</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageQual_OHE_NA</th>         <td>  945.7773</td> <td> 1729.266</td> <td>    0.547</td> <td> 0.585</td> <td>-2446.927</td> <td> 4338.482</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageQual_OHE_Po</th>         <td>-2.482e+04</td> <td>  2.3e+04</td> <td>   -1.081</td> <td> 0.280</td> <td>-6.99e+04</td> <td> 2.02e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageQual_OHE_TA</th>         <td>-2.368e+04</td> <td> 8441.072</td> <td>   -2.806</td> <td> 0.005</td> <td>-4.02e+04</td> <td>-7123.487</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCond_OHE_Ex</th>         <td>-8.566e+04</td> <td>  2.9e+04</td> <td>   -2.953</td> <td> 0.003</td> <td>-1.43e+05</td> <td>-2.88e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCond_OHE_Fa</th>         <td> 1.788e+04</td> <td> 8704.218</td> <td>    2.054</td> <td> 0.040</td> <td>  804.321</td> <td>  3.5e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCond_OHE_Gd</th>         <td> 2.134e+04</td> <td> 1.14e+04</td> <td>    1.870</td> <td> 0.062</td> <td>-1054.208</td> <td> 4.37e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCond_OHE_NA</th>         <td>  945.7773</td> <td> 1729.266</td> <td>    0.547</td> <td> 0.585</td> <td>-2446.927</td> <td> 4338.482</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCond_OHE_Po</th>         <td> 1.464e+04</td> <td> 1.48e+04</td> <td>    0.986</td> <td> 0.324</td> <td>-1.45e+04</td> <td> 4.38e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>GarageCond_OHE_TA</th>         <td> 2.197e+04</td> <td> 8023.293</td> <td>    2.738</td> <td> 0.006</td> <td> 6227.248</td> <td> 3.77e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>PavedDrive_OHE_N</th>          <td> -786.7359</td> <td> 3061.162</td> <td>   -0.257</td> <td> 0.797</td> <td>-6792.530</td> <td> 5219.058</td>\n", "</tr>\n", "<tr>\n", "  <th>PavedDrive_OHE_P</th>          <td>-6025.0798</td> <td> 3793.883</td> <td>   -1.588</td> <td> 0.113</td> <td>-1.35e+04</td> <td> 1418.264</td>\n", "</tr>\n", "<tr>\n", "  <th>PavedDrive_OHE_Y</th>          <td>-2075.2879</td> <td> 2469.093</td> <td>   -0.841</td> <td> 0.401</td> <td>-6919.483</td> <td> 2768.907</td>\n", "</tr>\n", "<tr>\n", "  <th>PoolQC_OHE_Ex</th>             <td> 6.616e+04</td> <td> 1.82e+04</td> <td>    3.629</td> <td> 0.000</td> <td> 3.04e+04</td> <td> 1.02e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>PoolQC_OHE_Fa</th>             <td> -3.16e+04</td> <td> 2.05e+04</td> <td>   -1.543</td> <td> 0.123</td> <td>-7.18e+04</td> <td> 8589.279</td>\n", "</tr>\n", "<tr>\n", "  <th>PoolQC_OHE_Gd</th>             <td> 1.028e+04</td> <td> 1.71e+04</td> <td>    0.600</td> <td> 0.549</td> <td>-2.33e+04</td> <td> 4.39e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>PoolQC_OHE_NA</th>             <td>-5.373e+04</td> <td> 9797.772</td> <td>   -5.484</td> <td> 0.000</td> <td> -7.3e+04</td> <td>-3.45e+04</td>\n", "</tr>\n", "<tr>\n", "  <th><PERSON><PERSON>_OHE_GdPrv</th>           <td>-7630.4719</td> <td> 3554.210</td> <td>   -2.147</td> <td> 0.032</td> <td>-1.46e+04</td> <td> -657.351</td>\n", "</tr>\n", "<tr>\n", "  <th><PERSON><PERSON>_OHE_GdWo</th>            <td> 2541.7890</td> <td> 3449.379</td> <td>    0.737</td> <td> 0.461</td> <td>-4225.660</td> <td> 9309.239</td>\n", "</tr>\n", "<tr>\n", "  <th><PERSON><PERSON>_OHE_MnPrv</th>           <td> 2481.3099</td> <td> 2689.166</td> <td>    0.923</td> <td> 0.356</td> <td>-2794.654</td> <td> 7757.274</td>\n", "</tr>\n", "<tr>\n", "  <th><PERSON><PERSON>_OHE_MnWw</th>            <td>-3269.8532</td> <td> 6239.134</td> <td>   -0.524</td> <td> 0.600</td> <td>-1.55e+04</td> <td> 8970.910</td>\n", "</tr>\n", "<tr>\n", "  <th><PERSON><PERSON>_OHE_NA</th>              <td>-3009.8774</td> <td> 2359.220</td> <td>   -1.276</td> <td> 0.202</td> <td>-7638.509</td> <td> 1618.755</td>\n", "</tr>\n", "<tr>\n", "  <th>MiscFeature_OHE_Gar2</th>      <td>-1.054e+05</td> <td> 7.98e+04</td> <td>   -1.320</td> <td> 0.187</td> <td>-2.62e+05</td> <td> 5.12e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MiscFeature_OHE_NA</th>        <td> 3.343e+04</td> <td>  2.8e+04</td> <td>    1.194</td> <td> 0.233</td> <td>-2.15e+04</td> <td> 8.83e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MiscFeature_OHE_Othr</th>      <td> 3.893e+04</td> <td> 2.48e+04</td> <td>    1.568</td> <td> 0.117</td> <td>-9766.082</td> <td> 8.76e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MiscFeature_OHE_Shed</th>      <td> 3.175e+04</td> <td> 2.37e+04</td> <td>    1.339</td> <td> 0.181</td> <td>-1.48e+04</td> <td> 7.83e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MiscFeature_OHE_TenC</th>      <td>-7590.1330</td> <td> 3.26e+04</td> <td>   -0.233</td> <td> 0.816</td> <td>-7.15e+04</td> <td> 5.64e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_COD</th>          <td> -1.36e+04</td> <td> 5758.295</td> <td>   -2.362</td> <td> 0.018</td> <td>-2.49e+04</td> <td>-2304.596</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_CWD</th>          <td>-2098.4435</td> <td> 1.21e+04</td> <td>   -0.174</td> <td> 0.862</td> <td>-2.58e+04</td> <td> 2.16e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_Con</th>          <td> 1.011e+04</td> <td> 1.65e+04</td> <td>    0.611</td> <td> 0.541</td> <td>-2.23e+04</td> <td> 4.26e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_ConLD</th>        <td> 1248.1659</td> <td> 9025.417</td> <td>    0.138</td> <td> 0.890</td> <td>-1.65e+04</td> <td>  1.9e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_ConLI</th>        <td>-2735.0310</td> <td> 1.09e+04</td> <td>   -0.251</td> <td> 0.802</td> <td>-2.41e+04</td> <td> 1.87e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_ConLw</th>        <td>-9016.4938</td> <td> 1.15e+04</td> <td>   -0.787</td> <td> 0.431</td> <td>-3.15e+04</td> <td> 1.35e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_New</th>          <td> 2.217e+04</td> <td> 1.42e+04</td> <td>    1.566</td> <td> 0.118</td> <td>-5606.285</td> <td> 4.99e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_Oth</th>          <td>-1335.1238</td> <td> 1.38e+04</td> <td>   -0.097</td> <td> 0.923</td> <td>-2.84e+04</td> <td> 2.58e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleType_OHE_WD</th>           <td>-1.363e+04</td> <td> 4322.187</td> <td>   -3.154</td> <td> 0.002</td> <td>-2.21e+04</td> <td>-5150.580</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleCondition_OHE_Abnorml</th> <td>-4331.4298</td> <td> 4710.968</td> <td>   -0.919</td> <td> 0.358</td> <td>-1.36e+04</td> <td> 4911.173</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleCondition_OHE_AdjLand</th> <td> 9443.5614</td> <td> 1.31e+04</td> <td>    0.722</td> <td> 0.471</td> <td>-1.62e+04</td> <td> 3.51e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleCondition_OHE_Alloca</th>  <td> 3457.3320</td> <td> 8613.688</td> <td>    0.401</td> <td> 0.688</td> <td>-1.34e+04</td> <td> 2.04e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleCondition_OHE_Family</th>  <td>-3097.8496</td> <td> 6351.270</td> <td>   -0.488</td> <td> 0.626</td> <td>-1.56e+04</td> <td> 9362.916</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleCondition_OHE_Normal</th>  <td> 3316.2751</td> <td> 4173.754</td> <td>    0.795</td> <td> 0.427</td> <td>-4872.350</td> <td> 1.15e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>SaleCondition_OHE_Partial</th> <td>-1.767e+04</td> <td> 1.33e+04</td> <td>   -1.327</td> <td> 0.185</td> <td>-4.38e+04</td> <td> 8450.767</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_20.0</th>       <td> 7963.2412</td> <td> 6544.517</td> <td>    1.217</td> <td> 0.224</td> <td>-4876.663</td> <td> 2.08e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_30.0</th>       <td> 9157.7316</td> <td> 7559.871</td> <td>    1.211</td> <td> 0.226</td> <td>-5674.228</td> <td>  2.4e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_40.0</th>       <td>-4097.6267</td> <td> 1.82e+04</td> <td>   -0.226</td> <td> 0.822</td> <td>-3.97e+04</td> <td> 3.15e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_45.0</th>       <td>  618.1253</td> <td> 2.53e+04</td> <td>    0.024</td> <td> 0.981</td> <td> -4.9e+04</td> <td> 5.03e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_50.0</th>       <td> 1.106e+04</td> <td> 8962.787</td> <td>    1.235</td> <td> 0.217</td> <td>-6519.392</td> <td> 2.86e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_60.0</th>       <td> 1.361e+04</td> <td> 7755.112</td> <td>    1.756</td> <td> 0.079</td> <td>-1600.793</td> <td> 2.88e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_70.0</th>       <td> 1.665e+04</td> <td> 7990.503</td> <td>    2.084</td> <td> 0.037</td> <td>  972.058</td> <td> 3.23e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_75.0</th>       <td>-1.453e+04</td> <td> 1.54e+04</td> <td>   -0.946</td> <td> 0.344</td> <td>-4.47e+04</td> <td> 1.56e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_80.0</th>       <td> 8460.2993</td> <td> 1.26e+04</td> <td>    0.671</td> <td> 0.502</td> <td>-1.63e+04</td> <td> 3.32e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_85.0</th>       <td>   14.9640</td> <td> 1.12e+04</td> <td>    0.001</td> <td> 0.999</td> <td>-2.19e+04</td> <td> 2.19e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_90.0</th>       <td>-3857.5649</td> <td> 4671.286</td> <td>   -0.826</td> <td> 0.409</td> <td> -1.3e+04</td> <td> 5307.186</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_120.0</th>      <td>-1.566e+04</td> <td> 1.31e+04</td> <td>   -1.197</td> <td> 0.231</td> <td>-4.13e+04</td> <td>    1e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_160.0</th>      <td>-9945.8069</td> <td> 1.49e+04</td> <td>   -0.669</td> <td> 0.503</td> <td>-3.91e+04</td> <td> 1.92e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_180.0</th>      <td>-2.013e+04</td> <td> 1.74e+04</td> <td>   -1.157</td> <td> 0.248</td> <td>-5.43e+04</td> <td>  1.4e+04</td>\n", "</tr>\n", "<tr>\n", "  <th>MSSubClass_OHE_190.0</th>      <td>-8199.1071</td> <td> 2.76e+04</td> <td>   -0.297</td> <td> 0.767</td> <td>-6.24e+04</td> <td>  4.6e+04</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>373.078</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th> <td>   1.937</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th> <td> 0.000</td>  <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>9764.841</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>          <td> 0.588</td>  <th>  Prob(JB):          </th> <td>    0.00</td>\n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>      <td>15.654</td>  <th>  Cond. No.          </th> <td>1.47e+19</td>\n", "</tr>\n", "</table><br/><br/>Notes:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.<br/>[2] The smallest eigenvalue is 1.73e-29. This might indicate that there are<br/>strong multicollinearity problems or that the design matrix is singular."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:              SalePrice   R-squared:                       0.923\n", "Model:                            OLS   Adj. R-squared:                  0.908\n", "Method:                 Least Squares   F-statistic:                     59.41\n", "Date:                Fri, 22 Apr 2022   Prob (F-statistic):               0.00\n", "Time:                        11:20:06   Log-Likelihood:                -16565.\n", "No. Observations:                1451   AIC:                         3.362e+04\n", "Df Residuals:                    1206   BIC:                         3.491e+04\n", "Df Model:                         244                                         \n", "Covariance Type:            nonrobust                                         \n", "=============================================================================================\n", "                                coef    std err          t      P>|t|      [0.025      0.975]\n", "---------------------------------------------------------------------------------------------\n", "LotFrontage                   9.1690     24.385      0.376      0.707     -38.674      57.012\n", "OverallQual                6443.4507   1098.337      5.867      0.000    4288.587    8598.315\n", "OverallCond                5761.3467    921.305      6.253      0.000    3953.808    7568.885\n", "GrLivArea                    71.2724      3.926     18.155      0.000      63.570      78.975\n", "BsmtFullBath               7808.0939   1862.581      4.192      0.000    4153.836    1.15e+04\n", "BsmtHalfBath               2704.3964   3161.287      0.855      0.392   -3497.837    8906.630\n", "FullBath                   4353.2284   2358.076      1.846      0.065    -273.159    8979.616\n", "HalfBath                   1125.2313   2213.287      0.508      0.611   -3217.089    5467.552\n", "BedroomAbvGr              -4678.0376   1473.399     -3.175      0.002   -7568.747   -1787.328\n", "KitchenAbvGr              -1.089e+04   6648.976     -1.639      0.102   -2.39e+04    2150.473\n", "TotRmsAbvGrd               1013.7051   1016.102      0.998      0.319    -979.818    3007.228\n", "GarageCars                 8663.3405   1708.010      5.072      0.000    5312.340     1.2e+04\n", "MiscVal                       9.0392      6.684      1.352      0.177      -4.074      22.153\n", "MoSold                     -510.0946    260.244     -1.960      0.050   -1020.675       0.486\n", "AgeAtSale                  -305.4555     85.143     -3.588      0.000    -472.501    -138.410\n", "YearsSinceRemodel           -27.7465     58.392     -0.475      0.635    -142.308      86.815\n", "HasDeck                    1621.8650   1585.671      1.023      0.307   -1489.115    4732.845\n", "HasPorch                   1870.3246   1739.962      1.075      0.283   -1543.365    5284.014\n", "HasFireplace               2375.0085   1818.079      1.306      0.192   -1191.941    5941.958\n", "HasFence                  -5877.2263   2031.768     -2.893      0.004   -9863.419   -1891.033\n", "Intercept                 -8887.1036   3683.133     -2.413      0.016   -1.61e+04   -1661.044\n", "MSZoning_OHE_C (all)      -2.978e+04   8731.959     -3.411      0.001   -4.69e+04   -1.27e+04\n", "MSZoning_OHE_FV            1.271e+04   6497.547      1.957      0.051     -33.757    2.55e+04\n", "MSZoning_OHE_RH            4234.7895   6421.424      0.659      0.510   -8363.614    1.68e+04\n", "MSZoning_OHE_RL            4630.5784   3487.791      1.328      0.185   -2212.233    1.15e+04\n", "MSZoning_OHE_RM            -682.4301   3937.317     -0.173      0.862   -8407.183    7042.323\n", "Street_OHE_Grvl           -8776.6589   6992.025     -1.255      0.210   -2.25e+04    4941.225\n", "Street_OHE_Pave            -110.4448   6575.318     -0.017      0.987    -1.3e+04    1.28e+04\n", "Alley_OHE_Grvl            -1810.1857   3604.374     -0.502      0.616   -8881.726    5261.355\n", "Alley_OHE_NA              -4693.0936   2616.409     -1.794      0.073   -9826.313     440.125\n", "Alley_OHE_Pave            -2383.8243   3913.934     -0.609      0.543   -1.01e+04    5295.053\n", "LotShape_OHE_IR1          -1.047e+04   2822.356     -3.711      0.000    -1.6e+04   -4935.205\n", "LotShape_OHE_IR2          -2372.8849   3985.625     -0.595      0.552   -1.02e+04    5446.644\n", "LotShape_OHE_IR3           1.303e+04   6882.455      1.894      0.059    -469.573    2.65e+04\n", "LotShape_OHE_Reg          -9075.0815   2919.659     -3.108      0.002   -1.48e+04   -3346.907\n", "LandContour_OHE_Bnk       -8623.9473   3406.031     -2.532      0.011   -1.53e+04   -1941.544\n", "LandContour_OHE_HLS        2046.0477   3508.549      0.583      0.560   -4837.490    8929.585\n", "LandContour_OHE_Low       -2995.0390   4504.323     -0.665      0.506   -1.18e+04    5842.141\n", "LandContour_OHE_Lvl         685.8349   2559.439      0.268      0.789   -4335.612    5707.282\n", "Utilities_OHE_AllPub       9427.5382   1.43e+04      0.658      0.511   -1.87e+04    3.75e+04\n", "Utilities_OHE_NoSeWa      -1.831e+04   1.58e+04     -1.161      0.246   -4.93e+04    1.26e+04\n", "LotConfig_OHE_Corner       1416.0814   3192.892      0.444      0.657   -4848.159    7680.322\n", "LotConfig_OHE_CulDSac      1.221e+04   3776.342      3.234      0.001    4803.547    1.96e+04\n", "LotConfig_OHE_FR2         -5854.6021   4099.815     -1.428      0.154   -1.39e+04    2188.960\n", "LotConfig_OHE_FR3         -1.593e+04   1.07e+04     -1.487      0.137   -3.69e+04    5082.020\n", "LotConfig_OHE_Inside       -728.1482   3025.508     -0.241      0.810   -6663.992    5207.696\n", "LandSlope_OHE_Gtl         -4550.2129   4120.952     -1.104      0.270   -1.26e+04    3534.819\n", "LandSlope_OHE_Mod          1596.2168   4053.999      0.394      0.694   -6357.457    9549.891\n", "LandSlope_OHE_Sev         -5933.1075   6686.760     -0.887      0.375   -1.91e+04    7185.867\n", "Neighborhood_OHE_Blmngtn  -8611.0187   7466.688     -1.153      0.249   -2.33e+04    6038.122\n", "Neighborhood_OHE_Blueste   8758.5694   1.88e+04      0.465      0.642   -2.82e+04    4.57e+04\n", "Neighborhood_OHE_BrDale    7873.6626   8884.523      0.886      0.376   -9557.175    2.53e+04\n", "Neighborhood_OHE_BrkSide  -2860.6985   5242.223     -0.546      0.585   -1.31e+04    7424.192\n", "Neighborhood_OHE_ClearCr  -7321.5088   5986.224     -1.223      0.222   -1.91e+04    4423.061\n", "Neighborhood_OHE_CollgCr  -7448.2921   3217.816     -2.315      0.021   -1.38e+04   -1135.153\n", "Neighborhood_OHE_Crawfor   1.347e+04   4666.804      2.885      0.004    4310.036    2.26e+04\n", "Neighborhood_OHE_Edwards  -1.837e+04   3377.030     -5.441      0.000    -2.5e+04   -1.17e+04\n", "Neighborhood_OHE_Gilbert  -1.216e+04   4007.323     -3.036      0.002      -2e+04   -4302.317\n", "Neighborhood_OHE_IDOTRR   -8124.2762   7043.131     -1.154      0.249   -2.19e+04    5693.874\n", "Neighborhood_OHE_MeadowV  -4486.2237   9507.217     -0.472      0.637   -2.31e+04    1.42e+04\n", "Neighborhood_OHE_Mitchel  -1.792e+04   4222.368     -4.245      0.000   -2.62e+04   -9640.032\n", "Neighborhood_OHE_NAmes    -1.482e+04   2980.842     -4.970      0.000   -2.07e+04   -8967.059\n", "Neighborhood_OHE_NPkVill   1.457e+04    1.3e+04      1.117      0.264    -1.1e+04    4.01e+04\n", "Neighborhood_OHE_NWAmes   -1.563e+04   3810.048     -4.103      0.000   -2.31e+04   -8155.691\n", "Neighborhood_OHE_NoRidge   3.405e+04   5108.666      6.664      0.000     2.4e+04    4.41e+04\n", "Neighborhood_OHE_NridgHt   2.408e+04   4495.108      5.357      0.000    1.53e+04    3.29e+04\n", "Neighborhood_OHE_OldTown  -1.378e+04   5188.658     -2.656      0.008    -2.4e+04   -3600.088\n", "Neighborhood_OHE_SWISU    -1.101e+04   6473.184     -1.700      0.089   -2.37e+04    1694.043\n", "Neighborhood_OHE_Sawyer   -9096.5155   3707.536     -2.454      0.014   -1.64e+04   -1822.579\n", "Neighborhood_OHE_SawyerW  -2713.1583   4030.968     -0.673      0.501   -1.06e+04    5195.331\n", "Neighborhood_OHE_Somerst    473.0768   6378.177      0.074      0.941    -1.2e+04     1.3e+04\n", "Neighborhood_OHE_StoneBr   4.238e+04   5929.178      7.148      0.000    3.08e+04     5.4e+04\n", "Neighborhood_OHE_Timber   -4534.9132   4950.992     -0.916      0.360   -1.42e+04    5178.602\n", "Neighborhood_OHE_Veenker   4353.6079   8133.294      0.535      0.593   -1.16e+04    2.03e+04\n", "Condition1_OHE_Artery     -7801.2753   5084.697     -1.534      0.125   -1.78e+04    2174.560\n", "Condition1_OHE_Feedr       -429.5102   4172.041     -0.103      0.918   -8614.775    7755.754\n", "Condition1_OHE_Norm        9584.1240   3175.648      3.018      0.003    3353.715    1.58e+04\n", "Condition1_OHE_PosA       -1570.5646   8957.572     -0.175      0.861   -1.91e+04     1.6e+04\n", "Condition1_OHE_PosN        2006.2559   6446.011      0.311      0.756   -1.06e+04    1.47e+04\n", "Condition1_OHE_RRAe       -1.461e+04   8110.361     -1.801      0.072   -3.05e+04    1302.710\n", "Condition1_OHE_RRAn        5895.9682   5781.315      1.020      0.308   -5446.584    1.72e+04\n", "Condition1_OHE_RRNe       -6683.7253   1.61e+04     -0.416      0.678   -3.82e+04    2.49e+04\n", "Condition1_OHE_RRNn        4720.8981   1.17e+04      0.403      0.687   -1.83e+04    2.77e+04\n", "Condition2_OHE_Artery      2.001e+04   2.45e+04      0.816      0.415   -2.81e+04    6.81e+04\n", "Condition2_OHE_Feedr       1.612e+04    1.7e+04      0.949      0.343   -1.72e+04    4.95e+04\n", "Condition2_OHE_Norm        1.434e+04   1.17e+04      1.221      0.222   -8698.454    3.74e+04\n", "Condition2_OHE_PosA        7.161e+04   3.15e+04      2.271      0.023    9740.065    1.33e+05\n", "Condition2_OHE_PosN       -1.802e+05   2.05e+04     -8.777      0.000    -2.2e+05    -1.4e+05\n", "Condition2_OHE_RRAe        1.586e+04   6.38e+04      0.248      0.804   -1.09e+05    1.41e+05\n", "Condition2_OHE_RRAn        7256.3041   2.53e+04      0.287      0.774   -4.23e+04    5.69e+04\n", "Condition2_OHE_RRNn        2.611e+04   2.01e+04      1.300      0.194   -1.33e+04    6.55e+04\n", "BldgType_OHE_1Fam          -716.5811   9984.921     -0.072      0.943   -2.03e+04    1.89e+04\n", "BldgType_OHE_2fmCon        8059.3471   2.37e+04      0.341      0.733   -3.84e+04    5.45e+04\n", "BldgType_OHE_Duplex       -3857.5649   4671.286     -0.826      0.409    -1.3e+04    5307.186\n", "BldgType_OHE_Twnhs        -8883.8366   1.14e+04     -0.778      0.437   -3.13e+04    1.35e+04\n", "BldgType_OHE_TwnhsE       -3488.4680   1.07e+04     -0.326      0.744   -2.45e+04    1.75e+04\n", "HouseStyle_OHE_1.5Fin     -4604.0771   8092.784     -0.569      0.570   -2.05e+04    1.13e+04\n", "HouseStyle_OHE_1.5Unf      2.016e+04   2.27e+04      0.889      0.374   -2.43e+04    6.46e+04\n", "HouseStyle_OHE_1Story      8291.4055   6853.861      1.210      0.227   -5155.411    2.17e+04\n", "HouseStyle_OHE_2.5Fin     -2.954e+04   1.44e+04     -2.052      0.040   -5.78e+04   -1299.420\n", "HouseStyle_OHE_2.5Unf      9664.9619   1.39e+04      0.694      0.488   -1.77e+04     3.7e+04\n", "HouseStyle_OHE_2Story     -1.393e+04   6785.508     -2.053      0.040   -2.72e+04    -619.319\n", "HouseStyle_OHE_SFoyer      4838.1623   9922.688      0.488      0.626   -1.46e+04    2.43e+04\n", "HouseStyle_OHE_SLvl       -3756.6333   1.19e+04     -0.316      0.752   -2.71e+04    1.95e+04\n", "RoofStyle_OHE_Flat        -1.689e+04   1.73e+04     -0.975      0.330   -5.09e+04    1.71e+04\n", "RoofStyle_OHE_Gable       -1.148e+04   7732.231     -1.485      0.138   -2.66e+04    3690.317\n", "RoofStyle_OHE_Gambrel     -7415.3888   1.04e+04     -0.713      0.476   -2.78e+04     1.3e+04\n", "RoofStyle_OHE_Hip         -9351.0472   7843.684     -1.192      0.233   -2.47e+04    6037.736\n", "RoofStyle_OHE_Mansard      -481.5264   1.12e+04     -0.043      0.966   -2.24e+04    2.14e+04\n", "RoofStyle_OHE_Shed         3.673e+04      3e+04      1.223      0.221   -2.22e+04    9.56e+04\n", "RoofMatl_OHE_ClyTile      -4.909e+05   3.37e+04    -14.583      0.000   -5.57e+05   -4.25e+05\n", "RoofMatl_OHE_CompShg       5.286e+04   1.07e+04      4.958      0.000    3.19e+04    7.38e+04\n", "RoofMatl_OHE_Membran       1.018e+05   2.88e+04      3.540      0.000    4.54e+04    1.58e+05\n", "RoofMatl_OHE_Metal         6.736e+04   2.72e+04      2.474      0.014    1.39e+04    1.21e+05\n", "RoofMatl_OHE_Roll          5.087e+04   2.61e+04      1.949      0.051    -327.761    1.02e+05\n", "RoofMatl_OHE_Tar&Grv       4.625e+04    1.5e+04      3.084      0.002    1.68e+04    7.57e+04\n", "RoofMatl_OHE_WdShake        4.21e+04   1.75e+04      2.400      0.017    7677.711    7.65e+04\n", "RoofMatl_OHE_WdShngl       1.208e+05   1.44e+04      8.413      0.000    9.26e+04    1.49e+05\n", "Exterior1st_OHE_AsbShng    9696.9557   1.27e+04      0.761      0.447   -1.53e+04    3.47e+04\n", "Exterior1st_OHE_AsphShn   -2.415e+04   3.09e+04     -0.781      0.435   -8.48e+04    3.65e+04\n", "Exterior1st_OHE_BrkComm    1.531e+04   2.56e+04      0.598      0.550   -3.49e+04    6.56e+04\n", "Exterior1st_OHE_BrkFace    1.711e+04   6574.131      2.602      0.009    4208.008       3e+04\n", "Exterior1st_OHE_CBlock    -1796.2630   1.35e+04     -0.133      0.894   -2.83e+04    2.48e+04\n", "Exterior1st_OHE_CemntBd    2.217e+04   1.51e+04      1.470      0.142   -7412.081    5.18e+04\n", "Exterior1st_OHE_HdBoard   -6860.0299   5879.900     -1.167      0.244   -1.84e+04    4675.940\n", "Exterior1st_OHE_ImStucc   -1.281e+04   2.52e+04     -0.509      0.611   -6.22e+04    3.66e+04\n", "Exterior1st_OHE_MetalSd    2689.8186   8964.917      0.300      0.764   -1.49e+04    2.03e+04\n", "Exterior1st_OHE_Plywood   -5150.1853   5983.701     -0.861      0.390   -1.69e+04    6589.436\n", "Exterior1st_OHE_Stone     -1.816e+04   2.09e+04     -0.867      0.386   -5.92e+04    2.29e+04\n", "Exterior1st_OHE_Stucco      510.5993   9283.028      0.055      0.956   -1.77e+04    1.87e+04\n", "Exterior1st_OHE_VinylSd   -4822.0000   8171.964     -0.590      0.555   -2.09e+04    1.12e+04\n", "Exterior1st_OHE_Wd Sdng   -1860.6358   5630.041     -0.330      0.741   -1.29e+04    9185.128\n", "Exterior1st_OHE_WdShing    -769.1740   7248.323     -0.106      0.916    -1.5e+04    1.35e+04\n", "Exterior2nd_OHE_AsbShng   -3203.8250   1.19e+04     -0.269      0.788   -2.66e+04    2.02e+04\n", "Exterior2nd_OHE_AsphShn    3915.7836   1.88e+04      0.209      0.835   -3.29e+04    4.07e+04\n", "Exterior2nd_OHE_Brk Cmn   -3173.2980   1.74e+04     -0.182      0.855   -3.74e+04     3.1e+04\n", "Exterior2nd_OHE_BrkFace    1258.8278   7598.163      0.166      0.868   -1.36e+04    1.62e+04\n", "Exterior2nd_OHE_CBlock    -1796.2630   1.35e+04     -0.133      0.894   -2.83e+04    2.48e+04\n", "Exterior2nd_OHE_CmentBd   -1.574e+04   1.51e+04     -1.044      0.297   -4.53e+04    1.38e+04\n", "Exterior2nd_OHE_HdBoard    6872.3491   5364.921      1.281      0.200   -3653.266    1.74e+04\n", "Exterior2nd_OHE_ImStucc    1.311e+04   9301.970      1.409      0.159   -5144.716    3.14e+04\n", "Exterior2nd_OHE_MetalSd     435.0711   8724.900      0.050      0.960   -1.67e+04    1.76e+04\n", "Exterior2nd_OHE_Other     -1.489e+04   2.48e+04     -0.600      0.548   -6.36e+04    3.38e+04\n", "Exterior2nd_OHE_Plywood    3124.0477   5007.673      0.624      0.533   -6700.671    1.29e+04\n", "Exterior2nd_OHE_Stone     -5797.3288   1.44e+04     -0.402      0.688   -3.41e+04    2.25e+04\n", "Exterior2nd_OHE_Stucco    -1983.3949   8964.950     -0.221      0.825   -1.96e+04    1.56e+04\n", "Exterior2nd_OHE_VinylSd    7700.3839   7298.120      1.055      0.292   -6618.039     2.2e+04\n", "Exterior2nd_OHE_Wd Sdng    3585.3264   5011.472      0.715      0.474   -6246.847    1.34e+04\n", "Exterior2nd_OHE_Wd Shng   -2299.6747   5899.731     -0.390      0.697   -1.39e+04    9275.203\n", "MasVnrType_OHE_BrkCmn     -6274.5203   5517.488     -1.137      0.256   -1.71e+04    4550.421\n", "MasVnrType_OHE_BrkFace    -2262.0880   2368.739     -0.955      0.340   -6909.395    2385.219\n", "MasVnrType_OHE_None       -3672.8917   2259.807     -1.625      0.104   -8106.482     760.698\n", "MasVnrType_OHE_Stone       3322.3963   2919.671      1.138      0.255   -2405.803    9050.596\n", "ExterQual_OHE_Ex           1.422e+04   5116.160      2.780      0.006    4185.143    2.43e+04\n", "ExterQual_OHE_Fa          -6367.8330   8771.450     -0.726      0.468   -2.36e+04    1.08e+04\n", "ExterQual_OHE_Gd          -8843.3857   3594.454     -2.460      0.014   -1.59e+04   -1791.308\n", "ExterQual_OHE_TA          -7898.5910   3564.036     -2.216      0.027   -1.49e+04    -906.190\n", "ExterCond_OHE_Ex           1874.0404   1.58e+04      0.118      0.906   -2.92e+04     3.3e+04\n", "ExterCond_OHE_Fa           -823.5374   8072.232     -0.102      0.919   -1.67e+04     1.5e+04\n", "ExterCond_OHE_Gd          -1806.4046   7095.527     -0.255      0.799   -1.57e+04    1.21e+04\n", "ExterCond_OHE_Po          -8032.0747   2.28e+04     -0.352      0.725   -5.29e+04    3.68e+04\n", "ExterCond_OHE_TA            -99.1272   6795.914     -0.015      0.988   -1.34e+04    1.32e+04\n", "Foundation_OHE_BrkTil      -735.2174   4378.082     -0.168      0.867   -9324.722    7854.287\n", "Foundation_OHE_CBlock      4520.0426   3868.479      1.168      0.243   -3069.654    1.21e+04\n", "Foundation_OHE_PConc       4739.6745   3924.169      1.208      0.227   -2959.282    1.24e+04\n", "Foundation_OHE_Slab       -6697.5708   9147.951     -0.732      0.464   -2.46e+04    1.13e+04\n", "Foundation_OHE_Stone       5152.2702   1.07e+04      0.481      0.631   -1.59e+04    2.62e+04\n", "Foundation_OHE_Wood       -1.587e+04   1.29e+04     -1.229      0.219   -4.12e+04    9465.221\n", "BsmtQual_OHE_Ex            1.391e+04   4262.926      3.262      0.001    5543.931    2.23e+04\n", "BsmtQual_OHE_Fa           -2292.1347   4875.000     -0.470      0.638   -1.19e+04    7272.289\n", "BsmtQual_OHE_Gd           -5966.2262   3339.328     -1.787      0.074   -1.25e+04     585.312\n", "BsmtQual_OHE_NA           -9754.4051      1e+04     -0.971      0.332   -2.95e+04    9955.272\n", "BsmtQual_OHE_TA           -4781.8426   3332.797     -1.435      0.152   -1.13e+04    1756.881\n", "BsmtCond_OHE_Fa           -4845.8247   8149.163     -0.595      0.552   -2.08e+04    1.11e+04\n", "BsmtCond_OHE_Gd           -7111.8847   8377.604     -0.849      0.396   -2.35e+04    9324.413\n", "BsmtCond_OHE_NA           -9754.4051      1e+04     -0.971      0.332   -2.95e+04    9955.272\n", "BsmtCond_OHE_Po            1.298e+04   2.42e+04      0.537      0.591   -3.44e+04    6.04e+04\n", "BsmtCond_OHE_TA            -150.8057   7993.237     -0.019      0.985   -1.58e+04    1.55e+04\n", "BsmtExposure_OHE_Av       -1690.9167   5189.468     -0.326      0.745   -1.19e+04    8490.473\n", "BsmtExposure_OHE_Gd        1.881e+04   5462.687      3.444      0.001    8096.549    2.95e+04\n", "BsmtExposure_OHE_Mn       -5336.8363   5397.044     -0.989      0.323   -1.59e+04    5251.801\n", "BsmtExposure_OHE_NA       -1.321e+04   1.95e+04     -0.676      0.499   -5.16e+04    2.51e+04\n", "BsmtExposure_OHE_No       -7467.7095   5089.471     -1.467      0.143   -1.75e+04    2517.491\n", "BsmtFinType1_OHE_ALQ       1183.0046   2593.365      0.456      0.648   -3905.004    6271.013\n", "BsmtFinType1_OHE_BLQ       2715.0436   2736.678      0.992      0.321   -2654.135    8084.222\n", "BsmtFinType1_OHE_GLQ       4919.8223   2639.327      1.864      0.063    -258.360    1.01e+04\n", "BsmtFinType1_OHE_LwQ      -4735.4031   3382.713     -1.400      0.162   -1.14e+04    1901.253\n", "BsmtFinType1_OHE_NA       -9754.4051      1e+04     -0.971      0.332   -2.95e+04    9955.272\n", "BsmtFinType1_OHE_Rec       1496.4299   2817.792      0.531      0.595   -4031.888    7024.748\n", "BsmtFinType1_OHE_Unf      -4711.5957   2408.305     -1.956      0.051   -9436.529      13.337\n", "BsmtFinType2_OHE_ALQ       1997.7787   6684.893      0.299      0.765   -1.11e+04    1.51e+04\n", "BsmtFinType2_OHE_BLQ      -7776.7648   5524.662     -1.408      0.159   -1.86e+04    3062.252\n", "BsmtFinType2_OHE_GLQ      -3241.5252   7811.910     -0.415      0.678   -1.86e+04    1.21e+04\n", "BsmtFinType2_OHE_LwQ      -9113.0595   5262.793     -1.732      0.084   -1.94e+04    1212.187\n", "BsmtFinType2_OHE_NA        1.938e+04   2.13e+04      0.911      0.362   -2.24e+04    6.11e+04\n", "BsmtFinType2_OHE_Rec      -4760.1815   5159.896     -0.923      0.356   -1.49e+04    5363.188\n", "BsmtFinType2_OHE_Unf      -5368.5507   4092.223     -1.312      0.190   -1.34e+04    2660.116\n", "Heating_OHE_Floor           439.6600   2.34e+04      0.019      0.985   -4.55e+04    4.64e+04\n", "Heating_OHE_GasA           3335.7938   6837.557      0.488      0.626   -1.01e+04    1.68e+04\n", "Heating_OHE_GasW           6368.9145   8447.243      0.754      0.451   -1.02e+04    2.29e+04\n", "Heating_OHE_Grav           1322.5788   1.18e+04      0.112      0.911   -2.19e+04    2.45e+04\n", "Heating_OHE_OthW          -3.457e+04   1.72e+04     -2.012      0.044   -6.83e+04    -859.113\n", "Heating_OHE_Wall           1.422e+04   1.41e+04      1.006      0.315   -1.35e+04     4.2e+04\n", "HeatingQC_OHE_Ex           2004.6743   5845.507      0.343      0.732   -9463.819    1.35e+04\n", "HeatingQC_OHE_Fa           2442.9863   6730.368      0.363      0.717   -1.08e+04    1.56e+04\n", "HeatingQC_OHE_Gd          -1012.4556   5879.638     -0.172      0.863   -1.25e+04    1.05e+04\n", "HeatingQC_OHE_Po          -1.197e+04   2.27e+04     -0.527      0.598   -5.66e+04    3.26e+04\n", "HeatingQC_OHE_TA           -348.6333   5795.189     -0.060      0.952   -1.17e+04     1.1e+04\n", "CentralAir_OHE_N          -5246.3361   2681.618     -1.956      0.051   -1.05e+04      14.818\n", "CentralAir_OHE_Y          -3640.7675   2852.738     -1.276      0.202   -9237.649    1956.114\n", "Electrical_OHE_FuseA       -791.5328   1.09e+04     -0.072      0.942   -2.22e+04    2.06e+04\n", "Electrical_OHE_FuseF      -1438.2511   1.18e+04     -0.122      0.903   -2.45e+04    2.17e+04\n", "Electrical_OHE_FuseP      -1.348e+04    1.7e+04     -0.795      0.427   -4.68e+04    1.98e+04\n", "Electrical_OHE_Mix         1.031e+04   3.75e+04      0.275      0.784   -6.33e+04    8.39e+04\n", "Electrical_OHE_SBrkr      -3482.1497   1.09e+04     -0.319      0.750   -2.49e+04     1.8e+04\n", "KitchenQual_OHE_Ex         1.696e+04   3297.921      5.143      0.000    1.05e+04    2.34e+04\n", "KitchenQual_OHE_Fa        -7828.8880   4265.506     -1.835      0.067   -1.62e+04     539.748\n", "KitchenQual_OHE_Gd        -9598.4777   2218.775     -4.326      0.000    -1.4e+04   -5245.390\n", "KitchenQual_OHE_TA        -8419.3093   2131.317     -3.950      0.000   -1.26e+04   -4237.808\n", "Functional_OHE_Maj1        -715.7203   8569.672     -0.084      0.933   -1.75e+04    1.61e+04\n", "Functional_OHE_Maj2       -8942.8567   1.22e+04     -0.735      0.462   -3.28e+04    1.49e+04\n", "Functional_OHE_Min1        4869.5743   6253.392      0.779      0.436   -7399.161    1.71e+04\n", "Functional_OHE_Min2        6029.6443   6505.458      0.927      0.354   -6733.629    1.88e+04\n", "Functional_OHE_Mod         1383.6326   8505.898      0.163      0.871   -1.53e+04    1.81e+04\n", "Functional_OHE_Sev        -3.265e+04   2.59e+04     -1.261      0.208   -8.35e+04    1.82e+04\n", "Functional_OHE_Typ         2.114e+04   5172.138      4.088      0.000     1.1e+04    3.13e+04\n", "GarageType_OHE_2Types     -1.567e+04   9932.399     -1.578      0.115   -3.52e+04    3813.919\n", "GarageType_OHE_Attchd      1981.8353   3076.841      0.644      0.520   -4054.720    8018.390\n", "GarageType_OHE_Basment    -1115.6943   6412.207     -0.174      0.862   -1.37e+04    1.15e+04\n", "GarageType_OHE_BuiltIn    -1923.5918   4083.332     -0.471      0.638   -9934.816    6087.633\n", "GarageType_OHE_CarPort     3052.2831   9302.887      0.328      0.743   -1.52e+04    2.13e+04\n", "GarageType_OHE_Detchd      3845.0677   3216.441      1.195      0.232   -2465.373    1.02e+04\n", "GarageType_OHE_NA           945.7773   1729.266      0.547      0.585   -2446.927    4338.482\n", "GarageFinish_OHE_Fin      -2013.3263   1793.821     -1.122      0.262   -5532.684    1506.031\n", "GarageFinish_OHE_NA         945.7773   1729.266      0.547      0.585   -2446.927    4338.482\n", "GarageFinish_OHE_RFn      -4884.6414   1679.299     -2.909      0.004   -8179.313   -1589.970\n", "GarageFinish_OHE_Unf      -2934.9133   1816.802     -1.615      0.106   -6499.357     629.530\n", "GarageQual_OHE_Ex          9.166e+04   2.53e+04      3.624      0.000     4.2e+04    1.41e+05\n", "GarageQual_OHE_Fa         -2.851e+04   8707.863     -3.274      0.001   -4.56e+04   -1.14e+04\n", "GarageQual_OHE_Gd         -2.447e+04   1.08e+04     -2.260      0.024   -4.57e+04   -3224.112\n", "GarageQual_OHE_NA           945.7773   1729.266      0.547      0.585   -2446.927    4338.482\n", "GarageQual_OHE_Po         -2.482e+04    2.3e+04     -1.081      0.280   -6.99e+04    2.02e+04\n", "GarageQual_OHE_TA         -2.368e+04   8441.072     -2.806      0.005   -4.02e+04   -7123.487\n", "GarageCond_OHE_Ex         -8.566e+04    2.9e+04     -2.953      0.003   -1.43e+05   -2.88e+04\n", "GarageCond_OHE_Fa          1.788e+04   8704.218      2.054      0.040     804.321     3.5e+04\n", "GarageCond_OHE_Gd          2.134e+04   1.14e+04      1.870      0.062   -1054.208    4.37e+04\n", "GarageCond_OHE_NA           945.7773   1729.266      0.547      0.585   -2446.927    4338.482\n", "GarageCond_OHE_Po          1.464e+04   1.48e+04      0.986      0.324   -1.45e+04    4.38e+04\n", "GarageCond_OHE_TA          2.197e+04   8023.293      2.738      0.006    6227.248    3.77e+04\n", "PavedDrive_OHE_N           -786.7359   3061.162     -0.257      0.797   -6792.530    5219.058\n", "PavedDrive_OHE_P          -6025.0798   3793.883     -1.588      0.113   -1.35e+04    1418.264\n", "PavedDrive_OHE_Y          -2075.2879   2469.093     -0.841      0.401   -6919.483    2768.907\n", "PoolQC_OHE_Ex              6.616e+04   1.82e+04      3.629      0.000    3.04e+04    1.02e+05\n", "PoolQC_OHE_Fa              -3.16e+04   2.05e+04     -1.543      0.123   -7.18e+04    8589.279\n", "PoolQC_OHE_Gd              1.028e+04   1.71e+04      0.600      0.549   -2.33e+04    4.39e+04\n", "PoolQC_OHE_NA             -5.373e+04   9797.772     -5.484      0.000    -7.3e+04   -3.45e+04\n", "Fence_OHE_GdPrv           -7630.4719   3554.210     -2.147      0.032   -1.46e+04    -657.351\n", "Fence_OHE_GdWo             2541.7890   3449.379      0.737      0.461   -4225.660    9309.239\n", "Fence_OHE_MnPrv            2481.3099   2689.166      0.923      0.356   -2794.654    7757.274\n", "Fence_OHE_MnWw            -3269.8532   6239.134     -0.524      0.600   -1.55e+04    8970.910\n", "Fence_OHE_NA              -3009.8774   2359.220     -1.276      0.202   -7638.509    1618.755\n", "MiscFeature_OHE_Gar2      -1.054e+05   7.98e+04     -1.320      0.187   -2.62e+05    5.12e+04\n", "MiscFeature_OHE_NA         3.343e+04    2.8e+04      1.194      0.233   -2.15e+04    8.83e+04\n", "MiscFeature_OHE_Othr       3.893e+04   2.48e+04      1.568      0.117   -9766.082    8.76e+04\n", "MiscFeature_OHE_Shed       3.175e+04   2.37e+04      1.339      0.181   -1.48e+04    7.83e+04\n", "MiscFeature_OHE_TenC      -7590.1330   3.26e+04     -0.233      0.816   -7.15e+04    5.64e+04\n", "SaleType_OHE_COD           -1.36e+04   5758.295     -2.362      0.018   -2.49e+04   -2304.596\n", "SaleType_OHE_CWD          -2098.4435   1.21e+04     -0.174      0.862   -2.58e+04    2.16e+04\n", "SaleType_OHE_Con           1.011e+04   1.65e+04      0.611      0.541   -2.23e+04    4.26e+04\n", "SaleType_OHE_ConLD         1248.1659   9025.417      0.138      0.890   -1.65e+04     1.9e+04\n", "SaleType_OHE_ConLI        -2735.0310   1.09e+04     -0.251      0.802   -2.41e+04    1.87e+04\n", "SaleType_OHE_ConLw        -9016.4938   1.15e+04     -0.787      0.431   -3.15e+04    1.35e+04\n", "SaleType_OHE_New           2.217e+04   1.42e+04      1.566      0.118   -5606.285    4.99e+04\n", "SaleType_OHE_Oth          -1335.1238   1.38e+04     -0.097      0.923   -2.84e+04    2.58e+04\n", "SaleType_OHE_WD           -1.363e+04   4322.187     -3.154      0.002   -2.21e+04   -5150.580\n", "SaleCondition_OHE_Abnorml -4331.4298   4710.968     -0.919      0.358   -1.36e+04    4911.173\n", "SaleCondition_OHE_AdjLand  9443.5614   1.31e+04      0.722      0.471   -1.62e+04    3.51e+04\n", "SaleCondition_OHE_Alloca   3457.3320   8613.688      0.401      0.688   -1.34e+04    2.04e+04\n", "SaleCondition_OHE_Family  -3097.8496   6351.270     -0.488      0.626   -1.56e+04    9362.916\n", "SaleCondition_OHE_Normal   3316.2751   4173.754      0.795      0.427   -4872.350    1.15e+04\n", "SaleCondition_OHE_Partial -1.767e+04   1.33e+04     -1.327      0.185   -4.38e+04    8450.767\n", "MSSubClass_OHE_20.0        7963.2412   6544.517      1.217      0.224   -4876.663    2.08e+04\n", "MSSubClass_OHE_30.0        9157.7316   7559.871      1.211      0.226   -5674.228     2.4e+04\n", "MSSubClass_OHE_40.0       -4097.6267   1.82e+04     -0.226      0.822   -3.97e+04    3.15e+04\n", "MSSubClass_OHE_45.0         618.1253   2.53e+04      0.024      0.981    -4.9e+04    5.03e+04\n", "MSSubClass_OHE_50.0        1.106e+04   8962.787      1.235      0.217   -6519.392    2.86e+04\n", "MSSubClass_OHE_60.0        1.361e+04   7755.112      1.756      0.079   -1600.793    2.88e+04\n", "MSSubClass_OHE_70.0        1.665e+04   7990.503      2.084      0.037     972.058    3.23e+04\n", "MSSubClass_OHE_75.0       -1.453e+04   1.54e+04     -0.946      0.344   -4.47e+04    1.56e+04\n", "MSSubClass_OHE_80.0        8460.2993   1.26e+04      0.671      0.502   -1.63e+04    3.32e+04\n", "MSSubClass_OHE_85.0          14.9640   1.12e+04      0.001      0.999   -2.19e+04    2.19e+04\n", "MSSubClass_OHE_90.0       -3857.5649   4671.286     -0.826      0.409    -1.3e+04    5307.186\n", "MSSubClass_OHE_120.0      -1.566e+04   1.31e+04     -1.197      0.231   -4.13e+04       1e+04\n", "MSSubClass_OHE_160.0      -9945.8069   1.49e+04     -0.669      0.503   -3.91e+04    1.92e+04\n", "MSSubClass_OHE_180.0      -2.013e+04   1.74e+04     -1.157      0.248   -5.43e+04     1.4e+04\n", "MSSubClass_OHE_190.0      -8199.1071   2.76e+04     -0.297      0.767   -6.24e+04     4.6e+04\n", "==============================================================================\n", "Omnibus:                      373.078   <PERSON><PERSON><PERSON>-Watson:                   1.937\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):             9764.841\n", "Skew:                           0.588   Prob(JB):                         0.00\n", "Kurtosis:                      15.654   Cond. No.                     1.47e+19\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The smallest eigenvalue is 1.73e-29. This might indicate that there are\n", "strong multicollinearity problems or that the design matrix is singular.\n", "\"\"\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train a linear regression using statsmodels\n", "model = sm.OLS(y, X_ohe)\n", "results = model.fit()\n", "results.summary()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Train a Fine-tuned Predictive ML Model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we train a LightGBM regression model and use grid search to do model tuning."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Split data into train and test\n", "from sklearn.model_selection import train_test_split\n", "\n", "x_train_ohe, x_test_ohe, x_train, x_test, y_train, y_test = train_test_split(\n", "    X_ohe, X, y, test_size=0.2, random_state=0\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# train a lightGBM regression model\n", "est = LGBMRegressor()\n", "param_grid = {\"learning_rate\": [0.1, 0.05, 0.01], \"max_depth\": [3, 5, 10]}\n", "search = GridSearchCV(est, param_grid, n_jobs=-1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best estimator:  {'learning_rate': 0.1, 'max_depth': 5}\n"]}], "source": ["search.fit(x_train_ohe, y_train)\n", "print(\"Best estimator: \", search.best_params_)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test set score:  0.8581588030286735\n"]}], "source": ["print(\"Test set score: \", search.best_estimator_.score(x_test_ohe, y_test))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Correlation Interpretation\n", "### Feature Importance - Shap Value\n", "We explain this ML model by understanding the top important features to predict the housing price, internally using **shap value**."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["fitted_model = search.best_estimator_"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 98%|===================| 286/291 [00:17<00:00]        "]}], "source": ["# use interventional approach\n", "background = shap.maskers.Independent(x_train_ohe, max_samples=1000)\n", "explainer = shap.TreeExplainer(\n", "    fitted_model, data=background, feature_names=X_ohe.columns\n", ")\n", "shap_values = explainer(x_test_ohe)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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************************************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\n", "text/plain": ["<Figure size 576x684 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot the feature importance\n", "shap.summary_plot(shap_values, x_test_ohe)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the summary plot above, we could see the **most important features** sorted by their importance level. It tells us that houses that measure high in overall quality, square footage, and are also relatively new will have a higher housing price. "]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Causal Interpretation\n", "### Direct Causal Effect --  Do certain house characteristics have a direct effect on home value?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We've seen which features are good predictors of home value, but are these features also causally signficiant? Below we use the CausalAnalysis class to assess the causal effects of certain house characteristics on home value."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# order feature names according to shap values\n", "vals = np.abs(shap_values.values).mean(0)\n", "feature_importance = pd.DataFrame(\n", "    list(zip(shap_values.feature_names, vals)), columns=[\"features\", \"importance\"]\n", ")\n", "feature_importance.sort_values(by=[\"importance\"], ascending=False, inplace=True)\n", "\n", "# keep top k features for causal analysis\n", "k = 5\n", "sorted_features = feature_importance[\"features\"]\n", "top_k_features = list(sorted_features.values)[:5]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.solutions.causal_analysis._causal_analysis.CausalAnalysis at 0x18dfacaf4c0>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.solutions.causal_analysis import CausalAnalysis\n", "\n", "# initialize heterogeneity features\n", "hetero_cols = ['AgeAtSale', 'MSZoning']\n", "ca = CausalAnalysis(\n", "    feature_inds=top_k_features + [\n", "        'HasFireplace',\n", "        'HasPorch',\n", "        'HasDeck',\n", "    ],\n", "    categorical=categorical + ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],\n", "    heterogeneity_inds=hetero_cols,\n", "    classification=False,\n", "    nuisance_models=\"automl\",\n", "    heterogeneity_model=\"linear\",\n", "    n_jobs=-1,\n", "    random_state=123,\n", "    upper_bound_on_cat_expansion=6\n", ")\n", "ca.fit(x_train, y_train)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>point</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>p_value</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>feature</th>\n", "      <th>feature_value</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>OverallQual</th>\n", "      <th>num</th>\n", "      <td>10103.815563</td>\n", "      <td>1675.148678</td>\n", "      <td>6.031593</td>\n", "      <td>1.623509e-09</td>\n", "      <td>6820.584485</td>\n", "      <td>13387.046640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GarageCars</th>\n", "      <th>num</th>\n", "      <td>13144.175268</td>\n", "      <td>2997.638920</td>\n", "      <td>4.384843</td>\n", "      <td>1.160696e-05</td>\n", "      <td>7268.910946</td>\n", "      <td>19019.439590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OverallCond</th>\n", "      <th>num</th>\n", "      <td>5645.799133</td>\n", "      <td>1457.913311</td>\n", "      <td>3.872520</td>\n", "      <td>1.077156e-04</td>\n", "      <td>2788.341551</td>\n", "      <td>8503.256716</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GrLivArea</th>\n", "      <th>num</th>\n", "      <td>53.680237</td>\n", "      <td>16.896738</td>\n", "      <td>3.176959</td>\n", "      <td>1.488283e-03</td>\n", "      <td>20.563239</td>\n", "      <td>86.797235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HasFireplace</th>\n", "      <th>1v0</th>\n", "      <td>4391.217178</td>\n", "      <td>1510.683165</td>\n", "      <td>2.906776</td>\n", "      <td>3.651749e-03</td>\n", "      <td>1430.332582</td>\n", "      <td>7352.101775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HasPorch</th>\n", "      <th>1v0</th>\n", "      <td>4702.903044</td>\n", "      <td>2279.139024</td>\n", "      <td>2.063456</td>\n", "      <td>3.906933e-02</td>\n", "      <td>235.872642</td>\n", "      <td>9169.933446</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AgeAtSale</th>\n", "      <th>num</th>\n", "      <td>-122.702114</td>\n", "      <td>110.966856</td>\n", "      <td>-1.105755</td>\n", "      <td>2.688327e-01</td>\n", "      <td>-340.193155</td>\n", "      <td>94.788926</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>1v0</th>\n", "      <td>1610.641783</td>\n", "      <td>1819.477098</td>\n", "      <td>0.885222</td>\n", "      <td>3.760367e-01</td>\n", "      <td>-1955.467800</td>\n", "      <td>5176.751367</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   point       stderr     zstat       p_value  \\\n", "feature      feature_value                                                      \n", "OverallQual  num            10103.815563  1675.148678  6.031593  1.623509e-09   \n", "GarageCars   num            13144.175268  2997.638920  4.384843  1.160696e-05   \n", "OverallCond  num             5645.799133  1457.913311  3.872520  1.077156e-04   \n", "GrLivArea    num               53.680237    16.896738  3.176959  1.488283e-03   \n", "HasFireplace 1v0             4391.217178  1510.683165  2.906776  3.651749e-03   \n", "HasPorch     1v0             4702.903044  2279.139024  2.063456  3.906933e-02   \n", "AgeAtSale    num             -122.702114   110.966856 -1.105755  2.688327e-01   \n", "HasDeck      1v0             1610.641783  1819.477098  0.885222  3.760367e-01   \n", "\n", "                               ci_lower      ci_upper  \n", "feature      feature_value                             \n", "OverallQual  num            6820.584485  13387.046640  \n", "GarageCars   num            7268.910946  19019.439590  \n", "OverallCond  num            2788.341551   8503.256716  \n", "GrLivArea    num              20.563239     86.797235  \n", "HasFireplace 1v0            1430.332582   7352.101775  \n", "HasPorch     1v0             235.872642   9169.933446  \n", "AgeAtSale    num            -340.193155     94.788926  \n", "HasDeck      1v0           -1955.467800   5176.751367  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# get global causal effect ordered by causal importance (pvalue)\n", "global_summ = ca.global_causal_effect(alpha=0.05)\n", "global_summ.sort_values(by=\"p_value\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The table above sorts the features by causal significance (p value). If we compare the causal summary table to the ordering of the SHAP feature importances plot, we notice that for the most part the top predictive features are also causally significant. For example, OverallQual is the most predictive feature according to SHAP, and is also the most causally signficant. However, in contrast, AgeAtSale is a good predictor, but not very causally significant. This could perhaps be explained by the idea that even though older houses are correlated with low quality characteristics, once you control for these characteristics, house age by itself is not causally significant.   "]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# helper function to plot error bar\n", "def errorbar(res):\n", "    xticks = res.index.get_level_values(0)\n", "    lowererr = res[\"point\"] - res[\"ci_lower\"]\n", "    uppererr = res[\"ci_upper\"] - res[\"point\"]\n", "    xticks = [\n", "        \"{}***\".format(t)\n", "        if p < 1e-6\n", "        else (\"{}**\".format(t) if p < 1e-3 else (\"{}*\".format(t) if p < 1e-2 else t))\n", "        for t, p in zip(xticks, res[\"p_value\"])\n", "    ]\n", "    plot_title = \"Direct Causal Effect of Each Feature with 95% Confidence Interval, \"\n", "    plt.figure(figsize=(15, 5))\n", "    plt.errorbar(\n", "        np.arange(len(xticks)),\n", "        res[\"point\"],\n", "        yerr=[lowererr, uppererr],\n", "        fmt=\"o\",\n", "        capsize=5,\n", "        capthick=1,\n", "        barsabove=True,\n", "    )\n", "    plt.xticks(np.arange(len(xticks)), xticks, rotation=45)\n", "    plt.title(plot_title)\n", "    plt.axhline(0, color=\"r\", linestyle=\"--\", alpha=0.5)\n", "    plt.ylabel(\"Average Treatment Effect\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["errorbar(global_summ)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We learn the **Average Treatment Effect (ATE)** for certain house characteristics (e.g. having a fireplace), assuming they are the treatment. The error bar above is ordered by **feature importance**, and the summary table above is ordered by **causal significance (p-value)**. Notice they are not in the exact same order. For example, the second most predictive feature, GrLivArea (total square footage), is only the fourth most causally significant feature."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Segmentation -- How do different type of houses respond differently to having a fireplace?\n", "From the analysis above, we learned the direct effect of a few different house amenities on housing price at an average level. However, the effect of these house amenities on house price may vary by features like home age and zoning classification. In the following section, we are going to use the presence of a fireplace as an example to learn how different type of houses may increase in value with the addition of a fireplace. "]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(12, 8))\n", "ca.plot_heterogeneity_tree(\n", "    x_test,\n", "    \"HasFireplace\",\n", "    max_depth=2,\n", "    min_impurity_decrease=1e-6,\n", "    min_samples_leaf = 5\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the global level, we know that the ATE of having a fireplace is 4.4k, which means on average that having a fireplace will raise the housing price by $4.4k. In the shallow tree above, we can see although overall fireplaces already have a positive effect on housing price, the effect is even more dramatic on houses older than 75 years old."]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Policy Analysis -- What is the best policy considering cost?\n", "To take a step further, we'd like to know the sub-population where the treatment effect will still be positive after taking cost into consideration. Assuming the average cost of adding a fireplace is $2,500, let us see what kind of houses have a housing price that will increase more than their cost.  "]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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\n", "text/plain": ["<Figure size 864x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(12, 8))\n", "ca.plot_policy_tree(\n", "    x_test,\n", "    \"HasFireplace\",\n", "    treatment_costs=2500,\n", "    max_depth=2,\n", "    min_samples_leaf = 5\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You could see if we follow the recommended policy above, on average, the housing price will increase by \\\\$2,356 compared with no fireplace added. Similarly, it will increase by \\\\$465 compared with adding a fireplace for every house. To be more detailed, we could also output the individualized policy. In the following table, we will only print the top five houses ordered by policy gains."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Treatment</th>\n", "      <th>Effect of treatment</th>\n", "      <th>Effect of treatment lower bound</th>\n", "      <th>Effect of treatment upper bound</th>\n", "      <th>MSSubClass</th>\n", "      <th>MSZoning</th>\n", "      <th>LotFrontage</th>\n", "      <th>Street</th>\n", "      <th>Alley</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>SaleType</th>\n", "      <th>SaleCondition</th>\n", "      <th>AgeAtSale</th>\n", "      <th>YearsSinceRemodel</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>HasPorch</th>\n", "      <th>Current treatment</th>\n", "      <th>HasFence</th>\n", "      <th>Intercept</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1350.0</th>\n", "      <td>1</td>\n", "      <td>12224.503178</td>\n", "      <td>3758.862937</td>\n", "      <td>20690.143420</td>\n", "      <td>70.0</td>\n", "      <td>RM</td>\n", "      <td>50.0</td>\n", "      <td>Pave</td>\n", "      <td>Pave</td>\n", "      <td>Reg</td>\n", "      <td>...</td>\n", "      <td>12.0</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>136.0</td>\n", "      <td>21.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1063.0</th>\n", "      <td>1</td>\n", "      <td>9574.109709</td>\n", "      <td>3261.378628</td>\n", "      <td>15886.840790</td>\n", "      <td>190.0</td>\n", "      <td>RM</td>\n", "      <td>85.0</td>\n", "      <td>Pave</td>\n", "      <td>Grvl</td>\n", "      <td>Reg</td>\n", "      <td>...</td>\n", "      <td>9.0</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>107.0</td>\n", "      <td>57.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243.0</th>\n", "      <td>1</td>\n", "      <td>9482.716831</td>\n", "      <td>3235.816161</td>\n", "      <td>15729.617501</td>\n", "      <td>50.0</td>\n", "      <td>RM</td>\n", "      <td>63.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>...</td>\n", "      <td>4.0</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>106.0</td>\n", "      <td>56.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199.0</th>\n", "      <td>1</td>\n", "      <td>8660.180927</td>\n", "      <td>2968.655603</td>\n", "      <td>14351.706250</td>\n", "      <td>75.0</td>\n", "      <td>RM</td>\n", "      <td>92.0</td>\n", "      <td>Pave</td>\n", "      <td>NA</td>\n", "      <td>Reg</td>\n", "      <td>...</td>\n", "      <td>7.0</td>\n", "      <td>WD</td>\n", "      <td>Abnorml</td>\n", "      <td>97.0</td>\n", "      <td>59.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>521.0</th>\n", "      <td>1</td>\n", "      <td>8618.608621</td>\n", "      <td>1186.794764</td>\n", "      <td>16050.422479</td>\n", "      <td>190.0</td>\n", "      <td>RL</td>\n", "      <td>60.0</td>\n", "      <td>Pave</td>\n", "      <td>Grvl</td>\n", "      <td>Reg</td>\n", "      <td>...</td>\n", "      <td>8.0</td>\n", "      <td>WD</td>\n", "      <td>Normal</td>\n", "      <td>108.0</td>\n", "      <td>8.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 68 columns</p>\n", "</div>"], "text/plain": ["       Treatment  Effect of treatment  Effect of treatment lower bound  \\\n", "Id                                                                       \n", "1350.0         1         12224.503178                      3758.862937   \n", "1063.0         1          9574.109709                      3261.378628   \n", "243.0          1          9482.716831                      3235.816161   \n", "199.0          1          8660.180927                      2968.655603   \n", "521.0          1          8618.608621                      1186.794764   \n", "\n", "        Effect of treatment upper bound  MSSubClass MSZoning  LotFrontage  \\\n", "Id                                                                          \n", "1350.0                     20690.143420        70.0       RM         50.0   \n", "1063.0                     15886.840790       190.0       RM         85.0   \n", "243.0                      15729.617501        50.0       RM         63.0   \n", "199.0                      14351.706250        75.0       RM         92.0   \n", "521.0                      16050.422479       190.0       RL         60.0   \n", "\n", "       Street Alley LotShape  ... MoSold SaleType SaleCondition AgeAtSale  \\\n", "Id                            ...                                           \n", "1350.0   Pave  Pave      Reg  ...   12.0       WD        Normal     136.0   \n", "1063.0   Pave  Grvl      Reg  ...    9.0       WD        Normal     107.0   \n", "243.0    Pave    NA      Reg  ...    4.0       WD        Normal     106.0   \n", "199.0    Pave    NA      Reg  ...    7.0       WD       Abnorml      97.0   \n", "521.0    Pave  Grvl      Reg  ...    8.0       WD        Normal     108.0   \n", "\n", "       YearsSinceRemodel HasDeck HasPorch Current treatment HasFence  \\\n", "Id                                                                     \n", "1350.0              21.0       0        1                 0        0   \n", "1063.0              57.0       0        1                 0        0   \n", "243.0               56.0       0        1                 0        0   \n", "199.0               59.0       0        1                 0        1   \n", "521.0                8.0       1        1                 0        0   \n", "\n", "        Intercept  \n", "Id                 \n", "1350.0          1  \n", "1063.0          1  \n", "243.0           1  \n", "199.0           1  \n", "521.0           1  \n", "\n", "[5 rows x 68 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["ca.individualized_policy(\n", "    x_test,\n", "    \"HasFireplace\",\n", "    n_rows=5,\n", "    treatment_costs=2500,\n", "    alpha=0.1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that here the `effect of treatment` is the treatment effect of selecting one of the discrete treatment values minus the cost. In the treament column, 1 corresponds to having a fireplace, and 0 corresponds to no fireplace."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### What If Analysis - How does the overall housing price change if every home had a fireplace?\n", "The causal analysis tool could also answer **what if** types of questions. For a given treatment, we'd also like to know the **counterfactuals** if we intervene it in a different way. In the example below, we will learn how the overall housing price changes if every house in Ames had a fireplace."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current average housing price on test set:  142705.43537414967\n", "Average housing price with one more room on test set:  147392.03061904767\n"]}], "source": ["whatif_df = (\n", "    x_test\n", "    .loc[\n", "        lambda df: df['HasFireplace'].eq(0)\n", "    ]\n", ")\n", "whatif_y = y_test.loc[whatif_df.index]\n", "\n", "cf = ca.whatif(\n", "    whatif_df, \n", "    whatif_df['HasFireplace'].add(1).clip(upper = 1), \n", "    'HasFireplace', \n", "    whatif_y)\n", "print(\"Current average housing price on test set: \", whatif_y.mean())\n", "print(\n", "    \"Average housing price with one more room on test set: \",\n", "    cf[\"point_estimate\"].mean(),\n", ")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Histogram of Housing price -- Current vs. One more room')"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYUAAAEWCAYAAACJ0YulAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjQuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/MnkTPAAAACXBIWXMAAAsTAAALEwEAmpwYAAAqYklEQVR4nO3de5gV1Z3u8e+bRgQVURRvQIRRjKOAii0oeImZqGgYyMR4RB2NlwxDFBxM4hkmYwwmjpOoSYyjhhjlEDMaHcxoiIPxkoQY0hhpJi2KV1QCLUQBQURFQX7nj6oui83u7mrYTXfj+3mefrpq1VpVa+2qXb9dt1WKCMzMzAA+1tYVMDOz9sNBwczMMg4KZmaWcVAwM7OMg4KZmWUcFMzMLNMhgoKkBZI+2db1aEuS/k7SEklrJR3R1vVpIOlBSV9o63oASJoi6ettXQ+zjqzNg4KkRZI+XZJ2vqTZDeMRcWhEzGpmPn0lhaROrVTVtnY9MD4idomIP5VOTNt+YEnaZEn/2ZqViohTI+InrbmMoiJiXER8q63rASDpbEm1aRBflgbPY9u6XrD592sbL3uYpN9IekvSm5J+KemQtqiLldfmQaGjaAfBZn9gQRvXod2SVNXWdWgg6cvADcA1wN7Ax4FbgNFbMK/Ntrt2sC1uEUnHAA8DvwD2A/oBTwJ/kPRXbVm3ltraddCu12FEtOkfsAj4dEna+cDscnmAIUAtsAZ4Dfhemr4YCGBt+ncMSdC7Avgz8DpwB9A9N9/z0mkrga+XLGcycC/wn+myvpguew6wGlgG3AR0zs0vgIuBF4G3gG8BB6Rl1gD/lc9f0uaydQV2TNsTwNvAS42UD+DAkrTJwH/mxocBc4E30//DGlsP+bJAl/RzWJm2fS6wdzptFvDF/HojOapZBbwCnJqbZz/gsfSzeRS4OV+/krp/EqgHvgasSOt3Tm76NOCHwMz0c/l0mnZ1Ls9ooC797F8CRqTp3YHb03X4KnA1UFWh7bl7ur7OaCJPaT0/CdSXrIt/BuYD7wEHpuv3IpLt/LE034XAs+ln/RCwf8n2MI5kW1yVftYC/hpYB3yQ1nN1mfqNAWpL0i4DZqTDpwHPpOvxVeCrBT+b3wO3lEl/ELijZL1/heR7sAy4IJd3x3T7Wkzy/Z8CdG1keecDfwC+T7LdvkzyHTgfWJLO/wsl6+4OYDnJ9/AK4GNl5vVGus1saV0ayje1vEb3XUDfdP1ekLZjVbquj0q3mdXATVu8DVfii7CVX6JFtCwozAHOTYd3AY4u+aA65cpdCCwE/irN+9/AT9Nph5B8KY4FOqcrdz2bBoX1wGfTFdQVOBI4GuiULu9ZYGLJF3EGsCtwKMkX+tfp8ruTfJG+0Mjn0Ghdc/M+sInPscmgAPRIN55z0/qflY7vUW49lJT9R+CXwE5AVfo57JpOm8WmQWE98A9pvi8BSwHl1t316ed9LMnOuqmgsAH4HsmX7wSSnf8n0unTSILb8HT9dCG3syUJ4G8CJ6XTewEHp9PuB34E7AzsBTwB/GOFtucRab07NZEnq2euraVBoQ7oQ7Ld9U3X7x1pnbuSbJcLSXbynUh2IDUl28MDwG4kRyrL+TAonk/u+1WmfjuR7PD759LmAmPS4WXAcenw7sDgAp/LTiSB6MQy0y4AlpWs928CO5AEoHeA3dPpN5B8x3oA3Ui2y39vZJnnp/O6gGR7vJpkB35zuk2dnLZzlzT/HSRHMd3Sz/wF4KKSeU1IP++uW1iXfPmmltfUvqthe5hCst2fTBLo7yfZnnuRBJITtmgbrsQXYSu/RItIf7Hk/t6h8aDwGHAVsGfJfBo+qHxQ+DVwcW78EyQ7rU7AlcDPSjba99k0KDzWTN0nAveVfBGH58bnAf+cG/8ucEMj82q0rrl5NxcU1pR8juv4cMd+LvBESZk5wPmln3Gu/Q1lLwRqgEFlljuLTYPCwpLPNIB9SHZMG4CdctP/k+aDws65tP8Cvp4OTyP9dZmbPo0Pg8KPgO+Xme/eJMG6ay7tLOC3FdqezwH+0kyerJ65tpYGhQvLbNt/lUt7kHQHko5/jOR7s39uezi25LOblFtPjQaF3Lq5Mh3uT7Lz3CkdX0zyQ2HXFnwuvdM6HVxm2ghgfe6zeJdNv8evk/wYE8kPgwNy044BXmlkmecDL+bGB6Z12DuXthI4nCRovAcckpv2j8Cs3LwW56ZtSV3y5ZtbXlP7robtoVdJO87Mjf+c3A/Wlvy1l2sKn42I3Rr+SE7BNOYi4CDgOUlzJY1sIu9+JIdfDf5M8qHunU5b0jAhIt4h+WDzluRHJB0k6QFJf5G0huSc8Z4lZV7LDb9bZnyXLahrUYNLPsdvNzH/hmX0KjDfn5Kcnrhb0lJJ10raoZG8f2kYSD9TSNq8H/BGLg1KPt8yVkXE2yX13a9g+T4kp4xK7U/yC3SZpNWSVpMEkL3KzSS9UNzw9/H0DqeG8a+VKbIS2LMC54zLtS2ftj/wg1wb3iDZUeXX519yw+/Q+LZXzl0kwRLgbOD+3Lo7neQX/J8l/S69VtCcVcBGYN8y0/YlOUXYYGVEbChT954kPzTm5dr9qzS9MaXfPyKi3HdyT5Ij2NLvYP7zzH/+W1KXfPnmlldkf7Cl+5omtZegUFhEvBgRZ5F8ib8D3CtpZ5LIWWopyZenQcOv1ddIDoF7N0yQ1BXYo3RxJeM/BJ4jOazeleR8t7a8NYXr2hrzb1jGq+nw2yQbeYN9GgYiYn1EXBURh5Cckx1Jcj2mJZYBPSTll9GnmTK7p+s2X9+lufFy67zBEpLrOeXS3yM50mwIoLtGxKHlZhLJ3V4Nf4sjucOpYfyaMkXmkByhfbaJujX6WecX3UzaEpJTXrvl/rpGRE0Ty21q3qUeJgluh5MEh7uywhFzI2I0yXfwfpKjkKYXmAT3OcAZZSb/H5Jfxs1ZQbKzOzTX5u4RsUU7vzLzXs/m38FXc+NRkr+ldSkt39TyWnt/0KgOFxQk/b2knhGxkeQUCSTnKpeT/BLJ38XwM+AySf0k7ULyy/6e9FfIvcDfprfIdSY5JdXcDr4bySmatZIOJjlnXilN1bUSZgIHpbdKdpJ0Jsl1lQfS6XXAGEk7SKoGPt9QUNKJkgamd/isIdmYP2jJwiPizyQ3CEyW1Dn9dfm3BYpeleY/jiQYTS+4yNuBCyT9jaSPSeol6eCIWEayw/uupF3TaQdIOqEl7WlMRLxJcmryZkmflbRT+pmeKunaNFsdcJqkHpL2ITkN2VJTgH+RdCiApO6Syu1wy3kN6J1u9421o+E7ch3JOfNH0uV0lnSOpO4RsZ5keyi6LUwCviDpUkndJO0u6WqS0y5XNVc4/c7/GPi+pL3S+vSSdErB5Tc17w9Igtu/pXXbH/gyyWm0itelwPJae3/QqA4XFEjOPy6QtBb4AcnFr3Xpoe2/kdzetlrS0cBUklMfj5HcCbOO5EIPEbEgHb6b5FfsWyTnLt9rYtlfJTmUfotkg7ingu1qtK6VEBErSXaqXyE5xfF/gZER0XDY/nWSX9arSL6gd+WK70Oyg1hDcnH9dzTyZWnGOSQ7gJUkF/3uoenP+y9pfZYCdwLjIuK5IguKiCdILjB+n+SC8+/48JfXeSSH7s+k87+X8qc1tkhEfI/kC34FyY+VJcB4kl/VkKznJ0muHTzMFmxHEXEfyZHy3empzKeBUwsW/w3J7c1/kbSiiXx3kdzVNb1kZ3QusChd7jjg7wHS02trJX28kTrPBk4BPkfynfszcATJtY8XC9b9n0kuwD6eLv9RkvPtlTCB5CjuZZK76O4i+V62Vl2aWl6r7g+a0nBXyEdeGo1Xk5waeqWNq/ORIOke4LmI+EaZaZ8kuQjdu3SambWejnikUDGS/jY9vN+Z5FbJp0h+vVkrkHRUeqrmY5JGkDxHcH8bV8vMcj7SQYFkp7Q0/etPcirKh06tZx+SW1jXAjcCX4oyXXaYWdvx6SMzM8t81I8UzMwsp112yrTnnntG375927oaZmYdxrx581ZERFMPzxVSKCikFwV/QPJo9m0R8e1G8h0FPE7yuPW9LSmb17dvX2pra4u1wMzMkFTaY8EWafb0UfrA0s0k90AfApylMv2fp/m+Q9IdQovKmplZ+1DkmsIQkk7OXo6I90ke9hpdJt8Ekk6YXt+CsmZm1g4UCQq92LQjp3pKOlGT1Av4O5JH71tUNjePsUreVFW7fPnyAtUyM7NKK3JNoVx/QKX3sd5A0kX0B9Im2YuUTRIjbgVuBaiurvZ9smZbaP369dTX17Nu3bq2roq1gi5dutC7d2922KGxjoq3TpGgUM+mvVn2ZtOeKgGqSfpggaRL2NMkbShY1swqqL6+nm7dutG3b19KfqRZBxcRrFy5kvr6evr169cqyyhy+mgu0D/tra8zyav6ZuQzRES/iOgbEX1JOhe7OCLuL1LWzCpr3bp17LHHHg4I2yFJ7LHHHq16FNjskUJEbJA0nuSuoipgakQskDQunV56HaHZspWpupk1xgFh+9Xa67bQcwoRMZOkP/58WtlgEBHnN1fWzMzap3b5RLOZVc5F0+ZWdH63n39Uk9Mvu+wy9t9/fyZOnAjAKaecQp8+fbjtttsA+MpXvkKvXr048MADeeaZZ5g0aRL3338/Bx10EIcckjzG9MlPfpLrr7+e6urqJpd1+eWXM3PmTE477TQOOOAAdtppJ847r6UvBdzctGnTqK2t5aabbtrqeXU0DgrbgUp/6YtqbudgH03Dhg1j+vTpTJw4kY0bN7JixQrWrFmTTa+pqeGGG25g6NChjBo1CoD777+fkSNHZkGhqB/96EcsX76cHXfcsdE8GzZsoFMn7+qKcod4ZlZRw4cPp6YmeVX0ggULGDBgAN26dWPVqlW89957PPvssxxxxBFMmzaN8ePHU1NTw4wZM7j88ss5/PDDeemllwCYPn06Q4YM4aCDDuL3v//9ZssZNWoUb7/9NkOHDuWee+5h8uTJXH/99UBypPG1r32NE044gR/84AfMmzePE044gSOPPJJTTjmFZcuWZfkmTpzIsGHDGDBgAE888cRmy/nlL3/J0KFDOeKII/j0pz/Na68lr0leu3YtF1xwAQMHDmTQoEH8/Oc/B+Dhhx/mmGOOYfDgwZxxxhmsXbu28h9yK3JQMLOK2m+//ejUqROLFy+mpqaGY445hqFDhzJnzhxqa2sZNGgQnTt/+HroYcOGMWrUKK677jrq6uo44IADgOQX/hNPPMENN9zAVVdt/grnGTNm0LVrV+rq6jjzzDM3m7569Wp+97vfcemllzJhwgTuvfde5s2bx4UXXsi//uu/ZvnefvttampquOWWW7jwwgs3m8+xxx7L448/zp/+9CfGjBnDtdcmr9r+1re+Rffu3XnqqaeYP38+n/rUp1ixYgVXX301jz76KP/7v/9LdXU13/ve97b6M92WfExlZhXXcLRQU1PDl7/8ZV599VVqamro3r07w4YNKzSPz33ucwAceeSRLFq0qMV1aAgUzz//PE8//TQnnXQSAB988AH77vvhK7nPOussAI4//njWrFnD6tWrN5lPfX09Z555JsuWLeP999/Png949NFHufvuu7N8u+++Ow888ADPPPMMw4cPB+D999/nmGOOaXHd25KDgplV3LBhw6ipqeGpp55iwIAB9OnTh+9+97vsuuuuZX+Nl9NwnaCqqooNGza0uA4777wzkDzwdeihhzJnzpyy+Upv8SwdnzBhAl/+8pcZNWoUs2bNYvLkydl8S/NGBCeddBI/+9nPWlzf9sKnj8ys4oYPH84DDzxAjx49qKqqokePHqxevZo5c+aU/eXcrVs33nrrrVapyyc+8QmWL1+eBYX169ezYMGHj0vdc889AMyePZvu3bvTvXv3Tcq/+eab9OqVdNn2k5/8JEs/+eSTN7k7adWqVRx99NH84Q9/YOHChQC88847vPDCC63SrtbiIwWz7Vxb3CU2cOBAVqxYwdlnn71J2tq1a9lzzz03yz9mzBj+4R/+gRtvvJF77723onXp3Lkz9957L5deeilvvvkmGzZsYOLEiRx66KFActpn2LBhrFmzhqlTp25WfvLkyZxxxhn06tWLo48+mldeeQWAK664gksuuYQBAwZQVVXFN77xDT73uc8xbdo0zjrrLN577z0Arr76ag466KCKtqk1tct3NFdXV4dfslOcb0m1vGeffZa//uu/butqdAhFn4dob8qtY0nzImKrG+LTR2ZmlvHpIzP7yJo1a1ZbV6Hd8ZGCmZllHBTMzCzjoGBmZhkHBTMzy/hCs9n27q7N+wXaKmff0+Tkbdl1dhHXXHMNX/va18pOmz59OldeeSX77LMP1113HXfccQc33njjVi9z0aJFjBw5kqeffnqr57Wt+UjBzCqqoYsLIOs6O/8EcU1NDcOHD2fUqFFMmjQJSLrOfuaZZ1qlPtdcc02j026//XZuueUWfvvb31JdXV02IGxJFxsdmYOCmVVUa3advW7duqy76iOOOILf/va3ANm8GowcOZJZs2YxadIk3n33XQ4//HDOOeecTer5zW9+k9mzZzNu3Dguv/xyZs2axciRI4HkKeaxY8dy8sknc95557F8+XJOP/10jjrqKI466ij+8Ic/ZPnOPfdcPvWpT9G/f39+/OMfb/Z5LFq0iOOOO47BgwczePDg7LMBuPbaaxk4cCCHHXZYFiBfeuklRowYwZFHHslxxx3Hc889V5H1UlSh00eSRgA/IHnP8m0R8e2S6aOBbwEbgQ3AxIiYnU5bBLwFfABsqMQTd2bWfpXrOvvVV19lzpw5dO/evdGus0eOHMnnP//5LL2h6+yZM2dy1VVX8eijj3LzzTcD8NRTT/Hcc89x8sknN9m30Le//W1uuukm6urqNpt25ZVX8pvf/CY7TVX6zMK8efOYPXs2Xbt25eyzz+ayyy7j2GOPZfHixZxyyik8++yzAMyfP5/HH3+ct99+myOOOILPfOYzm8xnr7324pFHHqFLly68+OKLnHXWWdTW1vLggw9y//3388c//pGddtqJN954A4CxY8cyZcoU+vfvzx//+EcuvvhifvOb37RoHWyNZoOCpCrgZuAkoB6YK2lGROSP9X4NzIiIkDQI+C/g4Nz0EyNiRQXrbSUmvHbFtl/oXbs1e37ZPppaq+vs2bNnM2HCBAAOPvhg9t9//1brcG7UqFF07doVSLrJzp/eWrNmTdaB3+jRo+natStdu3blxBNP5IknnuDwww/P8q5fv57x48dTV1dHVVVVVt9HH32UCy64gJ122gmAHj16sHbtWmpqajjjjDOy8g19KG0rRY4UhgALI+JlAEl3A6OB7BOKiPyrhXYG2l+HSma2zbRW19mN9dXWqVMnNm7cmI2vW7duK1vwYdfbkFwbmTNnThYk8prrevv73/8+e++9N08++SQbN26kS5cuQPmutzdu3Mhuu+1W9shmWylyTaEXsCQ3Xp+mbULS30l6DvgfIL/WA3hY0jxJYxtbiKSxkmol1S5fvrxY7c2sXWqtrrOPP/547rzzTgBeeOEFFi9ezCc+8Qn69u1LXV0dGzduZMmSJZu8VnOHHXZg/fr1W9We0m6y8zvtX/ziF6xbt46VK1cya9Ysjjpq044i33zzTfbdd18+9rGP8dOf/pQPPvggm+fUqVN55513AHjjjTfYdddd6devH9OnTweSwPHkk09uVd1bqsiRgsqkbRauI+I+4D5Jx5NcX/h0Oml4RCyVtBfwiKTnIuKxMuVvBW6FpJfUog0ws2a0wSm+1uo6++KLL2bcuHEMHDiQTp06MW3aNHbccUeGDx9Ov379GDhwIAMGDGDw4MFZmbFjxzJo0CAGDx6cBZSWuvHGG7nkkksYNGgQGzZs4Pjjj2fKlCkADBkyhM985jMsXryYr3/96+y3336bvCnu4osv5vTTT2f69OmceOKJ2RHIiBEjqKuro7q6ms6dO3PaaadxzTXXcOedd/KlL32Jq6++mvXr1zNmzBgOO+ywLar3lmi262xJxwCTI+KUdPxfACLi35so8wpwVOl1BEmTgbURcX1Ty3TX2S1z0bS5bXJN4fA+u/maQjvkrrO3ncmTJ7PLLrvw1a9+dZsut627zp4L9JfUT1JnYAwwo6QyByo9OSZpMNAZWClpZ0nd0vSdgZOBjvc0h5nZR0Szp48iYoOk8cBDJLekTo2IBZLGpdOnAKcD50laD7wLnJneibQ3ySmlhmXdFRG/aqW2mJltUw3va96eFHpOISJmAjNL0qbkhr8DfKdMuZeBbXcyzMyA8ne22Pahtd+W6SeazbYzXbp0YeXKla2+87BtLyJYuXJldltra3CHeGbbmd69e1NfX49v7d4+denShd69e7fa/B0UzLYzO+ywA/369WvralgH5dNHZmaWcVAwM7OMg4KZmWUcFMzMLOOgYGZmGQcFMzPLOCiYmVnGQcHMzDIOCmZmlnFQMDOzjIOCmZllHBTMzCzjoGBmZhkHBTMzyxQKCpJGSHpe0kJJk8pMHy1pvqQ6SbWSji1a1szM2o9mg4KkKuBm4FTgEOAsSYeUZPs1cFhEHA5cCNzWgrJmZtZOFDlSGAIsjIiXI+J94G5gdD5DRKyND9/9tzMQRcuamVn7USQo9AKW5Mbr07RNSPo7Sc8B/0NytFC4rJmZtQ9FgoLKpG32RvCIuC8iDgY+C3yrJWUBJI1Nr0fU+t2yZmZto0hQqAf65MZ7A0sbyxwRjwEHSNqzJWUj4taIqI6I6p49exaolpmZVVqRoDAX6C+pn6TOwBhgRj6DpAMlKR0eDHQGVhYpa2Zm7Uen5jJExAZJ44GHgCpgakQskDQunT4FOB04T9J64F3gzPTCc9myrdQWMzPbSs0GBYCImAnMLEmbkhv+DvCdomXNzKx98hPNZmaWcVAwM7OMg4KZmWUcFMzMLOOgYGZmGQcFMzPLOCiYmVnGQcHMzDIOCmZmlnFQMDOzjIOCmZllHBTMzCzjoGBmZhkHBTMzyzgomJlZxkHBzMwyDgpmZpZxUDAzs0yhoCBphKTnJS2UNKnM9HMkzU//aiQdlpu2SNJTkuok1Vay8mZmVlnNvqNZUhVwM3ASUA/MlTQjIp7JZXsFOCEiVkk6FbgVGJqbfmJErKhgvc3MrBUUOVIYAiyMiJcj4n3gbmB0PkNE1ETEqnT0caB3ZatpZmbbQpGg0AtYkhuvT9MacxHwYG48gIclzZM0trFCksZKqpVUu3z58gLVMjOzSmv29BGgMmlRNqN0IklQODaXPDwilkraC3hE0nMR8dhmM4y4leS0E9XV1WXnb2ZmravIkUI90Cc33htYWppJ0iDgNmB0RKxsSI+Ipen/14H7SE5HmZlZO1QkKMwF+kvqJ6kzMAaYkc8g6ePAfwPnRsQLufSdJXVrGAZOBp6uVOXNzKyymj19FBEbJI0HHgKqgKkRsUDSuHT6FOBKYA/gFkkAGyKiGtgbuC9N6wTcFRG/apWWmJnZVityTYGImAnMLEmbkhv+IvDFMuVeBg4rTTczs/bJTzSbmVnGQcHMzDIOCmZmlnFQMDOzjIOCmZllHBTMzCzjoGBmZhkHBTMzyxR6eM1a4K4zt/kiJ7y2epsv08y2Tz5SMDOzjIOCmZllHBTMzCzjoGBmZhkHBTMzyzgomJlZxkHBzMwyDgpmZpZxUDAzs0yhoCBphKTnJS2UNKnM9HMkzU//aiQdVrSsmZm1H812cyGpCrgZOAmoB+ZKmhERz+SyvQKcEBGrJJ0K3AoMLVjWOqi6Jav5j2lz22TZt59/VJss12x7V+RIYQiwMCJejoj3gbuB0fkMEVETEavS0ceB3kXLmplZ+1EkKPQCluTG69O0xlwEPNjSspLGSqqVVLt8+fIC1TIzs0orEhRUJi3KZpROJAkK/9zSshFxa0RUR0R1z549C1TLzMwqrUjX2fVAn9x4b2BpaSZJg4DbgFMjYmVLypqZWftQ5EhhLtBfUj9JnYExwIx8BkkfB/4bODciXmhJWTMzaz+aPVKIiA2SxgMPAVXA1IhYIGlcOn0KcCWwB3CLJIAN6amgsmVbqS1mZraVCr15LSJmAjNL0qbkhr8IfLFoWTMza5/8RLOZmWUcFMzMLOOgYGZmGQcFMzPLOCiYmVnGQcHMzDKFbkm15l2U9hY64bXVbVsRM7Ot4CMFMzPLOCiYmVnGQcHMzDIOCmZmlnFQMDOzjIOCmZllHBTMzCzj5xRsq0x47Yo2WvJDbbRcs+2bjxTMzCzjoGBmZhkHBTMzyxQKCpJGSHpe0kJJk8pMP1jSHEnvSfpqybRFkp6SVCeptlIVNzOzymv2QrOkKuBm4CSgHpgraUZEPJPL9gZwKfDZRmZzYkSs2Mq6mplZKytypDAEWBgRL0fE+8DdwOh8hoh4PSLmAutboY5mZraNFAkKvYAlufH6NK2oAB6WNE/S2MYySRorqVZS7fLly1swezMzq5QiQUFl0qIFyxgeEYOBU4FLJB1fLlNE3BoR1RFR3bNnzxbM3szMKqVIUKgH+uTGewNLiy4gIpam/18H7iM5HWVmZu1QkaAwF+gvqZ+kzsAYYEaRmUvaWVK3hmHgZODpLa2smZm1rmbvPoqIDZLGk/QrUAVMjYgFksal06dI2geoBXYFNkqaCBwC7AncJ6lhWXdFxK9apSVmZrbVCvV9FBEzgZklaVNyw38hOa1Uag1w2NZU0MzMth0/0WxmZhkHBTMzyzgomJlZxkHBzMwyDgpmZpZxUDAzs4yDgpmZZRwUzMws46BgZmYZBwUzM8s4KJiZWcZBwczMMg4KZmaWcVAwM7OMg4KZmWUcFMzMLFPoJTsd0l1nbtPFTXht9TZdnplZayh0pCBphKTnJS2UNKnM9IMlzZH0nqSvtqSsmZm1H80GBUlVwM3AqSTvXT5L0iEl2d4ALgWu34KyZmbWThQ5UhgCLIyIlyPifeBuYHQ+Q0S8HhFzgfUtLWtmZu1HkaDQC1iSG69P04ooXFbSWEm1kmqXL19ecPZmZlZJRYKCyqRFwfkXLhsRt0ZEdURU9+zZs+DszcyskooEhXqgT268N7C04Py3pqyZmW1jRYLCXKC/pH6SOgNjgBkF5781Zc3MbBtr9jmFiNggaTzwEFAFTI2IBZLGpdOnSNoHqAV2BTZKmggcEhFrypVtpbaYmdlWKvTwWkTMBGaWpE3JDf+F5NRQobJmZtY+uZsLMzPLOCiYmVnGQcHMzDIOCmZmlnFQMDOzjIOCmZllHBTMzCzjoGBmZhkHBTMzyzgomJlZxkHBzMwyhfo+6kgumjYXgAmvrW7biliraljP29rt5x/VJss121Z8pGBmZhkHBTMzyzgomJlZZru7pmAfDRNeu6KNlvxQGy3XbNvwkYKZmWUcFMzMLFMoKEgaIel5SQslTSozXZJuTKfPlzQ4N22RpKck1UmqrWTlzcysspq9piCpCrgZOAmoB+ZKmhERz+SynQr0T/+GAj9M/zc4MSJWVKzWZmbWKoocKQwBFkbEyxHxPnA3MLokz2jgjkg8Duwmad8K19XMzFpZkaDQC1iSG69P04rmCeBhSfMkjW1sIZLGSqqVVLt8+fIC1TIzs0orEhRUJi1akGd4RAwmOcV0iaTjyy0kIm6NiOqIqO7Zs2eBapmZWaUVCQr1QJ/ceG9gadE8EdHw/3XgPpLTUWZm1g4VCQpzgf6S+knqDIwBZpTkmQGcl96FdDTwZkQsk7SzpG4AknYGTgaermD9zcysgpq9+ygiNkgaT/IoZxUwNSIWSBqXTp8CzAROAxYC7wAXpMX3Bu6T1LCsuyLiVxVvhZmZVUShbi4iYibJjj+fNiU3HMAlZcq9DBy2lXU0M7NtxE80m5lZxkHBzMwyDgpmZpZx19lmLdBWrwFtS34F6UeLjxTMzCzjoGBmZhmfPjJrgbZ741tb8tvmPkp8pGBmZhkHBTMzyzgomJlZxtcUzKxpd53ZNss9+562We5HnI8UzMws46BgZmYZBwUzM8v4moKZNaluyeo2We7hbbJU4K4z26TN/7H31e2iSxEfKZiZWcZBwczMMg4KZmaWKRQUJI2Q9LykhZImlZkuSTem0+dLGly0rJmZtR/NBgVJVcDNwKnAIcBZkg4pyXYq0D/9Gwv8sAVlzcysnShypDAEWBgRL0fE+8DdwOiSPKOBOyLxOLCbpH0LljUzs3aiyC2pvYAlufF6YGiBPL0KlgVA0liSowyAtZKeL1C3Rk3dmsKtY09gRVtXohVsr+0Ct61tTdKWlGr/7WrUw0y9oMkMzbVt/0rUokhQKLdmomCeImWTxIhbgVsL1KdDklQbEdVtXY9K217bBW5bR7S9tgu2XduKBIV6oE9uvDewtGCezgXKmplZO1HkmsJcoL+kfpI6A2OAGSV5ZgDnpXchHQ28GRHLCpY1M7N2otkjhYjYIGk8yTv5qoCpEbFA0rh0+hRgJnAasBB4B7igqbKt0pL2b3s9Nba9tgvcto5oe20XbKO2KaLsKX4zM/sI8hPNZmaWcVAwM7OMg0IzJE2V9Lqkp3NpPSQ9IunF9P/uuWn/knbp8bykU3LpR0p6Kp12oySl6TtKuidN/6OkvrkyX0iX8aKkL2yDdk2W9KqkuvTvtI7WrnT+fST9VtKzkhZI+qc0vUOvtyba1eHXm6Qukp6Q9GTatqvS9I6+zhprV/tdZxHhvyb+gOOBwcDTubRrgUnp8CTgO+nwIcCTwI5AP+AloCqd9gRwDMmzGw8Cp6bpFwNT0uExwD3pcA/g5fT/7unw7q3crsnAV8vk7TDtSpexLzA4He4GvJC2oUOvtyba1eHXW1qPXdLhHYA/AkdvB+ussXa123XmI4VmRMRjwBslyaOBn6TDPwE+m0u/OyLei4hXSO7GGqKky49dI2JOJGvrjpIyDfO6F/ib9BfAKcAjEfFGRKwCHgFGtHK7GtNh2gUQEcsi4n/T4beAZ0meru/Q662JdjWmQ7QrbU9ExNp0dIf0L+j466yxdjWmzdvloLBl9o7kOQzS/3ul6U1191FfJn2TMhGxAXgT2KOJebW28Up6up2aO1TvsO1KD6WPIPmFtt2st5J2wXaw3iRVSaoDXifZmW0X66yRdkE7XWcOCpW1Jd19bHUXIRX0Q+AAkjchLgO+m6Z3yHZJ2gX4OTAxItY0lbWROrXL9pVp13ax3iLig4g4nKTngyGSBjSRvcO0rZF2tdt15qCwZV5LD+dI/7+epjfW3Ud9OlyavkkZSZ2A7iSndYp0L1JREfFaugFvBH5M0svtJnUsqUu7bZekHUh2nHdGxH+nyR1+vZVr1/a03gAiYjUwi+RUR4dfZw3y7WrX66wSF1O29z+gL5tekL2OTS9+XZsOH8qmF4le5sOLRHNJLjA1XCQ6LU2/hE0vEv1XfHiR6BWSC0S7p8M9Wrld++aGLyM5t9kR2yWSc643lKR36PXWRLs6/HoDegK7pcNdgd8DI7eDddZYu9rtOmvVnen28Af8jOTwbj1J5L2I5Hzdr4EX0/89cvn/leSOgedJ7w5I06uBp9NpN/Hh0+RdgOkkF5SeAP4qV+bCNH0hcME2aNdPgaeA+SR9VO3b0dqVzv9YksPk+UBd+ndaR19vTbSrw683YBDwp7QNTwNXpukdfZ011q52u87czYWZmWV8TcHMzDIOCmZmlnFQMDOzjIOCmZllHBTMzCzjoGAdhqS1JePnS7qpwsuYKWm3Csynr6R30x4wn5E0RdJm3zdJ+0m6d2uXZ1YpDgpmORFxWiRPnlbCS5F0bzCIpPfLz+YnSuoUEUsj4vMVWp7ZVnNQsO2CpP0l/TrtYOzXkj6epk+T9PlcvrXp/30lPZb+kn9a0nFp+iJJe6a/9J+V9OO0H/yHJXVN8xyVLmeOpOuUeydFOZF0UlYDHJge3UyX9Evg4XQ5T6fzrZJ0fdpn/nxJE9L0IyX9TtI8SQ81dPtg1hocFKwj6aoPX0pSB3wzN+0m4I6IGATcCdzYzLzOBh5Kf8kfRvJ0cKn+wM0RcSiwGjg9Tf9/wLiIOAb4oLlKS9oJ+BuSJ1gh6RP/CxHxqZKsY0m6NjiioR1pX0f/AXw+Io4EpgL/1twyzbZUp7augFkLvJvuxIHkmgLJo/+Q7Gg/lw7/lOTlLE2ZC0xNd7r3R0RdmTyv5NLnAX3T6w3dIqImTb+LpC+bcg5Ig1cAv4iIB9M6PxIR5d5l8WmSPmw2AETEG2mPmgOAR9IXbVWRdE9i1iocFGx71dB/ywbSI+L0xSOdIXnJkKTjgc8AP5V0XUTcUTKP93LDH5B0aFauO+LGvJQPYjlvN5JfbN61sYAF6VGJWavz6SPbXtSQ9BAJcA4wOx1eBByZDo8mefMVkvYHXo+IHwO3k7yatFmRvMHqLUlHp0ljmsrfQg8D49Luj5HUg6RTtJ6SjknTdpB0aAWXabYJBwXbXlwKXCBpPnAu8E9p+o+BEyQ9AQzlw1/pnwTqJP2J5FrBD1qwrIuAWyXNIfkl/+bWVx+A24DFwHxJTwJnR8T7wOeB76RpdcCwCi3PbDPuJdWshSTtEul7dyVNIun2+J+aKWbWIfiaglnLfUbSv5B8f/4MnN+21TGrHB8pmJlZxtcUzMws46BgZmYZBwUzM8s4KJiZWcZBwczMMv8fDEbIQ+Btk3MAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# distribution comparison\n", "plt.hist(cf.point_estimate, label=\"With fireplace\", alpha=0.7, weights=np.ones(len(whatif_y)) / len(whatif_y))\n", "plt.hist(whatif_y, label=\"Without fireplace\", alpha=0.7, weights=np.ones(len(whatif_y)) / len(whatif_y))\n", "plt.legend()\n", "plt.xlabel(\"Housing Price\")\n", "plt.title(\"Histogram of Housing price -- Current vs. One more room\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the summary table we could see overall if we add a fireplace to houses without fireplaces in the test set, the average housing price for those houses will increase by about \\\\$5k. And the histrogram shows a comparison between the current housing price distribution and the counterfactuals ditribution if we added a fireplace to the fireplace-less houses in the test set."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cohort Analysis -- What is the causal effect on a new dataset?\n", "The CausalAnalysis class can also help us learn the global and local causal effect of a new dataset given a trained model. From the two tables below, you can see the global effect on the test set is similar to that of the training set. And calculating the local effect gives you the heterogeneous treatment effect for each observation."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>point</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>p_value</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>feature</th>\n", "      <th>feature_value</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>OverallQual</th>\n", "      <th>num</th>\n", "      <td>10207.265524</td>\n", "      <td>1694.108274</td>\n", "      <td>6.025155</td>\n", "      <td>1.689473e-09</td>\n", "      <td>6886.874322</td>\n", "      <td>13527.656727</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GrLivArea</th>\n", "      <th>num</th>\n", "      <td>53.572155</td>\n", "      <td>16.896406</td>\n", "      <td>3.170624</td>\n", "      <td>1.521117e-03</td>\n", "      <td>20.455808</td>\n", "      <td>86.688502</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GarageCars</th>\n", "      <th>num</th>\n", "      <td>13215.235723</td>\n", "      <td>3030.831642</td>\n", "      <td>4.360267</td>\n", "      <td>1.299037e-05</td>\n", "      <td>7274.914862</td>\n", "      <td>19155.556584</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AgeAtSale</th>\n", "      <th>num</th>\n", "      <td>-139.794231</td>\n", "      <td>110.416091</td>\n", "      <td>-1.266068</td>\n", "      <td>2.054889e-01</td>\n", "      <td>-356.205793</td>\n", "      <td>76.617332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OverallCond</th>\n", "      <th>num</th>\n", "      <td>5723.914340</td>\n", "      <td>1462.849911</td>\n", "      <td>3.912851</td>\n", "      <td>9.121266e-05</td>\n", "      <td>2856.781200</td>\n", "      <td>8591.047479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HasFireplace</th>\n", "      <th>1v0</th>\n", "      <td>4390.835030</td>\n", "      <td>1523.103093</td>\n", "      <td>2.882822</td>\n", "      <td>3.941301e-03</td>\n", "      <td>1405.607823</td>\n", "      <td>7376.062237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HasPorch</th>\n", "      <th>1v0</th>\n", "      <td>4713.175859</td>\n", "      <td>2303.096997</td>\n", "      <td>2.046451</td>\n", "      <td>4.071199e-02</td>\n", "      <td>199.188692</td>\n", "      <td>9227.163026</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>1v0</th>\n", "      <td>1653.390857</td>\n", "      <td>1836.856976</td>\n", "      <td>0.900120</td>\n", "      <td>3.680566e-01</td>\n", "      <td>-1946.782662</td>\n", "      <td>5253.564375</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   point       stderr     zstat       p_value  \\\n", "feature      feature_value                                                      \n", "OverallQual  num            10207.265524  1694.108274  6.025155  1.689473e-09   \n", "GrLivArea    num               53.572155    16.896406  3.170624  1.521117e-03   \n", "GarageCars   num            13215.235723  3030.831642  4.360267  1.299037e-05   \n", "AgeAtSale    num             -139.794231   110.416091 -1.266068  2.054889e-01   \n", "OverallCond  num             5723.914340  1462.849911  3.912851  9.121266e-05   \n", "HasFireplace 1v0             4390.835030  1523.103093  2.882822  3.941301e-03   \n", "HasPorch     1v0             4713.175859  2303.096997  2.046451  4.071199e-02   \n", "HasDeck      1v0             1653.390857  1836.856976  0.900120  3.680566e-01   \n", "\n", "                               ci_lower      ci_upper  \n", "feature      feature_value                             \n", "OverallQual  num            6886.874322  13527.656727  \n", "GrLivArea    num              20.455808     86.688502  \n", "GarageCars   num            7274.914862  19155.556584  \n", "AgeAtSale    num            -356.205793     76.617332  \n", "OverallCond  num            2856.781200   8591.047479  \n", "HasFireplace 1v0            1405.607823   7376.062237  \n", "HasPorch     1v0             199.188692   9227.163026  \n", "HasDeck      1v0           -1946.782662   5253.564375  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# global effect on new dataset\n", "ca.cohort_causal_effect(x_test)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>point</th>\n", "      <th>stderr</th>\n", "      <th>zstat</th>\n", "      <th>p_value</th>\n", "      <th>ci_lower</th>\n", "      <th>ci_upper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>sample</th>\n", "      <th>feature</th>\n", "      <th>feature_value</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">0</th>\n", "      <th>OverallQual</th>\n", "      <th>num</th>\n", "      <td>10466.451123</td>\n", "      <td>1701.632579</td>\n", "      <td>6.150829</td>\n", "      <td>7.707896e-10</td>\n", "      <td>7131.312552</td>\n", "      <td>13801.589694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GrLivArea</th>\n", "      <th>num</th>\n", "      <td>54.152219</td>\n", "      <td>8.332353</td>\n", "      <td>6.499031</td>\n", "      <td>8.083900e-11</td>\n", "      <td>37.821108</td>\n", "      <td>70.483330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GarageCars</th>\n", "      <th>num</th>\n", "      <td>10319.768990</td>\n", "      <td>2381.310682</td>\n", "      <td>4.333651</td>\n", "      <td>1.466567e-05</td>\n", "      <td>5652.485818</td>\n", "      <td>14987.052162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AgeAtSale</th>\n", "      <th>num</th>\n", "      <td>-276.854177</td>\n", "      <td>115.013736</td>\n", "      <td>-2.407140</td>\n", "      <td>1.607800e-02</td>\n", "      <td>-502.276958</td>\n", "      <td>-51.431396</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OverallCond</th>\n", "      <th>num</th>\n", "      <td>8238.320947</td>\n", "      <td>1069.053989</td>\n", "      <td>7.706179</td>\n", "      <td>1.296412e-14</td>\n", "      <td>6143.013632</td>\n", "      <td>10333.628263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">290</th>\n", "      <th>AgeAtSale</th>\n", "      <th>num</th>\n", "      <td>-276.854177</td>\n", "      <td>115.013736</td>\n", "      <td>-2.407140</td>\n", "      <td>1.607800e-02</td>\n", "      <td>-502.276958</td>\n", "      <td>-51.431396</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OverallCond</th>\n", "      <th>num</th>\n", "      <td>5849.940818</td>\n", "      <td>3055.390550</td>\n", "      <td>1.914629</td>\n", "      <td>5.553977e-02</td>\n", "      <td>-138.514619</td>\n", "      <td>11838.396255</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HasFireplace</th>\n", "      <th>1v0</th>\n", "      <td>1339.570649</td>\n", "      <td>2653.292705</td>\n", "      <td>0.504871</td>\n", "      <td>6.136494e-01</td>\n", "      <td>-3860.787493</td>\n", "      <td>6539.928791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HasPorch</th>\n", "      <th>1v0</th>\n", "      <td>6906.589930</td>\n", "      <td>5156.130035</td>\n", "      <td>1.339491</td>\n", "      <td>1.804109e-01</td>\n", "      <td>-3199.239237</td>\n", "      <td>17012.419098</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>1v0</th>\n", "      <td>1899.860305</td>\n", "      <td>3984.727566</td>\n", "      <td>0.476785</td>\n", "      <td>6.335149e-01</td>\n", "      <td>-5910.062213</td>\n", "      <td>9709.782822</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2328 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                          point       stderr     zstat  \\\n", "sample feature      feature_value                                        \n", "0      OverallQual  num            10466.451123  1701.632579  6.150829   \n", "       GrLivArea    num               54.152219     8.332353  6.499031   \n", "       GarageCars   num            10319.768990  2381.310682  4.333651   \n", "       AgeAtSale    num             -276.854177   115.013736 -2.407140   \n", "       OverallCond  num             8238.320947  1069.053989  7.706179   \n", "...                                         ...          ...       ...   \n", "290    AgeAtSale    num             -276.854177   115.013736 -2.407140   \n", "       OverallCond  num             5849.940818  3055.390550  1.914629   \n", "       HasFireplace 1v0             1339.570649  2653.292705  0.504871   \n", "       HasPorch     1v0             6906.589930  5156.130035  1.339491   \n", "       HasDeck      1v0             1899.860305  3984.727566  0.476785   \n", "\n", "                                        p_value     ci_lower      ci_upper  \n", "sample feature      feature_value                                           \n", "0      OverallQual  num            7.707896e-10  7131.312552  13801.589694  \n", "       GrLivArea    num            8.083900e-11    37.821108     70.483330  \n", "       GarageCars   num            1.466567e-05  5652.485818  14987.052162  \n", "       AgeAtSale    num            1.607800e-02  -502.276958    -51.431396  \n", "       OverallCond  num            1.296412e-14  6143.013632  10333.628263  \n", "...                                         ...          ...           ...  \n", "290    AgeAtSale    num            1.607800e-02  -502.276958    -51.431396  \n", "       OverallCond  num            5.553977e-02  -138.514619  11838.396255  \n", "       HasFireplace 1v0            6.136494e-01 -3860.787493   6539.928791  \n", "       HasPorch     1v0            1.804109e-01 -3199.239237  17012.419098  \n", "       HasDeck      1v0            6.335149e-01 -5910.062213   9709.782822  \n", "\n", "[2328 rows x 6 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# local effect on new dataset\n", "ca.local_causal_effect(x_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"authors": [{"name": "mesameki"}], "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}