Contributing to DoW<PERSON>
=====================

DoWhy is a PyWhy community project and welcomes contributions.

There are multiple ways to contribute to DoWhy.
Here are some examples:

* Adding a Jupy<PERSON> notebook that describes the use of DoWhy for solving causal problems.

* Helping update the documentation for DoWhy.

* Helping implement a new method for any of the four steps of causal analysis:
  model, identify, estimate, refute

* Integrating DoWhy's API with external implementations for any of the four steps, so that external libraries can be called seamlessly from the `identify_effect`, `estimate_effect` or `refute_estimate` methods.

* Helping extend the DoWhy API so that we can support new functionality like interpretability of the estimate, counterfactual prediction and more.


If you would like to contribute, you can raise a pull request, see :doc:`contributing/contributing-code` for more info.
If you have questions before contributing, you can start by opening an issue on Github.


For a guide to contributing and a list of all contributors, check out `CONTRIBUTING.md <https://github.com/py-why/dowhy/blob/main/CONTRIBUTING.md>`_. Our contributor code of conduct is available `here <https://github.com/py-why/governance/blob/main/CODE-OF-CONDUCT.md>`_. You can also join the DoWhy development channel on Discord: |discord|_

.. |discord| image:: https://img.shields.io/discord/818456847551168542
.. _discord: https://discord.gg/cSBGb3vsZb

