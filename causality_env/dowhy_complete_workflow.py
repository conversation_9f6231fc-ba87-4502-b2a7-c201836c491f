#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
展示DoWhy完整的工作流程：模型、识别、估计和验证
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import networkx as nx
from dowhy import CausalModel
import dowhy.datasets


def simple_linear_dataset():
    """创建一个简单的线性数据集"""
    # 使用DoWhy内置的线性数据集生成器
    data = dowhy.datasets.linear_dataset(
        beta=10,              # 真实的因果效应大小
        num_common_causes=2,  # 共同原因的数量
        num_instruments=1,    # 工具变量的数量
        num_samples=5000,     # 样本量
        treatment_is_binary=False  # 连续处理变量
    )
    
    print("生成的数据集信息:")
    print(f"- 变量名称: {list(data['df'].columns)}")
    print(f"- 处理变量: {data['treatment_name']}")
    print(f"- 结果变量: {data['outcome_name']}")
    print(f"- 公共原因: {data['common_causes_names']}")
    print(f"- 工具变量: {data['instrument_names']}")
    print(f"- 数据形状: {data['df'].shape}")
    
    # 保存数据的摘要统计信息
    summary = data['df'].describe()
    print("\n数据摘要统计:\n", summary.round(2))
    
    return data


def visualize_data(data):
    """可视化数据集"""
    df = data['df']
    treatment = data['treatment_name']
    outcome = data['outcome_name']
    
    # 创建2x2的子图网格
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))
    
    # 处理变量的分布
    axs[0, 0].hist(df[treatment], bins=30, color='skyblue', edgecolor='black')
    axs[0, 0].set_title(f'分布: {treatment}')
    axs[0, 0].set_xlabel(treatment)
    axs[0, 0].set_ylabel('频数')
    
    # 结果变量的分布
    axs[0, 1].hist(df[outcome], bins=30, color='salmon', edgecolor='black')
    axs[0, 1].set_title(f'分布: {outcome}')
    axs[0, 1].set_xlabel(outcome)
    axs[0, 1].set_ylabel('频数')
    
    # 处理变量与结果变量的散点图
    axs[1, 0].scatter(df[treatment], df[outcome], alpha=0.5, color='purple')
    axs[1, 0].set_title(f'{treatment} vs {outcome}')
    axs[1, 0].set_xlabel(treatment)
    axs[1, 0].set_ylabel(outcome)
    
    # 绘制真实图结构
    G = nx.DiGraph()
    G.add_nodes_from(
        [data['treatment_name'], data['outcome_name']] + 
        data['common_causes_names'] + data['instrument_names']
    )
    
    # 添加处理->结果的边
    G.add_edge(data['treatment_name'], data['outcome_name'])
    
    # 添加共同原因->处理和共同原因->结果的边
    for common_cause in data['common_causes_names']:
        G.add_edge(common_cause, data['treatment_name'])
        G.add_edge(common_cause, data['outcome_name'])
    
    # 添加工具变量->处理的边
    for instrument in data['instrument_names']:
        G.add_edge(instrument, data['treatment_name'])
    
    # 在右下角子图中绘制图
    pos = nx.spring_layout(G, seed=42)
    nx.draw(G, pos, with_labels=True, node_color='lightgreen', 
            node_size=2000, font_size=12, font_weight='bold', 
            arrowsize=20, ax=axs[1, 1])
    axs[1, 1].set_title('因果图结构')
    
    plt.tight_layout()
    plt.savefig("obs_data.png")
    plt.close()


def causal_workflow(data):
    """完整的DoWhy因果推断工作流"""
    # 1. 模型（Model）
    model = CausalModel(
        data=data["df"],
        treatment=data["treatment_name"],
        outcome=data["outcome_name"],
        graph=data["gml_graph"]
    )
    print("\n步骤 1: 创建因果模型")
    
    # 2. 识别（Identify）
    identified_estimand = model.identify_effect(proceed_when_unidentifiable=True)
    print("\n步骤 2: 识别因果效应")
    print(identified_estimand)
    
    # 3. 估计（Estimate）
    # 使用多种方法进行估计
    estimation_methods = [
        "backdoor.linear_regression",
        "backdoor.propensity_score_matching",
        "backdoor.propensity_score_stratification",
        "backdoor.propensity_score_weighting",
        "iv.instrumental_variable"
    ]
    
    estimates = {}
    print("\n步骤 3: 使用不同方法估计因果效应")
    
    for method in estimation_methods:
        try:
            estimate = model.estimate_effect(
                identified_estimand, 
                method_name=method,
                control_value=0,
                treatment_value=1
            )
            
            estimates[method] = estimate
            print(f"\n使用 {method} 的估计结果:")
            print(f"- 因果效应点估计: {estimate.value:.4f}")
            print(f"- 置信区间: ({estimate.get_confidence_intervals()[0]:.4f}, "
                  f"{estimate.get_confidence_intervals()[1]:.4f})")
            
        except Exception as e:
            print(f"\n使用 {method} 时出错: {str(e)}")
    
    # 4. 验证（Refute）
    print("\n步骤 4: 验证估计结果")
    refutation_methods = [
        "random_common_cause",
        "placebo_treatment_effect",
        "data_subset_refuter"
    ]
    
    # 选择一种估计方法进行验证
    method_to_refute = "backdoor.linear_regression"
    estimate_to_refute = estimates.get(method_to_refute)
    
    if estimate_to_refute:
        for refute_method in refutation_methods:
            try:
                refutation = model.refute_estimate(
                    identified_estimand, 
                    estimate_to_refute, 
                    method_name=refute_method
                )
                print(f"\n使用 {refute_method} 的验证结果:")
                print(refutation)
                
            except Exception as e:
                print(f"\n使用 {refute_method} 验证时出错: {str(e)}")
    
    # 比较不同估计方法的结果
    compare_estimation_methods(estimates, data)


def compare_estimation_methods(estimates, data):
    """比较不同估计方法的结果"""
    if not estimates:
        print("没有可用的估计结果进行比较")
        return
    
    # 提取每种方法的点估计和置信区间
    methods = []
    point_estimates = []
    lower_cis = []
    upper_cis = []
    
    for method, estimate in estimates.items():
        # 简化方法名称以便显示
        method_display = method.split('.')[-1]
        methods.append(method_display)
        
        point_estimates.append(estimate.value)
        ci = estimate.get_confidence_intervals()
        lower_cis.append(ci[0])
        upper_cis.append(ci[1])
    
    # 创建比较图
    plt.figure(figsize=(10, 6))
    
    # 绘制每种方法的点估计和置信区间
    plt.errorbar(methods, point_estimates, 
                 yerr=[
                     np.array(point_estimates) - np.array(lower_cis), 
                     np.array(upper_cis) - np.array(point_estimates)
                 ],
                 fmt='o', color='blue', ecolor='black', capsize=5)
    
    # 添加真实效应的水平线（如果可用）
    true_effect = 10  # 根据linear_dataset的beta参数
    plt.axhline(y=true_effect, color='r', linestyle='-', label="真实效应")
    
    plt.title("不同估计方法的因果效应比较")
    plt.ylabel("效应大小")
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig("effect_comparison.png")
    plt.close()


if __name__ == "__main__":
    # 生成数据集
    data = simple_linear_dataset()
    
    # 可视化数据
    visualize_data(data)
    
    # 执行因果工作流
    causal_workflow(data)
    
    print("\nDoWhy完整工作流演示完成。") 