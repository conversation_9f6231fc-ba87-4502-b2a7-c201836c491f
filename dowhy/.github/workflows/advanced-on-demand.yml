# run advanced tests when `test advanced` comment is entered
name: Advanced Tests (on demand)

on:
  issue_comment:
    types: [created, edited]

jobs:
  verify:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11", "3.12"]
        poetry-version: [1.5.1]
        test-group: [1, 2, 3, 4]

    if: ${{ github.event.comment.body == 'test advanced'}}
    steps:
      - name: Message Initiating
        uses: actions/github-script@v7
        with:
          script: |
            github.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'Launching advanced tests 🚀',
            });
      - name: Get PR SHA
        id: sha
        uses: actions/github-script@v7
        with:
          result-encoding: string
          script: |
            const { owner, repo, number } = context.issue;
            const pr = await github.pulls.get({
              owner,
              repo,
              pull_number: number,
            });
            return pr.data.head.sha

      - uses: actions/checkout@v4
        with:
          ref: ${{ steps.sha.outputs.result }}

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Poetry ${{ matrix.poetry-version }}
        uses: abatilo/actions-poetry@v4.0.0
        with:
          poetry-version: ${{ matrix.poetry-version }}

      # At the moment needed to support scipy in Python 3.12
      - name: Install LLVM and Clang
        uses: KyleMayes/install-llvm-action@v2
        with:
          version: "14.0"
          directory: ${{ runner.temp }}/llvm

      - name: Install graphviz
        run: |
          sudo apt install graphviz libgraphviz-dev graphviz-dev pkg-config

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          echo "installing poetry dependencies"
          poetry install -E plotting -E pydot -E pygraphviz -E econml --with docs

      - name: Run Advanced Tests
        run: poetry run poe test_advanced  --splits 4 --group ${{ matrix.test-group }}

      - name: Message success
        if: ${{ success() }}
        uses: actions/github-script@v7
        with:
          script: |
            github.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'Advanced tests succeeded! ✅',
            });
      - name: Message failure
        if: ${{ failure() }}
        uses: actions/github-script@v7
        with:
          script: |
            github.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'Advanced tests failed! ❌',
            });
