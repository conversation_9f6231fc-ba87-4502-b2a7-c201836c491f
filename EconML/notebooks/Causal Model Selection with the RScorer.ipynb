{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Selection for Causal Effect Model with the RScorer"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import econml"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["## Ignore warnings\n", "import warnings\n", "warnings.filterwarnings('ignore') "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Main imports\n", "from econml.dml import DML, LinearDML, SparseLinearDML, NonParamDML\n", "from econml.metalearners import XLearner, TLearner, SLearner, DomainAdaptationLearner\n", "from econml.dr import DRLearner\n", "\n", "import numpy as np\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.preprocessing import PolynomialFeatures\n", "from sklearn.linear_model import LassoCV\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Example Usage with Single Binary Treatment Synthetic Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1. DGP \n", "We use the following DGP:\n", "\n", "\\begin{align}\n", "T \\sim & \\text{<PERSON><PERSON><PERSON>}\\left(f(W)\\right), &\\; f(W)=\\sigma(\\langle W, \\beta\\rangle + \\eta), \\;\\eta \\sim \\text{Uniform}(-1, 1)\\\\\n", "Y = & T\\cdot \\theta(X) + \\langle W, \\gamma\\rangle + \\epsilon, & \\; \\epsilon \\sim \\text{Uniform}(-1, 1)\\\\\n", "W \\sim & \\text{Normal}(0,\\, I_{n_w}) & \\\\\n", "X \\sim & \\text{Uniform}(0,\\, 1)^{n_x}\n", "\\end{align}\n", "\n", "where $W$ is a matrix of high-dimensional confounders, $\\beta, \\gamma$ have high sparsity and $\\sigma$ is the sigmoid function.\n", "\n", "For this DGP, \n", "\\begin{align}\n", "\\theta(x) = 1\\{x_0 > .5\\}\n", "\\end{align}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Treatment effect function\n", "def exp_te(x):\n", "    return x[:, 0] > 0.5\n", "\n", "np.random.seed(123)\n", "n = 5000\n", "support_size = 5\n", "n_x = 10\n", "# Outcome support\n", "support_Y = np.random.choice(range(n_x), size=support_size, replace=False)\n", "coefs_Y = np.random.uniform(0, 1, size=support_size)\n", "epsilon_sample = lambda n:np.random.uniform(-1, 1, size=n)\n", "# Treatment support\n", "support_T = support_Y\n", "coefs_T = np.random.uniform(0, 1, size=support_size)\n", "eta_sample = lambda n: np.random.uniform(-1, 1, size=n) \n", "\n", "# Generate controls, covariates, treatments and outcomes\n", "X = np.random.uniform(0, 1, size=(n, n_x))\n", "# Heterogeneous treatment effects\n", "TE = exp_te(X)\n", "# Define treatment\n", "log_odds = np.dot(X[:, support_T], coefs_T) + eta_sample(n)\n", "T_sigmoid = 1/(1 + np.exp(-log_odds))\n", "T = np.array([np.random.binomial(1, p) for p in T_sigmoid])\n", "# Define the outcome\n", "Y = TE * T + np.dot(X[:, support_Y], coefs_Y) + epsilon_sample(n)\n", "\n", "# get testing data\n", "X_test = np.random.uniform(0, 1, size=(n, n_x))\n", "X_test[:, 0] = np.linspace(0, 1, n)\n", "expected_te_test = exp_te(X_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2. <PERSON> Estimator"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["reg = lambda: RandomForestRegressor(min_samples_leaf=10)\n", "clf = lambda: RandomForestClassifier(min_samples_leaf=10)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["X_train, X_val, T_train, T_val, Y_train, Y_val = train_test_split(X, T, Y, test_size=.4)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["models = [('ldml', LinearDML(model_y=reg(), model_t=clf(), discrete_treatment=True,\n", "                             linear_first_stages=False, cv=3)),\n", "          ('sldml', SparseLinearDML(model_y=reg(), model_t=clf(), discrete_treatment=True,\n", "                                    featurizer=PolynomialFeatures(degree=2, include_bias=False),\n", "                                    linear_first_stages=False, cv=3)),\n", "          ('<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON><PERSON>(models=reg(), cate_models=reg(), propensity_model=clf())),\n", "          ('dalearner', DomainAdaptationLearner(models=reg(), final_models=reg(), propensity_model=clf())),\n", "          ('slearner', <PERSON><PERSON><PERSON>(overall_model=reg())),\n", "          ('tlearner', <PERSON><PERSON><PERSON><PERSON>(models=reg())),\n", "          ('d<PERSON><PERSON>ner', <PERSON><PERSON><PERSON><PERSON>(model_propensity=clf(), model_regression=reg(),\n", "                                  model_final=reg(), cv=3)),\n", "          ('rlearner', NonParamDML(model_y=reg(), model_t=clf(), model_final=reg(),\n", "                                   discrete_treatment=True, cv=3)),\n", "          ('dml3dlasso', DML(model_y=reg(), model_t=clf(), model_final=LassoCV(), discrete_treatment=True,\n", "                             featurizer=PolynomialFeatures(degree=3),\n", "                             linear_first_stages=False, cv=3))\n", "]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done   4 out of   9 | elapsed:   35.0s remaining:   43.8s\n", "[Parallel(n_jobs=-1)]: Done   9 out of   9 | elapsed:   54.0s finished\n"]}], "source": ["from joblib import Parallel, delayed\n", "\n", "def fit_model(name, model):\n", "    return name, model.fit(Y_train, T_train, X=X_train)\n", "\n", "models = Parallel(n_jobs=-1, verbose=1)(delayed(fit_model)(name, mdl) for name, mdl in models)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.score.rscorer.RScorer at 0x23a3270da90>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.score import RScorer\n", "\n", "scorer = RScorer(model_y=reg(), model_t=clf(),\n", "                 discrete_treatment=True, cv=3,\n", "                 mc_iters=3, mc_agg='median')\n", "scorer.fit(Y_val, T_val, X=X_val)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["rscore = [scorer.score(mdl) for _, mdl in models]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["expected_te_val = exp_te(X_val)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["rootpehe = [np.sqrt(np.mean((expected_te_val.flatten() - mdl.effect(X_val).flatten())**2)) for _, mdl in models]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(rootpehe, rscore)\n", "plt.xlabel('rpehe')\n", "plt.ylabel('rscore')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3. Performance Visualization"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************************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\n", "text/plain": ["<Figure size 1152x1152 with 9 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(16, 16))\n", "rows = int(np.ceil(len(models) / 3))\n", "for it, (name, mdl) in enumerate(models):\n", "    plt.subplot(rows, 3, it + 1)\n", "    plt.title('{}. RScore: {:.3f}, Root-PEHE: {:.3f}'.format(name, rscore[it], rootpehe[it]))\n", "    plt.plot(X_test[:, 0], mdl.effect(X_test), label='{}'.format(name))\n", "    plt.plot(X_test[:, 0], expected_te_test, 'b--', label='True effect')\n", "    plt.ylabel('Treatment Effect')\n", "    plt.xlabel('x')\n", "    plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Getting the Best Model"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["mdl, score  = scorer.best_model([mdl for _, mdl in models])\n", "rootpehe_best = np.sqrt(np.mean((expected_te_val.flatten() - mdl.effect(X_val).flatten())**2))\n", "plt.figure()\n", "plt.title('RScore: {:.3f}, Root-PEHE: {:.3f}'.format(score, rootpehe_best))\n", "plt.plot(X_test[:, 0], mdl.effect(X_test), label='best')\n", "plt.plot(X_test[:, 0], expected_te_test, 'b--', label='True effect')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Getting an Ensemble based on Scores"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["mdl, score  = scorer.ensemble([mdl for _, mdl in models])\n", "rootpehe_ensemble = np.sqrt(np.mean((expected_te_val.flatten() - mdl.effect(X_val).flatten())**2))\n", "plt.figure()\n", "plt.title('RScore: {:.3f}, Root-PEHE: {:.3f}'.format(score, rootpehe_ensemble))\n", "plt.plot(X_test[:, 0], mdl.effect(X_test), label='ensemble')\n", "plt.plot(X_test[:, 0], expected_te_test, 'b--', label='True effect')\n", "plt.ylabel('Treatment Effect')\n", "plt.xlabel('x')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Semi-Synthetic Data"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["reg = lambda: RandomForestRegressor(min_samples_leaf=10, random_state=123)\n", "clf = lambda: RandomForestClassifier(min_samples_leaf=10, random_state=123)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from econml.data.dgps import ihdp_surface_B, ihdp_surface_A\n", "Y, T, X, expected_te = ihdp_surface_B(random_state=123)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["X_train, X_val, T_train, T_val,\\\n", "Y_train, Y_val, expected_te_train, expected_te_val = train_test_split(X, T, Y, expected_te, test_size=.3, random_state=123)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["models = [('ldml', LinearDML(model_y=reg(), model_t=clf(), discrete_treatment=True,\n", "                             linear_first_stages=False, cv=3)),\n", "          ('<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON><PERSON>(models=reg(), cate_models=reg(), propensity_model=clf())),\n", "          ('dalearner', DomainAdaptationLearner(models=reg(), final_models=reg(), propensity_model=clf())),\n", "          ('slearner', <PERSON><PERSON><PERSON>(overall_model=reg())),\n", "          ('tlearner', <PERSON><PERSON><PERSON><PERSON>(models=reg())),\n", "          ('d<PERSON><PERSON>ner', <PERSON><PERSON><PERSON><PERSON>(model_propensity=clf(), model_regression=reg(),\n", "                                  model_final=reg(), cv=3)),\n", "          ('rlearner', NonParamDML(model_y=reg(), model_t=clf(), model_final=reg(),\n", "                                   discrete_treatment=True, cv=3)),\n", "          ('dml3dlasso', DML(model_y=reg(), model_t=clf(), model_final=LassoCV(), discrete_treatment=True,\n", "                             featurizer=PolynomialFeatures(degree=2, interaction_only=True, include_bias=False),\n", "                             linear_first_stages=False, cv=3))\n", "]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=-1)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=-1)]: Done   2 out of   8 | elapsed:    1.4s remaining:    4.3s\n", "[Parallel(n_jobs=-1)]: Done   8 out of   8 | elapsed:   10.2s finished\n"]}], "source": ["from joblib import Parallel, delayed\n", "\n", "def fit_model(name, model):\n", "    print(\"Training: \", name)\n", "    model.fit(Y_train, T_train, X=X_train)\n", "    print(\"Done training: \", name)\n", "    return name, model\n", "\n", "models = Parallel(n_jobs=-1, verbose=1)(delayed(fit_model)(name, mdl) for name, mdl in models)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.score.rscorer.RScorer at 0x23a332a05f8>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from econml.score import RScorer\n", "\n", "scorer = RScorer(model_y=reg(), model_t=clf(),\n", "                 discrete_treatment=True, cv=3,\n", "                 mc_iters=3, mc_agg='median')\n", "scorer.fit(Y_val, T_val, X=X_val)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["rscore = [scorer.score(mdl) for _, mdl in models]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["rootpehe = [np.sqrt(np.mean((expected_te.flatten() - mdl.effect(X).flatten())**2)) for _, mdl in models]"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(rootpehe, rscore)\n", "plt.xlabel('rpehe')\n", "plt.ylabel('rscore')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7393917952588073"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["best, score  = scorer.best_model([mdl for _, mdl in models])\n", "rootpehe_best = np.sqrt(np.nanmean((expected_te_val.flatten() - best.effect(X_val).flatten())**2))\n", "rootpehe_best"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7346069084804471"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["ensemble, score  = scorer.ensemble([mdl for _, mdl in models])\n", "rootpehe_ensemble = np.sqrt(np.nanmean((expected_te_val.flatten() - ensemble.effect(X_val).flatten())**2))\n", "rootpehe_ensemble"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Visualization of bias distribution\n", "plt.figure(figsize=(15, 5))\n", "plt.violinplot([np.abs(mdl.effect(X).flatten() - expected_te) for _, mdl in models] + \n", "               [np.abs(best.effect(X).flatten() - expected_te)] +\n", "               [np.abs(ensemble.effect(X).flatten() - expected_te)], showmeans=True)\n", "plt.ylabel(\"Bias distribution\")\n", "plt.xticks(np.arange(1, len(models) + 3), [name for name, _ in models] + ['best', 'ensemble'])\n", "plt.show()"]}], "metadata": {"file_extension": ".py", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}