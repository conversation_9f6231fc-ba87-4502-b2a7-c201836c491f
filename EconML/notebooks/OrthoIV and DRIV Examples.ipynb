{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<table border=\"0\">\n", "    <tr>\n", "        <td>\n", "            <img src=\"https://ictd2016.files.wordpress.com/2016/04/microsoft-research-logo-copy.jpg\" style=\"width 30px;\" />\n", "             </td>\n", "        <td>\n", "            <img src=\"https://www.microsoft.com/en-us/research/wp-content/uploads/2016/12/MSR-ALICE-HeaderGraphic-1920x720_1-800x550.jpg\" style=\"width 100px;\"/></td>\n", "        </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OrthoIV and DRIV: Use Cases and Examples\n", "\n", "OrthoIV, DMLIV, and DRIV are a suite of algorithms that use Double Machine Learning approach and Doubly Robust machine learning approach to estimate the heterogeneous treatment effect with an endogeneous treatment and an instrument.\n", "\n", "The EconML SDK implements the following  classes:\n", "\n", "* **OrthoIV classes**: solve the moment equation $E[(Y-E[Y|X]-\\theta(X) \\cdot (T-E[T|X]))(Z-E[Z|X])] = 0$\n", "* **DMLIV classes**: minimize the square loss   $E[(Y- E[Y|X] - \\theta(X) \\cdot (E[T|X, Z] - E[T|X]))^2]$\n", "* **DRIV classes**: minimize the square loss\n", "$$E[(\\theta_{pre}(X) + \\frac{(Y- E[Y|X]-\\theta_{pre}(X) \\cdot (T- E[T|X]))(Z-E[Z|X])}{E[T\\cdot Z|X]-E[T|X] \\cdot E[Z|X] }-\\theta(X))^2]$$\n", "* **Intent to Treat DRIV classes**: a special case of DRIV where the instrument is the assignment in a randomized controlled trail with non-compliance\n", "\n", "\n", "\n", "\n", "In ths notebook, we show the performance of OrthoIV and DRIV on estimating average treatment effect and heterogeneous treatment effect.\n", "\n", "**Notebook contents:**\n", "\n", "1. [Example Usage with Average Treatment Effects](#Example-Usage-with-Average-Treatment-Effects)\n", "2. [Example Usage with Heterogeneous Treatment Effects](#Example-Usage-with-Heterogeneous-Treatment-Effects)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 2;\n", "                var nbb_formatted_code = \"# Helper imports\\nimport numpy as np\\nfrom scipy import special\\nfrom sklearn.linear_model import LinearRegression, LogisticRegression\\nfrom sklearn.ensemble import RandomForestRegressor\\nimport matplotlib.pyplot as plt\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Helper imports\n", "import numpy as np\n", "from scipy import special\n", "from sklearn.linear_model import LinearRegression, LogisticRegression\n", "from sklearn.ensemble import RandomForestRegressor\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Example Usage with Average Treatment Effects"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DGP\n", "We construct the DGP as below. The instrument corresponds to a fully randomized recommendation of treatment. Then each sample complies with the recommendation to some degree. This probability depends on both the observed feature $X$ and an unobserved confounder that has a direct effect on the outcome.\n", "\n", "\\begin{align}\n", "W \\sim \\; & \\text{Normal}(0,\\, I_{n_w})   \\tag{Observed confounders}\\\\\n", "Z \\sim \\; & \\text{<PERSON><PERSON><PERSON>}(p=0.5)   \\tag{Instrument}\\\\\n", "\\nu \\sim \\; & \\text{U}[0, 5] \\tag{Unobserved confounder}\\\\\n", "C \\sim \\; & \\text{<PERSON><PERSON><PERSON>}(p=0.8 \\cdot \\text{Sigmoid}(0.4 \\cdot X[0] + \\nu))   \\tag{Compliers when recommended}\\\\\n", "C0 \\sim \\; & \\text{<PERSON><PERSON><PERSON>}(p=0.006)   \\tag{Non-Compliers when not recommended}\\\\\n", "T = \\; & C \\cdot Z + C0 \\cdot (1-Z)  \\tag{Treatment}\\\\\n", "y \\sim \\; & \\theta \\cdot T + 2 \\cdot \\nu + 5 \\cdot (X[3]>0) + 0.1 \\cdot \\text{U}[0, 1]  \\tag{Outcome}\n", "\\end{align}\n", "\n", "Moreover, the constant treatment effect is presdefined here, that's what we want to learn from the model.\n", "\\begin{align}\n", "\\theta = \\; & 10 \\tag{ATE}\\\\\n", "\\end{align}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 3;\n", "                var nbb_formatted_code = \"# test performance\\ndef dgp(n, p, true_fn):\\n    X = np.random.normal(0, 1, size=(n, p))\\n    Z = np.random.binomial(1, 0.5, size=(n,))\\n    nu = np.random.uniform(0, 5, size=(n,))\\n    coef_Z = 0.8\\n    C = np.random.binomial(\\n        1, coef_Z * special.expit(0.4 * X[:, 0] + nu)\\n    )  # Compliers when recomended\\n    C0 = np.random.binomial(\\n        1, 0.006 * np.ones(X.shape[0])\\n    )  # Non-compliers when not recommended\\n    T = C * Z + C0 * (1 - Z)\\n    y = (\\n        true_fn(X) * T\\n        + 2 * nu\\n        + 5 * (X[:, 3] > 0)\\n        + 0.1 * np.random.uniform(0, 1, size=(n,))\\n    )\\n    return y, T, Z, X\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# test performance\n", "def dgp(n, p, true_fn):\n", "    X = np.random.normal(0, 1, size=(n, p))\n", "    Z = np.random.binomial(1, 0.5, size=(n,))\n", "    nu = np.random.uniform(0, 5, size=(n,))\n", "    coef_Z = 0.8\n", "    C = np.random.binomial(\n", "        1, coef_Z * special.expit(0.4 * X[:, 0] + nu)\n", "    )  # Compliers when recomended\n", "    C0 = np.random.binomial(\n", "        1, 0.006 * np.ones(X.shape[0])\n", "    )  # Non-compliers when not recommended\n", "    T = C * Z + C0 * (1 - Z)\n", "    y = (\n", "        true_fn(X) * T\n", "        + 2 * nu\n", "        + 5 * (X[:, 3] > 0)\n", "        + 0.1 * np.random.uniform(0, 1, size=(n,))\n", "    )\n", "    return y, T, Z, X"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 4;\n", "                var nbb_formatted_code = \"func = lambda X: 10\\nn = 5000\\np = 10\\ny, T, Z, X = dgp(n, p, func)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["func = lambda X: 10\n", "n = 5000\n", "p = 10\n", "y, T, Z, X = dgp(n, p, func)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Estimators\n", "We train multiple estimators from OrthoIV and DRIV classes, and see whether they could all recover the true estimate\n", "### OrthoIV Estimator"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 5;\n", "                var nbb_formatted_code = \"model = lambda: LinearRegression()\\nmodel_clf = lambda: LogisticRegression()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = lambda: LinearRegression()\n", "model_clf = lambda: LogisticRegression()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect:  10\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>10.04</td>      <td>0.138</td> <td>72.517</td>   <td>0.0</td>    <td>9.768</td>   <td>10.311</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept          10.04  0.138 72.517    0.0    9.768   10.311\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 6;\n", "                var nbb_formatted_code = \"from econml.iv.dml import OrthoIV\\n\\nest1 = OrthoIV(projection=False, discrete_treatment=True, discrete_instrument=True)\\nest1.fit(y, T, Z=Z, X=None, W=X)\\nprint(\\\"True Treatment Effect: \\\", func(X))\\nest1.summary(alpha=0.05)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from econml.iv.dml import OrthoIV\n", "\n", "est1 = OrthoIV(projection=False, discrete_treatment=True, discrete_instrument=True)\n", "est1.fit(y, T, Z=Z, X=None, W=X)\n", "print(\"True Treatment Effect: \", func(X))\n", "est1.summary(alpha=0.05)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Projected OrthoIV Estimator"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect:  10\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>     <td>10.026</td>      <td>0.138</td> <td>72.874</td>   <td>0.0</td>    <td>9.756</td>   <td>10.295</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept         10.026  0.138 72.874    0.0    9.756   10.295\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 7;\n", "                var nbb_formatted_code = \"est2 = OrthoIV(projection=True, discrete_treatment=True, discrete_instrument=True)\\nest2.fit(y, T, Z=Z, X=None, W=X)\\nprint(\\\"True Treatment Effect: \\\", func(X))\\nest2.summary(alpha=0.05)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est2 = OrthoIV(projection=True, discrete_treatment=True, discrete_instrument=True)\n", "est2.fit(y, T, Z=Z, X=None, W=X)\n", "print(\"True Treatment Effect: \", func(X))\n", "est2.summary(alpha=0.05)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DMLIV Estimator"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect:  10\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>     <td>10.232</td>    \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "    CATE Intercept Results   \n", "=============================\n", "               point_estimate\n", "-----------------------------\n", "cate_intercept         10.232\n", "-----------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 8;\n", "                var nbb_formatted_code = \"from econml.iv.dml import DMLIV, NonParamDMLIV\\n\\nest3 = DMLIV(discrete_treatment=True, discrete_instrument=True)\\nest3.fit(y, T, Z=Z, X=None, W=X)\\nprint(\\\"True Treatment Effect: \\\", func(X))\\nest3.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from econml.iv.dml import DMLIV, NonParamDMLIV\n", "\n", "est3 = DMLIV(discrete_treatment=True, discrete_instrument=True)\n", "est3.fit(y, T, Z=Z, X=None, W=X)\n", "print(\"True Treatment Effect: \", func(X))\n", "est3.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Linear DRIV Estimator"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect:  10\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>     <td>10.034</td>      <td>0.139</td> <td>72.211</td>   <td>0.0</td>    <td>9.762</td>   <td>10.306</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept         10.034  0.139 72.211    0.0    9.762   10.306\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 9;\n", "                var nbb_formatted_code = \"from econml.iv.dr import LinearDRIV, SparseLinearDRIV, ForestDRIV\\n\\nest4 = LinearDRIV(discrete_instrument=True, discrete_treatment=True)\\nest4.fit(y, T, Z=Z, X=None, W=X)\\nprint(\\\"True Treatment Effect: \\\", func(X))\\nest4.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from econml.iv.dr import LinearDRIV, SparseLinearDRIV, ForestDRIV\n", "\n", "est4 = LinearDRIV(discrete_instrument=True, discrete_treatment=True)\n", "est4.fit(y, T, Z=Z, X=None, W=X)\n", "print(\"True Treatment Effect: \", func(X))\n", "est4.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Linear Intent-to-Treat DRIV Estimator"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect:  10\n", "Coefficient Results:  X is None, please call intercept_inference to learn the constant!\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>10.03</td>      <td>0.14</td>  <td>71.638</td>   <td>0.0</td>    <td>9.755</td>   <td>10.304</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                       CATE Intercept Results                       \n", "====================================================================\n", "               point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------------------\n", "cate_intercept          10.03   0.14 71.638    0.0    9.755   10.304\n", "--------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 10;\n", "                var nbb_formatted_code = \"from econml.iv.dr import LinearIntentToTreatDRIV\\n\\nest5 = LinearIntentToTreatDRIV(model_t_xwz=model_clf())\\nest5.fit(y, T, Z=Z, X=None, W=X)\\nprint(\\\"True Treatment Effect: \\\", func(X))\\nest5.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from econml.iv.dr import LinearIntentToTreatDRIV\n", "\n", "est5 = LinearIntentToTreatDRIV(model_t_xwz=model_clf())\n", "est5.fit(y, T, Z=Z, X=None, W=X)\n", "print(\"True Treatment Effect: \", func(X))\n", "est5.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Example Usage with Heterogeneous Treatment Effects"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DGP\n", "In this section, we keep the same data generation process, but only change the treatment effect $\\theta(X)$ to a function of $X$, and learn the heterogeneous treatment effect. We'd like to see the effect interval learnt from each model could recover the true estimate and the coefficient estimates in the final linear model could recover the true coefficient.\n", "\n", "The true effect function is as below:\n", "\\begin{align}\n", "\\theta = \\; & 10 \\cdot X[0] \\tag{CATE}\\\\\n", "\\end{align}"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 11;\n", "                var nbb_formatted_code = \"func = lambda X: 10 * X[:, 0]\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["func = lambda X: 10 * X[:, 0]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 12;\n", "                var nbb_formatted_code = \"n = 5000\\np = 10\\ny, T, Z, X = dgp(n, p, func)\\n# Generate test data\\nX_test = np.linspace(-2, 2, 100).reshape(-1, 1)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["n = 5000\n", "p = 10\n", "y, T, Z, X = dgp(n, p, func)\n", "# Generate test data\n", "X_test = np.linspace(-2, 2, 100).reshape(-1, 1)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 13;\n", "                var nbb_formatted_code = \"# save output for visualization\\nres_pred = []\\nres_lb = []\\nres_ub = []\\nname_list = [\\n    \\\"OrthoIV\\\",\\n    \\\"ProjectedOrthoIV\\\",\\n    \\\"NonParamDMLIV\\\",\\n    \\\"LinearDRIV\\\",\\n    \\\"ForestDRIV\\\",\\n    \\\"LinearIntentToTreatDRIV\\\",\\n]\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# save output for visualization\n", "res_pred = []\n", "res_lb = []\n", "res_ub = []\n", "name_list = [\n", "    \"OrthoIV\",\n", "    \"ProjectedOrthoIV\",\n", "    \"NonParamDMLIV\",\n", "    \"LinearDRIV\",\n", "    \"ForestDRIV\",\n", "    \"LinearIntentToTreatDRIV\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Estimators\n", "### OrthoIV Estimator"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect: 10\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "   <td></td>  <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>X0</th>     <td>10.043</td>      <td>0.142</td> <td>70.962</td>   <td>0.0</td>    <td>9.765</td>    <td>10.32</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>0.245</td>      <td>0.137</td> <td>1.793</td>  <td>0.073</td>  <td>-0.023</td>    <td>0.513</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                  Coefficient Results                   \n", "========================================================\n", "   point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------\n", "X0         10.043  0.142 70.962    0.0    9.765    10.32\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept          0.245  0.137 1.793  0.073   -0.023    0.513\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 14;\n", "                var nbb_formatted_code = \"est1 = OrthoIV(projection=False, discrete_treatment=True, discrete_instrument=True)\\nest1.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\\nprint(\\\"True Treatment Effect: 10\\\")\\nest1.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est1 = OrthoIV(projection=False, discrete_treatment=True, discrete_instrument=True)\n", "est1.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\n", "print(\"True Treatment Effect: 10\")\n", "est1.summary()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 15;\n", "                var nbb_formatted_code = \"te_pred1 = est1.effect(X_test)\\nte_pred1_lb, te_pred1_ub = est1.effect_interval(X_test, alpha=0.05)\\nres_pred.append(te_pred1)\\nres_lb.append(te_pred1_lb)\\nres_ub.append(te_pred1_ub)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["te_pred1 = est1.effect(X_test)\n", "te_pred1_lb, te_pred1_ub = est1.effect_interval(X_test, alpha=0.05)\n", "res_pred.append(te_pred1)\n", "res_lb.append(te_pred1_lb)\n", "res_ub.append(te_pred1_ub)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Projected OrthoIV Estimator"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect: 10\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "   <td></td>  <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>X0</th>     <td>10.052</td>      <td>0.142</td> <td>70.695</td>   <td>0.0</td>    <td>9.773</td>    <td>10.33</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>0.22</td>       <td>0.137</td> <td>1.604</td>  <td>0.109</td>  <td>-0.049</td>    <td>0.488</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                  Coefficient Results                   \n", "========================================================\n", "   point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------\n", "X0         10.052  0.142 70.695    0.0    9.773    10.33\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept           0.22  0.137 1.604  0.109   -0.049    0.488\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 16;\n", "                var nbb_formatted_code = \"est2 = OrthoIV(projection=True, discrete_treatment=True, discrete_instrument=True)\\nest2.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\\nprint(\\\"True Treatment Effect: 10\\\")\\nest2.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est2 = OrthoIV(projection=True, discrete_treatment=True, discrete_instrument=True)\n", "est2.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\n", "print(\"True Treatment Effect: 10\")\n", "est2.summary()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 17;\n", "                var nbb_formatted_code = \"te_pred2 = est2.effect(X_test)\\nte_pred2_lb, te_pred2_ub = est2.effect_interval(X_test, alpha=0.05)\\nres_pred.append(te_pred2)\\nres_lb.append(te_pred2_lb)\\nres_ub.append(te_pred2_ub)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["te_pred2 = est2.effect(X_test)\n", "te_pred2_lb, te_pred2_ub = est2.effect_interval(X_test, alpha=0.05)\n", "res_pred.append(te_pred2)\n", "res_lb.append(te_pred2_lb)\n", "res_ub.append(te_pred2_ub)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Non-Parametric DMLIV Estimator"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.iv.dml._dml.NonParamDMLIV at 0x12b3f873608>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 18;\n", "                var nbb_formatted_code = \"est3 = NonParamDMLIV(\\n    model_final=RandomForestRegressor(),\\n    discrete_treatment=True,\\n    discrete_instrument=True,\\n)\\nest3.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est3 = NonParamDMLIV(\n", "    model_final=RandomForestRegressor(),\n", "    discrete_treatment=True,\n", "    discrete_instrument=True,\n", ")\n", "est3.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 19;\n", "                var nbb_formatted_code = \"te_pred3 = est3.effect(X_test)\\nres_pred.append(te_pred2)\\nres_lb.append(np.array([]))\\nres_ub.append(np.array([]))\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["te_pred3 = est3.effect(X_test)\n", "res_pred.append(te_pred2)\n", "res_lb.append(np.array([]))\n", "res_ub.append(np.array([]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Linear DRIV Estimator"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect: 10\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "   <td></td>  <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>X0</th>     <td>10.058</td>      <td>0.141</td> <td>71.339</td>   <td>0.0</td>    <td>9.781</td>   <td>10.334</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>0.245</td>      <td>0.137</td> <td>1.791</td>  <td>0.073</td>  <td>-0.023</td>    <td>0.513</td> \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                  Coefficient Results                   \n", "========================================================\n", "   point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------\n", "X0         10.058  0.141 71.339    0.0    9.781   10.334\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept          0.245  0.137 1.791  0.073   -0.023    0.513\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 20;\n", "                var nbb_formatted_code = \"est4 = LinearDRIV(discrete_instrument=True, discrete_treatment=True)\\nest4.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\\nprint(\\\"True Treatment Effect: 10\\\")\\nest4.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est4 = LinearDRIV(discrete_instrument=True, discrete_treatment=True)\n", "est4.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\n", "print(\"True Treatment Effect: 10\")\n", "est4.summary()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 21;\n", "                var nbb_formatted_code = \"te_pred4 = est4.effect(X_test)\\nte_pred4_lb, te_pred4_ub = est4.effect_interval(X_test, alpha=0.05)\\nres_pred.append(te_pred4)\\nres_lb.append(te_pred4_lb)\\nres_ub.append(te_pred4_ub)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["te_pred4 = est4.effect(X_test)\n", "te_pred4_lb, te_pred4_ub = est4.effect_interval(X_test, alpha=0.05)\n", "res_pred.append(te_pred4)\n", "res_lb.append(te_pred4_lb)\n", "res_ub.append(te_pred4_ub)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Forest DRIV Estimator"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<econml.iv.dr._dr.<PERSON> at 0x12b3f8bcb48>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 22;\n", "                var nbb_formatted_code = \"est5 = ForestDRIV(discrete_instrument=True, discrete_treatment=True)\\nest5.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est5 = ForestDRIV(discrete_instrument=True, discrete_treatment=True)\n", "est5.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 23;\n", "                var nbb_formatted_code = \"te_pred5 = est5.effect(X_test)\\nte_pred5_lb, te_pred5_ub = est5.effect_interval(X_test, alpha=0.05)\\nres_pred.append(te_pred5)\\nres_lb.append(te_pred5_lb)\\nres_ub.append(te_pred5_ub)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["te_pred5 = est5.effect(X_test)\n", "te_pred5_lb, te_pred5_ub = est5.effect_interval(X_test, alpha=0.05)\n", "res_pred.append(te_pred5)\n", "res_lb.append(te_pred5_lb)\n", "res_ub.append(te_pred5_ub)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Linear Intent-to-Treat DRIV Estimator"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True Treatment Effect: 10\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Coefficient Results</caption>\n", "<tr>\n", "   <td></td>  <th>point_estimate</th> <th>stderr</th>  <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>X0</th>      <td>9.894</td>      <td>0.162</td> <td>61.058</td>   <td>0.0</td>    <td>9.577</td>   <td>10.212</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>CATE Intercept Results</caption>\n", "<tr>\n", "         <td></td>        <th>point_estimate</th> <th>stderr</th> <th>zstat</th> <th>pvalue</th> <th>ci_lower</th> <th>ci_upper</th>\n", "</tr>\n", "<tr>\n", "  <th>cate_intercept</th>      <td>0.267</td>      <td>0.155</td> <td>1.728</td>  <td>0.084</td>  <td>-0.036</td>    <td>0.57</td>  \n", "</tr>\n", "</table><br/><br/><sub>A linear parametric conditional average treatment effect (CATE) model was fitted:<br/>$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$<br/>where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:<br/>$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$<br/>where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>"], "text/plain": ["<class 'econml.utilities.Summary'>\n", "\"\"\"\n", "                  Coefficient Results                   \n", "========================================================\n", "   point_estimate stderr zstat  pvalue ci_lower ci_upper\n", "--------------------------------------------------------\n", "X0          9.894  0.162 61.058    0.0    9.577   10.212\n", "                       CATE Intercept Results                      \n", "===================================================================\n", "               point_estimate stderr zstat pvalue ci_lower ci_upper\n", "-------------------------------------------------------------------\n", "cate_intercept          0.267  0.155 1.728  0.084   -0.036     0.57\n", "-------------------------------------------------------------------\n", "\n", "<sub>A linear parametric conditional average treatment effect (CATE) model was fitted:\n", "$Y = \\Theta(X)\\cdot T + g(X, W) + \\epsilon$\n", "where for every outcome $i$ and treatment $j$ the CATE $\\Theta_{ij}(X)$ has the form:\n", "$\\Theta_{ij}(X) = \\phi(X)' coef_{ij} + cate\\_intercept_{ij}$\n", "where $\\phi(X)$ is the output of the `featurizer` or $X$ if `featurizer`=None. Coefficient Results table portrays the $coef_{ij}$ parameter vector for each outcome $i$ and treatment $j$. Intercept Results table portrays the $cate\\_intercept_{ij}$ parameter.</sub>\n", "\"\"\""]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 24;\n", "                var nbb_formatted_code = \"est6 = LinearIntentToTreatDRIV()\\nest6.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\\nprint(\\\"True Treatment Effect: 10\\\")\\nest6.summary()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["est6 = LinearIntentToTreatDRIV()\n", "est6.fit(y, T, Z=Z, X=X[:, :1], W=X[:, 1:])\n", "print(\"True Treatment Effect: 10\")\n", "est6.summary()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 25;\n", "                var nbb_formatted_code = \"te_pred6 = est6.effect(X_test)\\nte_pred6_lb, te_pred6_ub = est6.effect_interval(X_test, alpha=0.05)\\nres_pred.append(te_pred6)\\nres_lb.append(te_pred6_lb)\\nres_ub.append(te_pred6_ub)\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["te_pred6 = est6.effect(X_test)\n", "te_pred6_lb, te_pred6_ub = est6.effect_interval(X_test, alpha=0.05)\n", "res_pred.append(te_pred6)\n", "res_lb.append(te_pred6_lb)\n", "res_ub.append(te_pred6_ub)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  Performance Visualization"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1296x864 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"application/javascript": ["\n", "            setTimeout(function() {\n", "                var nbb_cell_id = 26;\n", "                var nbb_formatted_code = \"plt.figure(figsize=(18, 12))\\nfor i in range(6):\\n    plt.subplot(3, 2, i + 1)\\n    plt.plot(X_test[:, 0], res_pred[i], label=name_list[i], alpha=0.6)\\n    if res_lb[i].size != 0:\\n        plt.fill_between(X_test[:, 0], res_lb[i], res_ub[i], alpha=0.4)\\n    plt.plot(X_test[:, 0], func(X_test), \\\"b--\\\", label=\\\"True effect\\\")\\n    plt.xlabel(\\\"X[0]\\\")\\n    plt.ylabel(\\\"Treatment Effect\\\")\\n    plt.legend()\\nplt.show()\";\n", "                var nbb_cells = Jupyter.notebook.get_cells();\n", "                for (var i = 0; i < nbb_cells.length; ++i) {\n", "                    if (nbb_cells[i].input_prompt_number == nbb_cell_id) {\n", "                        nbb_cells[i].set_text(nbb_formatted_code);\n", "                        break;\n", "                    }\n", "                }\n", "            }, 500);\n", "            "], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(18, 12))\n", "for i in range(6):\n", "    plt.subplot(3, 2, i + 1)\n", "    plt.plot(X_test[:, 0], res_pred[i], label=name_list[i], alpha=0.6)\n", "    if res_lb[i].size != 0:\n", "        plt.fill_between(X_test[:, 0], res_lb[i], res_ub[i], alpha=0.4)\n", "    plt.plot(X_test[:, 0], func(X_test), \"b--\", label=\"True effect\")\n", "    plt.xlabel(\"X[0]\")\n", "    plt.ylabel(\"Treatment Effect\")\n", "    plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}