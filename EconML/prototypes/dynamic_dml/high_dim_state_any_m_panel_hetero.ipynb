{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Single Experiment\n", "\n", "In this notebook we run a single experiment and display the estimates of the dynamic effects based on our dynamic DML algorithm. We also display some performance of alternative benchmark approaches."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Generation from a Markovian Treatment Model\n", "\n", "We consider the following DGP:\n", "\n", "\\begin{align}\n", "    X_t =& (\\pi'X_{t-1} + 1) \\cdot A\\, T_{t-1} + B X_{t-1} + \\epsilon_t\\\\\n", "    T_t =& \\gamma\\, T_{t-1} + (1-\\gamma) \\cdot D X_t + \\zeta_t\\\\\n", "    Y_t =& (\\sigma' X_{t} + 1) \\cdot e\\, T_{t} + f' X_t + \\eta_t\n", "\\end{align}\n", "\n", "with $X_0, T_0 = 0$ and $\\epsilon_t, \\zeta_t, \\eta_t$ normal $N(0, \\sigma^2)$ r.v.'s. Moreover, $X_t \\in R^{n_x}$, $B[:, 0:s_x] \\neq 0$ and $B[:, s_x:-1] = 0$, $\\gamma\\in [0, 1]$, $D[:, 0:s_x] \\neq 0$, $D[:, s_x:-1]=0$, $f[0:s_x]\\neq 0$, $f[s_x:-1]=0$. We draw a single time series of samples of length $n\\_samples$."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 148, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "from dynamic_panel_dgp import DynamicPanelDGP, LongRangeDynamicPanelDGP, EndogenousDynamicPanelDGP\n", "from dynamic_panel_dgp import PastTreatmentHeteroDynamicPanelDGP\n", "n_units = 500\n", "n_periods = 3\n", "n_treatments = 1\n", "n_x = 450\n", "s_x = 1 # the first s_x features are relevant and endogenous\n", "s_t = 1\n", "sigma_x = .8\n", "sigma_t = .8\n", "sigma_y = .1\n", "gamma = 0\n", "autoreg = .25\n", "state_effect = .25\n", "conf_str = 5\n", "true_hetero_inds = np.arange(n_x-n_treatments, n_x) # subset of features that are exogenous and create heterogeneity\n", "hetero_strength = .5 # strength of heterogeneity wrt the exogenous variables (assumed to be the last s_x features)\n", "hetero_inds = np.arange(n_x-n_treatments, n_x) # subset of features wrt we estimate heterogeneity\n", "#hetero_inds = np.empty(shape=(0,)).astype(int) \n", "\n", "#dgp_class = LongRangeDynamicPanelDGP\n", "dgp_class = DynamicPanelDGP\n", "#dgp_class = EndogenousDynamicPanelDGP\n", "#dgp_class = PastTreatmentHeteroDynamicPanelDGP\n", "dgp = dgp_class(n_periods, n_treatments, n_x).create_instance(s_x, sigma_x, sigma_y,\n", "                                                              conf_str, hetero_strength, true_hetero_inds,\n", "                                                              autoreg, state_effect,\n", "                                                              random_seed=368)"]}, {"cell_type": "code", "execution_count": 149, "metadata": {"collapsed": true}, "outputs": [], "source": ["Y, T, X, groups = dgp.observational_data(n_units, gamma, s_t, sigma_t, random_seed=1245)\n", "panelX = X.reshape(-1, n_periods, n_x)\n", "panelT = T.reshape(-1, n_periods, n_treatments)\n", "panelY = Y.reshape(-1, n_periods)"]}, {"cell_type": "code", "execution_count": 150, "metadata": {"collapsed": true}, "outputs": [], "source": ["true_effect_inds = []\n", "for t in range(n_treatments):\n", "    true_effect_inds += [t * (1 + n_x)] + (list(t * (1 + n_x) + 1 + hetero_inds) if len(hetero_inds)>0 else [])\n", "true_effect_params = dgp.true_hetero_effect[:, true_effect_inds].flatten()"]}, {"cell_type": "code", "execution_count": 151, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt \n", "from coverage_panel_hetero import add_vlines\n", "plt.figure(figsize=(20, 5))\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             true_effect_params.flatten(), fmt='*')\n", "add_vlines(n_periods, n_treatments, hetero_inds)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 152, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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***********************************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\n", "text/plain": ["<Figure size 1440x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x216 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.figure(figsize=(20, 5))\n", "plt.subplot(1, 2, 1)\n", "plt.plot(Y, label=\"Outcome\")\n", "plt.plot(T, label=\"Treatment\")\n", "plt.legend()\n", "plt.subplot(1, 2, 2)\n", "for it in range(2):\n", "    plt.plot(X[:, it], label=\"X[{}]\".format(it))\n", "plt.legend()\n", "plt.show()\n", "\n", "plt.figure(figsize=(20, 3))\n", "plt.subplot(1, 3, 1)\n", "for t in range(n_periods):\n", "    plt.hist(panelX[:, t, 0], alpha=.3, label=\"Period {}\".format(t))\n", "plt.title(\"X[0]\")\n", "plt.legend()\n", "\n", "plt.subplot(1, 3, 2)\n", "for t in range(n_periods):\n", "    plt.hist(panelT[:, t, 0], alpha=.3, label=\"Period {}\".format(t))\n", "plt.title(\"T\")\n", "plt.legend()\n", "\n", "plt.subplot(1, 3, 3)\n", "for t in range(n_periods):\n", "    plt.hist(panelY[:, t], alpha=.3, label=\"Period {}\".format(t))\n", "plt.title(\"Y\")\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dynamic DML"]}, {"cell_type": "code", "execution_count": 153, "metadata": {"collapsed": true}, "outputs": [], "source": ["from sklearn.linear_model import LinearRegression, LassoCV, Lasso, MultiTaskLasso, MultiTaskLassoCV\n", "from sklearn.model_selection import GroupKFold, KFold\n", "from sklearn.ensemble import GradientBoostingRegressor\n", "from sklearn.multioutput import MultiOutputRegressor\n", "import lightgbm as lgb\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "np.random.seed(123)\n", "from econml.sklearn_extensions.linear_model import SelectiveRegularization\n", "\n", "lasso_model = lambda : LassoCV(cv=3, n_alphas=6, max_iter=200)\n", "gb_model = lambda : GradientBoostingRegressor(n_estimators=100, max_depth=3,\n", "                                                 min_samples_leaf=50, min_impurity_decrease=.1)\n", "lgbm_model = lambda : lgb.LGBMRegressor()\n", "mlasso_model = lambda : MultiTaskLassoCV(cv=3, n_alphas=6, max_iter=200)\n", "mgb_model = lambda : MultiOutputRegressor(GradientBoostingRegressor(n_estimators=100, max_depth=3,\n", "                                                                       min_samples_leaf=50, min_impurity_decrease=.1))\n", "mlgbm_model = lambda : MultiOutputRegressor(lgb.LGBMRegressor())\n", "\n", "alpha_regs = [1e-4, 1e-3, 5e-2, 1e-1, .5, 1]\n", "slasso_model = lambda : SelectiveRegularization([0],\n", "                                                LassoCV(cv=KFold(n_splits=3, shuffle=True),\n", "                                                        alphas=alpha_regs, max_iter=200, fit_intercept=False),\n", "                                                fit_intercept=False)"]}, {"cell_type": "code", "execution_count": 154, "metadata": {"collapsed": true}, "outputs": [], "source": ["from hetero_panel_dynamic_dml import HeteroDynamicPanelDML\n", "\n", "est = HeteroDynamicPanelDML(model_t=mlasso_model(),\n", "                            model_y=lasso_model(),\n", "                            model_final=slasso_model(),\n", "                            n_cfit_splits=3).fit(Y, T, X, groups, hetero_inds=hetero_inds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Parameter Recovery and Confidence Intervals"]}, {"cell_type": "code", "execution_count": 155, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Effect Lag=0, TX=0: 0.841 (0.613, 1.068), (Truth=0.619)\n", "Effect Lag=0, TX=1: 0.706 (0.278, 1.135), (Truth=0.243)\n", "Effect Lag=1, TX=0: -1.351 (-2.221, -0.482), (Truth=-1.250)\n", "Effect Lag=1, TX=1: -0.000 (-1.165, 1.165), (Truth=-0.744)\n", "Effect Lag=2, TX=0: -1.001 (-2.640, 0.639), (Truth=-0.188)\n", "Effect Lag=2, TX=1: -1.023 (-2.034, -0.012), (Truth=-0.112)\n"]}], "source": ["param_hat = est.param\n", "conf_ints = est.param_interval(alpha=.01)\n", "for kappa in range(n_periods):\n", "    for t in range(n_treatments * (len(hetero_inds) + 1)):\n", "        param_ind = kappa * (len(hetero_inds) + 1) * n_treatments + t\n", "        print(\"Effect Lag={}, TX={}: {:.3f} ({:.3f}, {:.3f}), (Truth={:.3f})\".format(kappa, t,\n", "                                                                                    param_hat[param_ind],\n", "                                                                                    *conf_ints[param_ind],\n", "                                                                                    true_effect_params[param_ind]))"]}, {"cell_type": "code", "execution_count": 156, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(20, 5))\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             param_hat, fmt='o')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             true_effect_params.flatten(), fmt='o', alpha=.6)\n", "add_vlines(n_periods, n_treatments, hetero_inds)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 157, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(20, 5))\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             param_hat, yerr=(conf_ints[:, 1] - param_hat,\n", "                              param_hat - conf_ints[:, 0]), fmt='o')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             true_effect_params.flatten(), fmt='o', alpha=.6)\n", "add_vlines(n_periods, n_treatments, hetero_inds)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Benchmark Method Comparison"]}, {"cell_type": "code", "execution_count": 158, "metadata": {"collapsed": true}, "outputs": [], "source": ["from econml.utilities import cross_product\n", "from statsmodels.tools.tools import add_constant\n", "\n", "panelTX = np.zeros((n_units, n_periods, (1 + len(hetero_inds)) * n_treatments))\n", "for t in range(n_periods):\n", "    panelTX[:, t, :] = cross_product(add_constant(panelX[:, t, hetero_inds], has_constant='add'),\n", "                            panelT[:, t, :])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2.1 Regressing Y on {T[t]*X[t, hetero_inds]}"]}, {"cell_type": "code", "execution_count": 159, "metadata": {"collapsed": true}, "outputs": [], "source": ["est_lr = LinearRegression().fit(panelTX[:, ::-1, :].reshape((n_units, -1)), panelY[:, -1]).coef_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2.2 Controlling for Subsets of the States"]}, {"cell_type": "code", "execution_count": 160, "metadata": {"collapsed": true}, "outputs": [], "source": ["est_lr_x0 = lasso_model().fit(np.hstack([panelTX[:, ::-1, :].reshape(n_units, -1),\n", "                                  panelX[:, 0, :]]), panelY[:, -1]).coef_[:-n_x]\n", "est_lr_xfinal = lasso_model().fit(np.hstack([panelTX[:, ::-1, :].reshape(n_units, -1),\n", "                                  panelX[:, -1, :]]), panelY[:, -1]).coef_[:-n_x]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2.3 Controlling for Subsets of States with Static DML"]}, {"cell_type": "code", "execution_count": 161, "metadata": {"collapsed": true}, "outputs": [], "source": ["from econml.dml import LinearDMLCateEstimator\n", "dml_model = lambda : LinearDMLCateEstimator(model_y=lasso_model(), model_t=mlasso_model(),\n", "                                      n_splits=3, linear_first_stages=False)\n", "est_dml_x0 = dml_model().fit(panelY[:, -1], T=panelTX[:, ::-1, :].reshape(n_units, -1),\n", "                      X=None, W=panelX[:, 0, :]).intercept_\n", "est_dml_xfinal = dml_model().fit(panelY[:, -1], T=panelTX[:, ::-1, :].reshape(n_units, -1),\n", "                      X=None, W=panelX[:, -1, :]).intercept_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2.4 Direct version of Dynamic DML"]}, {"cell_type": "code", "execution_count": 162, "metadata": {"collapsed": true}, "outputs": [], "source": ["Y_cal = panelY[:, -1].copy()\n", "direct_theta = np.zeros((n_periods, (1 + len(hetero_inds) * n_treatments)))\n", "for t in np.arange(n_periods):\n", "    direct_theta[t, :] = lasso_model().fit(np.hstack([panelTX[:, n_periods - 1 - t, :],\n", "                                                      panelX[:, n_periods - 1 - t, :]]), Y_cal).coef_[:-n_x]\n", "    Y_cal -= np.dot(panelTX[:, n_periods - 1 - t, :], direct_theta[t, :])\n", "est_direct = direct_theta.flatten()"]}, {"cell_type": "code", "execution_count": 163, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(25, 5))\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) - .08,\n", "             est_direct, fmt='o', label='dyn-direct')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) - .04,\n", "             param_hat, fmt='*', label='dyn-dml')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             true_effect_params.flatten(), fmt='*', alpha=.6, label='true')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .04,\n", "             est_lr.flatten(), fmt='o', alpha=.6, label='direct')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .08,\n", "             est_lr_x0.flatten(), fmt='o', alpha=.6, label='init-ctrls')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .12,\n", "             est_dml_x0.flatten(), fmt='o', alpha=.6, label='init-ctrls-dml')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .16,\n", "             est_lr_xfinal.flatten(), fmt='o', alpha=.6, label='fin-ctrls')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .20,\n", "             est_dml_xfinal.flatten(), fmt='o', alpha=.6, label='fin-ctrls-dml')\n", "add_vlines(n_periods, n_treatments, hetero_inds)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 164, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1800x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(25, 5))\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) - .08,\n", "             est_direct, fmt='o', label='dyn-direct')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) - .04,\n", "             param_hat, yerr=(conf_ints[:, 1] - param_hat,\n", "                              param_hat - conf_ints[:, 0]), fmt='o', label='dyn-dml')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments),\n", "             true_effect_params.flatten(), fmt='*', alpha=.6, label='true')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .04,\n", "             est_lr.flatten(), fmt='o', alpha=.6, label='direct')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .08,\n", "             est_lr_x0.flatten(), fmt='o', alpha=.6, label='init-ctrls')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .12,\n", "             est_dml_x0.flatten(), fmt='o', alpha=.6, label='init-ctrls-dml')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .16,\n", "             est_lr_xfinal.flatten(), fmt='o', alpha=.6, label='fin-ctrls')\n", "plt.errorbar(np.arange(n_periods * (len(hetero_inds) + 1) * n_treatments) + .20,\n", "             est_dml_xfinal.flatten(), fmt='o', alpha=.6, label='fin-ctrls-dml')\n", "add_vlines(n_periods, n_treatments, hetero_inds)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Policy Effect"]}, {"cell_type": "code", "execution_count": 165, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Policy effect for treatment seq: \n", " [[1]\n", " [1]\n", " [1]]\n", " -1.529 (-3.291, 0.234), (truth=-0.830)\n"]}], "source": ["tau = np.random.binomial(1, .8, size=(n_periods, n_treatments))\n", "true_policy_effect = dgp.static_policy_effect(tau, mc_samples=1000)\n", "policy_effect_hat, policy_ints, policy_std = est.policy_effect(tau, X[:, hetero_inds], groups)\n", "print(\"Policy effect for treatment seq: \\n {}\\n {:.3f} ({:.3f}, {:.3f}), (truth={:.3f})\".format(tau,\n", "                                                                                                policy_effect_hat,\n", "                                                                                                *policy_ints,\n", "                                                                                                true_policy_effect))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.1 Benchmark Comparison"]}, {"cell_type": "code", "execution_count": 166, "metadata": {"collapsed": true}, "outputs": [], "source": ["def static_policy_effect(tau, hetero_params):\n", "    return np.sum([tau[t].reshape(1, -1) @\\\n", "                   hetero_params.reshape(n_periods, n_treatments, 1 + len(hetero_inds))[n_periods - 1 - t] @\\\n", "                   np.mean(add_constant(panelX[:, t, hetero_inds], has_constant='add'), axis=0)\n", "                   for t in range(n_periods)])"]}, {"cell_type": "code", "execution_count": 167, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x1080 with 16 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["test_policies = np.random.binomial(1, .8, size=(16, n_periods, n_treatments))\n", "test_policies[15, :] = 0\n", "\n", "plt.figure(figsize=(15, 15))\n", "for t, tau in enumerate(test_policies):\n", "    true_policy_effect = dgp.static_policy_effect(tau, mc_samples=1000)\n", "    policy_effect_hat, policy_ints, _ = est.policy_effect(tau, X[:, hetero_inds], groups)\n", "    plt.subplot(4, 4, t + 1)\n", "    plt.errorbar([-.08], [static_policy_effect(tau, est_direct)], fmt='o', alpha=.6, label='dyn-direct')\n", "    plt.errorbar([-.04], [policy_effect_hat], yerr=([policy_ints[1] - policy_effect_hat],\n", "                                                  [policy_effect_hat - policy_ints[0]]), fmt='o', label='dyn-dml')\n", "    plt.errorbar([-.02], [static_policy_effect(tau, est_lr)], fmt='o', alpha=.6, label='no-ctrls')\n", "    plt.errorbar([0], [true_policy_effect], fmt='*', alpha=.6, label='true')\n", "    plt.hlines([true_policy_effect], - .1, .10, linestyles='--', alpha=.4)\n", "    plt.errorbar([.02], [static_policy_effect(tau, est_lr_x0)], fmt='o', alpha=.6, label='init-ctrls')\n", "    plt.errorbar([.04], [static_policy_effect(tau, est_dml_x0)], fmt='o', alpha=.6, label='init-ctrls-dml')\n", "    plt.errorbar([.06], [static_policy_effect(tau, est_lr_xfinal)], fmt='o', alpha=.6, label='fin-ctrls')\n", "    plt.errorbar([.08], [static_policy_effect(tau, est_dml_xfinal)], fmt='o', alpha=.6, label='fin-ctrls-dml')\n", "    plt.title(\"{}\".format(tau.flatten()))\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Adaptive Policy"]}, {"cell_type": "code", "execution_count": 168, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["0.6225670514435359"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["# Optimal Contextual Binary Treatment Policy\n", "def adaptive_policy(t, x, period):\n", "    return 1*(dgp.hetero_effect_fn(n_periods - 1 - period, x) > 0)\n", "dgp.adaptive_policy_effect(adaptive_policy)"]}, {"cell_type": "code", "execution_count": 169, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["(0.8070629317620176,\n", " (0.6213266303331364, 0.9927992331908988),\n", " 0.09476516042842899)"]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["est.adaptive_policy_effect(X, groups, adaptive_policy)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Estimation Diagnostics"]}, {"cell_type": "code", "execution_count": 131, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.plot(est.param_stderr)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 132, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.imshow(est._M)\n", "plt.colorbar()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 133, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.imshow(est._Sigma)\n", "plt.colorbar()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}