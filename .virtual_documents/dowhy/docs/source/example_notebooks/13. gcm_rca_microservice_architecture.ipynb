


from IPython.display import Image
Image('microservice-architecture-dependencies.png', width=500) 





import pandas as pd

normal_data = pd.read_csv("rca_microservice_architecture_latencies.csv")
normal_data.head()





import pandas as pd
import matplotlib.pyplot as plt

# 使用pandas的scatter_matrix绘制散点图矩阵
axes = pd.plotting.scatter_matrix(normal_data, figsize=(10, 10), c='#ff0d57', alpha=0.2, hist_kwds={'color':['#1E88E5']}) 

# 遍历所有的子图ax,设置旋转角度和对齐方式
for ax in axes.flatten():
  ax.xaxis.label.set_rotation(90)
  ax.yaxis.label.set_rotation(0)
  ax.yaxis.label.set_ha('right')

# 设置直方图颜色








import networkx as nx
from dowhy import gcm
from dowhy.utils import plot, bar_plot

causal_graph = nx.DiGraph([('www', 'Website'),
                           ('Auth Service', 'www'),
                           ('API', 'www'),
                           ('Customer DB', 'Auth Service'),
                           ('Customer DB', 'API'),
                           ('Product Service', 'API'),
                           ('Auth Service', 'API'),
                           ('Order Service', 'API'),
                           ('Shipping Cost Service', 'Product Service'),
                           ('Caching Service', 'Product Service'),
                           ('Product DB', 'Caching Service'),
                           ('Customer DB', 'Product Service'),
                           ('Order DB', 'Order Service')])


plot(causal_graph, figure_size=[13, 13])








from scipy.stats import halfnorm

causal_model = gcm.StructuralCausalModel(causal_graph)

# 对于每个节点
for node in causal_graph.nodes:

  # 如果该节点没有父节点
  if len(list(causal_graph.predecessors(node))) > 0:
    
    # 为其指定线性加性噪声模型
    causal_model.set_causal_mechanism(node, gcm.AdditiveNoiseModel(gcm.ml.create_linear_regressor()))

  # 否则,为其指定半正态分布   
  else:
    causal_model.set_causal_mechanism(node, gcm.ScipyDistribution(halfnorm))








outlier_data = pd.read_csv("rca_microservice_architecture_anomaly.csv")
outlier_data





outlier_data.iloc[0]['Website']-normal_data['Website'].mean()








# 禁用计算Shapley值时的打印语句
gcm.config.disable_progress_bars()  

# 通过引导采样计算中值归因结果和不确定性区间
median_attribs, uncertainty_attribs = gcm.confidence_intervals(
    gcm.fit_and_compute(gcm.attribute_anomalies, 
                        causal_model,
                        normal_data,
                        target_node='Website',
                        anomaly_samples=outlier_data),
    num_bootstrap_resamples=10)

# 这段代码使用bootstrap重采样方法计算了异常延迟的归因结果及其置信区间:

# 禁用打印语句,使输出更清晰
# 调用gcm.confidence_intervals并传入gcm.fit_and_compute的结果
# gcm.fit_and_compute拟合因果模型并计算归因
# 设置目标节点为Website,使用正常数据拟合,异常数据检验
# 通过bootstrap采样10次计算置信区间
# 返回中值归因结果和置信区间表示不确定性
# 总体上利用bootstrap采样使归因结果更可靠,计算结果的置信区间表征不确定性。





median_attribs





bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')








outlier_data = pd.read_csv("rca_microservice_architecture_anomaly_1000.csv")
outlier_data.head()





outlier_data['Website'].mean() - normal_data['Website'].mean()








import numpy as np

# 定义计算分布变化归因的函数 
def calc_distrib_change():
  return gcm.distribution_change(
    causal_model, 
    normal_data.sample(frac=0.6), # 采样正常数据
    outlier_data.sample(frac=0.6), # 采样异常数据 
    'Website',
    difference_estimation_func=lambda x, y: np.mean(y) - np.mean(x)) # 差值函数为平均数之差

# 计算归因结果的置信区间
median_attribs, uncertainty_attribs = gcm.confidence_intervals(
  calc_distrib_change,  
  num_bootstrap_resamples = 10) 

# 绘制归因结果的条形图
bar_plot(median_attribs, uncertainty_attribs, 'Attribution Score')








# 定义进行干预采样的函数
def interventional_sampling():
  return gcm.fit_and_compute(
    gcm.interventional_samples, 
    causal_model,
    outlier_data,
    interventions = {
      "Caching Service": lambda x: x-1, # Caching Service延迟减1
      "Shipping Cost Service": lambda x: x+2 # Shipping Cost Service延迟增2  
    },
    observed_data=outlier_data
  )().mean().to_dict() # 返回干预后的平均延迟

# 计算置信区间
median_mean_latencies, uncertainty_mean_latencies = gcm.confidence_intervals(
  interventional_sampling,
  num_bootstrap_resamples=10)





# 获取干预前Website平均延迟
avg_website_latency_before = outlier_data.mean().to_dict()['Website']

# 构建条形图数据
bar_data = dict(before=avg_website_latency_before, 
                after=median_mean_latencies['Website'])
error_data = dict(before=np.array([avg_website_latency_before, 
                                   avg_website_latency_before]),
                  after=uncertainty_mean_latencies['Website'])

# 绘制条形图  
bar_plot(bar_data, error_data, 
         ylabel='Avg. Website Latency',
         figure_size=(3, 2),
         bar_width=0.4,
         xticks=['Before', 'After'],
         xticks_rotation=45)








from scipy.stats import truncexpon, halfnorm


def create_observed_latency_data(unobserved_intrinsic_latencies):
    observed_latencies = {}
    observed_latencies['Product DB'] = unobserved_intrinsic_latencies['Product DB']
    observed_latencies['Customer DB'] = unobserved_intrinsic_latencies['Customer DB']
    observed_latencies['Order DB'] = unobserved_intrinsic_latencies['Order DB']
    observed_latencies['Shipping Cost Service'] = unobserved_intrinsic_latencies['Shipping Cost Service']
    observed_latencies['Caching Service'] = np.random.choice([0, 1], size=(len(observed_latencies['Product DB']),),
                                                             p=[.5, .5]) * \
                                            observed_latencies['Product DB'] \
                                            + unobserved_intrinsic_latencies['Caching Service']
    observed_latencies['Product Service'] = np.maximum(np.maximum(observed_latencies['Shipping Cost Service'],
                                                                  observed_latencies['Caching Service']),
                                                       observed_latencies['Customer DB']) \
                                            + unobserved_intrinsic_latencies['Product Service']
    observed_latencies['Auth Service'] = observed_latencies['Customer DB'] \
                                         + unobserved_intrinsic_latencies['Auth Service']
    observed_latencies['Order Service'] = observed_latencies['Order DB'] \
                                          + unobserved_intrinsic_latencies['Order Service']
    observed_latencies['API'] = observed_latencies['Product Service'] \
                                + observed_latencies['Customer DB'] \
                                + observed_latencies['Auth Service'] \
                                + observed_latencies['Order Service'] \
                                + unobserved_intrinsic_latencies['API']
    observed_latencies['www'] = observed_latencies['API'] \
                                + observed_latencies['Auth Service'] \
                                + unobserved_intrinsic_latencies['www']
    observed_latencies['Website'] = observed_latencies['www'] \
                                    + unobserved_intrinsic_latencies['Website']

    return pd.DataFrame(observed_latencies)


def unobserved_intrinsic_latencies_normal(num_samples):
    return {
        'Website': truncexpon.rvs(size=num_samples, b=3, scale=0.2),
        'www': truncexpon.rvs(size=num_samples, b=2, scale=0.2),
        'API': halfnorm.rvs(size=num_samples, loc=0.5, scale=0.2),
        'Auth Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.2),
        'Product Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.2),
        'Order Service': halfnorm.rvs(size=num_samples, loc=0.5, scale=0.2),
        'Shipping Cost Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.2),
        'Caching Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.1),
        'Order DB': truncexpon.rvs(size=num_samples, b=5, scale=0.2),
        'Customer DB': truncexpon.rvs(size=num_samples, b=6, scale=0.2),
        'Product DB': truncexpon.rvs(size=num_samples, b=10, scale=0.2)
    }


normal_data = create_observed_latency_data(unobserved_intrinsic_latencies_normal(10000))








def unobserved_intrinsic_latencies_anomalous(num_samples):
    return {
        'Website': truncexpon.rvs(size=num_samples, b=3, scale=0.2),
        'www': truncexpon.rvs(size=num_samples, b=2, scale=0.2),
        'API': halfnorm.rvs(size=num_samples, loc=0.5, scale=0.2),
        'Auth Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.2),
        'Product Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.2),
        'Order Service': halfnorm.rvs(size=num_samples, loc=0.5, scale=0.2),
        'Shipping Cost Service': halfnorm.rvs(size=num_samples, loc=0.1, scale=0.2),
        'Caching Service': 2 + halfnorm.rvs(size=num_samples, loc=0.1, scale=0.1),
        'Order DB': truncexpon.rvs(size=num_samples, b=5, scale=0.2),
        'Customer DB': truncexpon.rvs(size=num_samples, b=6, scale=0.2),
        'Product DB': truncexpon.rvs(size=num_samples, b=10, scale=0.2)
    }

outlier_data = create_observed_latency_data(unobserved_intrinsic_latencies_anomalous(1000))



