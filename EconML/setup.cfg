[pycodestyle]
ignore=E402,W504
max-line-length=119

[pydocstyle]
; Use numpy style
convention=numpy

[options]
packages = find_namespace:
test_suite = econml.tests
tests_require =
    pytest
    pytest-xdist
    pytest-cov
    
[options.packages.find]
include =
    econml
    econml.*
exclude = 
    econml.tests

[options.package_data]
; include all CSV files as data
* = *.csv
    *.jbl

; coverage configuration
[coverage:run]
branch = True
; need to explicitly add support for multiprocessing for OrthoForest
concurrency =
    thread
    multiprocessing
source = econml
omit = econml/tests/*
relative_files = True

[coverage:report]
exclude_lines =
    raise NotImplementedError\("(Abstract method|Defer to inference)"\)