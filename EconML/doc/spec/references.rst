References
==========

.. [Ch<PERSON><PERSON><PERSON><PERSON>2016]
    <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and
    <PERSON><PERSON> <PERSON><PERSON>. Double Machine Learning for Treatment and Causal Parameters. *ArXiv e-prints*, July 2016.


.. [Chernozhukov2017]
    <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
    Orthogonal Machine Learning for Demand Estimation: High Dimensional
    Causal Inference in Dynamic Panels.
    *ArXiv e-prints*, December 2017.

.. [Chernozhukov2018]
    V<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.
    Two-Stage Estimation with a High-Dimensional Second Stage.
    2018.

.. [Hartford2017]
    <PERSON>, <PERSON>, <PERSON>, and <PERSON>.
    Deep IV: A flexible approach for counterfactual prediction.
    *Proceedings of the 34th International Conference on Machine Learning*, 2017.

.. [Jaggi2010]
    <PERSON> and <PERSON><PERSON>.
    A simple algorithm for nuclear norm regularized problems.
    *Proceedings of the 27th International Conference on Machine
    Learning (ICML-10), June 21-24, 2010, Haifa, Israel*, pages 471--478, 2010.

.. [<PERSON>2017]
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
    <PERSON>a-learners for estimating heterogeneous treatment effects using
    machine learning.
    *arXiv preprint arXiv:1706.03461*, 2017.
    URL http://arxiv.org/abs/1706.03461.

.. [Mackey2017]
    Lester W. Mackey, Vasilis Syrgkanis, and Ilias Zadik.
    Orthogonal machine learning: Power and limitations.
    *CoRR*, abs/1711.00342, 2017.
    URL http://arxiv.org/abs/1711.00342.

.. [Newey2003]
    W. K. Newey and J. L. Powell.
    Instrumental variable estimation of nonparametric models.
    *Econometrica*, 71 (5): 1565--1578, 2003.

.. [Foster2019]
    D. Foster and V. Syrgkanis.
    Orthogonal Statistical Learning.
    *arXiv preprint arXiv:1901.09036*, 2019.
    URL http://arxiv.org/abs/1901.09036.

.. [Wager2018]
    S. Wager and S. Athey. 
    Estimation and inference of heterogeneous treatment effects using random forests.
    *Journal of the American Statistical Association*, 113(523), pp.1228-1242, 2018.

.. [Athey2019]
    S. Athey, J. Tibshirani and S. Wager.
    Generalized Random Forests.
    *Annals of Statistics*, 2019

.. [Oprescu2019]
    M. Oprescu, V. Syrgkanis and Z. S. Wu.
    Orthogonal Random Forest for Causal Inference.
    *Proceedings of the 36th International Conference on Machine Learning*, 2019.
    URL http://proceedings.mlr.press/v97/oprescu19a.html.

.. [Nie2017]
    X. Nie and S. Wager.
    Quasi-Oracle Estimation of Heterogeneous Treatment Effects.
    *arXiv preprint arXiv:1712.04912*, 2017.
    URL http://arxiv.org/abs/1712.04912.

.. [Buhlmann2011]
    P. Bühlmann and S. van de Geer
    Statistics for High-Dimensional Data
    Springer Series in Statistics, 2011
    URL https://www.springer.com/gp/book/9783642201912

.. [Robins1994]
    Robins, J.M., Rotnitzky, A., and Zhao, L.P. (1994).
    Estimation of regression coefficients when some regressors are not always observed.
    Journal of the American Statistical Association 89,846–866.

.. [Bang]
    Bang, H. and Robins, J.M. (2005).
    Doubly robust estimation in missing data and causal inference models.
    Biometrics 61,962–972.

.. [Tsiatis]
    Tsiatis AA (2006).
    Semiparametric Theory and Missing Data.
    New York: Springer; 2006.

.. [Dudik2014]
    Dudík, M., Erhan, D., Langford, J., & Li, L. (2014).
    Doubly robust policy evaluation and optimization.
    Statistical Science, 29(4), 485-511.

.. [Athey2017]
    Athey, S., & Wager, S. (2017).
    Efficient policy learning.
    arXiv preprint arXiv:1702.02896.

.. [Friedberg2018]
    Friedberg, R., Tibshirani, J., Athey, S., & Wager, S. (2018).
    Local linear forests.
    arXiv preprint arXiv:1807.11408.

.. [Lundberg2017]
    Lundberg, S.,  Lee, S. (2017).
    A Unified Approach to Interpreting Model Predictions.
    URL https://arxiv.org/abs/1705.07874

.. [Lewis2021] 
    Lewis, G., Syrgkanis, V. (2021).
    Double/Debiased Machine Learning for Dynamic Treatment Effects.
    URL https://arxiv.org/abs/2002.07285

.. [Hernan2010]
    Hernán, Miguel A., and James M. Robins (2010).
    Causal inference.
    URL https://www.hsph.harvard.edu/miguel-hernan/causal-inference-book/

.. [Syrgkanis2019]
    Syrgkanis, V., Lei, V., Oprescu, M., Hei, M., Battocchi, K., Lewis, G. (2019)
    Machine Learning Estimation of Heterogeneous Treatment Effects with Instruments
    URL https://arxiv.org/abs/1905.10176
