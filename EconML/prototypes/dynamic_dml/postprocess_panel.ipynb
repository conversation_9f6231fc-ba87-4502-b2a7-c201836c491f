{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [], "source": ["import numpy as np\n", "from dynamic_panel_dgp import DynamicPanelDGP\n", "import matplotlib.pyplot as plt\n", "import scipy \n", "from coverage_panel import add_vlines"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": false}, "outputs": [], "source": ["import os\n", "import joblib\n", "n_exps = 10\n", "dirname = os.path.join('results', 'constant')\n", "param_str = (\"n_exps_{}_n_units_500_n_periods_3_n_t_2_n_x_450_s_x_2\"\n", "              \"_s_t_2_sigma_x_1_sigma_t_1_sigma_y_1_conf_str_1_gamma_0.2\").format(n_exps)\n", "results = joblib.load(os.path.join(dirname, \"results_{}.jbl\".format(param_str)))\n", "dgp = joblib.load(os.path.join(dirname, \"dgp_obj_{}.jbl\".format(param_str)))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"collapsed": false}, "outputs": [], "source": ["n_periods = dgp.n_periods\n", "n_treatments = dgp.n_treatments"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": false}, "outputs": [], "source": ["results = np.array(results)\n", "points = results[:, 0]\n", "lowers = results[:, 1]\n", "uppers = results[:, 2]\n", "stderrs = results[:, 3]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABDAAAAFgCAYAAABNIolGAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAAAMB5/z+N90I4j7TBvAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["true_effect_params = dgp.true_effect.flatten()\n", "\n", "plt.figure(figsize=(15, 5))\n", "inds = np.arange(points.shape[1])\n", "plt.violinplot(points, positions=inds, showmeans=True)\n", "plt.scatter(inds, true_effect_params, marker='o',\n", "            color='#D43F3A', s=10, zorder=3, alpha=.5)\n", "add_vlines(n_periods, n_treatments)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABDAAAAFgCAYAAABNIolGAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nnm02AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 5))\n", "inds = np.arange(points.shape[1])\n", "plt.violinplot(stderrs, positions=inds, showmeans=True)\n", "true_std = np.std(points, axis=0)\n", "true_std_error = (true_std * (np.sqrt((n_exps-1)/scipy.stats.chi2.ppf((1-.05/2), n_exps-1)) - 1),\n", "                  true_std * (1 - np.sqrt((n_exps-1)/scipy.stats.chi2.ppf((.05/2), n_exps-1))))\n", "plt.errorbar(inds, true_std, yerr=true_std_error, fmt='o',\n", "            color='#D43F3A', elinewidth=2, alpha=.9, capthick=.5, uplims=True, lolims=True)\n", "add_vlines(n_periods, n_treatments)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABDAAAAFgCAYAAABNIolGAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 1080x360 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["coverage = np.mean((true_effect_params.reshape(1, -1) <= uppers) & (\n", "    true_effect_params.reshape(1, -1) >= lowers), axis=0)\n", "plt.figure(figsize=(15, 5))\n", "inds = np.arange(points.shape[1])\n", "plt.scatter(inds, coverage)\n", "add_vlines(n_periods, n_treatments)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}