


# 导入所需的库和模块
import numpy as np  # NumPy 库用于数值计算
import pandas as pd  # Pandas 库用于数据处理和分析

from dowhy import CausalModel  # 从 DoWhy 库中导入 CausalModel 类，用于因果模型的创建和分析
import dowhy.datasets  # 导入 DoWhy 的数据集模块，这通常包含一些内置的数据集用于实验和测试

# Warnings 和日志处理
import warnings  # 导入 warnings 库，用于警告处理
warnings.filterwarnings('ignore')  # 过滤掉所有的警告信息，以使输出更加整洁






# 使用 DoWhy 库的 `linear_dataset` 函数创建一个数据集。
# 数据集包含一个混杂因素（num_common_causes=1）、一个中介变量（num_frontdoor_variables=1），
# 以及一个处理变量（num_treatments=1）。样本数量为 10000。
data = dowhy.datasets.linear_dataset(
    beta=10,  # 因果效应的大小
    num_common_causes=1,  # 混杂因素的数量
    num_samples=10000,  # 样本数量
    num_instruments=0,  # 工具变量的数量
    num_effect_modifiers=0,  # 效应修饰变量的数量
    num_treatments=1,  # 处理（或干预）变量的数量
    num_frontdoor_variables=1,  # 前门变量（即中介变量）的数量
    treatment_is_binary=False,  # 处理变量是否是二进制（True 或 False）
    outcome_is_binary=False  # 结果变量是否是二进制（True 或 False）
)

# 从返回的数据字典中提取 DataFrame
df = data['df']

# 打印 DataFrame 的前几行以查看数据
print(df.head())






# 使用 CausalModel 类创建一个因果模型。
# 我们使用之前生成的 DataFrame（df）以及从数据字典（data）中提取的处理变量名、结果变量名和因果图（gml_graph）。
model = CausalModel(
    df,  # 数据集
    data["treatment_name"],  # 处理（或干预）变量的名称
    data["outcome_name"],  # 结果变量的名称
    data["gml_graph"],  # 因果图的 GML（Graph Modeling Language）表示
    missing_nodes_as_confounders=True  # 如果因果图中缺少某些节点，则将其视为潜在的混杂因素
)

# 使用 view_model 方法可视化因果模型。
# 这通常会生成一个 PNG 图像文件，名为 "causal_model.png"。
model.view_model()





# Natural direct effect (NDE)

# 使用 model 的 identify_effect 方法来识别自然直接效应（Natural Direct Effect, NDE）。
# 我们设置 estimand_type 为 "nonparametric-nde" 来指定我们想要非参数化的 NDE。
# 如果因果效应不可识别（例如，因果图不满足所有假设），proceed_when_unidentifiable=True 允许方法继续进行。
identified_estimand_nde = model.identify_effect(
    estimand_type="nonparametric-nde",  # 指定我们要识别的效应类型为非参数化的自然直接效应
    proceed_when_unidentifiable=True  # 即使效应不可识别，也继续进行
)

# 打印识别的自然直接效应（NDE）的估计量。
# 这将给出一个公式或表示，描述了如何计算 NDE。
print(identified_estimand_nde)



# 使用 model 的 identify_effect 方法来识别自然间接效应（Natural Indirect Effect, NIE）。
# 我们设置 estimand_type 为 "nonparametric-nie" 来指定我们想要非参数化的 NIE。
# 如果因果效应不可识别（例如，因果图不满足所有假设），proceed_when_unidentifiable=True 允许方法继续进行。
identified_estimand_nie = model.identify_effect(
    estimand_type="nonparametric-nie",  # 指定我们要识别的效应类型为非参数化的自然间接效应
    proceed_when_unidentifiable=True  # 即使效应不可识别，也继续进行
)

# 打印识别的自然间接效应（NIE）的估计量。
# 这将给出一个公式或表示，描述了如何计算 NIE。
print(identified_estimand_nie)









# 导入 DoWhy 库中的线性回归估计器
import dowhy.causal_estimators.linear_regression_estimator

# 使用 model 的 estimate_effect 方法来估算自然间接效应（NIE）。
# 我们使用了之前识别的 estimand（identified_estimand_nie）和指定了使用两阶段回归方法（"mediation.two_stage_regression"）。
# 此外，我们还设置了不生成置信区间（confidence_intervals=False）和不进行显著性检验（test_significance=False）。
causal_estimate_nie = model.estimate_effect(
    identified_estimand_nie,  # 使用之前识别的自然间接效应（NIE）的估计量
    method_name="mediation.two_stage_regression",  # 指定使用两阶段回归作为估计方法
    confidence_intervals=False,  # 不计算置信区间
    test_significance=False,  # 不进行显著性检验
    method_params={
        'first_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator,  # 指定第一阶段使用线性回归模型
        'second_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator  # 指定第二阶段也使用线性回归模型
    }
)

# 打印出自然间接效应（NIE）的因果估计结果。
print(causal_estimate_nie)






print(causal_estimate_nie.value, data["ate"])








# 使用 model 的 estimate_effect 方法来估算自然直接效应（NDE，Natural Direct Effect）。
# 我们使用了之前识别的 estimand（identified_estimand_nde）并指定了使用两阶段回归方法（"mediation.two_stage_regression"）。
# 此外，我们还设置了不生成置信区间（confidence_intervals=False）和不进行显著性检验（test_significance=False）。
causal_estimate_nde = model.estimate_effect(
    identified_estimand_nde,  # 使用之前识别的自然直接效应（NDE）的估计量
    method_name="mediation.two_stage_regression",  # 指定使用两阶段回归作为估计方法
    confidence_intervals=False,  # 不计算置信区间
    test_significance=False,  # 不进行显著性检验
    method_params={
        'first_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator,  # 指定第一阶段使用线性回归模型
        'second_stage_model': dowhy.causal_estimators.linear_regression_estimator.LinearRegressionEstimator  # 指定第二阶段也使用线性回归模型
    }
)

# 打印出自然直接效应（NDE）的因果估计结果。
print(causal_estimate_nde)




