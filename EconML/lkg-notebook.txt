Jinja2==3.1.2
Keras-Applications==1.0.8; python_version<='3.8'
Keras-Preprocessing==1.1.2; python_version<='3.8'
Keras==2.3.1; python_version<='3.8'
Markdown==3.4.3; python_version<='3.8'
MarkupSafe==2.1.2
Pillow==9.5.0
PyYAML==6.0
Pygments==2.15.1
QtPy==2.3.1
Send2Trash==1.8.2
Werkzeug==2.3.4; python_version<='3.8'
absl-py==1.4.0; python_version<='3.8'
anyio==3.6.2
argon2-cffi-bindings==21.2.0
argon2-cffi==21.3.0
arrow==1.2.3
asttokens==2.2.1
astunparse==1.6.3; python_version<='3.8'
attrs==23.1.0
backcall==0.2.0
beautifulsoup4==4.12.2
bleach==6.0.0
cachetools==4.2.4; python_version<='3.8'
certifi==2023.5.7; python_version<='3.8'
cffi==1.15.1
charset-normalizer==3.1.0; python_version<='3.8'
cloudpickle==2.2.1
comm==0.1.3
cycler==0.11.0
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
dowhy==0.8; '3.9'<=python_version
executing==1.2.0
fastjsonschema==2.16.3
fonttools==4.39.4
fqdn==1.5.1
gast==0.3.3; python_version<='3.8'
google-auth-oauthlib==0.4.6; python_version<='3.8'
google-auth==1.35.0; python_version<='3.8'
google-pasta==0.2.0; python_version<='3.8'
graphviz==0.20.1
grpcio==1.54.2; python_version<='3.8'
h5py==2.10.0; python_version<='3.8'
idna==3.4
importlib-metadata==6.6.0
importlib-resources==5.12.0; python_version<='3.8'
ipykernel==6.23.1
ipython-genutils==0.2.0
ipython==8.12.2; python_version<='3.8'
ipython==8.13.2; '3.9'<=python_version
ipywidgets==8.0.6
isoduration==20.11.0
jedi==0.18.2
joblib==1.2.0
jsonpointer==2.3
jsonschema==4.17.3
jupyter-console==6.6.3
jupyter-events==0.6.3
jupyter==1.0.0
jupyter_client==8.2.0
jupyter_core==5.3.0
jupyter_server==2.5.0
jupyter_server_terminals==0.4.4
jupyterlab-pygments==0.2.2
jupyterlab-widgets==3.0.7
kiwisolver==1.4.4
lightgbm==3.3.5
llvmlite==0.40.0
matplotlib-inline==0.1.6
matplotlib==3.5.3
mistune==2.0.5
mpmath==1.3.0; '3.9'<=python_version
nbclassic==1.0.0
nbclient==0.7.4
nbconvert==7.4.0
nbformat==5.8.0
nest-asyncio==1.5.6
networkx==3.1; '3.9'<=python_version
notebook==6.5.4
notebook_shim==0.2.3
numba==0.57.0
numpy==1.23.5; python_version<='3.8'
numpy==1.24.3; '3.9'<=python_version
oauthlib==3.2.2; python_version<='3.8'
opt-einsum==3.3.0; python_version<='3.8'
packaging==23.1
pandas==2.0.1
pandocfilters==1.5.0
parso==0.8.3
patsy==0.5.3
pexpect==4.8.0
pickleshare==0.7.5
pkgutil_resolve_name==1.3.10; python_version<='3.8'
platformdirs==3.5.1
prometheus-client==0.16.0
prompt-toolkit==3.0.38
protobuf==3.20.3; python_version<='3.8'
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
pyasn1-modules==0.3.0; python_version<='3.8'
pyasn1==0.5.0; python_version<='3.8'
pycparser==2.21
pydot==1.4.2; '3.9'<=python_version
pyparsing==3.0.9
pyrsistent==0.19.3
python-dateutil==2.8.2
python-json-logger==2.0.7
pytz==2023.3
pyzmq==25.0.2
qtconsole==5.4.3
requests-oauthlib==1.3.1; python_version<='3.8'
requests==2.30.0; python_version<='3.8'
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rsa==4.9; python_version<='3.8'
scikit-learn==1.2.2
scipy==1.10.1; '3.9'<=python_version
scipy==1.4.1; python_version<='3.8'
seaborn==0.12.2
shap==0.41.0
six==1.16.0
slicer==0.0.7
sniffio==1.3.0
soupsieve==2.4.1
sparse==0.14.0
stack-data==0.6.2
statsmodels==0.14.0
sympy==1.12; '3.9'<=python_version
tensorboard-plugin-wit==1.8.1; python_version<='3.8'
tensorboard==2.2.2; python_version<='3.8'
tensorflow-estimator==2.2.0; python_version<='3.8'
tensorflow==2.2.0; python_version<='3.8'
termcolor==2.3.0; python_version<='3.8'
terminado==0.17.1
threadpoolctl==3.1.0
tinycss2==1.2.1
tornado==6.3.2
tqdm==4.65.0
traitlets==5.9.0
typing_extensions==4.5.0
tzdata==2023.3
uri-template==1.2.0
urllib3==2.0.2; python_version<='3.8'
wcwidth==0.2.6
webcolors==1.13
webencodings==0.5.1
websocket-client==1.5.1
widgetsnbextension==4.0.7
wrapt==1.15.0; python_version<='3.8'
xgboost==1.7.5
zipp==3.15.0